kind: pipeline
type: docker
name: 工作台测试环境

# workspace:
#   path: mrcuix

# 挂载 host 缓存目录
volumes:
  - name: cache
    host:
      path: /data/drone_cache/wx-qy-program_cache

clone:
  depth: 20
  disable: false # 启用代码拉取

steps:
  - name: 开始构建工作台通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        构建人: {{.Build.AuthorName}}({{.Build.AuthorEmail}})
        > 正在构建: data_analysis 项目
        执行编号: {{.Build.Number}}
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./node_modules
        # - ./apps/data_analysis/node_modules
        # - ./packages/utils/node_modules
        # - ./packages/wecom/node_modules

  - name: setup
    image: node:alpine
    depends_on: [restore-cache]
    pull: if-not-exists
    environment:
      NODE_OPTIONS: --max_old_space_size=8192
    commands:
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - rm -rf apps/data_analysis/dist  # 添加这行，清理旧的构建文件
      - corepack enable
      - corepack prepare pnpm@9.12.0 --activate
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm config set store-dir .pnpm-store
      - pnpm install --no-frozen-lockfile
      - pnpm --filter @ly/abu-ai build:widget
      - pnpm --filter data_analysis build:test
      - ls -la

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    depends_on: [setup]
    volumes:
      - name: cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./node_modules
        # - ./apps/data_analysis/node_modules
        # - ./packages/utils/node_modules
        # - ./packages/wecom/node_modules
  # - name: debug-git
  #   image: alpine/git
  #   commands:
  #     - git rev-parse HEAD  # 显示当前 commit hash
  #     - git log -1         # 显示最后一次提交信息

  - name: deploy
    image: appleboy/drone-scp
    pull: if-not-exists
    depends_on: [rebuild-cache]
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      source: apps/data_analysis/dist/*
      target:
        from_secret: TEST_TARGET_PATH
      strip_components: 3 # 用来控制在复制文件时要去除的路径层级数量的。
      overwrite: true
      debug: true  # 添加调试输出，方便排查问题

  - name: 构建结果通知
    image: lazytim/drone-wechat-robot:v1.0
    depends_on: [deploy]
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{if eq .Build.Status "success"}}
        前端测试环境 - <font color="info">**[成功]**</font>
        {{else}}
        前端测试环境 - <font color="red">**[失败]**</font>
        {{end}}
        仓库: `{{.Repo.Name}}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        提交: [{{.Build.ShortCommit}}]({{.Build.Link}})
        构建人: {{.Build.AuthorName}}
        执行编号: {{.Build.Number}}
        
        > <font color="comment">{{.Build.Message}}</font>
    when:
      status:
        - success
        - failure

# 触发条件：当 data_analysis 项目或其依赖包发生变化时
trigger:
  branch:
    - workspace
  event:
    - push
  # paths:
  #   - 'apps/data_analysis/**'
  #   - 'packages/utils/**'
  #   - 'packages/wecom/**'

---
kind: pipeline
type: docker
name: aBuAI测试环境

volumes:
  - name: cache
    host:
      path: /data/drone_cache/wx-qy-program_cache

clone:
  depth: 20
  disable: false


steps:
  - name: 开始构建阿布智能通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        构建人: {{.Build.AuthorName}}({{.Build.AuthorEmail}})
        > 正在构建: aBuAI 项目
        执行编号: {{.Build.Number}}
    when:
      status:
        - started

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./node_modules

  - name: setup
    image: node:alpine
    depends_on: [restore-cache]
    pull: if-not-exists
    environment:
      NODE_OPTIONS: --max_old_space_size=8192
    commands:
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - rm -rf apps/aBuAI/dist
      - corepack enable
      - corepack prepare pnpm@9.12.0 --activate
      - pnpm config set registry https://registry.npmmirror.com
      - pnpm config set store-dir .pnpm-store
      - pnpm install --no-frozen-lockfile
      - pnpm --filter @ly/abu-ai export
      - pnpm --filter @ly/abu-ai build:widget
      - cp -r apps/aBuAI/.widget apps/aBuAI/dist/  # 添加这行，将 .widget 复制到 dist 目录
      - ls apps/aBuAI/dist/ -la
      # - cd apps/aBuAI
      # - pnpm run export
      # - pnpm run build:widget
      # - mkdir -p dist
      # - if [ -d ".widget" ]; then cp -r .widget dist/; fi
      # - cd ../../
      # - ls apps/aBuAI/dist/ -la

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    depends_on: [setup]
    volumes:
      - name: cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./node_modules

  - name: deploy
    image: appleboy/drone-scp
    pull: if-not-exists
    depends_on: [rebuild-cache]
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      source: apps/aBuAI/dist/*
      target:
        from_secret: TEST_CHAT_TARGET_PATH
      strip_components: 2
      overwrite: true
      debug: true

  - name: 构建结果通知
    image: lazytim/drone-wechat-robot:v1.0
    depends_on: [deploy]
    settings:
      key: f2f92eaf-47f3-42db-a59f-ba466c48621d
      msgtype: markdown.template
      raw: true
      content: |
        {{if eq .Build.Status "success"}}
        前端测试环境 - <font color="info">**[成功]**</font>
        {{else}}
        前端测试环境 - <font color="red">**[失败]**</font>
        {{end}}
        仓库: `{{.Repo.Name}}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        提交: [{{.Build.ShortCommit}}]({{.Build.Link}})
        构建人: {{.Build.AuthorName}}
        执行编号: {{.Build.Number}}
        
        > <font color="comment">{{.Build.Message}}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - abu
  event:
    - push
  # paths:
  #   - 'apps/aBuAI/**'
  #   - 'packages/utils/**'
  #   - 'packages/wecom/**'
