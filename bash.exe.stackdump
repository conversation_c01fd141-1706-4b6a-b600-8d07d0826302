Stack trace:
Frame         Function      Args
0007FFFFAC40  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B40) msys-2.0.dll+0x1FE8E
0007FFFFAC40  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAF18) msys-2.0.dll+0x67F9
0007FFFFAC40  000210046832 (000210286019, 0007FFFFAAF8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC40  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC40  000210068E24 (0007FFFFAC50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAF20  00021006A225 (0007FFFFAC50, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB8C5E0000 ntdll.dll
7FFB8AC40000 KERNEL32.DLL
7FFB898C0000 KERNELBASE.dll
7FFB8B390000 USER32.dll
7FFB89D20000 win32u.dll
7FFB8C380000 GDI32.dll
7FFB8A1A0000 gdi32full.dll
7FFB89770000 msvcp_win.dll
7FFB89ED0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB8B220000 advapi32.dll
7FFB8B7B0000 msvcrt.dll
7FFB8B2E0000 sechost.dll
7FFB8C1E0000 RPCRT4.dll
7FFB88C70000 CRYPTBASE.DLL
7FFB89820000 bcryptPrimitives.dll
7FFB8B5C0000 IMM32.DLL
