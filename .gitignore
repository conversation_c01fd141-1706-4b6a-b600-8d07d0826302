# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
/node_modules
# dependencies
apps/**/node_modules
packages/**/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
apps/**/.next
apps/**/out/
apps/aBuAI/.widget/
apps/**/dist/
dist.zip
.widget.zip

# production
packages/**/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
dev

.vscode
.idea
# docker-compose env files
.env

*.key
*.key.pub

masks.json
