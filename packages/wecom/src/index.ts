import * as ww from '@wecom/jssdk'
import { WWLoginPanelSizeType, WWLoginRedirectType, WWLoginType, WWLoginInstance } from '@wecom/jssdk'
export * from '@wecom/jssdk'
// 企业微信登录
interface WecomLoginConfig {
  el: string;
  appid: string;
  agentid: string;
  redirectUri: string;
  state?: string;
  loginType?: WWLoginType;
  panelSize?: WWLoginPanelSizeType;
  redirectType?: WWLoginRedirectType;
  onSuccess?: (code: string) => void;
  onFail?: (error: any) => void;
  onCheck?: (isWeComLogin: boolean) => void;
}
// 相关企业微信文档 https://developer.work.weixin.qq.com/document/path/98152
class WecomLogin {
  private wwLogin: WWLoginInstance | null = null;
  private config: WecomLoginConfig;

  constructor(config: WecomLoginConfig) {
    this.config = {
      loginType: WWLoginType.corpApp,
      state: 'loginState',
      panelSize: WWLoginPanelSizeType.small,
      redirectType: WWLoginRedirectType.callback,
      ...config
    };
  }

  mount() {
    if (this.wwLogin) {
      return;
    }

    this.wwLogin = ww.createWWLoginPanel({
      el: this.config.el,
      params: {
        login_type: this.config.loginType || WWLoginType.corpApp,
        appid: this.config.appid,
        agentid: this.config.agentid,
        redirect_uri: this.config.redirectUri,
        state: this.config.state,
        panel_size: this.config.panelSize,
        redirect_type: this.config.redirectType,
      },
      onCheckWeComLogin: ({ isWeComLogin }) => {
        this.config.onCheck?.(isWeComLogin);
      },
      onLoginSuccess: ({ code }) => {
        this.config.onSuccess?.(code);
      },
      onLoginFail: (err) => {
        this.config.onFail?.(err);
      },
    });
  }

  unmount() {
    this.wwLogin?.unmount();
    this.wwLogin = null;
  }
}

export default WecomLogin;
