import currency from "currency.js"
import { DIGITS } from "./constants"
import { printFn } from "./number"

/**
 * 格式化价格 (乘以)
 * @param {number} val
 * @returns
 */
export function formatPriceMul(val: string | number, digit = DIGITS.PRICE) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化价格 (除以)
 * @param {number} val
 * @returns
 */
export function formatPriceDiv(val: number, digit = DIGITS.PRICE) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化数据显示，常用于价格显示
 * @param {number} value 值
 * @param {boolean} showZero 是否显示0
 * @param {string} suffix 后缀
 * @param {string} prefix 前缀 *
 * @returns prefix=‘￥’传入 10120.10 返回 ￥10,120.1
 */
export function formatPriceSymbol({
  value = 0,
  showZero = false,
  suffix = '',
  prefix = '',
}: {
  value: number
  prefix?: string
  suffix?: string
  showZero?: boolean
}) {
  if (value === 0) {
    return showZero ? `${value}` : ''
  }
  else {
    // const absVal = Math.abs(value)
    // return value > 0 ? `${prefix}${absVal}${suffix}` : `-${prefix}${absVal}${suffix}`
    let formatValue = currency(value, { symbol: prefix, precision: 2 }).format() // 格式化
    formatValue = formatValue.replace(/(\.\d*?)0+$/, '$1').replace(/\.$/, '') // 去除小数点后面的0
    if (suffix)
      formatValue += suffix

    return formatValue
  }
}
