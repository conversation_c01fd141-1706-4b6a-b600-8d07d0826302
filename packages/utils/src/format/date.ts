import dayjs from "dayjs"
import { IMG_CND_Prefix, URL_REGEXP } from "./constants"

/**
 *  格式化时间
 * @param {string} val 时间字符串，为空返回空
 * @returns
 */
export function formatTime(val: dayjs.ConfigType, emptyTips = '') {
  return val === '' || val == null ? emptyTips : dayjs(val).format('YYYY-MM-DD HH:mm:ss')
}

/**
 *  格式化时间
 * @param {string} val 时间字符串，为空返回空
 * @returns
 */
export function formatDate(val = '', emptyTips = '') {
  return val === '' || val == null ? emptyTips : dayjs(val).format('YYYY-MM-DD')
}


/**
 *  czm格式化时间（筛选用）
 * @param {Array} val 开始时间和结束时间数组
 * @returns {object} 返回处理后的数组
 */
export function searchFormatDate(val: dayjs.ConfigType[]) {
  const arr = { date_min: '', date_max: '' }
  if (val) {
    arr.date_min = `${dayjs(val[0]).format('YYYY-MM-DD')} 00:00:00`
    arr.date_max = `${dayjs(val[1]).add(1, 'day').format('YYYY-MM-DD')} 00:00:00`
  }
  return arr
}

// 当返回单个时间用
export function searchOneFormatDate(val: dayjs.ConfigType) {
  return `${dayjs(val).format('YYYY-MM-DD')} 00:00:00`
}

const DateAddr = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

/**
 *
 * @param {string} timestamp 时间戳或者Date格式
 * @param {string} str YYYY-MM-dd hh:mm;ss
 * @param {string} lang zh | en  输出中文还是英文
 * @returns
 */
export function formatTimeCustomized(timestamp: number | string | Date, str = 'yyyy-MM-dd hh:mm:ss', lang = 'zh') {
  const currentTime = new Date(timestamp)
  const o = {
    'y+': currentTime.getFullYear(), // 年
    'M+': currentTime.getMonth() + 1,
    'd+': currentTime.getDate(),
    'h+': currentTime.getHours(),
    'm+': currentTime.getMinutes(),
    's+': currentTime.getSeconds(),
  }
  if (lang === 'zh') {
    let item: keyof typeof o
    for (item in o) {
      if (new RegExp(`(${item})`).test(str)) {
        let temp = ''
        if (RegExp.$1.length === 1)
          temp = `${o[item]}`
        else
          temp = (`00${o[item]}`).split('').reverse().slice(0, RegExp.$1.length).reverse().join('')

        str = str.replace(RegExp.$1, temp)
      }
    }
  }
  else if (lang === 'en') {
    let newStr = ''
    let item
    for (item in o) {
      if (new RegExp(`(${item})`).test(str)) {
        let temp = ''
        if (item === 'h+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
        if (item === 'm+') {
          temp = `${o[item]}`
          newStr = `:${newStr}${temp}`
        }
        if (item === 's+') {
          temp = `${o[item]}`
          newStr = `:${newStr}${temp}`
        }
        if (item === 'M+') {
          temp = DateAddr[o[item] - 1]
          newStr = `${newStr + temp} `
        }
        if (item === 'd+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
        if (item === 'y+') {
          temp = `${o[item]}`
          newStr = newStr + temp
        }
      }
    }
    str = newStr
  }
  return str
}
export const formatDateTime = (val: number | string | Date, fmt = 'YYYY-MM-DD HH:mm:ss') => {
  if (val) {
    const time = new Date(val)
    const Y = time.getFullYear()
    const M = time.getMonth() + 1
    const d = time.getDate()
    const h = time.getHours()
    const m = time.getMinutes()
    const s = time.getSeconds()

    fmt = fmt
      .replace('YYYY', Y.toString())
      .replace('MM', M.toString().padStart(2, '0'))
      .replace('DD', d.toString().padStart(2, '0'))
      .replace('HH', h.toString().padStart(2, '0'))
      .replace('mm', m.toString().padStart(2, '0'))
      .replace('ss', s.toString().padStart(2, '0'))
    return fmt
  }
  else {
    return val
  }
}
