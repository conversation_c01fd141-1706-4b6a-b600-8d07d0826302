
// 正则 匹配 尾部井号
const WellNumberReg = /\#$/

/**
 * 移除井号
 * @param {string} val code 编码
 * @returns
 */
export function formatRemoveHashTag(val = '') {
  //
  return WellNumberReg.test(val) ? val.replace('#', '') : val
}

/**
 * 格式化编码+名称显示方式，若code和name一致则只显示一个
 * @param {string} code 编码
 * @param {string} name 名称
 * @param {*} mode 模式 both:code + 名称 name: 仅显示名称
 * @returns
 */
export function formatHashTag(code = '', name = '', mode = 'both') {
  if (!code && !name)
    return ''
  if (mode === 'both')
    return code === name ? code : `${formatRemoveHashTag(code)}# ${name}`
  else if (mode === 'name')
    return `${name}`
}

export function formatBoolean(val: number) {
  if (val !== null)
    return val === 1 ? '是' : '否'
  else
    return ''
}
