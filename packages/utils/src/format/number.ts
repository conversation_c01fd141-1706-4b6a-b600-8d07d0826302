
/**
 * @description 数字转中文数码
 *
 * @param {number | string}   num     数字[正整数]
 * @param {string}          type    文本类型，lower|upper，默认upper
 *
 * @example number2text(100000000) => "壹亿元整"
 */

import currency from "currency.js"
import { DIGITS } from './constants'

export function number2text(number: number, type: 'lower' | 'upper' | 'decimal' | 'maxNumber' = 'upper') {
  // 配置
  const confs = {
    lower: {
      num: ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'],
      unit: ['', '十', '百', '千', '万'],
      level: ['', '万', '亿'],
    },
    upper: {
      num: ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'],
      unit: ['', '拾', '佰', '仟'],
      level: ['', '万', '亿'],
    },
    decimal: {
      unit: ['分', '角'],
    },
    maxNumber: 999999999999.99,
  }

  // 过滤不合法参数
  if (Number(number) > confs.maxNumber) {
    console.error(`The maxNumber is ${confs.maxNumber}. ${number} is bigger than it!`)
    return false
  }

  const conf: any = confs[type]
  const numbers = String(Number(number).toFixed(2)).split('.')
  const integer = numbers[0].split('')
  const decimal = Number(numbers[1]) === 0 ? [] : numbers[1].split('')

  // 四位分级
  const levels = integer.reverse().reduce((pre: any[], item, idx) => {
    const level = pre[0] && pre[0].length < 4 ? pre[0] : []
    const value = item === '0' ? conf.num[item] : conf.num[item] + conf.unit[idx % 4]
    level.unshift(value)

    if (level.length === 1)
      pre.unshift(level)
    else
      pre[0] = level

    return pre
  }, [])

  // 整数部分
  const _integer = levels.reduce((pre, item, idx) => {
    let _level: any = conf.level[levels.length - idx - 1]
    let _item = item.join('').replace(/(零)\1+/g, '$1') // 连续多个零字的部分设置为单个零字

    // 如果这一级只有一个零字，则去掉这级
    if (_item === '零') {
      _item = ''
      _level = ''

      // 否则如果末尾为零字，则去掉这个零字
    }
    else if (_item[_item.length - 1] === '零') {
      _item = _item.slice(0, _item.length - 1)
    }

    return pre + _item + _level
  }, '')

  // 小数部分
  let _decimal = decimal
    .map((item, idx) => {
      const unit = confs.decimal.unit
      const _unit = item !== '0' ? unit[unit.length - idx - 1] : ''

      return `${conf.num[item]}${_unit}`
    })
    .join('')

  // 如果小数最后一项是零，就去掉
  if (_decimal[_decimal.length - 1] === '零')
    _decimal = _decimal.slice(0, _decimal.length - 1)

  // 如果是整数，则补个整字
  return `${_integer}元${_decimal || '整'}`
}

// 合计
export function sumNum(list: any[] = [], field = '') {
  let count = currency(0)

  if (list?.length === 0 || !list)
    return 0
  let currentValue
  for (let i = 0, ls = list.length - 1; i <= ls; i++) {
    currentValue = list[i][field]
    if (!currentValue)
      continue

    count = count.add(currentValue)
  }
  return count.value
}

// 求和
export function sumTotal(arr = [], keyName = '') {
  const sum = arr.reduce((acc, cur: any) => {
    const num = Number.parseFloat(cur[keyName])
    if (!Number.isNaN(num))
      return acc + num
    else
      return acc
  }, 0)
  return sum
}

// 格式化取整
export function printFn(val: number) {
  return currency(val, {
    precision: 14,
    separator: '',
    symbol: ''
  }).value
}


/**
 * 格式化长度 (乘以)
 * @param {number} val
 * @returns
 */
export function formatMeterMul(val: number, digit = DIGITS.METER) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化长度 (除以)
 * @param {*} val
 */
export function formatMeterDiv(val: number, digit = DIGITS.METER) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化单位长度 (乘以) length
 * @param {number} val
 * @returns
 */
export function formatLengthMul(val: number, digit = DIGITS.LENGTH) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化单位长度  (除以) length
 * @param {*} val
 */
export function formatLengthDiv(val: number, digit = DIGITS.LENGTH) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化数量单位 (乘以)
 * @param {number} val
 * @returns
 */
export function formatWeightMul(val: number, digit = DIGITS.WEIGHT) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化单价单位 (乘以)
 * @param {number} val
 * @returns
 */
export function formatUnitPriceMul(val: number, digit = DIGITS.UNIT) {
  return printFn(Number(val * digit)) || 0
}

/**
 * 格式化两位小数 (乘以)
 * @param {number} val
 * @returns
 */
export function formatTwoDecimalsMul(val: number, digit = 100) {
  return printFn(Number(val * digit)) || 0
}
/**
 * 格式化两位小数 (除以)
 * @param {number} val
 * @returns
 */
export function formatTwoDecimalsDiv(val: number, digit = 100) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化单价单位 (除以以)
 * @param {number} val
 * @returns
 */
export function formatUnitPriceDiv(val: number, digit = DIGITS.UNIT) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化数量单位 (除以)
 * @param {*} val
 */
export function formatWeightDiv(val: number, digit = DIGITS.WEIGHT) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化匹数 (乘以)
 * @param {number} val
 * @returns
 */
export function formatRollMul(val: string | number, digit = DIGITS.ROLL) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化匹数 (除以)
 * @param {number} val
 * @returns
 */
export function formatRollDiv(val: number, digit = DIGITS.ROLL) {
  return printFn(Number(val / digit)) || 0
}

/**
 * 格式化欠磅率单位 (除以)
 * @param {number} val
 * @returns
 */
export function formatUnderweightRateDiv(val: number) {
  return printFn(Number(val / 100)) || 0
}

/**
 * 格式化税率 (乘以)
 * @param {number} val
 * @returns
 */
export function formatRateMul(val: string | number, digit = DIGITS.RATE) {
  return printFn(Number(Number(val) * digit)) || 0
}

/**
 * 格式化税率 (除以)
 * @param {number} val
 * @returns
 */
export function formatRateDiv(val: number, digit = DIGITS.RATE) {
  return printFn(Number(val / digit)) || 0
}
