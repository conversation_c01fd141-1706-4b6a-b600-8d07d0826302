export function createEventHook() {
  const fns: Array<(parma: any) => void> = []
  const trigger = (param: any) => {
    fns.forEach(fn => fn(param))
  }
  const off = (fn: (param: any) => void) => {
    const index = fns.indexOf(fn)
    if (index !== -1)
      fns.splice(index, 1)
  }
  const on = (fn: (param: any) => void) => {
    fns.push(fn)
    return {
      off: () => off(fn),
    }
  }
  return {
    on,
    off,
    trigger,
  }
}
