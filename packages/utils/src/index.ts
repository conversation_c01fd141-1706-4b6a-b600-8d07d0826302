export * from './format/index'
export * from './hooks/createEventHook'
export * from './http/index'
export * from './handBinary/index'
export function isFirefox() {
    return (
        typeof navigator !== "undefined" && /firefox/i.test(navigator.userAgent)
    );
}

/**
 * Detects Macintosh
 */
export function isMacOS(): boolean {
    if (typeof window !== "undefined") {
        const userAgent = window.navigator.userAgent.toLocaleLowerCase();
        const macintosh = /iphone|ipad|ipod|macintosh/.test(userAgent);
        return !!macintosh;
    }
    return false;
}

export function isIOS() {
    const userAgent = navigator.userAgent.toLowerCase();
    return /iphone|ipad|ipod/.test(userAgent);
}

// 是否使用主单位
export const isUseMainUnit = (item: {auxiliary_unit_id: number, measurement_unit_id: number}) => {
  return (
    !item.auxiliary_unit_id
    || item.auxiliary_unit_id === item.measurement_unit_id
  )
}

// 订单类型
export enum SaleModeEnum {
  Bulk = 1, // 大货
  Plate = 2, // 剪板
  CustomerBulk = 3, // 客订-大货
  CustomerPlate = 4, // 客订-剪板
}
// 判断是否为大货
export const isBulk = (sale_mode: SaleModeEnum) => {
  return [SaleModeEnum.Bulk, SaleModeEnum.CustomerBulk].includes(
    Number(sale_mode)
  )
}
// 判断是否为客订
export const isCustomerBook = (sale_mode: SaleModeEnum) => {
  return [
    SaleModeEnum.CustomerPlate,
    SaleModeEnum.CustomerBulk,
  ].includes(Number(sale_mode))
}
/** Phone reg */
export const REG_PHONE
  = /^1((3\d)|(4[014-9])|(5[0-35-9])|(6[2567])|(7[0-8])|(8\d)|(9[0-35-9]))\d{8}$/
// 判断手机号是否合法
export function isPhoneValid(phone: string) {
  if (phone.trim() === '') {
    return false
  }

  if (!REG_PHONE.test(phone)) {
    return false
  }

  return true
}
export const orderTypeArr = [
  {
    label: '大货',
    value: SaleModeEnum.Bulk,
  },
  {
    label: '剪板',
    value: SaleModeEnum.Plate,
  },
  {
    label: '客订-大货',
    value: SaleModeEnum.CustomerBulk,
  },
  {
    label: '客订-剪板',
    value: SaleModeEnum.CustomerPlate,
  },
]
