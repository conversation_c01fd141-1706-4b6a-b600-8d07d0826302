# 数据分析应用 (React + TypeScript + Vite)

## 项目概述

这是一个基于React、TypeScript和Vite构建的数据分析应用，使用了自动路由生成系统来简化路由管理。

## 路由系统说明

本项目使用了基于 [elegant-router](https://github.com/mufeng889/elegant-router) 和 [react-auto-route](https://github.com/Ohh-889/react-auto-route) 的自动路由生成系统，大大简化了路由配置和管理工作。

### 路由目录结构

```
src/router/
├── elegant/               # 自动生成的路由相关文件
│   ├── imports.ts        # 自动导入的布局和页面组件
│   ├── routes.ts         # 自动生成的路由配置
│   └── transform.ts      # 路由转换工具函数
├── routes/               # 自定义路由配置
│   ├── builtin.ts        # 内置路由（如根路由、404页面）
│   └── index.ts          # 路由整合导出
├── index.ts              # 路由主文件
└── type.ts               # 路由类型定义
```

### 路由自动生成机制

1. **组件自动导入**：`elegant/imports.ts` 文件会自动扫描并导入项目中的布局和页面组件：
   - `layouts`: 定义了布局组件（如 base 布局）
   - `pages`: 自动导入 `src/pages` 目录下的所有页面组件

2. **路由配置生成**：`elegant/routes.ts` 文件根据页面结构自动生成路由配置，包括：
   - 路径映射（URL路径）
   - 组件关联
   - 元数据配置（如页面标题）
   - 嵌套路由结构

3. **路由转换**：`elegant/transform.ts` 负责将自动生成的路由配置转换为 React Router 可用的格式

4. **路由整合**：`routes/index.ts` 将自动生成的路由与内置路由整合

```typescript
// routes/index.ts
import { layouts, pages } from '../elegant/imports'
import { generatedRoutes } from '../elegant/routes'
import { transformElegantRoutesToReactRoutes } from '../elegant/transform'
import { builtinRoutes } from './builtin'

// 总路由
const elegantRoutes = [...builtinRoutes, ...generatedRoutes]

export const routes = transformElegantRoutesToReactRoutes(elegantRoutes, layouts, pages)
```

### 路由使用方法

1. **创建新页面**：
   - 在 `src/pages` 目录下创建新的页面组件
   - 遵循目录命名规范，如 `customer/analysis/index.tsx` 将自动生成 `/customer/analysis` 路由

2. **路由跳转**：使用 `useRouterPush` hook 进行路由跳转

```typescript
import { useRouterPush } from '@/hooks/routerPush'
// 注意这里一定要是 Component 命名
function Component() {
  const { routerPushByKey } = useRouterPush()

  // 跳转到指定路由
  const handleClick = () => {
    routerPushByKey('/customer/analysis')
  }

  return <button onClick={handleClick}>跳转到客户分析</button>
}
```

3. **路由配置**：如需自定义路由，可在 `routes/builtin.ts` 中添加

## 开发指南

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm run dev
```

### 构建生产版本

```bash
pnpm run build
```

### 预览生产版本

```bash
pnpm run preview
```

## 扩展ESLint配置

如果您正在开发生产应用，我们建议更新配置以启用类型感知的lint规则。详情请参考Vite官方文档。
