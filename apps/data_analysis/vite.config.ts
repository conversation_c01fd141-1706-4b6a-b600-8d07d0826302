import { fileURLToPath } from 'node:url'
import ElegantReactRouter from '@ohh-889/react-auto-route/vite'
import tailwindcss from '@tailwindcss/vite'
import react from '@vitejs/plugin-react'
import pxtorem from 'postcss-pxtorem'
import { defineConfig } from 'vite'
import svgr from 'vite-plugin-svgr'
import { getVersion } from './src/utils/getVersion'

const { CURRENT_GITHASH, CURRENT_VERSION } = getVersion()

// https://vite.dev/config/
export default defineConfig({
  base: `/qywx_workspace`,
  define: {
    CURRENT_GITHASH: JSON.stringify(CURRENT_GITHASH),
    CURRENT_VERSION: JSON.stringify(CURRENT_VERSION),
  },
  plugins: [
    tailwindcss(),
    react(),
    ElegantReactRouter({
      layouts: {
        base: 'src/layout/index',
      },
    }),
    pxtorem({
      rootValue: 16,
      propList: ['*'],
      selectorBlackList: [],
      minPixelValue: 1,
      mediaQuery: false,
      exclude: /node_modules/i,
    }),
    svgr(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  optimizeDeps: {
    include: ['ahooks'],
  },
  build: {
    dynamicImportVarsOptions: {
      exclude: [],
    },
  },
  css: {
    devSourcemap: false,
    sourcemap: false,
  },
  server: {
    proxy: {
      // 代理 iframe 成品销售页面
      '/qywx_back/finishProductSaleOrder*': {
        target: 'https://hcscmtest.zzfzyc.com',
        changeOrigin: true,
        secure: false,
      },
      '/upyun': {
        target: 'http://v0.api.upyun.com',
        changeOrigin: true,
        rewrite: url_path => url_path.replace(/^\/upyun/, '/'),
      },
    },
  },
})
