import { j as e } from "../vendor.B2rgTlKu.js";
import * as c from "react";
const j = "container_UGZpR", x = "content_UbepZ", N = "purchase_aYclv", _ = "debtRow_iuE0z", b = "debtInfo_zF4x0", v = "debtLabel_iZNY4", f = "debtAmount_UY-3h", y = "detailsButton_2VxKI", I = "arrow_fykfz", a = {
  container: j,
  content: x,
  purchase: N,
  debtRow: _,
  debtInfo: b,
  debtLabel: v,
  debtAmount: f,
  detailsButton: y,
  arrow: I
}, S = (o) => {
  const {
    props: {
      monthlyPurchase: t,
      debt: n
    },
    events: l
  } = o;
  return /* @__PURE__ */ e.jsx("div", { className: a.container, children: /* @__PURE__ */ e.jsxs("div", { className: a.content, children: [
    /* @__PURE__ */ e.jsxs("div", { className: a.purchase, children: [
      "该客户本月共采购 ",
      t.toLocaleString(),
      "元"
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: a.debtRow, children: [
      /* @__PURE__ */ e.jsxs("div", { className: a.debtInfo, children: [
        /* @__PURE__ */ e.jsx("span", { className: a.debtLabel, children: "累欠:" }),
        /* @__PURE__ */ e.jsxs("span", { className: a.debtAmount, children: [
          "¥ ",
          n.toFixed(2)
        ] })
      ] }),
      /* @__PURE__ */ e.jsxs("button", { className: a.detailsButton, onClick: l.onClick, children: [
        "账单明细 ",
        /* @__PURE__ */ e.jsx("span", { className: a.arrow, children: "›" })
      ] })
    ] })
  ] }) });
}, M = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: S
}, Symbol.toStringTag, { value: "Module" })), C = "container_jL7pG", w = "infoItem_mZmdb", g = "label_Ly2Wg", B = "value_9r5xB", P = "moreButton_WZN-y", s = {
  container: C,
  infoItem: w,
  label: g,
  value: B,
  moreButton: P
}, D = (o) => /* @__PURE__ */ c.createElement("svg", { className: "icon--SJP_d", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", style: {
  minWidth: 16,
  minHeight: 16
}, ...o }, /* @__PURE__ */ c.createElement("g", null, /* @__PURE__ */ c.createElement("path", { "data-follow-fill": "currentColor", fillRule: "evenodd", clipRule: "evenodd", d: "M5.248 14.444a.625.625 0 0 1-.005-.884l5.068-5.12a.625.625 0 0 0 0-.88L5.243 2.44a.625.625 0 1 1 .889-.88l5.067 5.121c.723.73.723 1.907 0 2.638l-5.067 5.12a.625.625 0 0 1-.884.005Z", fill: "currentColor" }))), R = (o) => {
  const {
    props: {
      customerName: t,
      level: n,
      debt: l,
      note: d,
      mostPurchased: i,
      lastOrder: m,
      followUpPerson: u,
      salesperson: p
    },
    events: h
  } = o;
  return /* @__PURE__ */ e.jsxs("div", { className: s.container, children: [
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "客户名称:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: t })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "等级登记:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: n })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "累欠金额:" }),
      /* @__PURE__ */ e.jsxs("span", { className: s.value, children: [
        "¥ ",
        l.toFixed(2)
      ] })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "备注信息:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: d })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "采购最多:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: i })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "最近下单:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: m })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "跟单人员:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: u })
    ] }),
    /* @__PURE__ */ e.jsxs("div", { className: s.infoItem, children: [
      /* @__PURE__ */ e.jsx("span", { className: s.label, children: "销售人员:" }),
      /* @__PURE__ */ e.jsx("span", { className: s.value, children: p })
    ] }),
    /* @__PURE__ */ e.jsxs("button", { className: s.moreButton, onClick: h.onClick, children: [
      "更多 ",
      /* @__PURE__ */ e.jsx(D, {})
    ] })
  ] });
}, K = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: R
}, Symbol.toStringTag, { value: "Module" })), L = "order-card_HCYxx", z = "order-header_DvjqA", T = "order-id_TypDc", Z = "order-content_WD1rv", F = "order-info_8QBRU", O = "order-desc_CVUra", U = "order-status_wVwKN", k = "order-footer_T2Vjt", A = "order-time_ZorIC", V = "time-label_dFGNS", W = "time_DZ5Fz", E = "payment-status_P6D8i", H = "order-price_-zW1S", r = {
  "order-card": "order-card_HCYxx",
  orderCard: L,
  "order-header": "order-header_DvjqA",
  orderHeader: z,
  "order-id": "order-id_TypDc",
  orderId: T,
  "order-content": "order-content_WD1rv",
  orderContent: Z,
  "order-info": "order-info_8QBRU",
  orderInfo: F,
  "order-desc": "order-desc_CVUra",
  orderDesc: O,
  "order-status": "order-status_wVwKN",
  orderStatus: U,
  "order-footer": "order-footer_T2Vjt",
  orderFooter: k,
  "order-time": "order-time_ZorIC",
  orderTime: A,
  "time-label": "time-label_dFGNS",
  timeLabel: V,
  time: W,
  "payment-status": "payment-status_P6D8i",
  paymentStatus: E,
  "order-price": "order-price_-zW1S",
  orderPrice: H
}, Y = (o) => {
  const {
    props: t,
    events: n
  } = o;
  return /* @__PURE__ */ e.jsxs("div", { className: r["order-card"], onClick: n.onClick, children: [
    /* @__PURE__ */ e.jsx("div", { className: r["order-header"], children: /* @__PURE__ */ e.jsx("span", { className: r["order-id"], children: t.orderNo }) }),
    /* @__PURE__ */ e.jsxs("div", { className: r["order-content"], children: [
      /* @__PURE__ */ e.jsxs("div", { className: r["order-info"], children: [
        /* @__PURE__ */ e.jsxs("span", { className: r["order-desc"], children: [
          "共",
          t.roll,
          "匹 (",
          t.weight,
          "公斤、",
          t.length,
          "米)"
        ] }),
        /* @__PURE__ */ e.jsx("span", { className: r["order-status"], children: t.statusName })
      ] }),
      /* @__PURE__ */ e.jsxs("div", { className: r["order-footer"], children: [
        /* @__PURE__ */ e.jsxs("div", { className: r["order-time"], children: [
          /* @__PURE__ */ e.jsx("span", { className: r["time-label"], children: "昨日" }),
          /* @__PURE__ */ e.jsx("span", { className: r.time, children: t.time }),
          /* @__PURE__ */ e.jsx("span", { className: r["payment-status"], children: t.paymentStatus })
        ] }),
        /* @__PURE__ */ e.jsxs("span", { className: r["order-price"], children: [
          "¥ ",
          t.price
        ] })
      ] })
    ] })
  ] });
}, $ = /* @__PURE__ */ Object.freeze(/* @__PURE__ */ Object.defineProperty({
  __proto__: null,
  default: Y
}, Symbol.toStringTag, { value: "Module" }));
export {
  M as b,
  K as c,
  $ as o
};
//# sourceMappingURL=interactive.CPN5bvSW.js.map
