{"version": 3, "file": "interactive.CPN5bvSW.js", "sources": ["../../app/components/interactive/billingSummary.template.tsx", "../../app/asserts/icons/arrow.svg", "../../app/components/interactive/customer.template.tsx", "../../app/components/interactive/order.template.tsx"], "sourcesContent": ["import React from 'react';\nimport styles from './billingSummary.template.module.scss';\n\ninterface BillingSummaryProps {\n  props: {\n    monthlyPurchase: number;\n    debt: number;\n  },\n  events: {\n    onClick: () => void;\n  }\n}\n\nconst BillingSummary: React.FC<BillingSummaryProps> = (props) => {\n  const { props: { monthlyPurchase, debt }, events } = props;\n  return (\n    <div className={styles.container}>\n      <div className={styles.content}>\n        <div className={styles.purchase}>\n          该客户本月共采购 {monthlyPurchase.toLocaleString()}元\n        </div>\n        <div className={styles.debtRow}>\n          <div className={styles.debtInfo}>\n            <span className={styles.debtLabel}>累欠:</span>\n            <span className={styles.debtAmount}>¥ {debt.toFixed(2)}</span>\n          </div>\n          <button className={styles.detailsButton} onClick={events.onClick}>\n            账单明细 <span className={styles.arrow}>›</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BillingSummary;\n", "import * as React from \"react\";\nconst SvgArrow = (props) => /* @__PURE__ */ React.createElement(\"svg\", { className: \"icon--SJP_d\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", style: {\n  minWidth: 16,\n  minHeight: 16\n}, ...props }, /* @__PURE__ */ React.createElement(\"g\", null, /* @__PURE__ */ React.createElement(\"path\", { \"data-follow-fill\": \"currentColor\", fillRule: \"evenodd\", clipRule: \"evenodd\", d: \"M5.248 14.444a.625.625 0 0 1-.005-.884l5.068-5.12a.625.625 0 0 0 0-.88L5.243 2.44a.625.625 0 1 1 .889-.88l5.067 5.121c.723.73.723 1.907 0 2.638l-5.067 5.12a.625.625 0 0 1-.884.005Z\", fill: \"currentColor\" })));\nexport default SvgArrow;\n", "import React from 'react';\nimport styles from './customer.template.module.scss';\nimport Arrow from '@/asserts/icons/arrow.svg'\ninterface CustomerInfoProps {\n  props: {\n    customerName: string;\n    level: string;\n    debt: number;\n    note: string;\n    mostPurchased: string;\n    lastOrder: string;\n    followUpPerson: string;\n    salesperson: string;\n  },\n  events: {\n    onClick: () => void;\n  }\n}\n\nconst CustomerInfo: React.FC<CustomerInfoProps> = (props) => {\n  const { props: { customerName, level, debt, note, mostPurchased, lastOrder, followUpPerson, salesperson }, events } = props;\n  return (\n    <div className={styles.container}>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>客户名称:</span>\n        <span className={styles.value}>{customerName}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>等级登记:</span>\n        <span className={styles.value}>{level}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>累欠金额:</span>\n        <span className={styles.value}>¥ {debt.toFixed(2)}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>备注信息:</span>\n        <span className={styles.value}>{note}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>采购最多:</span>\n        <span className={styles.value}>{mostPurchased}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>最近下单:</span>\n        <span className={styles.value}>{lastOrder}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>跟单人员:</span>\n        <span className={styles.value}>{followUpPerson}</span>\n      </div>\n      <div className={styles.infoItem}>\n        <span className={styles.label}>销售人员:</span>\n        <span className={styles.value}>{salesperson}</span>\n      </div>\n      <button className={styles.moreButton} onClick={events.onClick}>更多 <Arrow/></button>\n    </div>\n  );\n};\n\nexport default CustomerInfo;\n", "import styles from './order.template.module.scss';\ninterface OrderInfoProps {\n  props: {\n    orderNo: string\n    roll: number\n    weight: number\n    length: number\n    statusName: string\n    time: string\n    price: number\n    paymentStatus: string\n  },\n  events: {\n    onClick: () => void\n  }\n}\nconst OrderInfo = (props: OrderInfoProps) => {\n  const { props: orderInfoProps, events } = props\n  return <div className={styles[\"order-card\"]} onClick={events.onClick}>\n  <div className={styles[\"order-header\"]}>\n    <span className={styles[\"order-id\"]}>{orderInfoProps.orderNo}</span>\n  </div>\n  \n  <div className={styles[\"order-content\"]}>\n    <div className={styles[\"order-info\"]}>\n      <span className={styles[\"order-desc\"]}>共{orderInfoProps.roll}匹 ({orderInfoProps.weight}公斤、{orderInfoProps.length}米)</span>\n      <span className={styles[\"order-status\"]}>{orderInfoProps.statusName}</span>\n    </div>\n    \n    <div className={styles[\"order-footer\"]}>\n      <div className={styles[\"order-time\"]}>\n        <span className={styles[\"time-label\"]}>昨日</span>\n        <span className={styles[\"time\"]}>{orderInfoProps.time}</span>\n        <span className={styles[\"payment-status\"]}>{orderInfoProps.paymentStatus}</span>\n      </div>\n      <span className={styles[\"order-price\"]}>¥ {orderInfoProps.price}</span>\n    </div>\n  </div>\n</div>\n}\n\nexport default OrderInfo;\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "monthlyPurchase", "debt", "events", "jsx", "styles", "container", "jsxs", "content", "purchase", "toLocaleString", "debtRow", "debtInfo", "debtLabel", "debtAmount", "toFixed", "detailsButton", "onClick", "arrow", "SvgArrow", "React", "CustomerInfo", "customerName", "level", "note", "mostPurchased", "lastOrder", "follow<PERSON><PERSON><PERSON><PERSON>", "salesperson", "infoItem", "label", "value", "moreButton", "Arrow", "OrderInfo", "orderInfoProps", "orderNo", "roll", "weight", "length", "statusName", "time", "paymentStatus", "price"], "mappings": ";;;;;;;;;;;;GAaMA,IAAiDC,CAAUA,MAAA;AACzD,QAAA;AAAA,IAAEA,OAAO;AAAA,MAAEC,iBAAAA;AAAAA,MAAiBC,MAAAA;AAAAA,IAAK;AAAA,IAAGC,QAAAA;AAAAA,EAAAA,IAAWH;AAEnD,SAAAI,gBAAAA,EAAA,IAAC,SAAI,WAAWC,EAAOC,WACrB,UAACC,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOG,SACrB,UAAA;AAAA,IAACD,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOI,UAAS,UAAA;AAAA,MAAA;AAAA,MACpBR,EAAgBS,eAAe;AAAA,MAAE;AAAA,IAAA,GAC7C;AAAA,IACCH,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOM,SACrB,UAAA;AAAA,MAACJ,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOO,UACrB,UAAA;AAAA,QAAAR,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOQ,WAAW,UAAG,OAAA;AAAA,QACrCN,gBAAAA,EAAA,KAAA,QAAA,EAAK,WAAWF,EAAOS,YAAY,UAAA;AAAA,UAAA;AAAA,UAAGZ,EAAKa,QAAQ,CAAC;AAAA,QAAA,EAAE,CAAA;AAAA,MAAA,GACzD;AAAA,6BACC,UAAO,EAAA,WAAWV,EAAOW,eAAe,SAASb,EAAOc,SAAQ,UAAA;AAAA,QAAA;AAAA,QACzDb,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAOa,OAAO,UAAC,IAAA,CAAA;AAAA,MAAA,EACvC,CAAA;AAAA,IAAA,EACF,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA;AAEJ;;;;;;;;;GChCMC,IAAW,CAACnB,MAA0B,gBAAAoB,EAAM,cAAc,OAAO,EAAE,WAAW,eAAe,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,OAAO;AAAA,EACzK,UAAU;AAAA,EACV,WAAW;AACb,GAAG,GAAGpB,KAAyB,gBAAAoB,EAAM,cAAc,KAAK,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,oBAAoB,gBAAgB,UAAU,WAAW,UAAU,WAAW,GAAG,wLAAwL,MAAM,eAAgB,CAAA,CAAC,CAAC,GCevYC,IAA6CrB,CAAUA,MAAA;AACrD,QAAA;AAAA,IAAEA,OAAO;AAAA,MAAEsB,cAAAA;AAAAA,MAAcC,OAAAA;AAAAA,MAAOrB,MAAAA;AAAAA,MAAMsB,MAAAA;AAAAA,MAAMC,eAAAA;AAAAA,MAAeC,WAAAA;AAAAA,MAAWC,gBAAAA;AAAAA,MAAgBC,aAAAA;AAAAA,IAAY;AAAA,IAAGzB,QAAAA;AAAAA,EAAAA,IAAWH;AACtH,SACGO,gBAAAA,EAAAA,KAAA,OAAA,EAAI,WAAWF,EAAOC,WACrB,UAAA;AAAA,IAACC,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQT,UAAaA,EAAA,CAAA;AAAA,IAAA,GAC/C;AAAA,IACCf,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQR,UAAMA,EAAA,CAAA;AAAA,IAAA,GACxC;AAAA,IACChB,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnCvB,gBAAAA,EAAA,KAAA,QAAA,EAAK,WAAWF,EAAO0B,OAAO,UAAA;AAAA,QAAA;AAAA,QAAG7B,EAAKa,QAAQ,CAAC;AAAA,MAAA,EAAE,CAAA;AAAA,IAAA,GACpD;AAAA,IACCR,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQP,UAAKA,EAAA,CAAA;AAAA,IAAA,GACvC;AAAA,IACCjB,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQN,UAAcA,EAAA,CAAA;AAAA,IAAA,GAChD;AAAA,IACClB,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQL,UAAUA,EAAA,CAAA;AAAA,IAAA,GAC5C;AAAA,IACCnB,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQJ,UAAeA,EAAA,CAAA;AAAA,IAAA,GACjD;AAAA,IACCpB,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAOwB,UACrB,UAAA;AAAA,MAAAzB,gBAAAA,EAAA,IAAC,QAAK,EAAA,WAAWC,EAAOyB,OAAO,UAAK,SAAA;AAAA,MACnC1B,gBAAAA,EAAA,IAAA,QAAA,EAAK,WAAWC,EAAO0B,OAAQH,UAAYA,EAAA,CAAA;AAAA,IAAA,GAC9C;AAAA,2BACC,UAAO,EAAA,WAAWvB,EAAO2B,YAAY,SAAS7B,EAAOc,SAAS,UAAA;AAAA,MAAA;AAAA,4BAAIgB,GAAO,CAAA,CAAA;AAAA,IAAA,EAAA,CAAA;AAAA,EAAA,GAC5E;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GC1CMC,IAAYA,CAAClC,MAA0B;AACrC,QAAA;AAAA,IAAEA,OAAOmC;AAAAA,IAAgBhC,QAAAA;AAAAA,EAAAA,IAAWH;AACnC,SAAAO,gBAAAA,OAAC,SAAI,WAAWF,EAAO,YAAY,GAAG,SAASF,EAAOc,SAC7D,UAAA;AAAA,IAAAb,gBAAAA,EAAA,IAAC,OAAI,EAAA,WAAWC,EAAO,cAAc,GACnC,UAAAD,gBAAAA,MAAC,QAAK,EAAA,WAAWC,EAAO,UAAU,GAAI8B,UAAAA,EAAeC,QAAQ,CAAA,GAC/D;AAAA,IAEC7B,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAO,eAAe,GACpC,UAAA;AAAA,MAAAE,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAWF,EAAO,YAAY,GACjC,UAAA;AAAA,QAAAE,gBAAAA,EAAA,KAAC,QAAK,EAAA,WAAWF,EAAO,YAAY,GAAG,UAAA;AAAA,UAAA;AAAA,UAAE8B,EAAeE;AAAAA,UAAK;AAAA,UAAIF,EAAeG;AAAAA,UAAO;AAAA,UAAIH,EAAeI;AAAAA,UAAO;AAAA,QAAA,GAAE;AAAA,8BAClH,QAAK,EAAA,WAAWlC,EAAO,cAAc,GAAI8B,YAAeK,WAAW,CAAA;AAAA,MAAA,GACtE;AAAA,MAECjC,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWF,EAAO,cAAc,GACnC,UAAA;AAAA,QAAAE,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAWF,EAAO,YAAY,GACjC,UAAA;AAAA,UAAAD,gBAAAA,MAAC,QAAK,EAAA,WAAWC,EAAO,YAAY,GAAG,UAAE,MAAA;AAAA,gCACxC,QAAK,EAAA,WAAWA,EAAO,MAAU8B,YAAeM,MAAK;AAAA,gCACrD,QAAK,EAAA,WAAWpC,EAAO,gBAAgB,GAAI8B,YAAeO,cAAc,CAAA;AAAA,QAAA,GAC3E;AAAA,QACCnC,gBAAAA,EAAA,KAAA,QAAA,EAAK,WAAWF,EAAO,aAAa,GAAG,UAAA;AAAA,UAAA;AAAA,UAAG8B,EAAeQ;AAAAA,QAAAA,EAAM,CAAA;AAAA,MAAA,EAClE,CAAA;AAAA,IAAA,EACF,CAAA;AAAA,EAAA,GACF;AACA;;;;"}