import { j as s, w as qt, x as dt, n as D, y as Yt, z as Qt, A as Jt, B as te, C as ee, G as ne, I as rt, f as $, J as ae, q as se, K as oe } from "./vendor.B2rgTlKu.js";
import * as o from "react";
import ce, { forwardRef as re, useRef as T, useState as v, useEffect as I, useImperativeHandle as ie, useMemo as G, use<PERSON><PERSON><PERSON> as ut, Fragment as le } from "react";
import { U as me, S as he, f as j, c as de, a as ue, g as gt, u as ge, b as Z, d as wt, e as it, F as pe, I as vt, h as kt, i as fe, s as xe, j as ye, k as we, l as W, p as ve, B as pt, C as V, m as ke, n as Ct, D as Q, o as K, q as Ce, r as _e, t as ft, v as xt, w as X, x as Ee, R as Me, y as Ie } from "./init.BiPU5MWW.js";
const je = (e, a, n) => {
  const c = e[a];
  return c ? typeof c == "function" ? c() : Promise.resolve(c) : new Promise((r, l) => {
    (typeof queueMicrotask == "function" ? queueMicrotask : setTimeout)(
      l.bind(
        null,
        new Error(
          "Unknown variable dynamic import: " + a + (a.split("/").length !== n ? ". Note that variables only represent file names one level deep." : "")
        )
      )
    );
  });
}, Se = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0 8 8)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })), /* @__PURE__ */ o.createElement("g", { mask: "url(#bg-mask-0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 1", style: {
  stroke: "#fff",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,4.71L6.67,6L8.34,12.67L12.67,0L0,4.71Z", transform: "translate(1.3333333333333333 2) rotate(0 6.333333333333333 6.333333333333333)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 2", style: {
  stroke: "#fff",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,1.89L1.89,0", transform: "translate(8.002766666666666 6.1172) rotate(0 0.9428000000000001 0.9428000000000001)" })))), be = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0 8 8)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })), /* @__PURE__ */ o.createElement("g", { mask: "url(#bg-mask-0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 1", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,2.48L0,0.94C0,0.42 0.42,0 0.94,0L9.06,0C9.58,0 10,0.42 10,0.94L10,9.06C10,9.58 9.58,10 9.06,10L7.51,10", transform: "translate(4.333333333333333 1.6666666666666665) rotate(0 5 5)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 2", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0.94,0C0.42,0 0,0.42 0,0.94L0,9.06C0,9.58 0.42,10 0.94,10L9.06,10C9.58,10 10,9.58 10,9.06L10,0.94C10,0.42 9.58,0 9.06,0L0.94,0Z", transform: "translate(1.6666666666666665 4.333333333333333) rotate(0 5 5)" })))), _t = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", width: "1em", height: "1em", fill: "#fff", style: {}, ...e }, /* @__PURE__ */ o.createElement("rect", { id: "backgroundrect", width: "100%", height: "100%", x: 0, y: 0, fill: "none", stroke: "none", style: {}, className: "" }), /* @__PURE__ */ o.createElement("g", { className: "currentLayer", style: {} }, /* @__PURE__ */ o.createElement("title", null, "Layer 1"), /* @__PURE__ */ o.createElement("circle", { cx: 4, cy: 8, r: 1.926, fill: "#333", id: "svg_1", className: "" }, /* @__PURE__ */ o.createElement("animate", { attributeName: "r", begin: "0s", calcMode: "linear", dur: "0.8s", from: 2, repeatCount: "indefinite", to: 2, values: "2;1.2;2" }), /* @__PURE__ */ o.createElement("animate", { attributeName: "fill-opacity", begin: "0s", calcMode: "linear", dur: "0.8s", from: 1, repeatCount: "indefinite", to: 1, values: "1;.5;1" })), /* @__PURE__ */ o.createElement("circle", { cx: 8, cy: 8, r: 1.2736, fill: "#333", fillOpacity: 0.3, id: "svg_2", className: "" }, /* @__PURE__ */ o.createElement("animate", { attributeName: "r", begin: "0s", calcMode: "linear", dur: "0.8s", from: 1.2, repeatCount: "indefinite", to: 1.2, values: "1.2;2;1.2" }), /* @__PURE__ */ o.createElement("animate", { attributeName: "fill-opacity", begin: "0s", calcMode: "linear", dur: "0.8s", from: 0.5, repeatCount: "indefinite", to: 0.5, values: ".5;1;.5" })), /* @__PURE__ */ o.createElement("circle", { cx: 12, cy: 8, r: 1.926, fill: "#333", id: "svg_3", className: "" }, /* @__PURE__ */ o.createElement("animate", { attributeName: "r", begin: "0s", calcMode: "linear", dur: "0.8s", from: 2, repeatCount: "indefinite", to: 2, values: "2;1.2;2" }), /* @__PURE__ */ o.createElement("animate", { attributeName: "fill-opacity", begin: "0s", calcMode: "linear", dur: "0.8s", from: 1, repeatCount: "indefinite", to: 1, values: "1;.5;1" })))), Le = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0 8 8)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })), /* @__PURE__ */ o.createElement("g", { mask: "url(#bg-mask-0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u5206\\u7EC4 1", style: {
  stroke: "#333",
  strokeWidth: 1.3,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M1.36683 1.36683L2.77683 2.77683 M4.66667 0L4.66667 2 M4.66667 2L4.66667 0 M7.9623 1.36683L6.5523 2.77683 M6.5523 2.77683L7.9623 1.36683 M9.33333 4.66667L7.33333 4.66667 M7.33333 4.66667L9.33333 4.66667 M7.9623 7.9623L6.5523 6.5523 M6.5523 6.5523L7.9623 7.9623 M4.66667 9.33333L4.66667 7.33333 M4.66667 7.33333L4.66667 9.33333 M1.36683 7.9623L2.77683 6.5523 M2.77683 6.5523L1.36683 7.9623 M0 4.66667L2 4.66667 M2 4.66667L0 4.66667", transform: "translate(5.333333333333333 1.3333333333333333) rotate(0 4.666666666666666 4.666666666666666)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 9", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M8.01,0L0,8.01", transform: "translate(1.847983333333333 6.1381) rotate(0 4.006941666666666 4.006933333333333)" })))), Te = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("g", { opacity: 1 }, /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0) translate(1.0001220703125 2) rotate(0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 1", style: {
  fill: "#333",
  opacity: 1
}, d: "M13.275,-0.27515c0.261,0.26101 0.3915,0.57606 0.3915,0.94515v10.66c0,0.36907 -0.1305,0.68413 -0.3915,0.9452c-0.261,0.261 -0.57603,0.3915 -0.9451,0.3915h-10.66002c-0.36909,0 -0.68415,-0.1305 -0.94516,-0.3915c-0.26101,-0.26107 -0.39151,-0.57613 -0.39151,-0.9452v-10.66c0,-0.3691 0.1305,-0.68415 0.39151,-0.94515c0.26101,-0.26101 0.57606,-0.39151 0.94516,-0.39151h10.66002c0.36907,0 0.6841,0.1305 0.9451,0.39151zM1.66655,11.33c0,0.0022 0.00111,0.0033 0.00333,0.0033h10.66002c0.0022,0 0.0033,-0.0011 0.0033,-0.0033v-10.66c0,-0.00222 -0.0011,-0.00333 -0.0033,-0.00333l-10.66002,0c-0.00222,0 -0.00333,0.00111 -0.00333,0.00333z" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 2", style: {
  fill: "#333",
  opacity: 1
}, d: "M9.76327,7.50715c-0.02999,0.02563 -0.06201,0.04842 -0.09604,0.06837c-0.03403,0.01995 -0.06956,0.03674 -0.10658,0.05039c-0.03702,0.01364 -0.07495,0.02391 -0.11379,0.03082c-0.03885,0.00691 -0.07799,0.01035 -0.11744,0.0103c-0.03945,-0.00004 -0.07859,-0.00356 -0.11742,-0.01055c-0.03883,-0.00699 -0.07674,-0.01734 -0.11373,-0.03106c-0.03699,-0.01372 -0.07248,-0.03059 -0.10647,-0.05061c-0.03399,-0.02002 -0.06596,-0.04288 -0.0959,-0.06858l-1.89578,-1.62728l-1.89578,1.62728c-0.02993,0.0257 -0.0619,0.04856 -0.09589,0.06858c-0.03399,0.02002 -0.06949,0.03689 -0.10648,0.05061c-0.03699,0.01372 -0.07489,0.02407 -0.11372,0.03106c-0.03883,0.00699 -0.07797,0.01051 -0.11742,0.01055c-0.03945,0.00005 -0.0786,-0.00339 -0.11744,-0.0103c-0.03885,-0.00691 -0.07678,-0.01718 -0.11379,-0.03082c-0.03702,-0.01365 -0.07255,-0.03044 -0.10658,-0.05039c-0.03404,-0.01995 -0.06605,-0.04274 -0.09604,-0.06837l-1.90593,-1.629l-1.89671,1.62808c-0.06708,0.05758 -0.14263,0.10013 -0.22664,0.12766c-0.08401,0.02753 -0.17009,0.03793 -0.25824,0.03121c-0.08815,-0.00671 -0.17166,-0.03004 -0.25053,-0.06998c-0.07887,-0.03994 -0.14709,-0.09345 -0.20467,-0.16054c-0.02851,-0.03321 -0.05351,-0.06889 -0.07499,-0.10703c-0.02148,-0.03814 -0.03904,-0.07801 -0.05267,-0.11961c-0.01363,-0.04159 -0.02307,-0.08412 -0.02832,-0.12758c-0.00525,-0.04346 -0.00622,-0.08701 -0.00289,-0.13066c0.00333,-0.04365 0.01088,-0.08655 0.02266,-0.12871c0.01178,-0.04216 0.02755,-0.08277 0.04733,-0.12182c0.01978,-0.03905 0.04317,-0.07579 0.07019,-0.11024c0.02701,-0.03444 0.05713,-0.06592 0.09035,-0.09443l2.32999,-2c0.02994,-0.02569 0.06191,-0.04855 0.0959,-0.06857c0.03399,-0.02003 0.06948,-0.0369 0.10647,-0.05062c0.03699,-0.01372 0.0749,-0.02407 0.11373,-0.03106c0.03883,-0.00699 0.07797,-0.01051 0.11742,-0.01055c0.03945,-0.00004 0.0786,0.00339 0.11744,0.0103c0.03884,0.00691 0.07677,0.01718 0.11379,0.03082c0.03702,0.01365 0.07255,0.03044 0.10658,0.05039c0.03404,0.01995 0.06605,0.04274 0.09604,0.06837l1.90592,1.629l1.89671,-1.62808c0.02998,-0.02573 0.062,-0.04862 0.09605,-0.06866c0.03405,-0.02005 0.0696,-0.03693 0.10665,-0.05065c0.03705,-0.01372 0.07503,-0.02407 0.11392,-0.03104c0.03889,-0.00697 0.07809,-0.01045 0.1176,-0.01045c0.03951,0 0.07872,0.00348 0.11761,0.01045c0.03889,0.00697 0.07686,0.01732 0.11391,0.03104c0.03705,0.01372 0.0726,0.0306 0.10665,0.05065c0.03405,0.02004 0.06607,0.04293 0.09605,0.06866l1.89671,1.62808l1.90595,-1.629c0.03,-0.02563 0.062,-0.04842 0.096,-0.06837c0.03407,-0.01995 0.0696,-0.03674 0.1066,-0.05038c0.037,-0.01365 0.07493,-0.02392 0.1138,-0.03083c0.03887,-0.00691 0.078,-0.01034 0.1174,-0.0103c0.03947,0.00004 0.0786,0.00356 0.1174,0.01055c0.03887,0.00699 0.0768,0.01734 0.1138,0.03106c0.037,0.01372 0.07247,0.03059 0.1064,0.05062c0.034,0.02002 0.06597,0.04288 0.0959,0.06857l2.33,2c0.06713,0.05758 0.12067,0.12581 0.1606,0.20468c0.03993,0.07887 0.06327,0.16237 0.07,0.25052c0.00667,0.08815 -0.00377,0.17424 -0.0313,0.25825c-0.02747,0.08401 -0.07,0.15955 -0.1276,0.22663c-0.02853,0.03322 -0.06,0.06334 -0.0944,0.09035c-0.03447,0.02701 -0.07123,0.05041 -0.1103,0.07019c-0.03907,0.01977 -0.07967,0.03555 -0.1218,0.04733c-0.04213,0.01177 -0.08503,0.01932 -0.1287,0.02265c-0.04367,0.00333 -0.08723,0.00236 -0.1307,-0.00289c-0.04347,-0.00525 -0.086,-0.01469 -0.1276,-0.02832c-0.0416,-0.01363 -0.08147,-0.03118 -0.1196,-0.05267c-0.03813,-0.02148 -0.0738,-0.04648 -0.107,-0.07499l-1.8967,-1.62808z" })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0) translate(0 0) rotate(0)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })))), /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 }))), Et = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0 8 8)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })), /* @__PURE__ */ o.createElement("g", { mask: "url(#bg-mask-0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 1", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M1,9.67L9.67,9.67L10.67,0L0,0L1,9.67Z", transform: "translate(2.6666666666666665 5) rotate(0 5.333333333333333 4.833333333333333)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 2", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,0L0,3.33", transform: "translate(6.667333333333333 8.334133333333334) rotate(0 0 1.6666999999999998)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 3", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,0L0,3.33", transform: "translate(9.334133333333334 8.333166666666667) rotate(0 0 1.666283333333333)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 4", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M0,4L5.44,0L8,4", transform: "translate(4 1) rotate(0 4 2)" })))), Fe = (e) => /* @__PURE__ */ o.createElement("svg", { t: 1734508841547, className: "icon", viewBox: "0 0 1024 1024", xmlns: "http://www.w3.org/2000/svg", "p-id": 5152, xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", ...e }, /* @__PURE__ */ o.createElement("path", { d: "M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z", fill: "#45B058", "p-id": 5153 }), /* @__PURE__ */ o.createElement("path", { d: "M374.4 862.4c-3.2 0-6.4-1.6-8-3.2l-59.2-80-60.8 80c-1.6 1.6-4.8 3.2-8 3.2-6.4 0-11.2-4.8-11.2-11.2 0-1.6 0-4.8 1.6-6.4l62.4-81.6-57.6-78.4c-1.6-1.6-3.2-3.2-3.2-6.4 0-4.8 4.8-11.2 11.2-11.2 4.8 0 8 1.6 9.6 4.8l56 73.6 54.4-73.6c1.6-3.2 4.8-4.8 8-4.8 6.4 0 12.8 4.8 12.8 11.2 0 3.2-1.6 4.8-1.6 6.4l-59.2 76.8 62.4 83.2c1.6 1.6 3.2 4.8 3.2 6.4 0 6.4-6.4 11.2-12.8 11.2z m160-1.6H448c-9.6 0-17.6-8-17.6-17.6V678.4c0-6.4 4.8-11.2 12.8-11.2 6.4 0 11.2 4.8 11.2 11.2v161.6h80c6.4 0 11.2 4.8 11.2 9.6 0 6.4-4.8 11.2-11.2 11.2z m112 3.2c-28.8 0-51.2-9.6-67.2-24-3.2-1.6-3.2-4.8-3.2-8 0-6.4 3.2-12.8 11.2-12.8 1.6 0 4.8 1.6 6.4 3.2 12.8 11.2 32 20.8 54.4 20.8 33.6 0 44.8-19.2 44.8-33.6 0-49.6-113.6-22.4-113.6-89.6 0-32 27.2-54.4 65.6-54.4 24 0 46.4 8 60.8 20.8 3.2 1.6 4.8 4.8 4.8 8 0 6.4-4.8 12.8-11.2 12.8-1.6 0-4.8-1.6-6.4-3.2-14.4-11.2-32-16-49.6-16-24 0-40 11.2-40 30.4 0 43.2 113.6 17.6 113.6 89.6 0 27.2-19.2 56-70.4 56z", fill: "#FFFFFF", "p-id": 5154 }), /* @__PURE__ */ o.createElement("path", { d: "M960 326.4v16H755.2s-102.4-20.8-99.2-108.8c0 0 3.2 92.8 96 92.8h208z", fill: "#349C42", "p-id": 5155 }), /* @__PURE__ */ o.createElement("path", { d: "M656 0v233.6c0 25.6 19.2 92.8 99.2 92.8H960L656 0z", fill: "#FFFFFF", "p-id": 5156 })), Ne = (e) => /* @__PURE__ */ o.createElement("svg", { t: 1734508866521, className: "icon", viewBox: "0 0 1024 1024", xmlns: "http://www.w3.org/2000/svg", "p-id": 6161, xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", ...e }, /* @__PURE__ */ o.createElement("path", { d: "M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z", fill: "#8C181A", "p-id": 6162 }), /* @__PURE__ */ o.createElement("path", { d: "M960 326.4v16H755.2s-100.8-20.8-97.6-107.2c0 0 3.2 91.2 96 91.2H960z", fill: "#6B0D12", "p-id": 6163 }), /* @__PURE__ */ o.createElement("path", { d: "M657.6 0v233.6c0 27.2 17.6 92.8 97.6 92.8H960L657.6 0z", fill: "#FFFFFF", "p-id": 6164 }), /* @__PURE__ */ o.createElement("path", { d: "M302.4 784h-52.8v65.6c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h59.2c38.4 0 60.8 27.2 60.8 57.6 0 32-22.4 57.6-60.8 57.6z m-1.6-94.4h-51.2v73.6h51.2c22.4 0 38.4-14.4 38.4-36.8s-16-36.8-38.4-36.8z m166.4 171.2h-48c-9.6 0-17.6-8-17.6-17.6v-156.8c0-9.6 8-17.6 17.6-17.6h48c59.2 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c46.4 0 73.6-33.6 73.6-75.2 1.6-40-25.6-73.6-73.6-73.6z m260.8 0h-92.8V752h91.2c6.4 0 9.6 4.8 9.6 11.2s-4.8 9.6-9.6 9.6h-91.2v76.8c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h99.2c6.4 0 9.6 4.8 9.6 11.2 1.6 4.8-3.2 9.6-9.6 9.6z", fill: "#FFFFFF", "p-id": 6165 })), De = (e) => /* @__PURE__ */ o.createElement("svg", { t: 1734509033364, className: "icon", viewBox: "0 0 1024 1024", xmlns: "http://www.w3.org/2000/svg", "p-id": 2552, xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", ...e }, /* @__PURE__ */ o.createElement("path", { d: "M145.6 0C100.8 0 64 35.2 64 80v862.4C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z", fill: "#14A9DA", "p-id": 2553 }), /* @__PURE__ */ o.createElement("path", { d: "M960 326.4v16H755.2s-100.8-20.8-99.2-108.8c0 0 4.8 92.8 97.6 92.8H960z", fill: "#0F93D0", "p-id": 2554 }), /* @__PURE__ */ o.createElement("path", { d: "M657.6 0v233.6c0 25.6 17.6 92.8 97.6 92.8H960L657.6 0z", fill: "#FFFFFF", "p-id": 2555 }), /* @__PURE__ */ o.createElement("path", { d: "M291.2 862.4h-48c-9.6 0-17.6-8-17.6-17.6v-158.4c0-9.6 8-16 17.6-16h48c60.8 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c48 0 75.2-33.6 75.2-73.6 0-41.6-27.2-75.2-75.2-75.2z m232 174.4c-57.6 0-96-43.2-96-99.2s38.4-99.2 96-99.2c56 0 94.4 41.6 94.4 99.2 0 56-38.4 99.2-94.4 99.2z m0-177.6c-43.2 0-70.4 33.6-70.4 78.4 0 44.8 27.2 76.8 70.4 76.8 41.6 0 70.4-32 70.4-76.8S564.8 688 523.2 688z m294.4 6.4c1.6 1.6 3.2 4.8 3.2 8 0 6.4-4.8 11.2-11.2 11.2-3.2 0-6.4-1.6-8-3.2-11.2-14.4-30.4-22.4-48-22.4-41.6 0-73.6 32-73.6 78.4 0 44.8 32 76.8 73.6 76.8 17.6 0 35.2-6.4 48-20.8 1.6-3.2 4.8-4.8 8-4.8 6.4 0 11.2 6.4 11.2 12.8 0 3.2-1.6 4.8-3.2 8-14.4 16-35.2 27.2-64 27.2-56 0-99.2-40-99.2-99.2s43.2-99.2 99.2-99.2c28.8 0 49.6 11.2 64 27.2z", fill: "#FFFFFF", "p-id": 2556 })), Pe = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", fill: "none", height: "1em", width: "1em", xmlSpace: "preserve", style: {}, ...e }, /* @__PURE__ */ o.createElement("rect", { id: "backgroundrect", width: "100%", height: "100%", x: 0, y: 0, fill: "none", stroke: "none" }), /* @__PURE__ */ o.createElement("g", { className: "currentLayer", style: {} }, /* @__PURE__ */ o.createElement("title", null, "Layer 1"), /* @__PURE__ */ o.createElement("g", { id: "svg_1", className: "", fill: "#333", fillOpacity: 1 }, /* @__PURE__ */ o.createElement("polygon", { points: "2.4690866470336914,2.4690725803375244 4.447190761566162,2.4690725803375244 4.447190761566162,1.6882386207580566 1.6882381439208984,1.6882386207580566 1.6882381439208984,4.44719123840332 2.4690866470336914,4.44719123840332 ", id: "svg_2", fill: "#333", fillOpacity: 1 }), /* @__PURE__ */ o.createElement("polygon", { points: "11.552804470062256,1.6882386207580566 11.552804470062256,2.4690725803375244 13.530910968780518,2.4690725803375244 13.530910968780518,4.44719123840332 14.311760425567627,4.44719123840332 14.311760425567627,1.6882386207580566 ", id: "svg_3", fill: "#333", fillOpacity: 1 }), /* @__PURE__ */ o.createElement("polygon", { points: "13.530910968780518,13.530919075012207 11.552804470062256,13.530919075012207 11.552804470062256,14.311760902404785 14.311760425567627,14.311760902404785 14.311760425567627,11.552801132202148 13.530910968780518,11.552801132202148 ", id: "svg_4", fill: "#333", fillOpacity: 1 }), /* @__PURE__ */ o.createElement("polygon", { points: "2.4690866470336914,11.552801132202148 1.6882381439208984,11.552801132202148 1.6882381439208984,14.311760902404785 4.447190761566162,14.311760902404785 4.447190761566162,13.530919075012207 2.4690866470336914,13.530919075012207 ", id: "svg_5", fill: "#333", fillOpacity: 1 }), /* @__PURE__ */ o.createElement("path", { d: "M8.830417847409231,6.243117030680995 c0.68169614081525,0 1.2363241834494423,-0.5546280426341942 1.2363241834494423,-1.2363241834494423 S9.51214001610201,3.770468663782117 8.830417847409231,3.770468663782117 s-1.2363241834494423,0.5546280426341942 -1.2363241834494423,1.2363241834494423 S8.14872170659398,6.243117030680995 8.830417847409231,6.243117030680995 z", id: "svg_6", fill: "#333", fillOpacity: 1 }), /* @__PURE__ */ o.createElement("polygon", { points: "3.7704806327819824,12.229532241821289 12.229516506195068,12.229532241821289 12.229516506195068,9.709510803222656 10.70320463180542,8.099010467529297 8.852166652679443,9.175727844238281 6.275332450866699,7.334256172180176 3.7704806327819824,9.977211952209473 ", id: "svg_7", fill: "#333", fillOpacity: 1 })))), He = (e) => /* @__PURE__ */ o.createElement("svg", { xmlns: "http://www.w3.org/2000/svg", xmlnsXlink: "http://www.w3.org/1999/xlink", width: "1em", height: "1em", fill: "none", viewBox: "0 0 16 16", ...e }, /* @__PURE__ */ o.createElement("defs", null, /* @__PURE__ */ o.createElement("rect", { id: "path_0", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ o.createElement("g", { opacity: 1, transform: "translate(0 0) rotate(0 8 8)" }, /* @__PURE__ */ o.createElement("mask", { id: "bg-mask-0", fill: "#fff" }, /* @__PURE__ */ o.createElement("use", { xlinkHref: "#path_0" })), /* @__PURE__ */ o.createElement("g", { mask: "url(#bg-mask-0)" }, /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 1", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M8,0L4,4L0,0", transform: "translate(4 4) rotate(0 4 2)" }), /* @__PURE__ */ o.createElement("path", { id: "\\u8DEF\\u5F84 2", style: {
  stroke: "#333",
  strokeWidth: 1.3333333333333333,
  strokeOpacity: 1,
  strokeDasharray: "0 0"
}, d: "M8,0L4,4L0,0", transform: "translate(4 8) rotate(0 4 2)" }))));
function yt(e) {
  const a = new FormData();
  return a.append("file", e), fetch(me, {
    method: "post",
    body: a,
    mode: "cors",
    credentials: "include"
  }).then((n) => n.json()).then((n) => {
    if ((n == null ? void 0 : n.code) == 0 && (n != null && n.data))
      return n == null ? void 0 : n.data;
    throw Error(`upload Error: ${n == null ? void 0 : n.msg}`);
  });
}
const Ae = "attach-images_MgEo7", Oe = "attach-image_mepk4", Ke = "attach-image-mask_Tt-RU", Be = "delete-image_5MH68", Re = "chat-input-actions_aoK0k", Ue = "chat-input-actions-end_8VE6H", ze = "chat-input-action_xdwOW", We = "slide-in_IT0Qh", Ve = "text_IKt4v", Xe = "icon_CW0pJ", Ge = "prompt-toast_WhVKU", $e = "prompt-toast-inner_w-uqk", Ze = "slide-in-from-top_IpOWY", qe = "prompt-toast-content_iCpGT", Ye = "section-title_uCHBK", Qe = "section-title-action_E6tVP", Je = "context-prompt_QJmk-", tn = "context-prompt-insert_qE5F-", en = "context-prompt-row_bUUhL", nn = "context-drag_3mPm8", an = "context-role_m4mIE", sn = "context-content_EPYwg", on = "context-delete-button_jj2yr", cn = "context-prompt-button_8XV-f", rn = "memory-prompt_2ZVbD", ln = "memory-prompt-content_gur0O", mn = "clear-context_QU2-1", hn = "clear-context-tips_--8JT", dn = "clear-context-revert-btn_0wkiC", un = "chat_DRsCp", gn = "chat-body_VjIXX", pn = "chat-body-main-title_ZKVCC", fn = "chat-body-title_mHix-", xn = "chat-message_m2d4y", yn = "chat-message-user_--6A1", wn = "chat-message-header_NaPCi", vn = "chat-message-actions_-ZW3E", kn = "chat-model-name_pNRG1", Cn = "chat-message-container_2phlO", _n = "chat-message-edit_TCQGZ", En = "chat-message-avatar_IwmGK", Mn = "chat-message-status_ynopK", In = "chat-message-tools_R6dfa", jn = "chat-message-tool_51f-X", Sn = "chat-message-item_Z-vkb", bn = "chat-message-audio_ljL3m", Ln = "chat-message-item-file_iO6Id", Tn = "chat-message-item-file-icon_sb20Q", Fn = "chat-message-item-file-name_uezci", Nn = "chat-message-item-image_wELXr", Dn = "chat-message-item-images_NSnWG", Pn = "chat-message-item-image-multi_v5hYh", Hn = "chat-message-action-date_VPn-M", An = "chat-input-panel_Hd8VK", On = "prompt-hints_iGv3F", Kn = "prompt-hint_Mvgrj", Bn = "hint-title_rNMxn", Rn = "hint-content_ciC-I", Un = "prompt-hint-selected_QO3uC", zn = "chat-input-panel-inner_1jOBM", Wn = "chat-input-panel-inner-attach_6T5kH", Vn = "chat-input_XTGHK", Xn = "chat-input-send_EW2Kh", Gn = "shortcut-key-container_Aw6Vk", $n = "shortcut-key-grid_V8gqg", Zn = "shortcut-key-item_rjpDO", qn = "shortcut-key-title_LX7E9", Yn = "shortcut-key-keys_wUYZv", Qn = "shortcut-key_dzBx3", Jn = "chat-main_zNJV3", ta = "chat-body-container_UGcwa", ea = "chat-side-panel_nWDGx", na = "chat-side-panel-show_ZS2qP", d = {
  "attach-images": "attach-images_MgEo7",
  attachImages: Ae,
  "attach-image": "attach-image_mepk4",
  attachImage: Oe,
  "attach-image-mask": "attach-image-mask_Tt-RU",
  attachImageMask: Ke,
  "delete-image": "delete-image_5MH68",
  deleteImage: Be,
  "chat-input-actions": "chat-input-actions_aoK0k",
  chatInputActions: Re,
  "chat-input-actions-end": "chat-input-actions-end_8VE6H",
  chatInputActionsEnd: Ue,
  "chat-input-action": "chat-input-action_xdwOW",
  chatInputAction: ze,
  "slide-in": "slide-in_IT0Qh",
  slideIn: We,
  text: Ve,
  icon: Xe,
  "prompt-toast": "prompt-toast_WhVKU",
  promptToast: Ge,
  "prompt-toast-inner": "prompt-toast-inner_w-uqk",
  promptToastInner: $e,
  "slide-in-from-top": "slide-in-from-top_IpOWY",
  slideInFromTop: Ze,
  "prompt-toast-content": "prompt-toast-content_iCpGT",
  promptToastContent: qe,
  "section-title": "section-title_uCHBK",
  sectionTitle: Ye,
  "section-title-action": "section-title-action_E6tVP",
  sectionTitleAction: Qe,
  "context-prompt": "context-prompt_QJmk-",
  contextPrompt: Je,
  "context-prompt-insert": "context-prompt-insert_qE5F-",
  contextPromptInsert: tn,
  "context-prompt-row": "context-prompt-row_bUUhL",
  contextPromptRow: en,
  "context-drag": "context-drag_3mPm8",
  contextDrag: nn,
  "context-role": "context-role_m4mIE",
  contextRole: an,
  "context-content": "context-content_EPYwg",
  contextContent: sn,
  "context-delete-button": "context-delete-button_jj2yr",
  contextDeleteButton: on,
  "context-prompt-button": "context-prompt-button_8XV-f",
  contextPromptButton: cn,
  "memory-prompt": "memory-prompt_2ZVbD",
  memoryPrompt: rn,
  "memory-prompt-content": "memory-prompt-content_gur0O",
  memoryPromptContent: ln,
  "clear-context": "clear-context_QU2-1",
  clearContext: mn,
  "clear-context-tips": "clear-context-tips_--8JT",
  clearContextTips: hn,
  "clear-context-revert-btn": "clear-context-revert-btn_0wkiC",
  clearContextRevertBtn: dn,
  chat: un,
  "chat-body": "chat-body_VjIXX",
  chatBody: gn,
  "chat-body-main-title": "chat-body-main-title_ZKVCC",
  chatBodyMainTitle: pn,
  "chat-body-title": "chat-body-title_mHix-",
  chatBodyTitle: fn,
  "chat-message": "chat-message_m2d4y",
  chatMessage: xn,
  "chat-message-user": "chat-message-user_--6A1",
  chatMessageUser: yn,
  "chat-message-header": "chat-message-header_NaPCi",
  chatMessageHeader: wn,
  "chat-message-actions": "chat-message-actions_-ZW3E",
  chatMessageActions: vn,
  "chat-model-name": "chat-model-name_pNRG1",
  chatModelName: kn,
  "chat-message-container": "chat-message-container_2phlO",
  chatMessageContainer: Cn,
  "chat-message-edit": "chat-message-edit_TCQGZ",
  chatMessageEdit: _n,
  "chat-message-avatar": "chat-message-avatar_IwmGK",
  chatMessageAvatar: En,
  "chat-message-status": "chat-message-status_ynopK",
  chatMessageStatus: Mn,
  "chat-message-tools": "chat-message-tools_R6dfa",
  chatMessageTools: In,
  "chat-message-tool": "chat-message-tool_51f-X",
  chatMessageTool: jn,
  "chat-message-item": "chat-message-item_Z-vkb",
  chatMessageItem: Sn,
  "chat-message-audio": "chat-message-audio_ljL3m",
  chatMessageAudio: bn,
  "chat-message-item-file": "chat-message-item-file_iO6Id",
  chatMessageItemFile: Ln,
  "chat-message-item-file-icon": "chat-message-item-file-icon_sb20Q",
  chatMessageItemFileIcon: Tn,
  "chat-message-item-file-name": "chat-message-item-file-name_uezci",
  chatMessageItemFileName: Fn,
  "chat-message-item-image": "chat-message-item-image_wELXr",
  chatMessageItemImage: Nn,
  "chat-message-item-images": "chat-message-item-images_NSnWG",
  chatMessageItemImages: Dn,
  "chat-message-item-image-multi": "chat-message-item-image-multi_v5hYh",
  chatMessageItemImageMulti: Pn,
  "chat-message-action-date": "chat-message-action-date_VPn-M",
  chatMessageActionDate: Hn,
  "chat-input-panel": "chat-input-panel_Hd8VK",
  chatInputPanel: An,
  "prompt-hints": "prompt-hints_iGv3F",
  promptHints: On,
  "prompt-hint": "prompt-hint_Mvgrj",
  promptHint: Kn,
  "hint-title": "hint-title_rNMxn",
  hintTitle: Bn,
  "hint-content": "hint-content_ciC-I",
  hintContent: Rn,
  "prompt-hint-selected": "prompt-hint-selected_QO3uC",
  promptHintSelected: Un,
  "chat-input-panel-inner": "chat-input-panel-inner_1jOBM",
  chatInputPanelInner: zn,
  "chat-input-panel-inner-attach": "chat-input-panel-inner-attach_6T5kH",
  chatInputPanelInnerAttach: Wn,
  "chat-input": "chat-input_XTGHK",
  chatInput: Vn,
  "chat-input-send": "chat-input-send_EW2Kh",
  chatInputSend: Xn,
  "shortcut-key-container": "shortcut-key-container_Aw6Vk",
  shortcutKeyContainer: Gn,
  "shortcut-key-grid": "shortcut-key-grid_V8gqg",
  shortcutKeyGrid: $n,
  "shortcut-key-item": "shortcut-key-item_rjpDO",
  shortcutKeyItem: Zn,
  "shortcut-key-title": "shortcut-key-title_LX7E9",
  shortcutKeyTitle: qn,
  "shortcut-key-keys": "shortcut-key-keys_wUYZv",
  shortcutKeyKeys: Yn,
  "shortcut-key": "shortcut-key_dzBx3",
  shortcutKey: Qn,
  "chat-main": "chat-main_zNJV3",
  chatMain: Jn,
  "chat-body-container": "chat-body-container_UGcwa",
  chatBodyContainer: ta,
  "chat-side-panel": "chat-side-panel_nWDGx",
  chatSidePanel: ea,
  "chat-side-panel-show": "chat-side-panel-show_ZS2qP",
  chatSidePanelShow: na
};
function aa(e, a) {
  return `https://fastly.jsdelivr.net/npm/emoji-datasource-apple/img/${a}/64/${e}.png`;
}
function ot(e) {
  return e.model ? /* @__PURE__ */ s.jsx("div", { className: "no-dark", children: /* @__PURE__ */ s.jsx(he, { className: "user-avatar" }) }) : /* @__PURE__ */ s.jsx("div", { className: "user-avatar", children: e.avatar && /* @__PURE__ */ s.jsx(sa, { avatar: e.avatar }) });
}
function sa(e) {
  return /* @__PURE__ */ s.jsx(qt, { unified: e.avatar, size: e.size ?? 18, getEmojiUrl: aa });
}
const Mt = /^[:：]/;
function oa(e = {}) {
  function a(r) {
    return r.match(Mt) ? r.slice(1) : r;
  }
  function n(r) {
    const l = a(r), m = j.Chat.Commands;
    return Object.keys(e).filter((f) => f.startsWith(l)).map((f) => ({
      title: m[f],
      content: ":" + f
    }));
  }
  function c(r) {
    const l = a(r), m = typeof e[l] == "function";
    return {
      matched: m,
      invoke: () => m && e[l](r)
    };
  }
  return {
    match: c,
    search: n
  };
}
const A = {
  ready: !1,
  builtinEngine: new dt([], {
    keys: ["title"]
  }),
  userEngine: new dt([], {
    keys: ["title"]
  }),
  count: {
    builtin: 0
  },
  allPrompts: [],
  builtinPrompts: [],
  init(e, a) {
    this.ready || (this.allPrompts = a.concat(e), this.builtinPrompts = e.slice(), this.builtinEngine.setCollection(e), this.userEngine.setCollection(a), this.ready = !0);
  },
  remove(e) {
    this.userEngine.remove((a) => a.id === e);
  },
  add(e) {
    this.userEngine.add(e);
  },
  search(e) {
    const a = this.userEngine.search(e), n = this.builtinEngine.search(e);
    return a.concat(n).map((c) => c.item);
  }
}, It = de({
  counter: 0,
  prompts: {}
}, (e, a) => ({
  add(n) {
    const c = a().prompts;
    return n.id = D(), n.isUser = !0, n.createdAt = Date.now(), c[n.id] = n, e(() => ({
      prompts: c
    })), n.id;
  },
  get(n) {
    const c = a().prompts[n];
    return c || A.builtinPrompts.find((r) => r.id === n);
  },
  remove(n) {
    const c = a().prompts;
    delete c[n], Object.entries(c).some(([r, l]) => l.id === n ? (delete c[r], !0) : !1), A.remove(n), e(() => ({
      prompts: c,
      counter: a().counter + 1
    }));
  },
  getUserPrompts() {
    const n = Object.values(a().prompts ?? {});
    return n.sort((c, r) => r.id && c.id ? r.createdAt - c.createdAt : 0), n;
  },
  updatePrompt(n, c) {
    const r = a().prompts[n] ?? {
      title: "",
      content: "",
      id: n
    };
    A.remove(n), c(r);
    const l = a().prompts;
    l[n] = r, e(() => ({
      prompts: l
    })), A.add(r);
  },
  search(n) {
    return n.length === 0 ? this.getUserPrompts().concat(A.builtinPrompts) : A.search(n);
  }
}), {
  name: ue.Prompt,
  version: 3,
  migrate(e, a) {
    const n = JSON.parse(JSON.stringify(e));
    return a < 3 && Object.values(n.prompts).forEach((c) => c.id = D()), n;
  },
  onRehydrateStorage(e) {
    if (typeof window > "u")
      return;
    fetch("./prompts.json").then((n) => n.json()).then((n) => {
      console.log("res", n);
      let c = [n.en, n.tw, n.cn];
      Object.keys(n).includes(gt()) && (c = [n[gt()]]);
      const r = c.map((f) => f.map(([u, S]) => ({
        id: D(),
        title: u,
        content: S,
        createdAt: Date.now()
      }))), l = It.getState().getUserPrompts() ?? [], m = r.reduce((f, u) => f.concat(u), []).filter((f) => !!f.title && !!f.content);
      A.count.builtin = n.en.length + n.cn.length + n.tw.length, A.init(m, l);
    });
  }
}), ca = "artifacts_Gd5ff", ra = "artifacts-header_RnHYD", ia = "artifacts-title_XphTn", la = "artifacts-content_VDTZt", ma = "artifacts-iframe_LjzMI", ha = {
  artifacts: ca,
  "artifacts-header": "artifacts-header_RnHYD",
  artifactsHeader: ra,
  "artifacts-title": "artifacts-title_XphTn",
  artifactsTitle: ia,
  "artifacts-content": "artifacts-content_VDTZt",
  artifactsContent: la,
  "artifacts-iframe": "artifacts-iframe_LjzMI",
  artifactsIframe: ma
}, da = re(function(a, n) {
  const c = T(null), [r, l] = v(D()), [m, f] = v(600), [u, S] = v("");
  I(() => {
    const _ = (k) => {
      const {
        id: E,
        height: w,
        title: F
      } = k.data;
      S(F), E == r && f(w);
    };
    return window.addEventListener("message", _), () => {
      window.removeEventListener("message", _);
    };
  }, [r]), ie(n, () => ({
    reload: () => {
      l(D());
    }
  }));
  const b = G(() => {
    if (!a.autoHeight) return a.height || 600;
    if (typeof a.height == "string")
      return a.height;
    const _ = a.height || 600;
    return m + 40 > _ ? _ : m + 40;
  }, [a.autoHeight, a.height, m]), O = G(() => {
    const _ = `<script>window.addEventListener("DOMContentLoaded", () => new ResizeObserver((entries) => parent.postMessage({id: '${r}', height: entries[0].target.clientHeight}, '*')).observe(document.body))<\/script>`;
    return a.code.includes("<!DOCTYPE html>") && a.code.replace("<!DOCTYPE html>", "<!DOCTYPE html>" + _), _ + a.code;
  }, [a.code, r]), P = () => {
    a != null && a.onLoad && a.onLoad(u);
  };
  return /* @__PURE__ */ s.jsx("iframe", { className: ha["artifacts-iframe"], ref: c, sandbox: "allow-forms allow-modals allow-scripts", style: {
    height: b
  }, srcDoc: O, onLoad: P }, r);
});
function ua(e) {
  const a = T(null), [n, c] = v(!1);
  I(() => {
    e.code && a.current && ae.run({
      nodes: [a.current],
      suppressErrors: !0
    }).catch((l) => {
      c(!0), console.error("[Mermaid] ", l.message);
    });
  }, [e.code]);
  function r() {
    var u;
    const l = (u = a.current) == null ? void 0 : u.querySelector("svg");
    if (!l) return;
    const m = new XMLSerializer().serializeToString(l), f = new Blob([m], {
      type: "image/svg+xml"
    });
    xe(URL.createObjectURL(f));
  }
  return n ? null : /* @__PURE__ */ s.jsx("div", { className: $("no-dark", "mermaid"), style: {
    cursor: "pointer",
    overflow: "auto"
  }, ref: a, onClick: () => r(), children: e.code });
}
function ga(e) {
  var _;
  const a = T(null), n = T(null), [c, r] = v(""), [l, m] = v(""), {
    height: f
  } = ge(), S = Z().currentSession(), b = rt(() => {
    var F;
    if (!a.current) return;
    const k = a.current.querySelector("code.language-mermaid");
    k && r(k.innerText);
    const E = a.current.querySelector("code.language-html"), w = (F = a.current.querySelector("code")) == null ? void 0 : F.innerText;
    E ? m(E.innerText) : (w != null && w.startsWith("<!DOCTYPE") || w != null && w.startsWith("<svg") || w != null && w.startsWith("<?xml")) && m(w);
  }, 600), O = wt(), P = ((_ = S.mask) == null ? void 0 : _.enableArtifacts) !== !1 && O.enableArtifacts;
  return I(() => {
    if (a.current) {
      const k = a.current.querySelectorAll("code"), E = ["", "md", "markdown", "text", "txt", "plaintext", "tex", "latex"];
      k.forEach((w) => {
        const F = w.className.match(/language-(\w+)/), N = F ? F[1] : "";
        E.includes(N) && (w.style.whiteSpace = "pre-wrap");
      }), setTimeout(b, 1);
    }
  }, []), /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    /* @__PURE__ */ s.jsxs("pre", { ref: a, children: [
      /* @__PURE__ */ s.jsx("span", { className: "copy-code-button", onClick: () => {
        var k;
        a.current && it(((k = a.current.querySelector("code")) == null ? void 0 : k.innerText) ?? "");
      } }),
      e.children
    ] }),
    c.length > 0 && /* @__PURE__ */ s.jsx(ua, { code: c }, c),
    l.length > 0 && P && /* @__PURE__ */ s.jsxs(pe, { className: "no-dark html", right: 70, children: [
      /* @__PURE__ */ s.jsx(vt, { style: {
        position: "absolute",
        right: 120,
        top: 10
      }, bordered: !0, icon: /* @__PURE__ */ s.jsx(kt, {}), shadow: !0, onClick: () => {
        var k;
        return (k = n.current) == null ? void 0 : k.reload();
      } }),
      /* @__PURE__ */ s.jsx(da, { ref: n, code: l, autoHeight: !document.fullscreenElement, height: document.fullscreenElement ? f : 600 })
    ] })
  ] });
}
function pa(e) {
  var P;
  const n = Z().currentSession(), c = wt(), r = ((P = n.mask) == null ? void 0 : P.enableCodeFold) !== !1 && c.enableCodeFold, l = T(null), [m, f] = v(!0), [u, S] = v(!1);
  I(() => {
    if (l.current) {
      const _ = l.current.scrollHeight;
      S(_ > 400), l.current.scrollTop = l.current.scrollHeight;
    }
  }, [e.children]);
  const b = () => {
    f((_) => !_);
  }, O = () => u && r && m ? /* @__PURE__ */ s.jsx("div", { className: $("show-hide-button", {
    collapsed: m,
    expanded: !m
  }), children: /* @__PURE__ */ s.jsx("button", { onClick: b, children: j.NewChat.More }) }) : null;
  return /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    /* @__PURE__ */ s.jsx("code", { className: $(e == null ? void 0 : e.className), ref: l, style: {
      maxHeight: r && m ? "400px" : "none",
      overflowY: "hidden"
    }, children: e.children }),
    O()
  ] });
}
function fa(e) {
  const a = /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
  return e.replace(a, (n, c, r, l) => c || (r ? `$$${r}$$` : l ? `$${l}$` : n));
}
function xa(e) {
  return e.replace(/([`]*?)(\w*?)([\n\r]*?)(<!DOCTYPE html>)/g, (a, n, c, r, l) => n ? a : "\n```html\n" + l).replace(/(<\/body>)([\r\n\s]*?)(<\/html>)([\n\r]*)([`]*)([\n\r]*?)/g, (a, n, c, r, l, m) => m ? a : n + c + r + "\n```\n");
}
function ya(e) {
  const a = G(() => xa(fa(e.content)), [e.content]);
  return /* @__PURE__ */ s.jsx(Yt, { remarkPlugins: [Qt, Jt, te], rehypePlugins: [ee, [ne, {
    detect: !1,
    ignoreMissing: !0
  }]], components: {
    pre: ga,
    code: pa,
    p: (n) => (console.log("pProps", n), /* @__PURE__ */ s.jsx("p", { ...n, dir: "auto" })),
    a: (n) => {
      const c = n.href || "";
      if (/\.(aac|mp3|opus|wav)$/.test(c))
        return /* @__PURE__ */ s.jsx("figure", { children: /* @__PURE__ */ s.jsx("audio", { controls: !0, src: c }) });
      if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(c))
        return /* @__PURE__ */ s.jsx("video", { controls: !0, width: "99.9%", children: /* @__PURE__ */ s.jsx("source", { src: c }) });
      const l = /^\/#/i.test(c) ? "_self" : n.target ?? "_blank";
      return /* @__PURE__ */ s.jsx("a", { ...n, target: l });
    }
  }, children: a });
}
const wa = ce.memo(ya);
function va(e) {
  const a = T(null);
  return /* @__PURE__ */ s.jsx("div", { className: "markdown-body", style: {
    fontSize: `${e.fontSize ?? 14}px`,
    fontFamily: e.fontFamily || "inherit"
  }, ref: a, onContextMenu: e.onContextMenu, onDoubleClickCapture: e.onDoubleClickCapture, dir: "auto", children: e.loading ? /* @__PURE__ */ s.jsx(fe, {}) : /* @__PURE__ */ s.jsx(wa, { content: e.content }) });
}
const ct = ye();
function ka() {
  const e = T(!1);
  return I(() => {
    const n = () => {
      e.current = !0;
    }, c = () => {
      e.current = !1;
    };
    return window.addEventListener("compositionstart", n), window.addEventListener("compositionend", c), () => {
      window.removeEventListener("compositionstart", n), window.removeEventListener("compositionend", c);
    };
  }, []), {
    shouldSubmit: (n) => n.keyCode == 229 || n.key !== "Enter" || n.key === "Enter" && (n.nativeEvent.isComposing || e.current) ? !1 : n.altKey || n.ctrlKey || n.shiftKey || n.metaKey || !n.altKey && !n.ctrlKey && !n.shiftKey && !n.metaKey
  };
}
function Ca(e) {
  const a = e.prompts.length === 0, [n, c] = v(0), r = T(null);
  return I(() => {
    c(0);
  }, [e.prompts.length]), I(() => {
    const l = (m) => {
      if (a || m.metaKey || m.altKey || m.ctrlKey)
        return;
      const f = (u) => {
        var b;
        m.stopPropagation(), m.preventDefault();
        const S = Math.max(0, Math.min(e.prompts.length - 1, n + u));
        c(S), (b = r.current) == null || b.scrollIntoView({
          block: "center"
        });
      };
      if (m.key === "ArrowUp")
        f(1);
      else if (m.key === "ArrowDown")
        f(-1);
      else if (m.key === "Enter") {
        const u = e.prompts.at(n);
        u && e.onPromptSelect(u);
      }
    };
    return window.addEventListener("keydown", l), () => window.removeEventListener("keydown", l);
  }, [e.prompts.length, n]), a ? null : /* @__PURE__ */ s.jsx("div", { className: d["prompt-hints"], children: e.prompts.map((l, m) => /* @__PURE__ */ s.jsxs("div", { ref: m === n ? r : null, className: $(d["prompt-hint"], {
    [d["prompt-hint-selected"]]: m === n
  }), onClick: () => e.onPromptSelect(l), onMouseEnter: () => c(m), children: [
    /* @__PURE__ */ s.jsx("div", { className: d["hint-title"], children: l.title }),
    /* @__PURE__ */ s.jsx("div", { className: d["hint-content"], children: l.content })
  ] }, l.title + m.toString())) });
}
function _a() {
  const e = Z(), a = e.currentSession();
  return /* @__PURE__ */ s.jsxs("div", { className: d["clear-context"], onClick: () => e.updateTargetSession(a, (n) => n.clearContextIndex = void 0), children: [
    /* @__PURE__ */ s.jsx("div", { className: d["clear-context-tips"], children: j.Context.Clear }),
    /* @__PURE__ */ s.jsx("div", { className: d["clear-context-revert-btn"], children: j.Context.Revert })
  ] });
}
function B(e) {
  const a = T(null), n = T(null), [c, r] = v({
    full: 16,
    icon: 16
  });
  function l() {
    if (!a.current || !n.current) return;
    const m = (S) => S.getBoundingClientRect().width, f = m(n.current), u = m(a.current);
    r({
      full: f + u,
      icon: u
    });
  }
  return /* @__PURE__ */ s.jsxs("div", { className: $(d["chat-input-action"], "clickable"), onClick: () => {
    e.onClick(), setTimeout(l, 1);
  }, onMouseEnter: l, onTouchStart: l, style: {
    "--icon-width": `${c.icon}px`,
    "--full-width": `${c.full}px`
  }, children: [
    /* @__PURE__ */ s.jsx("div", { ref: a, className: d.icon, children: e.icon }),
    /* @__PURE__ */ s.jsx("div", { className: d.text, ref: n, children: e.text })
  ] });
}
function Ea(e, a = !1) {
  const [n, c] = v(!0);
  function r() {
    const l = e.current;
    l && requestAnimationFrame(() => {
      c(!0), l.scrollTo(0, l.scrollHeight);
    });
  }
  return I(() => {
    n && !a && r();
  }), {
    scrollRef: e,
    autoScroll: n,
    setAutoScroll: c,
    scrollDomToBottom: r
  };
}
function Ma(e) {
  const a = Z(), n = a.currentSession(), c = n.mask.modelConfig.model, [r, l] = v(!1);
  return I(() => {
    const m = Ct(c);
    l(m), m || (e.setAttachImages([]), e.setUploading(!1));
  }, [a, c, n]), /* @__PURE__ */ s.jsx("div", { className: d["chat-input-actions"], children: /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
    !e.hitBottom && /* @__PURE__ */ s.jsx(B, { onClick: e.scrollToBottom, text: j.Chat.InputActions.ToBottom, icon: /* @__PURE__ */ s.jsx(He, {}) }),
    r && /* @__PURE__ */ s.jsx(B, { onClick: e.uploadImage, text: j.Chat.InputActions.UploadImage, icon: e.uploading ? /* @__PURE__ */ s.jsx(_t, {}) : /* @__PURE__ */ s.jsx(Pe, {}) }),
    /* @__PURE__ */ s.jsx(B, { onClick: e.showPromptHints, text: j.Chat.InputActions.Prompt, icon: /* @__PURE__ */ s.jsx(Le, {}) }),
    /* @__PURE__ */ s.jsx(B, { text: j.Chat.InputActions.Clear, icon: /* @__PURE__ */ s.jsx(Te, {}), onClick: () => {
      a.updateTargetSession(n, (m) => {
        m.clearContextIndex === m.messages.length ? m.clearContextIndex = void 0 : (m.clearContextIndex = m.messages.length, m.memoryPrompt = "");
      });
    } })
  ] }) });
}
function Ia(e) {
  return /* @__PURE__ */ s.jsx("div", { className: d["delete-image"], onClick: e.deleteImage, children: /* @__PURE__ */ s.jsx(Et, {}) });
}
const J = /* @__PURE__ */ new Map();
function ja() {
  var ht;
  const e = Z(), a = e.currentSession();
  console.log("session", a);
  const n = T(null), [c, r] = v(""), [l, m] = v(!1), {
    shouldSubmit: f
  } = ka(), u = T(null), S = u != null && u.current ? Math.abs(u.current.scrollHeight - (u.current.scrollTop + u.current.clientHeight)) <= 1 : !1, {
    setAutoScroll: b,
    scrollDomToBottom: O
  } = Ea(u, S), [P, _] = v(!0), k = we(), [E, w] = v([]), [F, N] = v(!1), [jt, St] = v(2), bt = rt(() => {
    const t = n.current ? Ee(n.current) : 1, i = Math.min(20, Math.max(2 + +!k, t));
    St(i);
  }, 100, {
    leading: !0,
    trailing: !0
  });
  I(bt, [c]);
  const tt = oa({
    new: () => e.newSession(),
    prev: () => e.nextSession(-1),
    next: () => e.nextSession(1),
    clear: () => e.updateTargetSession(a, (t) => t.clearContextIndex = t.messages.length),
    fork: () => e.forkSession(),
    del: () => e.deleteSession(e.currentSessionIndex)
  }), Lt = 30, Tt = (t) => {
    r(t);
    const i = t.trim().length;
    if (i === 0)
      Y([]);
    else if (t.match(Mt))
      Y(tt.search(t));
    else if (i < Lt && t.startsWith("/")) {
      const h = t.slice(1);
      mt(h);
    }
  }, lt = (t) => {
    var h;
    if (t.trim() === "" && oe(E)) return;
    const i = tt.match(t);
    if (i.matched) {
      r(""), i.invoke();
      return;
    }
    m(!0), e.onUserInput(t, E).then(() => m(!1)), w([]), e.setLastInput(t), r(""), k || (h = n.current) == null || h.focus(), b(!0);
  };
  I(() => {
    a.messages.push(W({
      id: D(),
      date: (/* @__PURE__ */ new Date()).toLocaleString(),
      role: "assistant",
      content: [{
        type: "excel_url",
        excel_url: {
          name: "excel.xlsx",
          url: "https://example.com/excel.xlsx"
        }
      }]
    })), a.messages.push(W({
      id: D(),
      date: (/* @__PURE__ */ new Date()).toLocaleString(),
      role: "assistant",
      content: [{
        type: "interactive",
        interactive: {
          template: "order.template.tsx",
          props: {
            orderNo: "XS-ZT-20240012447",
            roll: 13,
            weight: 200,
            length: 10,
            statusName: "已下单-配布中",
            time: "14:21",
            price: 1e3,
            paymentStatus: "未收款"
          },
          events: {
            onClick: () => {
              console.log("onClick");
            }
          }
        }
      }]
    })), a.messages.push(W({
      id: D(),
      date: (/* @__PURE__ */ new Date()).toLocaleString(),
      role: "assistant",
      content: [{
        type: "interactive",
        interactive: {
          template: "customer.template.tsx",
          props: {
            customerName: "张三",
            level: "VIP",
            debt: 1e3,
            note: "备注信息",
            mostPurchased: "布料",
            lastOrder: "2024-01-01",
            followUpPerson: "李四",
            salesperson: "王五"
          },
          events: {
            onClick: () => {
              console.log("onClick");
            }
          }
        }
      }]
    })), a.messages.push(W({
      id: D(),
      date: (/* @__PURE__ */ new Date()).toLocaleString(),
      role: "assistant",
      content: [{
        type: "interactive",
        interactive: {
          template: "billingSummary.template.tsx",
          props: {
            monthlyPurchase: 2e3,
            debt: 30.99
          },
          events: {
            onClick: () => {
              console.log("onClick");
            }
          }
        }
      }]
    }));
  }, []), I(() => {
    e.updateTargetSession(a, (t) => {
      const i = Date.now() - Me;
      console.log("session", t), t.messages.forEach((h) => {
        (h.isError || new Date(h.date).getTime() < i) && (h.streaming && (h.streaming = !1), h.content.length === 0 && (h.isError = !0, h.content = ve({
          error: !0,
          message: "empty response"
        })));
      });
    });
  }, [a]);
  const Ft = (t) => {
    if (t.key === "ArrowUp" && c.length <= 0 && !(t.metaKey || t.altKey || t.ctrlKey)) {
      r(e.lastInput ?? ""), t.preventDefault();
      return;
    }
    f(t) && st.length === 0 && (lt(c), t.preventDefault());
  }, Nt = (t, i) => {
    Ie(t.currentTarget, K(i)) && (c.length === 0 && r(K(i)), t.preventDefault());
  }, et = (t) => {
    e.updateTargetSession(a, (i) => i.messages = i.messages.filter((h) => h.id !== t));
  }, Dt = (t) => {
    et(t);
  }, Pt = (t) => {
    var L;
    const i = a.messages.findIndex((y) => y.id === t.id);
    if (i < 0 || i >= a.messages.length) {
      console.error("[Chat] failed to find resending message", t);
      return;
    }
    let h, g;
    if (t.role === "assistant") {
      g = t;
      for (let y = i; y >= 0; y -= 1)
        if (a.messages[y].role === "user") {
          h = a.messages[y];
          break;
        }
    } else if (t.role === "user") {
      h = t;
      for (let y = i; y < a.messages.length; y += 1)
        if (a.messages[y].role === "assistant") {
          g = a.messages[y];
          break;
        }
    }
    if (h === void 0) {
      console.error("[Chat] failed to resend", t);
      return;
    }
    et(h.id), et(g == null ? void 0 : g.id), m(!0);
    const p = K(h), C = X(h);
    e.onUserInput(p, C).then(() => m(!1)), (L = n.current) == null || L.focus();
  }, R = G(() => a.mask.hideContext ? [] : a.mask.context.slice(), [a.mask.context, a.mask.hideContext]);
  if (R.length === 0 && ((ht = a.messages.at(0)) == null ? void 0 : ht.content) !== pt.content) {
    const t = Object.assign({}, pt);
    R.push(t);
  }
  const U = G(() => R.concat(a.messages).concat(l ? [{
    ...W({
      role: "assistant",
      content: "……"
    }),
    preview: !0
  }] : []).concat(c.length > 0 ? [{
    ...W({
      role: "user",
      content: c
    }),
    preview: !0
  }] : []), [R, l, a.messages, c]), [z, Ht] = v(Math.max(0, U.length - V));
  function nt(t) {
    t = Math.min(U.length - V, t), t = Math.max(0, t), Ht(t);
  }
  const q = G(() => {
    const t = Math.min(z + 3 * V, U.length);
    return U.slice(z, t);
  }, [z, U]), At = (t) => {
    const i = t.scrollTop + t.clientHeight, h = t.clientHeight, g = t.scrollTop <= h, p = i >= t.scrollHeight - h, C = i >= t.scrollHeight - (k ? 4 : 10), L = z - V, y = z + V;
    g && !p ? nt(L) : p && nt(y), _(C), b(C);
  };
  function at() {
    nt(U.length - V), O();
  }
  const Ot = (a.clearContextIndex ?? -1) >= 0 ? a.clearContextIndex + R.length - z : -1, Kt = !k;
  I(() => {
    const t = ke(a.id), i = ct.getItem(t);
    i && c.length === 0 && (r(i), ct.removeItem(t));
    const h = n.current;
    return () => {
      ct.setItem(t, (h == null ? void 0 : h.value) ?? "");
    };
  }, []);
  const Bt = ut(async (t) => {
    const i = e.currentSession().mask.modelConfig.model;
    if (!Ct(i))
      return;
    const h = (t.clipboardData || window.clipboardData).items;
    for (const g of h)
      if (g.kind === "file" && g.type.startsWith("image/")) {
        t.preventDefault();
        const p = g.getAsFile();
        if (p) {
          const C = [];
          C.push(...E), C.push(...await new Promise((y, H) => {
            N(!0);
            const x = [];
            yt(p).then((M) => {
              x.push(M), N(!1), y(x);
            }).catch((M) => {
              N(!1), H(M);
            });
          }));
          const L = C.length;
          L > 3 && C.splice(3, L - 3), w(C);
        }
      }
  }, [E, e]);
  async function Rt() {
    const t = [];
    t.push(...E), t.push(...await new Promise((h, g) => {
      const p = document.createElement("input");
      p.type = "file", p.accept = "image/png, image/jpeg, image/webp, image/heic, image/heif", p.multiple = !0, p.onchange = (C) => {
        N(!0);
        const L = C.target.files, y = [];
        for (let H = 0; H < L.length; H++) {
          const x = C.target.files[H];
          yt(x).then((M) => {
            y.push(M), (y.length === 3 || y.length === L.length) && (N(!1), h(y));
          }).catch((M) => {
            N(!1), g(M);
          });
        }
      }, p.click();
    }));
    const i = t.length;
    i > 3 && t.splice(3, i - 3), w(t);
  }
  const Ut = It(), [st, Y] = v([]), zt = (t) => {
    setTimeout(() => {
      var h;
      Y([]);
      const i = tt.match(t.content);
      i.matched ? (i.invoke(), r("")) : r(t.content), (h = n.current) == null || h.focus();
    }, 30);
  }, mt = rt((t) => {
    const i = Ut.search(t);
    Y(i);
  }, 100, {
    leading: !0,
    trailing: !0
  }), Wt = (t) => {
    console.log("file", t), t.url && (t.type === "excel_url" ? window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(t.url)}`, "_blank") : t.type === "word_url" ? window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(t.url)}`, "_blank") : t.type === "pdf_url" && window.open(t.url, "_blank"));
  }, Vt = ut((t) => {
    console.log("interactiveComponents", J, t);
    const i = t.replace(".tsx", "");
    if (!J.has(t)) {
      const h = se(() => je(/* @__PURE__ */ Object.assign({ "../interactive/billingSummary.template.tsx": () => import("./interactive/interactive.CPN5bvSW.js").then((p) => p.b), "../interactive/customer.template.tsx": () => import("./interactive/interactive.CPN5bvSW.js").then((p) => p.c), "../interactive/order.template.tsx": () => import("./interactive/interactive.CPN5bvSW.js").then((p) => p.o) }), `../interactive/${i}.tsx`, 3), {
        loading: () => /* @__PURE__ */ s.jsx("div", { children: "Loading..." }),
        ssr: !1
      });
      console.log("Component", h);
      const g = (p) => (I(() => {
        const C = document.createElement("style");
        document.head.appendChild(C), document.head.removeChild(C);
      }, []), /* @__PURE__ */ s.jsx(h, { ...p }));
      J.set(t, g);
    }
    return J.get(t);
  }, []);
  return I(() => {
    const t = (i) => {
      var h;
      if (i.shiftKey && i.key.toLowerCase() === "escape")
        i.preventDefault(), (h = n.current) == null || h.focus();
      else if ((i.metaKey || i.ctrlKey) && i.shiftKey && i.code === "Semicolon") {
        i.preventDefault();
        const g = document.querySelectorAll(".copy-code-button");
        g.length > 0 && g[g.length - 1].click();
      } else if ((i.metaKey || i.ctrlKey) && i.shiftKey && i.key.toLowerCase() === "c") {
        i.preventDefault();
        const g = q.filter((p) => p.role !== "user").pop();
        if (g) {
          const p = K(g);
          it(p);
        }
      }
    };
    return window.addEventListener("keydown", t), () => {
      window.removeEventListener("keydown", t);
    };
  }, [q, e]), I(() => {
    const t = document.createElement("style");
    document.head.appendChild(t), document.head.removeChild(t);
  }, []), /* @__PURE__ */ s.jsx(s.Fragment, { children: /* @__PURE__ */ s.jsx("div", { className: d.chat, children: /* @__PURE__ */ s.jsx("div", { className: d["chat-main"], children: /* @__PURE__ */ s.jsxs("div", { className: d["chat-body-container"], children: [
    /* @__PURE__ */ s.jsx("div", { className: d["chat-body"], ref: u, onScroll: (t) => At(t.currentTarget), onMouseDown: () => {
      var t;
      return (t = n.current) == null ? void 0 : t.blur();
    }, onTouchStart: () => {
      var t;
      (t = n.current) == null || t.blur(), b(!1);
    }, children: q.map((t, i) => {
      var y, H;
      console.log("messages", q);
      const h = t.role === "user", g = i < R.length, p = i > 0 && !(t.preview || t.content.length === 0) && !g, C = t.preview || t.streaming, L = i === Ot - 1;
      return /* @__PURE__ */ s.jsxs(le, { children: [
        /* @__PURE__ */ s.jsx("div", { className: h ? d["chat-message-user"] : d["chat-message"], children: /* @__PURE__ */ s.jsxs("div", { className: d["chat-message-container"], children: [
          /* @__PURE__ */ s.jsxs("div", { className: d["chat-message-header"], children: [
            /* @__PURE__ */ s.jsx("div", { className: d["chat-message-avatar"], children: h ? /* @__PURE__ */ s.jsx(ot, { avatar: Q.avatar }) : /* @__PURE__ */ s.jsx(s.Fragment, { children: ["system"].includes(t.role) ? /* @__PURE__ */ s.jsx(ot, { avatar: "2699-fe0f" }) : /* @__PURE__ */ s.jsx(ot, { model: t.model || a.mask.modelConfig.model }) }) }),
            !h && /* @__PURE__ */ s.jsx("div", { className: d["chat-model-name"], children: "阿布" }),
            p && /* @__PURE__ */ s.jsx("div", { className: d["chat-message-actions"], children: /* @__PURE__ */ s.jsx("div", { className: d["chat-input-actions"], children: /* @__PURE__ */ s.jsxs(s.Fragment, { children: [
              /* @__PURE__ */ s.jsx(B, { text: j.Chat.Actions.Retry, icon: /* @__PURE__ */ s.jsx(kt, {}), onClick: () => Pt(t) }),
              /* @__PURE__ */ s.jsx(B, { text: j.Chat.Actions.Delete, icon: /* @__PURE__ */ s.jsx(Et, {}), onClick: () => Dt(t.id ?? i.toString()) }),
              /* @__PURE__ */ s.jsx(B, { text: j.Chat.Actions.Copy, icon: /* @__PURE__ */ s.jsx(be, {}), onClick: () => it(K(t)) })
            ] }) }) })
          ] }),
          ((y = t == null ? void 0 : t.tools) == null ? void 0 : y.length) == 0 && C && /* @__PURE__ */ s.jsx("div", { className: d["chat-message-status"], children: j.Chat.Typing }),
          (t == null ? void 0 : t.tools) && (t == null ? void 0 : t.tools.length) > 0 && /* @__PURE__ */ s.jsx("div", { className: d["chat-message-tools"], children: (H = t == null ? void 0 : t.tools) == null ? void 0 : H.map((x) => {
            var M;
            return /* @__PURE__ */ s.jsxs("div", { title: x == null ? void 0 : x.errorMsg, className: d["chat-message-tool"], children: [
              x.isError === !1 ? /* @__PURE__ */ s.jsx(Ce, {}) : x.isError === !0 ? /* @__PURE__ */ s.jsx(_e, {}) : /* @__PURE__ */ s.jsx(_t, {}),
              /* @__PURE__ */ s.jsx("span", { children: (M = x == null ? void 0 : x.function) == null ? void 0 : M.name })
            ] }, x.id);
          }) }),
          /* @__PURE__ */ s.jsxs("div", { className: d["chat-message-item"], children: [
            /* @__PURE__ */ s.jsx(
              va,
              {
                content: K(t),
                loading: (t.preview || t.streaming) && t.content.length === 0 && !h,
                onContextMenu: (x) => Nt(x, t),
                onDoubleClickCapture: () => {
                  k && r(K(t));
                },
                fontSize: Q.fontSize,
                fontFamily: Q.fontFamily,
                parentRef: u,
                defaultShow: i >= q.length - 6
              },
              t.streaming ? "loading" : "done"
            ),
            ft(t).length > 0 && ft(t).map((x, M) => {
              if (!x) return null;
              const {
                template: Xt,
                props: Gt,
                events: $t
              } = x, Zt = Vt(Xt);
              return /* @__PURE__ */ s.jsx(Zt, { props: Gt || null, events: $t || null }, `${t.id}-${M}`);
            }),
            xt(t).length ? xt(t).map((x, M) => /* @__PURE__ */ s.jsxs("div", { className: d["chat-message-item-file"], onClick: () => Wt(x), children: [
              x.type === "excel_url" ? /* @__PURE__ */ s.jsx(Fe, { className: d["chat-message-item-file-icon"] }) : x.type === "word_url" ? /* @__PURE__ */ s.jsx(De, { className: d["chat-message-item-file-icon"] }) : /* @__PURE__ */ s.jsx(Ne, { className: d["chat-message-item-file-icon"] }),
              /* @__PURE__ */ s.jsx("div", { className: d["chat-message-item-file-name"], children: x.name })
            ] }, M)) : null,
            X(t).length == 1 && /* @__PURE__ */ s.jsx("img", { className: d["chat-message-item-image"], src: X(t)[0], alt: "" }),
            X(t).length > 1 && /* @__PURE__ */ s.jsx("div", { className: d["chat-message-item-images"], style: {
              "--image-count": X(t).length
            }, children: X(t).map((x, M) => /* @__PURE__ */ s.jsx("img", { className: d["chat-message-item-image-multi"], src: x, alt: "" }, M)) })
          ] }),
          (t == null ? void 0 : t.audio_url) && /* @__PURE__ */ s.jsx("div", { className: d["chat-message-audio"], children: /* @__PURE__ */ s.jsx("audio", { src: t.audio_url, controls: !0 }) }),
          /* @__PURE__ */ s.jsx("div", { className: d["chat-message-action-date"], children: g ? j.Chat.IsContext : t.date.toLocaleString() })
        ] }) }),
        L && /* @__PURE__ */ s.jsx(_a, {})
      ] }, t.id);
    }) }),
    /* @__PURE__ */ s.jsxs("div", { className: d["chat-input-panel"], children: [
      /* @__PURE__ */ s.jsx(Ca, { prompts: st, onPromptSelect: zt }),
      /* @__PURE__ */ s.jsx(Ma, { uploadImage: Rt, setAttachImages: w, setUploading: N, scrollToBottom: at, showPromptHints: () => {
        var t;
        if (st.length > 0) {
          Y([]);
          return;
        }
        (t = n.current) == null || t.focus(), r("/"), mt("");
      }, hitBottom: P, uploading: F, setUserInput: r }),
      /* @__PURE__ */ s.jsxs("label", { className: $(d["chat-input-panel-inner"], {
        [d["chat-input-panel-inner-attach"]]: E.length !== 0
      }), htmlFor: "chat-input", children: [
        /* @__PURE__ */ s.jsx("textarea", { id: "chat-input", ref: n, className: d["chat-input"], placeholder: j.Chat.Input("Enter"), onInput: (t) => Tt(t.currentTarget.value), value: c, onKeyDown: Ft, onFocus: at, onClick: at, onPaste: Bt, rows: jt, autoFocus: Kt, style: {
          fontSize: Q.fontSize,
          fontFamily: Q.fontFamily
        } }),
        E.length != 0 && /* @__PURE__ */ s.jsx("div", { className: d["attach-images"], children: E.map((t, i) => /* @__PURE__ */ s.jsx("div", { className: d["attach-image"], style: {
          backgroundImage: `url("${t}")`
        }, children: /* @__PURE__ */ s.jsx("div", { className: d["attach-image-mask"], children: /* @__PURE__ */ s.jsx(Ia, { deleteImage: () => {
          w(E.filter((h, g) => g !== i));
        } }) }) }, i)) }),
        /* @__PURE__ */ s.jsx(vt, { icon: /* @__PURE__ */ s.jsx(Se, {}), text: j.Chat.Send, className: d["chat-input-send"], type: "primary", onClick: () => lt(c) })
      ] })
    ] })
  ] }) }) }, a.id) });
}
function Ta() {
  const a = Z().currentSession();
  return /* @__PURE__ */ s.jsx(ja, {}, a.id);
}
export {
  Ta as Chat,
  B as ChatAction,
  Ma as ChatActions,
  Ia as DeleteImageButton,
  Ca as PromptHints
};
//# sourceMappingURL=chat.klCQVjDQ.js.map
