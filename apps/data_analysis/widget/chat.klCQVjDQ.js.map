{"version": 3, "file": "chat.klCQVjDQ.js", "sources": ["../app/asserts/icons/send-white.svg", "../app/asserts/icons/copy.svg", "../app/asserts/icons/loading.svg", "../app/asserts/icons/prompt.svg", "../app/asserts/icons/break.svg", "../app/asserts/icons/clear.svg", "../app/asserts/icons/excel.svg", "../app/asserts/icons/pdf.svg", "../app/asserts/icons/word.svg", "../app/asserts/icons/image.svg", "../app/asserts/icons/bottom.svg", "../app/common/utils/chat.ts", "../app/components/emoji/emoji.tsx", "../app/common/command.ts", "../app/store/prompt.ts", "../app/components/artifacts/artifacts.tsx", "../app/components/markdown/markdown.tsx", "../app/components/chat/chat.tsx"], "sourcesContent": ["import * as React from \"react\";\nconst SvgSendWhite = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0 8 8)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })), /* @__PURE__ */ React.createElement(\"g\", { mask: \"url(#bg-mask-0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 1\", style: {\n  stroke: \"#fff\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,4.71L6.67,6L8.34,12.67L12.67,0L0,4.71Z\", transform: \"translate(1.3333333333333333 2) rotate(0 6.333333333333333 6.333333333333333)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 2\", style: {\n  stroke: \"#fff\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,1.89L1.89,0\", transform: \"translate(8.002766666666666 6.1172) rotate(0 0.9428000000000001 0.9428000000000001)\" }))));\nexport default SvgSendWhite;\n", "import * as React from \"react\";\nconst SvgCopy = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0 8 8)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })), /* @__PURE__ */ React.createElement(\"g\", { mask: \"url(#bg-mask-0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 1\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,2.48L0,0.94C0,0.42 0.42,0 0.94,0L9.06,0C9.58,0 10,0.42 10,0.94L10,9.06C10,9.58 9.58,10 9.06,10L7.51,10\", transform: \"translate(4.333333333333333 1.6666666666666665) rotate(0 5 5)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 2\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0.94,0C0.42,0 0,0.42 0,0.94L0,9.06C0,9.58 0.42,10 0.94,10L9.06,10C9.58,10 10,9.58 10,9.06L10,0.94C10,0.42 9.58,0 9.06,0L0.94,0Z\", transform: \"translate(1.6666666666666665 4.333333333333333) rotate(0 5 5)\" }))));\nexport default SvgCopy;\n", "import * as React from \"react\";\nconst SvgLoading = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", width: \"1em\", height: \"1em\", fill: \"#fff\", style: {}, ...props }, /* @__PURE__ */ React.createElement(\"rect\", { id: \"backgroundrect\", width: \"100%\", height: \"100%\", x: 0, y: 0, fill: \"none\", stroke: \"none\", style: {}, className: \"\" }), /* @__PURE__ */ React.createElement(\"g\", { className: \"currentLayer\", style: {} }, /* @__PURE__ */ React.createElement(\"title\", null, \"Layer 1\"), /* @__PURE__ */ React.createElement(\"circle\", { cx: 4, cy: 8, r: 1.926, fill: \"#333\", id: \"svg_1\", className: \"\" }, /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"r\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 2, repeatCount: \"indefinite\", to: 2, values: \"2;1.2;2\" }), /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"fill-opacity\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 1, repeatCount: \"indefinite\", to: 1, values: \"1;.5;1\" })), /* @__PURE__ */ React.createElement(\"circle\", { cx: 8, cy: 8, r: 1.2736, fill: \"#333\", fillOpacity: 0.3, id: \"svg_2\", className: \"\" }, /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"r\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 1.2, repeatCount: \"indefinite\", to: 1.2, values: \"1.2;2;1.2\" }), /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"fill-opacity\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 0.5, repeatCount: \"indefinite\", to: 0.5, values: \".5;1;.5\" })), /* @__PURE__ */ React.createElement(\"circle\", { cx: 12, cy: 8, r: 1.926, fill: \"#333\", id: \"svg_3\", className: \"\" }, /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"r\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 2, repeatCount: \"indefinite\", to: 2, values: \"2;1.2;2\" }), /* @__PURE__ */ React.createElement(\"animate\", { attributeName: \"fill-opacity\", begin: \"0s\", calcMode: \"linear\", dur: \"0.8s\", from: 1, repeatCount: \"indefinite\", to: 1, values: \"1;.5;1\" }))));\nexport default SvgLoading;\n", "import * as React from \"react\";\nconst SvgPrompt = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0 8 8)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })), /* @__PURE__ */ React.createElement(\"g\", { mask: \"url(#bg-mask-0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u5206\\\\u7EC4 1\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M1.36683 1.36683L2.77683 2.77683 M4.66667 0L4.66667 2 M4.66667 2L4.66667 0 M7.9623 1.36683L6.5523 2.77683 M6.5523 2.77683L7.9623 1.36683 M9.33333 4.66667L7.33333 4.66667 M7.33333 4.66667L9.33333 4.66667 M7.9623 7.9623L6.5523 6.5523 M6.5523 6.5523L7.9623 7.9623 M4.66667 9.33333L4.66667 7.33333 M4.66667 7.33333L4.66667 9.33333 M1.36683 7.9623L2.77683 6.5523 M2.77683 6.5523L1.36683 7.9623 M0 4.66667L2 4.66667 M2 4.66667L0 4.66667\", transform: \"translate(5.333333333333333 1.3333333333333333) rotate(0 4.666666666666666 4.666666666666666)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 9\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M8.01,0L0,8.01\", transform: \"translate(1.847983333333333 6.1381) rotate(0 4.006941666666666 4.006933333333333)\" }))));\nexport default SvgPrompt;\n", "import * as React from \"react\";\nconst SvgBreak = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"g\", { opacity: 1 }, /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0) translate(1.0001220703125 2) rotate(0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 1\", style: {\n  fill: \"#333\",\n  opacity: 1\n}, d: \"M13.275,-0.27515c0.261,0.26101 0.3915,0.57606 0.3915,0.94515v10.66c0,0.36907 -0.1305,0.68413 -0.3915,0.9452c-0.261,0.261 -0.57603,0.3915 -0.9451,0.3915h-10.66002c-0.36909,0 -0.68415,-0.1305 -0.94516,-0.3915c-0.26101,-0.26107 -0.39151,-0.57613 -0.39151,-0.9452v-10.66c0,-0.3691 0.1305,-0.68415 0.39151,-0.94515c0.26101,-0.26101 0.57606,-0.39151 0.94516,-0.39151h10.66002c0.36907,0 0.6841,0.1305 0.9451,0.39151zM1.66655,11.33c0,0.0022 0.00111,0.0033 0.00333,0.0033h10.66002c0.0022,0 0.0033,-0.0011 0.0033,-0.0033v-10.66c0,-0.00222 -0.0011,-0.00333 -0.0033,-0.00333l-10.66002,0c-0.00222,0 -0.00333,0.00111 -0.00333,0.00333z\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 2\", style: {\n  fill: \"#333\",\n  opacity: 1\n}, d: \"M9.76327,7.50715c-0.02999,0.02563 -0.06201,0.04842 -0.09604,0.06837c-0.03403,0.01995 -0.06956,0.03674 -0.10658,0.05039c-0.03702,0.01364 -0.07495,0.02391 -0.11379,0.03082c-0.03885,0.00691 -0.07799,0.01035 -0.11744,0.0103c-0.03945,-0.00004 -0.07859,-0.00356 -0.11742,-0.01055c-0.03883,-0.00699 -0.07674,-0.01734 -0.11373,-0.03106c-0.03699,-0.01372 -0.07248,-0.03059 -0.10647,-0.05061c-0.03399,-0.02002 -0.06596,-0.04288 -0.0959,-0.06858l-1.89578,-1.62728l-1.89578,1.62728c-0.02993,0.0257 -0.0619,0.04856 -0.09589,0.06858c-0.03399,0.02002 -0.06949,0.03689 -0.10648,0.05061c-0.03699,0.01372 -0.07489,0.02407 -0.11372,0.03106c-0.03883,0.00699 -0.07797,0.01051 -0.11742,0.01055c-0.03945,0.00005 -0.0786,-0.00339 -0.11744,-0.0103c-0.03885,-0.00691 -0.07678,-0.01718 -0.11379,-0.03082c-0.03702,-0.01365 -0.07255,-0.03044 -0.10658,-0.05039c-0.03404,-0.01995 -0.06605,-0.04274 -0.09604,-0.06837l-1.90593,-1.629l-1.89671,1.62808c-0.06708,0.05758 -0.14263,0.10013 -0.22664,0.12766c-0.08401,0.02753 -0.17009,0.03793 -0.25824,0.03121c-0.08815,-0.00671 -0.17166,-0.03004 -0.25053,-0.06998c-0.07887,-0.03994 -0.14709,-0.09345 -0.20467,-0.16054c-0.02851,-0.03321 -0.05351,-0.06889 -0.07499,-0.10703c-0.02148,-0.03814 -0.03904,-0.07801 -0.05267,-0.11961c-0.01363,-0.04159 -0.02307,-0.08412 -0.02832,-0.12758c-0.00525,-0.04346 -0.00622,-0.08701 -0.00289,-0.13066c0.00333,-0.04365 0.01088,-0.08655 0.02266,-0.12871c0.01178,-0.04216 0.02755,-0.08277 0.04733,-0.12182c0.01978,-0.03905 0.04317,-0.07579 0.07019,-0.11024c0.02701,-0.03444 0.05713,-0.06592 0.09035,-0.09443l2.32999,-2c0.02994,-0.02569 0.06191,-0.04855 0.0959,-0.06857c0.03399,-0.02003 0.06948,-0.0369 0.10647,-0.05062c0.03699,-0.01372 0.0749,-0.02407 0.11373,-0.03106c0.03883,-0.00699 0.07797,-0.01051 0.11742,-0.01055c0.03945,-0.00004 0.0786,0.00339 0.11744,0.0103c0.03884,0.00691 0.07677,0.01718 0.11379,0.03082c0.03702,0.01365 0.07255,0.03044 0.10658,0.05039c0.03404,0.01995 0.06605,0.04274 0.09604,0.06837l1.90592,1.629l1.89671,-1.62808c0.02998,-0.02573 0.062,-0.04862 0.09605,-0.06866c0.03405,-0.02005 0.0696,-0.03693 0.10665,-0.05065c0.03705,-0.01372 0.07503,-0.02407 0.11392,-0.03104c0.03889,-0.00697 0.07809,-0.01045 0.1176,-0.01045c0.03951,0 0.07872,0.00348 0.11761,0.01045c0.03889,0.00697 0.07686,0.01732 0.11391,0.03104c0.03705,0.01372 0.0726,0.0306 0.10665,0.05065c0.03405,0.02004 0.06607,0.04293 0.09605,0.06866l1.89671,1.62808l1.90595,-1.629c0.03,-0.02563 0.062,-0.04842 0.096,-0.06837c0.03407,-0.01995 0.0696,-0.03674 0.1066,-0.05038c0.037,-0.01365 0.07493,-0.02392 0.1138,-0.03083c0.03887,-0.00691 0.078,-0.01034 0.1174,-0.0103c0.03947,0.00004 0.0786,0.00356 0.1174,0.01055c0.03887,0.00699 0.0768,0.01734 0.1138,0.03106c0.037,0.01372 0.07247,0.03059 0.1064,0.05062c0.034,0.02002 0.06597,0.04288 0.0959,0.06857l2.33,2c0.06713,0.05758 0.12067,0.12581 0.1606,0.20468c0.03993,0.07887 0.06327,0.16237 0.07,0.25052c0.00667,0.08815 -0.00377,0.17424 -0.0313,0.25825c-0.02747,0.08401 -0.07,0.15955 -0.1276,0.22663c-0.02853,0.03322 -0.06,0.06334 -0.0944,0.09035c-0.03447,0.02701 -0.07123,0.05041 -0.1103,0.07019c-0.03907,0.01977 -0.07967,0.03555 -0.1218,0.04733c-0.04213,0.01177 -0.08503,0.01932 -0.1287,0.02265c-0.04367,0.00333 -0.08723,0.00236 -0.1307,-0.00289c-0.04347,-0.00525 -0.086,-0.01469 -0.1276,-0.02832c-0.0416,-0.01363 -0.08147,-0.03118 -0.1196,-0.05267c-0.03813,-0.02148 -0.0738,-0.04648 -0.107,-0.07499l-1.8967,-1.62808z\" })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0) translate(0 0) rotate(0)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })))), /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })));\nexport default SvgBreak;\n", "import * as React from \"react\";\nconst SvgClear = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0 8 8)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })), /* @__PURE__ */ React.createElement(\"g\", { mask: \"url(#bg-mask-0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 1\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M1,9.67L9.67,9.67L10.67,0L0,0L1,9.67Z\", transform: \"translate(2.6666666666666665 5) rotate(0 5.333333333333333 4.833333333333333)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 2\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,0L0,3.33\", transform: \"translate(6.667333333333333 8.334133333333334) rotate(0 0 1.6666999999999998)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 3\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,0L0,3.33\", transform: \"translate(9.334133333333334 8.333166666666667) rotate(0 0 1.666283333333333)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 4\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M0,4L5.44,0L8,4\", transform: \"translate(4 1) rotate(0 4 2)\" }))));\nexport default SvgClear;\n", "import * as React from \"react\";\nconst SvgExcel = (props) => /* @__PURE__ */ React.createElement(\"svg\", { t: 1734508841547, className: \"icon\", viewBox: \"0 0 1024 1024\", xmlns: \"http://www.w3.org/2000/svg\", \"p-id\": 5152, xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", ...props }, /* @__PURE__ */ React.createElement(\"path\", { d: \"M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z\", fill: \"#45B058\", \"p-id\": 5153 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M374.4 862.4c-3.2 0-6.4-1.6-8-3.2l-59.2-80-60.8 80c-1.6 1.6-4.8 3.2-8 3.2-6.4 0-11.2-4.8-11.2-11.2 0-1.6 0-4.8 1.6-6.4l62.4-81.6-57.6-78.4c-1.6-1.6-3.2-3.2-3.2-6.4 0-4.8 4.8-11.2 11.2-11.2 4.8 0 8 1.6 9.6 4.8l56 73.6 54.4-73.6c1.6-3.2 4.8-4.8 8-4.8 6.4 0 12.8 4.8 12.8 11.2 0 3.2-1.6 4.8-1.6 6.4l-59.2 76.8 62.4 83.2c1.6 1.6 3.2 4.8 3.2 6.4 0 6.4-6.4 11.2-12.8 11.2z m160-1.6H448c-9.6 0-17.6-8-17.6-17.6V678.4c0-6.4 4.8-11.2 12.8-11.2 6.4 0 11.2 4.8 11.2 11.2v161.6h80c6.4 0 11.2 4.8 11.2 9.6 0 6.4-4.8 11.2-11.2 11.2z m112 3.2c-28.8 0-51.2-9.6-67.2-24-3.2-1.6-3.2-4.8-3.2-8 0-6.4 3.2-12.8 11.2-12.8 1.6 0 4.8 1.6 6.4 3.2 12.8 11.2 32 20.8 54.4 20.8 33.6 0 44.8-19.2 44.8-33.6 0-49.6-113.6-22.4-113.6-89.6 0-32 27.2-54.4 65.6-54.4 24 0 46.4 8 60.8 20.8 3.2 1.6 4.8 4.8 4.8 8 0 6.4-4.8 12.8-11.2 12.8-1.6 0-4.8-1.6-6.4-3.2-14.4-11.2-32-16-49.6-16-24 0-40 11.2-40 30.4 0 43.2 113.6 17.6 113.6 89.6 0 27.2-19.2 56-70.4 56z\", fill: \"#FFFFFF\", \"p-id\": 5154 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M960 326.4v16H755.2s-102.4-20.8-99.2-108.8c0 0 3.2 92.8 96 92.8h208z\", fill: \"#349C42\", \"p-id\": 5155 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M656 0v233.6c0 25.6 19.2 92.8 99.2 92.8H960L656 0z\", fill: \"#FFFFFF\", \"p-id\": 5156 }));\nexport default SvgExcel;\n", "import * as React from \"react\";\nconst SvgPdf = (props) => /* @__PURE__ */ React.createElement(\"svg\", { t: 1734508866521, className: \"icon\", viewBox: \"0 0 1024 1024\", xmlns: \"http://www.w3.org/2000/svg\", \"p-id\": 6161, xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", ...props }, /* @__PURE__ */ React.createElement(\"path\", { d: \"M145.6 0C100.8 0 64 36.8 64 81.6v860.8C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z\", fill: \"#8C181A\", \"p-id\": 6162 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M960 326.4v16H755.2s-100.8-20.8-97.6-107.2c0 0 3.2 91.2 96 91.2H960z\", fill: \"#6B0D12\", \"p-id\": 6163 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M657.6 0v233.6c0 27.2 17.6 92.8 97.6 92.8H960L657.6 0z\", fill: \"#FFFFFF\", \"p-id\": 6164 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M302.4 784h-52.8v65.6c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h59.2c38.4 0 60.8 27.2 60.8 57.6 0 32-22.4 57.6-60.8 57.6z m-1.6-94.4h-51.2v73.6h51.2c22.4 0 38.4-14.4 38.4-36.8s-16-36.8-38.4-36.8z m166.4 171.2h-48c-9.6 0-17.6-8-17.6-17.6v-156.8c0-9.6 8-17.6 17.6-17.6h48c59.2 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c46.4 0 73.6-33.6 73.6-75.2 1.6-40-25.6-73.6-73.6-73.6z m260.8 0h-92.8V752h91.2c6.4 0 9.6 4.8 9.6 11.2s-4.8 9.6-9.6 9.6h-91.2v76.8c0 6.4-4.8 11.2-12.8 11.2-6.4 0-11.2-4.8-11.2-11.2V686.4c0-9.6 8-17.6 17.6-17.6h99.2c6.4 0 9.6 4.8 9.6 11.2 1.6 4.8-3.2 9.6-9.6 9.6z\", fill: \"#FFFFFF\", \"p-id\": 6165 }));\nexport default SvgPdf;\n", "import * as React from \"react\";\nconst SvgWord = (props) => /* @__PURE__ */ React.createElement(\"svg\", { t: 1734509033364, className: \"icon\", viewBox: \"0 0 1024 1024\", xmlns: \"http://www.w3.org/2000/svg\", \"p-id\": 2552, xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", ...props }, /* @__PURE__ */ React.createElement(\"path\", { d: \"M145.6 0C100.8 0 64 35.2 64 80v862.4C64 987.2 100.8 1024 145.6 1024h732.8c44.8 0 81.6-36.8 81.6-81.6V324.8L657.6 0h-512z\", fill: \"#14A9DA\", \"p-id\": 2553 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M960 326.4v16H755.2s-100.8-20.8-99.2-108.8c0 0 4.8 92.8 97.6 92.8H960z\", fill: \"#0F93D0\", \"p-id\": 2554 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M657.6 0v233.6c0 25.6 17.6 92.8 97.6 92.8H960L657.6 0z\", fill: \"#FFFFFF\", \"p-id\": 2555 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M291.2 862.4h-48c-9.6 0-17.6-8-17.6-17.6v-158.4c0-9.6 8-16 17.6-16h48c60.8 0 99.2 41.6 99.2 96s-38.4 96-99.2 96z m0-171.2h-41.6v148.8h41.6c48 0 75.2-33.6 75.2-73.6 0-41.6-27.2-75.2-75.2-75.2z m232 174.4c-57.6 0-96-43.2-96-99.2s38.4-99.2 96-99.2c56 0 94.4 41.6 94.4 99.2 0 56-38.4 99.2-94.4 99.2z m0-177.6c-43.2 0-70.4 33.6-70.4 78.4 0 44.8 27.2 76.8 70.4 76.8 41.6 0 70.4-32 70.4-76.8S564.8 688 523.2 688z m294.4 6.4c1.6 1.6 3.2 4.8 3.2 8 0 6.4-4.8 11.2-11.2 11.2-3.2 0-6.4-1.6-8-3.2-11.2-14.4-30.4-22.4-48-22.4-41.6 0-73.6 32-73.6 78.4 0 44.8 32 76.8 73.6 76.8 17.6 0 35.2-6.4 48-20.8 1.6-3.2 4.8-4.8 8-4.8 6.4 0 11.2 6.4 11.2 12.8 0 3.2-1.6 4.8-3.2 8-14.4 16-35.2 27.2-64 27.2-56 0-99.2-40-99.2-99.2s43.2-99.2 99.2-99.2c28.8 0 49.6 11.2 64 27.2z\", fill: \"#FFFFFF\", \"p-id\": 2556 }));\nexport default SvgWord;\n", "import * as React from \"react\";\nconst SvgImage = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", fill: \"none\", height: \"1em\", width: \"1em\", xmlSpace: \"preserve\", style: {}, ...props }, /* @__PURE__ */ React.createElement(\"rect\", { id: \"backgroundrect\", width: \"100%\", height: \"100%\", x: 0, y: 0, fill: \"none\", stroke: \"none\" }), /* @__PURE__ */ React.createElement(\"g\", { className: \"currentLayer\", style: {} }, /* @__PURE__ */ React.createElement(\"title\", null, \"Layer 1\"), /* @__PURE__ */ React.createElement(\"g\", { id: \"svg_1\", className: \"\", fill: \"#333\", fillOpacity: 1 }, /* @__PURE__ */ React.createElement(\"polygon\", { points: \"2.4690866470336914,2.4690725803375244 4.447190761566162,2.4690725803375244 4.447190761566162,1.6882386207580566 1.6882381439208984,1.6882386207580566 1.6882381439208984,4.44719123840332 2.4690866470336914,4.44719123840332 \", id: \"svg_2\", fill: \"#333\", fillOpacity: 1 }), /* @__PURE__ */ React.createElement(\"polygon\", { points: \"11.552804470062256,1.6882386207580566 11.552804470062256,2.4690725803375244 13.530910968780518,2.4690725803375244 13.530910968780518,4.44719123840332 14.311760425567627,4.44719123840332 14.311760425567627,1.6882386207580566 \", id: \"svg_3\", fill: \"#333\", fillOpacity: 1 }), /* @__PURE__ */ React.createElement(\"polygon\", { points: \"13.530910968780518,13.530919075012207 11.552804470062256,13.530919075012207 11.552804470062256,14.311760902404785 14.311760425567627,14.311760902404785 14.311760425567627,11.552801132202148 13.530910968780518,11.552801132202148 \", id: \"svg_4\", fill: \"#333\", fillOpacity: 1 }), /* @__PURE__ */ React.createElement(\"polygon\", { points: \"2.4690866470336914,11.552801132202148 1.6882381439208984,11.552801132202148 1.6882381439208984,14.311760902404785 4.447190761566162,14.311760902404785 4.447190761566162,13.530919075012207 2.4690866470336914,13.530919075012207 \", id: \"svg_5\", fill: \"#333\", fillOpacity: 1 }), /* @__PURE__ */ React.createElement(\"path\", { d: \"M8.830417847409231,6.243117030680995 c0.68169614081525,0 1.2363241834494423,-0.5546280426341942 1.2363241834494423,-1.2363241834494423 S9.51214001610201,3.770468663782117 8.830417847409231,3.770468663782117 s-1.2363241834494423,0.5546280426341942 -1.2363241834494423,1.2363241834494423 S8.14872170659398,6.243117030680995 8.830417847409231,6.243117030680995 z\", id: \"svg_6\", fill: \"#333\", fillOpacity: 1 }), /* @__PURE__ */ React.createElement(\"polygon\", { points: \"3.7704806327819824,12.229532241821289 12.229516506195068,12.229532241821289 12.229516506195068,9.709510803222656 10.70320463180542,8.099010467529297 8.852166652679443,9.175727844238281 6.275332450866699,7.334256172180176 3.7704806327819824,9.977211952209473 \", id: \"svg_7\", fill: \"#333\", fillOpacity: 1 }))));\nexport default SvgImage;\n", "import * as React from \"react\";\nconst SvgBottom = (props) => /* @__PURE__ */ React.createElement(\"svg\", { xmlns: \"http://www.w3.org/2000/svg\", xmlnsXlink: \"http://www.w3.org/1999/xlink\", width: \"1em\", height: \"1em\", fill: \"none\", viewBox: \"0 0 16 16\", ...props }, /* @__PURE__ */ React.createElement(\"defs\", null, /* @__PURE__ */ React.createElement(\"rect\", { id: \"path_0\", width: 16, height: 16, x: 0, y: 0 })), /* @__PURE__ */ React.createElement(\"g\", { opacity: 1, transform: \"translate(0 0) rotate(0 8 8)\" }, /* @__PURE__ */ React.createElement(\"mask\", { id: \"bg-mask-0\", fill: \"#fff\" }, /* @__PURE__ */ React.createElement(\"use\", { xlinkHref: \"#path_0\" })), /* @__PURE__ */ React.createElement(\"g\", { mask: \"url(#bg-mask-0)\" }, /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 1\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M8,0L4,4L0,0\", transform: \"translate(4 4) rotate(0 4 2)\" }), /* @__PURE__ */ React.createElement(\"path\", { id: \"\\\\u8DEF\\\\u5F84 2\", style: {\n  stroke: \"#333\",\n  strokeWidth: 1.3333333333333333,\n  strokeOpacity: 1,\n  strokeDasharray: \"0 0\"\n}, d: \"M8,0L4,4L0,0\", transform: \"translate(4 8) rotate(0 4 2)\" }))));\nexport default SvgBottom;\n", "import {\r\n  CACHE_URL_PREFIX,\r\n  UPLOAD_URL,\r\n  REQUEST_TIMEOUT_MS,\r\n} from \"@/common/constant\";\r\nimport { RequestMessage } from \"@/common/utils/utils\";\r\nimport Locale from \"@/locales\";\r\nimport {\r\n  EventStreamContentType,\r\n  fetchEventSource,\r\n} from \"@fortaine/fetch-event-source\";\r\nimport { prettyObject } from \"./format\";\r\nimport { fetch as tauriFetch } from \"./stream\";\r\n\r\nexport function compressImage(file: Blob, maxSize: number): Promise<string> {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader();\r\n    reader.onload = (readerEvent: any) => {\r\n      const image = new Image();\r\n      image.onload = () => {\r\n        const canvas = document.createElement(\"canvas\");\r\n        const ctx = canvas.getContext(\"2d\");\r\n        let width = image.width;\r\n        let height = image.height;\r\n        let quality = 0.9;\r\n        let dataUrl;\r\n\r\n        do {\r\n          canvas.width = width;\r\n          canvas.height = height;\r\n          ctx?.clearRect(0, 0, canvas.width, canvas.height);\r\n          ctx?.drawImage(image, 0, 0, width, height);\r\n          dataUrl = canvas.toDataURL(\"image/jpeg\", quality);\r\n\r\n          if (dataUrl.length < maxSize) break;\r\n\r\n          if (quality > 0.5) {\r\n            // Prioritize quality reduction\r\n            quality -= 0.1;\r\n          } else {\r\n            // Then reduce the size\r\n            width *= 0.9;\r\n            height *= 0.9;\r\n          }\r\n        } while (dataUrl.length > maxSize);\r\n\r\n        resolve(dataUrl);\r\n      };\r\n      image.onerror = reject;\r\n      image.src = readerEvent.target.result;\r\n    };\r\n    reader.onerror = reject;\r\n\r\n    if (file.type.includes(\"heic\")) {\r\n      try {\r\n        const heic2any = require(\"heic2any\");\r\n        heic2any({ blob: file, toType: \"image/jpeg\" })\r\n          .then((blob: Blob) => {\r\n            reader.readAsDataURL(blob);\r\n          })\r\n          .catch((e: any) => {\r\n            reject(e);\r\n          });\r\n      } catch (e) {\r\n        reject(e);\r\n      }\r\n    }\r\n\r\n    reader.readAsDataURL(file);\r\n  });\r\n}\r\n\r\nexport async function preProcessImageContent(\r\n  content: RequestMessage[\"content\"],\r\n) {\r\n  if (typeof content === \"string\") {\r\n    return content;\r\n  }\r\n  const result = [];\r\n  for (const part of content) {\r\n    if (part?.type == \"image_url\" && part?.image_url?.url) {\r\n      try {\r\n        const url = await cacheImageToBase64Image(part?.image_url?.url);\r\n        result.push({ type: part.type, image_url: { url } });\r\n      } catch (error) {\r\n        console.error(\"Error processing image URL:\", error);\r\n      }\r\n    } else {\r\n      result.push({ ...part });\r\n    }\r\n  }\r\n  return result;\r\n}\r\n\r\nconst imageCaches: Record<string, string> = {};\r\nexport function cacheImageToBase64Image(imageUrl: string) {\r\n  if (imageUrl.includes(CACHE_URL_PREFIX)) {\r\n    if (!imageCaches[imageUrl]) {\r\n      const reader = new FileReader();\r\n      return fetch(imageUrl, {\r\n        method: \"GET\",\r\n        mode: \"cors\",\r\n        credentials: \"include\",\r\n      })\r\n        .then((res) => res.blob())\r\n        .then(\r\n          async (blob) =>\r\n            (imageCaches[imageUrl] = await compressImage(blob, 256 * 1024)),\r\n        ); // compressImage\r\n    }\r\n    return Promise.resolve(imageCaches[imageUrl]);\r\n  }\r\n  return Promise.resolve(imageUrl);\r\n}\r\n\r\nexport function base64Image2Blob(base64Data: string, contentType: string) {\r\n  const byteCharacters = atob(base64Data);\r\n  const byteNumbers = new Array(byteCharacters.length);\r\n  for (let i = 0; i < byteCharacters.length; i++) {\r\n    byteNumbers[i] = byteCharacters.charCodeAt(i);\r\n  }\r\n  const byteArray = new Uint8Array(byteNumbers);\r\n  return new Blob([byteArray], { type: contentType });\r\n}\r\n\r\nexport function uploadImage(file: Blob): Promise<string> {\r\n  // if (!window._SW_ENABLED) {\r\n  //   // if serviceWorker register error, using compressImage\r\n  //   return compressImage(file, 256 * 1024);\r\n  // }\r\n  const body = new FormData();\r\n  body.append(\"file\", file);\r\n  return fetch(UPLOAD_URL, {\r\n    method: \"post\",\r\n    body,\r\n    mode: \"cors\",\r\n    credentials: \"include\",\r\n  })\r\n    .then((res) => res.json())\r\n    .then((res) => {\r\n      // console.log(\"res\", res);\r\n      if (res?.code == 0 && res?.data) {\r\n        return res?.data;\r\n      }\r\n      throw Error(`upload Error: ${res?.msg}`);\r\n    });\r\n}\r\n\r\nexport function removeImage(imageUrl: string) {\r\n  return fetch(imageUrl, {\r\n    method: \"DELETE\",\r\n    mode: \"cors\",\r\n    credentials: \"include\",\r\n  });\r\n}\r\n\r\nexport function stream(\r\n  chatPath: string,\r\n  requestPayload: any,\r\n  headers: any,\r\n  tools: any[],\r\n  funcs: Record<string, Function>,\r\n  controller: AbortController,\r\n  parseSSE: (text: string, runTools: any[]) => string | undefined,\r\n  processToolMessage: (\r\n    requestPayload: any,\r\n    toolCallMessage: any,\r\n    toolCallResult: any[],\r\n  ) => void,\r\n  options: any,\r\n) {\r\n  let responseText = \"\";\r\n  let remainText = \"\";\r\n  let finished = false;\r\n  let running = false;\r\n  const runTools: any[] = [];\r\n  let responseRes: Response;\r\n\r\n  // animate response to make it looks smooth\r\n  function animateResponseText() {\r\n    if (finished || controller.signal.aborted) {\r\n      responseText += remainText;\r\n      console.log(\"[Response Animation] finished\");\r\n      if (responseText?.length === 0) {\r\n        options.onError?.(new Error(\"empty response from server\"));\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (remainText.length > 0) {\r\n      const fetchCount = Math.max(1, Math.round(remainText.length / 60));\r\n      const fetchText = remainText.slice(0, fetchCount);\r\n      responseText += fetchText;\r\n      remainText = remainText.slice(fetchCount);\r\n      options.onUpdate?.(responseText, fetchText);\r\n    }\r\n\r\n    requestAnimationFrame(animateResponseText);\r\n  }\r\n\r\n  // start animaion\r\n  animateResponseText();\r\n\r\n  const finish = () => {\r\n    if (!finished) {\r\n      if (!running && runTools.length > 0) {\r\n        const toolCallMessage = {\r\n          role: \"assistant\",\r\n          tool_calls: [...runTools],\r\n        };\r\n        running = true;\r\n        runTools.splice(0, runTools.length); // empty runTools\r\n        return Promise.all(\r\n          toolCallMessage.tool_calls.map((tool) => {\r\n            options?.onBeforeTool?.(tool);\r\n            return Promise.resolve(\r\n              // @ts-ignore\r\n              funcs[tool.function.name](\r\n                // @ts-ignore\r\n                tool?.function?.arguments\r\n                  ? JSON.parse(tool?.function?.arguments)\r\n                  : {},\r\n              ),\r\n            )\r\n              .then((res) => {\r\n                let content = res.data || res?.statusText;\r\n                // hotfix #5614\r\n                content =\r\n                  typeof content === \"string\"\r\n                    ? content\r\n                    : JSON.stringify(content);\r\n                if (res.status >= 300) {\r\n                  return Promise.reject(content);\r\n                }\r\n                return content;\r\n              })\r\n              .then((content) => {\r\n                options?.onAfterTool?.({\r\n                  ...tool,\r\n                  content,\r\n                  isError: false,\r\n                });\r\n                return content;\r\n              })\r\n              .catch((e) => {\r\n                options?.onAfterTool?.({\r\n                  ...tool,\r\n                  isError: true,\r\n                  errorMsg: e.toString(),\r\n                });\r\n                return e.toString();\r\n              })\r\n              .then((content) => ({\r\n                name: tool.function.name,\r\n                role: \"tool\",\r\n                content,\r\n                tool_call_id: tool.id,\r\n              }));\r\n          }),\r\n        ).then((toolCallResult) => {\r\n          processToolMessage(requestPayload, toolCallMessage, toolCallResult);\r\n          setTimeout(() => {\r\n            // call again\r\n            console.debug(\"[ChatAPI] restart\");\r\n            running = false;\r\n            chatApi(chatPath, headers, requestPayload, tools); // call fetchEventSource\r\n          }, 60);\r\n        });\r\n        return;\r\n      }\r\n      if (running) {\r\n        return;\r\n      }\r\n      console.debug(\"[ChatAPI] end\");\r\n      finished = true;\r\n      options.onFinish(responseText + remainText, responseRes); // 将res传递给onFinish\r\n    }\r\n  };\r\n\r\n  controller.signal.onabort = finish;\r\n\r\n  function chatApi(\r\n    chatPath: string,\r\n    headers: any,\r\n    requestPayload: any,\r\n    tools: any,\r\n  ) {\r\n    const chatPayload = {\r\n      method: \"POST\",\r\n      body: JSON.stringify({\r\n        ...requestPayload,\r\n        tools: tools && tools.length ? tools : undefined,\r\n      }),\r\n      signal: controller.signal,\r\n      headers,\r\n    };\r\n    const requestTimeoutId = setTimeout(\r\n      () => controller.abort(),\r\n      REQUEST_TIMEOUT_MS,\r\n    );\r\n    fetchEventSource(chatPath, {\r\n      fetch: tauriFetch as any,\r\n      ...chatPayload,\r\n      async onopen(res) {\r\n        clearTimeout(requestTimeoutId);\r\n        const contentType = res.headers.get(\"content-type\");\r\n        console.log(\"[Request] response content type: \", contentType);\r\n        responseRes = res;\r\n\r\n        if (contentType?.startsWith(\"text/plain\")) {\r\n          responseText = await res.clone().text();\r\n          return finish();\r\n        }\r\n\r\n        if (\r\n          !res.ok ||\r\n          !res.headers\r\n            .get(\"content-type\")\r\n            ?.startsWith(EventStreamContentType) ||\r\n          res.status !== 200\r\n        ) {\r\n          const responseTexts = [responseText];\r\n          let extraInfo = await res.clone().text();\r\n          try {\r\n            const resJson = await res.clone().json();\r\n            extraInfo = prettyObject(resJson);\r\n          } catch {}\r\n\r\n          if (res.status === 401) {\r\n            responseTexts.push(Locale.Error.Unauthorized);\r\n          }\r\n\r\n          if (extraInfo) {\r\n            responseTexts.push(extraInfo);\r\n          }\r\n\r\n          responseText = responseTexts.join(\"\\n\\n\");\r\n\r\n          return finish();\r\n        }\r\n      },\r\n      onmessage(msg) {\r\n        if (msg.data === \"[DONE]\" || finished) {\r\n          return finish();\r\n        }\r\n        const text = msg.data;\r\n        try {\r\n          const chunk = parseSSE(msg.data, runTools);\r\n          if (chunk) {\r\n            remainText += chunk;\r\n          }\r\n        } catch (e) {\r\n          console.error(\"[Request] parse error\", text, msg, e);\r\n        }\r\n      },\r\n      onclose() {\r\n        finish();\r\n      },\r\n      onerror(e) {\r\n        options?.onError?.(e);\r\n        throw e;\r\n      },\r\n      openWhenHidden: true,\r\n    });\r\n  }\r\n  console.debug(\"[ChatAPI] start\");\r\n  chatApi(chatPath, headers, requestPayload, tools); // call fetchEventSource\r\n}\r\n", "import EmojiPicker, {\r\n  Emoji,\r\n  EmojiStyle,\r\n  Theme as EmojiTheme,\r\n} from \"emoji-picker-react\";\r\n\r\n// import ABuIcon from '@/asserts/icons/abu.png'\r\nimport ABuIcon from '@/asserts/icons/aBu.svg'\r\n\r\nexport function getEmojiUrl(unified: string, style: EmojiStyle) {\r\n  // Whoever owns this Content Delivery Network (CDN), I am using your CDN to serve emojis\r\n  // Old CDN broken, so I had to switch to this one\r\n  // Author: https://github.com/H0llyW00dzZ\r\n  return `https://fastly.jsdelivr.net/npm/emoji-datasource-apple/img/${style}/64/${unified}.png`;\r\n}\r\n\r\nexport function AvatarPicker(props: {\r\n  onEmojiClick: (emojiId: string) => void;\r\n}) {\r\n  return (\r\n    <EmojiPicker\r\n      width={\"100%\"}\r\n      lazyLoadEmojis\r\n      theme={EmojiTheme.AUTO}\r\n      getEmojiUrl={getEmojiUrl}\r\n      onEmojiClick={(e) => {\r\n        props.onEmojiClick(e.unified);\r\n      }}\r\n    />\r\n  );\r\n}\r\n\r\nexport function Avatar(props: { model?: any; avatar?: string }) {\r\n  if (props.model) {\r\n    return (\r\n      <div className=\"no-dark\">\r\n        <ABuIcon className=\"user-avatar\" />\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"user-avatar\">\r\n      {props.avatar && <EmojiAvatar avatar={props.avatar} />}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function EmojiAvatar(props: { avatar: string; size?: number }) {\r\n  return (\r\n    <Emoji\r\n      unified={props.avatar}\r\n      size={props.size ?? 18}\r\n      getEmojiUrl={getEmojiUrl}\r\n    />\r\n  );\r\n}\r\n", "import { useEffect } from \"react\";\r\nimport { useSearchParams } from \"react-router-dom\";\r\nimport Locale from \"@/locales\";\r\n\r\ntype Command = (param: string) => void;\r\ninterface Commands {\r\n  fill?: Command;\r\n  submit?: Command;\r\n  mask?: Command;\r\n  code?: Command;\r\n  settings?: Command;\r\n}\r\n\r\nexport function useCommand(commands: Commands = {}) {\r\n  const [searchParams, setSearchParams] = useSearchParams();\r\n\r\n  useEffect(() => {\r\n    let shouldUpdate = false;\r\n    searchParams.forEach((param, name) => {\r\n      const commandName = name as keyof Commands;\r\n      if (typeof commands[commandName] === \"function\") {\r\n        commands[commandName]!(param);\r\n        searchParams.delete(name);\r\n        shouldUpdate = true;\r\n      }\r\n    });\r\n\r\n    if (shouldUpdate) {\r\n      setSearchParams(searchParams);\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [searchParams, commands]);\r\n}\r\n\r\ninterface ChatCommands {\r\n  new?: Command;\r\n  newm?: Command;\r\n  next?: Command;\r\n  prev?: Command;\r\n  clear?: Command;\r\n  fork?: Command;\r\n  del?: Command;\r\n}\r\n\r\n// Compatible with Chinese colon character \"：\"\r\nexport const ChatCommandPrefix = /^[:：]/;\r\n\r\nexport function useChatCommand(commands: ChatCommands = {}) {\r\n  function extract(userInput: string) {\r\n    const match = userInput.match(ChatCommandPrefix);\r\n    if (match) {\r\n      return userInput.slice(1) as keyof ChatCommands;\r\n    }\r\n    return userInput as keyof ChatCommands;\r\n  }\r\n\r\n  function search(userInput: string) {\r\n    const input = extract(userInput);\r\n    const desc = Locale.Chat.Commands;\r\n    return Object.keys(commands)\r\n      .filter((c) => c.startsWith(input))\r\n      .map((c) => ({\r\n        title: desc[c as keyof ChatCommands],\r\n        content: \":\" + c,\r\n      }));\r\n  }\r\n\r\n  function match(userInput: string) {\r\n    const command = extract(userInput);\r\n    const matched = typeof commands[command] === \"function\";\r\n\r\n    return {\r\n      matched,\r\n      invoke: () => matched && commands[command]!(userInput),\r\n    };\r\n  }\r\n\r\n  return { match, search };\r\n}\r\n", "import Fuse from \"fuse.js\";\r\nimport { nanoid } from \"nanoid\";\r\nimport { StoreKey } from \"@/common/constant\";\r\nimport { getLang } from \"@/locales\";\r\nimport { createPersistStore } from \"@/common/utils/store\";\r\n\r\nexport interface Prompt {\r\n  id: string;\r\n  isUser?: boolean;\r\n  title: string;\r\n  content: string;\r\n  createdAt: number;\r\n}\r\n\r\nexport const SearchService = {\r\n  ready: false,\r\n  builtinEngine: new Fuse<Prompt>([], { keys: [\"title\"] }),\r\n  userEngine: new Fuse<Prompt>([], { keys: [\"title\"] }),\r\n  count: {\r\n    builtin: 0,\r\n  },\r\n  allPrompts: [] as Prompt[],\r\n  builtinPrompts: [] as Prompt[],\r\n\r\n  init(builtinPrompts: Prompt[], userPrompts: Prompt[]) {\r\n    if (this.ready) {\r\n      return;\r\n    }\r\n    this.allPrompts = userPrompts.concat(builtinPrompts);\r\n    this.builtinPrompts = builtinPrompts.slice();\r\n    this.builtinEngine.setCollection(builtinPrompts);\r\n    this.userEngine.setCollection(userPrompts);\r\n    this.ready = true;\r\n  },\r\n\r\n  remove(id: string) {\r\n    this.userEngine.remove((doc) => doc.id === id);\r\n  },\r\n\r\n  add(prompt: Prompt) {\r\n    this.userEngine.add(prompt);\r\n  },\r\n\r\n  search(text: string) {\r\n    const userResults = this.userEngine.search(text);\r\n    const builtinResults = this.builtinEngine.search(text);\r\n    return userResults.concat(builtinResults).map((v) => v.item);\r\n  },\r\n};\r\n\r\nexport const usePromptStore = createPersistStore(\r\n  {\r\n    counter: 0,\r\n    prompts: {} as Record<string, Prompt>,\r\n  },\r\n\r\n  (set, get) => ({\r\n    add(prompt: Prompt) {\r\n      const prompts = get().prompts;\r\n      prompt.id = nanoid();\r\n      prompt.isUser = true;\r\n      prompt.createdAt = Date.now();\r\n      prompts[prompt.id] = prompt;\r\n\r\n      set(() => ({\r\n        prompts: prompts,\r\n      }));\r\n\r\n      return prompt.id!;\r\n    },\r\n\r\n    get(id: string) {\r\n      const targetPrompt = get().prompts[id];\r\n\r\n      if (!targetPrompt) {\r\n        return SearchService.builtinPrompts.find((v) => v.id === id);\r\n      }\r\n\r\n      return targetPrompt;\r\n    },\r\n\r\n    remove(id: string) {\r\n      const prompts = get().prompts;\r\n      delete prompts[id];\r\n\r\n      Object.entries(prompts).some(([key, prompt]) => {\r\n        if (prompt.id === id) {\r\n          delete prompts[key];\r\n          return true;\r\n        }\r\n        return false;\r\n      });\r\n\r\n      SearchService.remove(id);\r\n\r\n      set(() => ({\r\n        prompts,\r\n        counter: get().counter + 1,\r\n      }));\r\n    },\r\n\r\n    getUserPrompts() {\r\n      const userPrompts = Object.values(get().prompts ?? {});\r\n      userPrompts.sort((a, b) =>\r\n        b.id && a.id ? b.createdAt - a.createdAt : 0,\r\n      );\r\n      return userPrompts;\r\n    },\r\n\r\n    updatePrompt(id: string, updater: (prompt: Prompt) => void) {\r\n      const prompt = get().prompts[id] ?? {\r\n        title: \"\",\r\n        content: \"\",\r\n        id,\r\n      };\r\n\r\n      SearchService.remove(id);\r\n      updater(prompt);\r\n      const prompts = get().prompts;\r\n      prompts[id] = prompt;\r\n      set(() => ({ prompts }));\r\n      SearchService.add(prompt);\r\n    },\r\n\r\n    search(text: string) {\r\n      if (text.length === 0) {\r\n        // return all rompts\r\n        return this.getUserPrompts().concat(SearchService.builtinPrompts);\r\n      }\r\n      return SearchService.search(text) as Prompt[];\r\n    },\r\n  }),\r\n  {\r\n    name: StoreKey.Prompt,\r\n    version: 3,\r\n\r\n    migrate(state, version) {\r\n      const newState = JSON.parse(JSON.stringify(state)) as {\r\n        prompts: Record<string, Prompt>;\r\n      };\r\n\r\n      if (version < 3) {\r\n        Object.values(newState.prompts).forEach((p) => (p.id = nanoid()));\r\n      }\r\n\r\n      return newState as any;\r\n    },\r\n\r\n    onRehydrateStorage(state) {\r\n      // Skip store rehydration on server side\r\n      if (typeof window === \"undefined\") {\r\n        return;\r\n      }\r\n\r\n      const PROMPT_URL = \"./prompts.json\";\r\n\r\n      type PromptList = Array<[string, string]>;\r\n\r\n      fetch(PROMPT_URL)\r\n        .then((res) => res.json())\r\n        .then((res) => {\r\n          console.log(\"res\", res)\r\n          Object.keys(res)\r\n          let fetchPrompts = [res.en, res.tw, res.cn];\r\n          if (Object.keys(res).includes(getLang())) {\r\n            fetchPrompts = [res[getLang()]]\r\n          }\r\n          const builtinPrompts = fetchPrompts.map((promptList: PromptList) => {\r\n            return promptList.map(\r\n              ([title, content]) =>\r\n                ({\r\n                  id: nanoid(),\r\n                  title,\r\n                  content,\r\n                  createdAt: Date.now(),\r\n                }) as Prompt,\r\n            );\r\n          });\r\n\r\n          const userPrompts = usePromptStore.getState().getUserPrompts() ?? [];\r\n\r\n          const allPromptsForSearch = builtinPrompts\r\n            .reduce((pre, cur) => pre.concat(cur), [])\r\n            .filter((v) => !!v.title && !!v.content);\r\n          SearchService.count.builtin =\r\n            res.en.length + res.cn.length + res.tw.length;\r\n          SearchService.init(allPromptsForSearch, userPrompts);\r\n        });\r\n    },\r\n  },\r\n);\r\n", "import {\r\n  useEffect,\r\n  useState,\r\n  useRef,\r\n  useMemo,\r\n  forwardRef,\r\n  useImperativeHandle,\r\n} from \"react\";\r\nimport { nanoid } from \"nanoid\";\r\nimport styles from \"./artifacts.module.scss\";\r\n\r\ntype HTMLPreviewProps = {\r\n  code: string;\r\n  autoHeight?: boolean;\r\n  height?: number | string;\r\n  onLoad?: (title?: string) => void;\r\n};\r\n\r\nexport type HTMLPreviewHander = {\r\n  reload: () => void;\r\n};\r\n\r\nexport const HTMLPreview = forwardRef<HTMLPreviewHander, HTMLPreviewProps>(\r\n  function HTMLPreview(props, ref) {\r\n    const iframeRef = useRef<HTMLIFrameElement>(null);\r\n    const [frameId, setFrameId] = useState<string>(nanoid());\r\n    const [iframeHeight, setIframeHeight] = useState(600);\r\n    const [title, setTitle] = useState(\"\");\r\n    /*\r\n     * https://stackoverflow.com/questions/19739001/what-is-the-difference-between-srcdoc-and-src-datatext-html-in-an\r\n     * 1. using srcdoc\r\n     * 2. using src with dataurl:\r\n     *    easy to share\r\n     *    length limit (Data URIs cannot be larger than 32,768 characters.)\r\n     */\r\n\r\n    useEffect(() => {\r\n      const handleMessage = (e: any) => {\r\n        const { id, height, title } = e.data;\r\n        setTitle(title);\r\n        if (id == frameId) {\r\n          setIframeHeight(height);\r\n        }\r\n      };\r\n      window.addEventListener(\"message\", handleMessage);\r\n      return () => {\r\n        window.removeEventListener(\"message\", handleMessage);\r\n      };\r\n    }, [frameId]);\r\n\r\n    useImperativeHandle(ref, () => ({\r\n      reload: () => {\r\n        setFrameId(nanoid());\r\n      },\r\n    }));\r\n\r\n    const height = useMemo(() => {\r\n      if (!props.autoHeight) return props.height || 600;\r\n      if (typeof props.height === \"string\") {\r\n        return props.height;\r\n      }\r\n      const parentHeight = props.height || 600;\r\n      return iframeHeight + 40 > parentHeight\r\n        ? parentHeight\r\n        : iframeHeight + 40;\r\n    }, [props.autoHeight, props.height, iframeHeight]);\r\n\r\n    const srcDoc = useMemo(() => {\r\n      const script = `<script>window.addEventListener(\"DOMContentLoaded\", () => new ResizeObserver((entries) => parent.postMessage({id: '${frameId}', height: entries[0].target.clientHeight}, '*')).observe(document.body))</script>`;\r\n      if (props.code.includes(\"<!DOCTYPE html>\")) {\r\n        props.code.replace(\"<!DOCTYPE html>\", \"<!DOCTYPE html>\" + script);\r\n      }\r\n      return script + props.code;\r\n    }, [props.code, frameId]);\r\n\r\n    const handleOnLoad = () => {\r\n      if (props?.onLoad) {\r\n        props.onLoad(title);\r\n      }\r\n    };\r\n\r\n    return (\r\n      <iframe\r\n        className={styles[\"artifacts-iframe\"]}\r\n        key={frameId}\r\n        ref={iframeRef}\r\n        sandbox=\"allow-forms allow-modals allow-scripts\"\r\n        style={{ height }}\r\n        srcDoc={srcDoc}\r\n        onLoad={handleOnLoad}\r\n      />\r\n    );\r\n  },\r\n);\r\n", "import ReactMarkdown from \"react-markdown\";\r\nimport \"katex/dist/katex.min.css\";\r\nimport RemarkMath from \"remark-math\";\r\nimport RemarkBreaks from \"remark-breaks\";\r\nimport RehypeKatex from \"rehype-katex\";\r\nimport RemarkGfm from \"remark-gfm\";\r\nimport Rehype<PERSON>ighlight from \"rehype-highlight\";\r\nimport { useRef, useState, RefObject, useEffect, useMemo } from \"react\";\r\nimport { copyToClipboard, useWindowSize } from \"@/common/utils/utils\";\r\nimport mermaid from \"mermaid\";\r\nimport Locale from \"@/locales\";\r\nimport LoadingIcon from \"@/asserts/icons/three-dots.svg\";\r\nimport ReloadButtonIcon from \"@/asserts/icons/reload.svg\";\r\nimport React from \"react\";\r\nimport { useDebouncedCallback } from \"use-debounce\";\r\nimport { showImageModal, FullScreen } from \"@/components/ui-lib/ui-lib\";\r\nimport '../../styles/markdown.scss'\r\nimport {\r\n  HTMLPreview,\r\n  HTMLPreviewHander,\r\n} from \"../artifacts/artifacts\";\r\nimport { useChatStore } from \"@/store\";\r\nimport { IconButton } from \"@/components/button/button\";\r\n\r\nimport { useAppConfig } from \"@/store/config\";\r\nimport clsx from \"clsx\";\r\n\r\nexport function Mermaid(props: { code: string }) {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n  const [hasError, setHasError] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (props.code && ref.current) {\r\n      mermaid\r\n        .run({\r\n          nodes: [ref.current],\r\n          suppressErrors: true,\r\n        })\r\n        .catch((e) => {\r\n          setHasError(true);\r\n          console.error(\"[Mermaid] \", e.message);\r\n        });\r\n    }\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [props.code]);\r\n\r\n  function viewSvgInNewWindow() {\r\n    const svg = ref.current?.querySelector(\"svg\");\r\n    if (!svg) return;\r\n    const text = new XMLSerializer().serializeToString(svg);\r\n    const blob = new Blob([text], { type: \"image/svg+xml\" });\r\n    showImageModal(URL.createObjectURL(blob));\r\n  }\r\n\r\n  if (hasError) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={clsx(\"no-dark\", \"mermaid\")}\r\n      style={{\r\n        cursor: \"pointer\",\r\n        overflow: \"auto\",\r\n      }}\r\n      ref={ref}\r\n      onClick={() => viewSvgInNewWindow()}\r\n    >\r\n      {props.code}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function PreCode(props: { children: any }) {\r\n  const ref = useRef<HTMLPreElement>(null);\r\n  const previewRef = useRef<HTMLPreviewHander>(null);\r\n  const [mermaidCode, setMermaidCode] = useState(\"\");\r\n  const [htmlCode, setHtmlCode] = useState(\"\");\r\n  const { height } = useWindowSize();\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n\r\n  const renderArtifacts = useDebouncedCallback(() => {\r\n    if (!ref.current) return;\r\n    const mermaidDom = ref.current.querySelector(\"code.language-mermaid\");\r\n    if (mermaidDom) {\r\n      setMermaidCode((mermaidDom as HTMLElement).innerText);\r\n    }\r\n    const htmlDom = ref.current.querySelector(\"code.language-html\");\r\n    const refText = ref.current.querySelector(\"code\")?.innerText;\r\n    if (htmlDom) {\r\n      setHtmlCode((htmlDom as HTMLElement).innerText);\r\n    } else if (\r\n      refText?.startsWith(\"<!DOCTYPE\") ||\r\n      refText?.startsWith(\"<svg\") ||\r\n      refText?.startsWith(\"<?xml\")\r\n    ) {\r\n      setHtmlCode(refText);\r\n    }\r\n  }, 600);\r\n\r\n  const config = useAppConfig();\r\n  const enableArtifacts =\r\n    session.mask?.enableArtifacts !== false && config.enableArtifacts;\r\n\r\n  //Wrap the paragraph for plain-text\r\n  useEffect(() => {\r\n    if (ref.current) {\r\n      const codeElements = ref.current.querySelectorAll(\r\n        \"code\",\r\n      ) as NodeListOf<HTMLElement>;\r\n      const wrapLanguages = [\r\n        \"\",\r\n        \"md\",\r\n        \"markdown\",\r\n        \"text\",\r\n        \"txt\",\r\n        \"plaintext\",\r\n        \"tex\",\r\n        \"latex\",\r\n      ];\r\n      codeElements.forEach((codeElement) => {\r\n        const languageClass = codeElement.className.match(/language-(\\w+)/);\r\n        const name = languageClass ? languageClass[1] : \"\";\r\n        if (wrapLanguages.includes(name)) {\r\n          codeElement.style.whiteSpace = \"pre-wrap\";\r\n        }\r\n      });\r\n      setTimeout(renderArtifacts, 1);\r\n    }\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <pre ref={ref}>\r\n        <span\r\n          className=\"copy-code-button\"\r\n          onClick={() => {\r\n            if (ref.current) {\r\n              copyToClipboard(\r\n                ref.current.querySelector(\"code\")?.innerText ?? \"\",\r\n              );\r\n            }\r\n          }}\r\n        ></span>\r\n        {props.children}\r\n      </pre>\r\n      {mermaidCode.length > 0 && (\r\n        <Mermaid code={mermaidCode} key={mermaidCode} />\r\n      )}\r\n      {htmlCode.length > 0 && enableArtifacts && (\r\n        <FullScreen className=\"no-dark html\" right={70}>\r\n          {/* <ArtifactsShareButton\r\n            style={{ position: \"absolute\", right: 20, top: 10 }}\r\n            getCode={() => htmlCode}\r\n          /> */}\r\n          <IconButton\r\n            style={{ position: \"absolute\", right: 120, top: 10 }}\r\n            bordered\r\n            icon={<ReloadButtonIcon />}\r\n            shadow\r\n            onClick={() => previewRef.current?.reload()}\r\n          />\r\n          <HTMLPreview\r\n            ref={previewRef}\r\n            code={htmlCode}\r\n            autoHeight={!document.fullscreenElement}\r\n            height={!document.fullscreenElement ? 600 : height}\r\n          />\r\n        </FullScreen>\r\n      )}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction CustomCode(props: { children: any; className?: string }) {\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n  const config = useAppConfig();\r\n  const enableCodeFold =\r\n    session.mask?.enableCodeFold !== false && config.enableCodeFold;\r\n\r\n  const ref = useRef<HTMLPreElement>(null);\r\n  const [collapsed, setCollapsed] = useState(true);\r\n  const [showToggle, setShowToggle] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (ref.current) {\r\n      const codeHeight = ref.current.scrollHeight;\r\n      setShowToggle(codeHeight > 400);\r\n      ref.current.scrollTop = ref.current.scrollHeight;\r\n    }\r\n  }, [props.children]);\r\n\r\n  const toggleCollapsed = () => {\r\n    setCollapsed((collapsed) => !collapsed);\r\n  };\r\n  const renderShowMoreButton = () => {\r\n    if (showToggle && enableCodeFold && collapsed) {\r\n      return (\r\n        <div\r\n          className={clsx(\"show-hide-button\", {\r\n            collapsed,\r\n            expanded: !collapsed,\r\n          })}\r\n        >\r\n          <button onClick={toggleCollapsed}>{Locale.NewChat.More}</button>\r\n        </div>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n  return (\r\n    <>\r\n      <code\r\n        className={clsx(props?.className)}\r\n        ref={ref}\r\n        style={{\r\n          maxHeight: enableCodeFold && collapsed ? \"400px\" : \"none\",\r\n          overflowY: \"hidden\",\r\n        }}\r\n      >\r\n        {props.children}\r\n      </code>\r\n\r\n      {renderShowMoreButton()}\r\n    </>\r\n  );\r\n}\r\n\r\nfunction escapeBrackets(text: string) {\r\n  const pattern =\r\n    /(```[\\s\\S]*?```|`.*?`)|\\\\\\[([\\s\\S]*?[^\\\\])\\\\\\]|\\\\\\((.*?)\\\\\\)/g;\r\n  return text.replace(\r\n    pattern,\r\n    (match, codeBlock, squareBracket, roundBracket) => {\r\n      if (codeBlock) {\r\n        return codeBlock;\r\n      } else if (squareBracket) {\r\n        return `$$${squareBracket}$$`;\r\n      } else if (roundBracket) {\r\n        return `$${roundBracket}$`;\r\n      }\r\n      return match;\r\n    },\r\n  );\r\n}\r\n\r\nfunction tryWrapHtmlCode(text: string) {\r\n  // try add wrap html code (fixed: html codeblock include 2 newline)\r\n  return text\r\n    .replace(\r\n      /([`]*?)(\\w*?)([\\n\\r]*?)(<!DOCTYPE html>)/g,\r\n      (match, quoteStart, lang, newLine, doctype) => {\r\n        return !quoteStart ? \"\\n```html\\n\" + doctype : match;\r\n      },\r\n    )\r\n    .replace(\r\n      /(<\\/body>)([\\r\\n\\s]*?)(<\\/html>)([\\n\\r]*)([`]*)([\\n\\r]*?)/g,\r\n      (match, bodyEnd, space, htmlEnd, newLine, quoteEnd) => {\r\n        return !quoteEnd ? bodyEnd + space + htmlEnd + \"\\n```\\n\" : match;\r\n      },\r\n    );\r\n}\r\n\r\nfunction _MarkDownContent(props: { content: string }) {\r\n  // eslint-disable-next-line react-hooks/rules-of-hooks\r\n  const escapedContent = useMemo(() => {\r\n    return tryWrapHtmlCode(escapeBrackets(props.content));\r\n  }, [props.content]);\r\n\r\n  return (\r\n    <ReactMarkdown\r\n      remarkPlugins={[RemarkMath, RemarkGfm, RemarkBreaks]}\r\n      rehypePlugins={[\r\n        RehypeKatex,\r\n        [\r\n          RehypeHighlight,\r\n          {\r\n            detect: false,\r\n            ignoreMissing: true,\r\n          },\r\n        ],\r\n      ]}\r\n      components={{\r\n        pre: PreCode,\r\n        code: CustomCode,\r\n        p: (pProps) => {\r\n          console.log('pProps',pProps)\r\n          return <p {...pProps} dir=\"auto\" />\r\n        },\r\n        a: (aProps) => {\r\n          const href = aProps.href || \"\";\r\n          if (/\\.(aac|mp3|opus|wav)$/.test(href)) {\r\n            return (\r\n              <figure>\r\n                <audio controls src={href}></audio>\r\n              </figure>\r\n            );\r\n          }\r\n          if (/\\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {\r\n            return (\r\n              <video controls width=\"99.9%\">\r\n                <source src={href} />\r\n              </video>\r\n            );\r\n          }\r\n          const isInternal = /^\\/#/i.test(href);\r\n          const target = isInternal ? \"_self\" : aProps.target ?? \"_blank\";\r\n          return <a {...aProps} target={target} />;\r\n        },\r\n      }}\r\n    >\r\n      {escapedContent}\r\n    </ReactMarkdown>\r\n  );\r\n}\r\n\r\nexport const MarkdownContent = React.memo(_MarkDownContent);\r\n\r\nexport function Markdown(\r\n  props: {\r\n    content: string;\r\n    loading?: boolean;\r\n    fontSize?: number;\r\n    fontFamily?: string;\r\n    parentRef?: RefObject<HTMLDivElement>;\r\n    defaultShow?: boolean;\r\n  } & React.DOMAttributes<HTMLDivElement>,\r\n) {\r\n  const mdRef = useRef<HTMLDivElement>(null);\r\n\r\n  return (\r\n    <div\r\n      className=\"markdown-body\"\r\n      style={{\r\n        fontSize: `${props.fontSize ?? 14}px`,\r\n        fontFamily: props.fontFamily || \"inherit\",\r\n      }}\r\n      ref={mdRef}\r\n      onContextMenu={props.onContextMenu}\r\n      onDoubleClickCapture={props.onDoubleClickCapture}\r\n      dir=\"auto\"\r\n    >\r\n      {props.loading ? (\r\n        <LoadingIcon />\r\n      ) : (\r\n        <MarkdownContent content={props.content} />\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n", "import { useDebouncedCallback } from \"use-debounce\";\r\nimport React, {\r\n  useState,\r\n  useRef,\r\n  useEffect,\r\n  useMemo,\r\n  useCallback,\r\n  Fragment,\r\n  RefObject,\r\n} from \"react\";\r\nimport \"../../styles/highlight.scss\";\r\nimport SendWhiteIcon from \"@/asserts/icons/send-white.svg\";\r\nimport CopyIcon from \"@/asserts/icons/copy.svg\";\r\nimport LoadingButtonIcon from \"@/asserts/icons/loading.svg\";\r\nimport PromptIcon from \"@/asserts/icons/prompt.svg\";\r\nimport ResetIcon from \"@/asserts/icons/reload.svg\";\r\nimport BreakIcon from \"@/asserts/icons/break.svg\";\r\nimport DeleteIcon from \"@/asserts/icons/clear.svg\";\r\nimport ExcelIcon from \"@/asserts/icons/excel.svg\";\r\nimport PDFIcon from \"@/asserts/icons/pdf.svg\";\r\nimport WordIcon from \"@/asserts/icons/word.svg\";\r\nimport ConfirmIcon from \"@/asserts/icons/confirm.svg\";\r\nimport CloseIcon from \"@/asserts/icons/close.svg\";\r\nimport ImageIcon from \"@/asserts/icons/image.svg\";\r\nimport BottomIcon from \"@/asserts/icons/bottom.svg\";\r\nimport {\r\n  ChatMessage,\r\n  useChatStore,\r\n  BOT_HELLO,\r\n  createMessage,\r\n  DEFAULT_CONFIG,\r\n} from \"@/store\";\r\n\r\nimport {\r\n  copyToClipboard,\r\n  selectOrCopy,\r\n  autoGrowTextArea,\r\n  useMobileScreen,\r\n  getMessageTextContent,\r\n  getMessageImages,\r\n  isVisionModel,\r\n  safeLocalStorage,\r\n  getMessageFiles,\r\n  FileType,\r\n  getMessageInteractives,\r\n} from \"@/common/utils/utils\";\r\n\r\nimport { uploadImage as uploadImageRemote } from \"@/common/utils/chat\";\r\n\r\n// import dynamic from \"next/dynamic\";\r\n\r\nimport Locale from \"@/locales\";\r\n\r\nimport { IconButton } from \"@/components/button/button\";\r\nimport styles from \"./chat.module.scss\";\r\n\r\n\r\nimport {\r\n  CHAT_PAGE_SIZE,\r\n  Path,\r\n  REQUEST_TIMEOUT_MS,\r\n  UNFINISHED_INPUT,\r\n} from \"@/common/constant\";\r\nimport { Avatar } from \"@/components/emoji/emoji\";\r\nimport { MaskAvatar } from \"@/components/mask/mask\";\r\nimport {ChatCommandPrefix, useChatCommand} from \"@/common/command\";\r\nimport { prettyObject } from \"@/common/utils/format\";\r\n\r\nimport { isEmpty } from \"lodash-es\";\r\nimport clsx from \"clsx\";\r\nimport {Prompt, usePromptStore} from \"@/store/prompt\";\r\nimport {Markdown} from \"@/components/markdown/markdown\";\r\nimport dynamic from \"next/dynamic\";\r\nimport {nanoid} from \"nanoid\";\r\n\r\nconst localStorage = safeLocalStorage();\r\n\r\n\r\n// const Markdown = dynamic(async () => (await import(\"@/componets/markdown/markdown\")).Markdown, {\r\n//   loading: () => <LoadingIcon />,\r\n// });\r\n\r\nfunction useSubmitHandler() {\r\n  const isComposing = useRef(false);\r\n\r\n  useEffect(() => {\r\n    const onCompositionStart = () => {\r\n      isComposing.current = true;\r\n    };\r\n    const onCompositionEnd = () => {\r\n      isComposing.current = false;\r\n    };\r\n\r\n    window.addEventListener(\"compositionstart\", onCompositionStart);\r\n    window.addEventListener(\"compositionend\", onCompositionEnd);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"compositionstart\", onCompositionStart);\r\n      window.removeEventListener(\"compositionend\", onCompositionEnd);\r\n    };\r\n  }, []);\r\n\r\n  const shouldSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    // Fix Chinese input method \"Enter\" on Safari\r\n    if (e.keyCode == 229) return false;\r\n    if (e.key !== \"Enter\") return false;\r\n    if (e.key === \"Enter\" && (e.nativeEvent.isComposing || isComposing.current))\r\n      return false;\r\n    return (\r\n      e.altKey ||\r\n      e.ctrlKey ||\r\n      e.shiftKey ||\r\n      e.metaKey ||\r\n      (\r\n        !e.altKey &&\r\n        !e.ctrlKey &&\r\n        !e.shiftKey &&\r\n        !e.metaKey)\r\n    );\r\n  };\r\n\r\n  return {\r\n    shouldSubmit,\r\n  };\r\n}\r\n\r\nexport type RenderPrompt = Pick<Prompt, \"title\" | \"content\">;\r\n\r\nexport function PromptHints(props: {\r\n  prompts: RenderPrompt[];\r\n  onPromptSelect: (prompt: RenderPrompt) => void;\r\n}) {\r\n  const noPrompts = props.prompts.length === 0;\r\n  const [selectIndex, setSelectIndex] = useState(0);\r\n  const selectedRef = useRef<HTMLDivElement>(null);\r\n\r\n  useEffect(() => {\r\n    setSelectIndex(0);\r\n  }, [props.prompts.length]);\r\n\r\n  useEffect(() => {\r\n    const onKeyDown = (e: KeyboardEvent) => {\r\n      if (noPrompts || e.metaKey || e.altKey || e.ctrlKey) {\r\n        return;\r\n      }\r\n      // arrow up / down to select prompt\r\n      const changeIndex = (delta: number) => {\r\n        e.stopPropagation();\r\n        e.preventDefault();\r\n        const nextIndex = Math.max(\r\n          0,\r\n          Math.min(props.prompts.length - 1, selectIndex + delta),\r\n        );\r\n        setSelectIndex(nextIndex);\r\n        selectedRef.current?.scrollIntoView({\r\n          block: \"center\",\r\n        });\r\n      };\r\n\r\n      if (e.key === \"ArrowUp\") {\r\n        changeIndex(1);\r\n      } else if (e.key === \"ArrowDown\") {\r\n        changeIndex(-1);\r\n      } else if (e.key === \"Enter\") {\r\n        const selectedPrompt = props.prompts.at(selectIndex);\r\n        if (selectedPrompt) {\r\n          props.onPromptSelect(selectedPrompt);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", onKeyDown);\r\n\r\n    return () => window.removeEventListener(\"keydown\", onKeyDown);\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [props.prompts.length, selectIndex]);\r\n\r\n  if (noPrompts) return null;\r\n  return (\r\n    <div className={styles[\"prompt-hints\"]}>\r\n      {props.prompts.map((prompt, i) => (\r\n        <div\r\n          ref={i === selectIndex ? selectedRef : null}\r\n          className={clsx(styles[\"prompt-hint\"], {\r\n            [styles[\"prompt-hint-selected\"]]: i === selectIndex,\r\n          })}\r\n          key={prompt.title + i.toString()}\r\n          onClick={() => props.onPromptSelect(prompt)}\r\n          onMouseEnter={() => setSelectIndex(i)}\r\n        >\r\n          <div className={styles[\"hint-title\"]}>{prompt.title}</div>\r\n          <div className={styles[\"hint-content\"]}>{prompt.content}</div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\nfunction ClearContextDivider() {\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n\r\n  return (\r\n    <div\r\n      className={styles[\"clear-context\"]}\r\n      onClick={() =>\r\n        chatStore.updateTargetSession(\r\n          session,\r\n          (session) => (session.clearContextIndex = undefined),\r\n        )\r\n      }\r\n    >\r\n      <div className={styles[\"clear-context-tips\"]}>{Locale.Context.Clear}</div>\r\n      <div className={styles[\"clear-context-revert-btn\"]}>\r\n        {Locale.Context.Revert}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function ChatAction(props: {\r\n  text: string;\r\n  icon: JSX.Element;\r\n  onClick: () => void;\r\n}) {\r\n  const iconRef = useRef<HTMLDivElement>(null);\r\n  const textRef = useRef<HTMLDivElement>(null);\r\n  const [width, setWidth] = useState({\r\n    full: 16,\r\n    icon: 16,\r\n  });\r\n\r\n  function updateWidth() {\r\n    if (!iconRef.current || !textRef.current) return;\r\n    const getWidth = (dom: HTMLDivElement) => dom.getBoundingClientRect().width;\r\n    const textWidth = getWidth(textRef.current);\r\n    const iconWidth = getWidth(iconRef.current);\r\n    setWidth({\r\n      full: textWidth + iconWidth,\r\n      icon: iconWidth,\r\n    });\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={clsx(styles[\"chat-input-action\"], \"clickable\")}\r\n      onClick={() => {\r\n        props.onClick();\r\n        setTimeout(updateWidth, 1);\r\n      }}\r\n      onMouseEnter={updateWidth}\r\n      onTouchStart={updateWidth}\r\n      style={\r\n        {\r\n          \"--icon-width\": `${width.icon}px`,\r\n          \"--full-width\": `${width.full}px`,\r\n        } as React.CSSProperties\r\n      }\r\n    >\r\n      <div ref={iconRef} className={styles[\"icon\"]}>\r\n        {props.icon}\r\n      </div>\r\n      <div className={styles[\"text\"]} ref={textRef}>\r\n        {props.text}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction useScrollToBottom(\r\n  scrollRef: RefObject<HTMLDivElement>,\r\n  detach: boolean = false,\r\n) {\r\n  // for auto-scroll\r\n\r\n  const [autoScroll, setAutoScroll] = useState(true);\r\n  function scrollDomToBottom() {\r\n    const dom = scrollRef.current;\r\n    if (dom) {\r\n      requestAnimationFrame(() => {\r\n        setAutoScroll(true);\r\n        dom.scrollTo(0, dom.scrollHeight);\r\n      });\r\n    }\r\n  }\r\n\r\n  // auto scroll\r\n  useEffect(() => {\r\n    if (autoScroll && !detach) {\r\n      scrollDomToBottom();\r\n    }\r\n  });\r\n\r\n  return {\r\n    scrollRef,\r\n    autoScroll,\r\n    setAutoScroll,\r\n    scrollDomToBottom,\r\n  };\r\n}\r\n\r\nexport function ChatActions(props: {\r\n  uploadImage: () => void;\r\n  showPromptHints: () => void;\r\n  setAttachImages: (images: string[]) => void;\r\n  setUploading: (uploading: boolean) => void;\r\n  scrollToBottom: () => void;\r\n  hitBottom: boolean;\r\n  uploading: boolean;\r\n  setUserInput: (input: string) => void;\r\n}) {\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n\r\n  // switch model\r\n  const currentModel = session.mask.modelConfig.model;\r\n  const [showUploadImage, setShowUploadImage] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const show = isVisionModel(currentModel);\r\n    setShowUploadImage(show);\r\n    if (!show) {\r\n      props.setAttachImages([]);\r\n      props.setUploading(false);\r\n    }\r\n\r\n  }, [chatStore, currentModel, session]);\r\n\r\n  return (\r\n    <div className={styles[\"chat-input-actions\"]}>\r\n      <>\r\n        {!props.hitBottom && (\r\n          <ChatAction\r\n            onClick={props.scrollToBottom}\r\n            text={Locale.Chat.InputActions.ToBottom}\r\n            icon={<BottomIcon />}\r\n          />\r\n        )}\r\n\r\n        {showUploadImage && (\r\n          <ChatAction\r\n            onClick={props.uploadImage}\r\n            text={Locale.Chat.InputActions.UploadImage}\r\n            icon={props.uploading ? <LoadingButtonIcon /> : <ImageIcon />}\r\n          />\r\n        )}\r\n        <ChatAction\r\n          onClick={props.showPromptHints}\r\n          text={Locale.Chat.InputActions.Prompt}\r\n          icon={<PromptIcon />}\r\n        />\r\n        <ChatAction\r\n          text={Locale.Chat.InputActions.Clear}\r\n          icon={<BreakIcon />}\r\n          onClick={() => {\r\n            chatStore.updateTargetSession(session, (session) => {\r\n              if (session.clearContextIndex === session.messages.length) {\r\n                session.clearContextIndex = undefined;\r\n              } else {\r\n                session.clearContextIndex = session.messages.length;\r\n                session.memoryPrompt = \"\"; // will clear memory\r\n              }\r\n            });\r\n          }}\r\n        />\r\n      </>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function DeleteImageButton(props: { deleteImage: () => void }) {\r\n  return (\r\n    <div className={styles[\"delete-image\"]} onClick={props.deleteImage}>\r\n      <DeleteIcon />\r\n    </div>\r\n  );\r\n}\r\n\r\n// 将动态导入移到组件外部\r\nconst interactiveComponents = new Map();\r\n\r\nfunction CChat() {\r\n  type RenderMessage = ChatMessage & { preview?: boolean };\r\n\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n  console.log('session',session)\r\n  const inputRef = useRef<HTMLTextAreaElement>(null);\r\n  const [userInput, setUserInput] = useState(\"\");\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { shouldSubmit } = useSubmitHandler();\r\n  const scrollRef = useRef<HTMLDivElement>(null);\r\n  const isScrolledToBottom = scrollRef?.current\r\n    ? Math.abs(\r\n    scrollRef.current.scrollHeight -\r\n    (scrollRef.current.scrollTop + scrollRef.current.clientHeight),\r\n  ) <= 1\r\n    : false;\r\n  const { setAutoScroll, scrollDomToBottom } = useScrollToBottom(\r\n    scrollRef,\r\n    isScrolledToBottom,\r\n  );\r\n  const [hitBottom, setHitBottom] = useState(true);\r\n  const isMobileScreen = useMobileScreen();\r\n  const [attachImages, setAttachImages] = useState<string[]>([]);\r\n  const [uploading, setUploading] = useState(false);\r\n\r\n  // auto grow input\r\n  const [inputRows, setInputRows] = useState(2);\r\n  const measure = useDebouncedCallback(\r\n    () => {\r\n      const rows = inputRef.current ? autoGrowTextArea(inputRef.current) : 1;\r\n      const inputRows = Math.min(\r\n        20,\r\n        Math.max(2 + Number(!isMobileScreen), rows),\r\n      );\r\n      setInputRows(inputRows);\r\n    },\r\n    100,\r\n    {\r\n      leading: true,\r\n      trailing: true,\r\n    },\r\n  );\r\n\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  useEffect(measure, [userInput]);\r\n\r\n  // chat commands shortcuts\r\n  const chatCommands = useChatCommand({\r\n    new: () => chatStore.newSession(),\r\n    prev: () => chatStore.nextSession(-1),\r\n    next: () => chatStore.nextSession(1),\r\n    clear: () =>\r\n      chatStore.updateTargetSession(\r\n        session,\r\n        (session) => (session.clearContextIndex = session.messages.length),\r\n      ),\r\n    fork: () => chatStore.forkSession(),\r\n    del: () => chatStore.deleteSession(chatStore.currentSessionIndex),\r\n  });\r\n\r\n  // only search prompts when user input is short\r\n  const SEARCH_TEXT_LIMIT = 30;\r\n  const onInput = (text: string) => {\r\n    setUserInput(text);\r\n    const n = text.trim().length;\r\n\r\n    // clear search results\r\n    if (n === 0) {\r\n      setPromptHints([]);\r\n    } else if (text.match(ChatCommandPrefix)) {\r\n      setPromptHints(chatCommands.search(text));\r\n    } else if (!DEFAULT_CONFIG.disablePromptHint && n < SEARCH_TEXT_LIMIT) {\r\n      // check if need to trigger auto completion\r\n      if (text.startsWith(\"/\")) {\r\n        const searchText = text.slice(1);\r\n        onSearch(searchText);\r\n      }\r\n    }\r\n  };\r\n\r\n  const doSubmit = (userInput: string) => {\r\n    if (userInput.trim() === \"\" && isEmpty(attachImages)) return;\r\n    const matchCommand = chatCommands.match(userInput);\r\n    if (matchCommand.matched) {\r\n      setUserInput(\"\");\r\n      matchCommand.invoke();\r\n      return;\r\n    }\r\n    setIsLoading(true);\r\n    chatStore\r\n      .onUserInput(userInput, attachImages)\r\n      .then(() => setIsLoading(false));\r\n    setAttachImages([]);\r\n    chatStore.setLastInput(userInput);\r\n    setUserInput(\"\");\r\n    if (!isMobileScreen) inputRef.current?.focus();\r\n    setAutoScroll(true);\r\n  };\r\n  // TODO: 模拟后端发送文件消息或者可交互消息\r\n  useEffect(() => {\r\n    // 文件消息\r\n    session.messages.push(createMessage({\r\n      id: nanoid(),\r\n      date: new Date().toLocaleString(),\r\n      role: \"assistant\",\r\n      content: [\r\n        {\r\n          type: \"excel_url\",\r\n          excel_url: {\r\n            name: 'excel.xlsx',\r\n            url: \"https://example.com/excel.xlsx\"\r\n          }\r\n        }\r\n      ]\r\n    }))\r\n    // 可交互消息\r\n    session.messages.push(createMessage({\r\n      id: nanoid(),\r\n      date: new Date().toLocaleString(),\r\n      role: \"assistant\",\r\n      content: [\r\n        {\r\n          type: \"interactive\",\r\n          interactive: {\r\n            template: 'order.template.tsx',\r\n            props: {\r\n              orderNo: 'XS-ZT-20240012447',\r\n              roll: 13,\r\n              weight: 200,\r\n              length: 10,\r\n              statusName: '已下单-配布中',\r\n              time: '14:21',\r\n              price: 1000,\r\n              paymentStatus: '未收款'\r\n            },\r\n            events: {\r\n              onClick: () => {\r\n                // 跳转到订单进度\r\n                console.log('onClick')\r\n              }\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    }))\r\n    // 可交互消息\r\n    session.messages.push(createMessage({\r\n      id: nanoid(),\r\n      date: new Date().toLocaleString(),\r\n      role: \"assistant\",\r\n      content: [\r\n        {\r\n          type: \"interactive\",\r\n          interactive: {\r\n            template: 'customer.template.tsx',\r\n            props: {\r\n              customerName: '张三',\r\n              level: 'VIP',\r\n              debt: 1000,\r\n              note: '备注信息',\r\n              mostPurchased: '布料',\r\n              lastOrder: '2024-01-01',\r\n              followUpPerson: '李四',\r\n              salesperson: '王五',\r\n            },\r\n            events: {\r\n              onClick: () => {\r\n                // 跳转到客户信息\r\n                console.log('onClick')\r\n              }\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    }))\r\n    // 可交互消息\r\n    session.messages.push(createMessage({\r\n      id: nanoid(),\r\n      date: new Date().toLocaleString(),\r\n      role: \"assistant\",\r\n      content: [\r\n        {\r\n          type: \"interactive\",\r\n          interactive: {\r\n            template: 'billingSummary.template.tsx',\r\n            props: {\r\n              monthlyPurchase: 2000,\r\n              debt: 30.99\r\n            },\r\n            events: {\r\n              onClick: () => {\r\n                // 跳转到客户对账单\r\n                console.log('onClick')\r\n              }\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    }))\r\n  }, [])\r\n\r\n  useEffect(() => {\r\n    chatStore.updateTargetSession(session, (session) => {\r\n      const stopTiming = Date.now() - REQUEST_TIMEOUT_MS;\r\n\r\n      console.log('session',session)\r\n      session.messages.forEach((m) => {\r\n        // check if should stop all stale messages\r\n        if (m.isError || new Date(m.date).getTime() < stopTiming) {\r\n          if (m.streaming) {\r\n            m.streaming = false;\r\n          }\r\n\r\n          if (m.content.length === 0) {\r\n            m.isError = true;\r\n            m.content = prettyObject({\r\n              error: true,\r\n              message: \"empty response\",\r\n            });\r\n          }\r\n        }\r\n      });\r\n\r\n    });\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, [session]);\r\n\r\n  // check if should send message\r\n  const onInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {\r\n    // if ArrowUp and no userInput, fill with last input\r\n    if (\r\n      e.key === \"ArrowUp\" &&\r\n      userInput.length <= 0 &&\r\n      !(e.metaKey || e.altKey || e.ctrlKey)\r\n    ) {\r\n      setUserInput(chatStore.lastInput ?? \"\");\r\n      e.preventDefault();\r\n      return;\r\n    }\r\n    if (shouldSubmit(e) && promptHints.length === 0) {\r\n      doSubmit(userInput);\r\n      e.preventDefault();\r\n    }\r\n  };\r\n  const onRightClick = (e: any, message: ChatMessage) => {\r\n    // copy to clipboard\r\n    if (selectOrCopy(e.currentTarget, getMessageTextContent(message))) {\r\n      if (userInput.length === 0) {\r\n        setUserInput(getMessageTextContent(message));\r\n      }\r\n\r\n      e.preventDefault();\r\n    }\r\n  };\r\n\r\n  const deleteMessage = (msgId?: string) => {\r\n    chatStore.updateTargetSession(\r\n      session,\r\n      (session) =>\r\n        (session.messages = session.messages.filter((m) => m.id !== msgId)),\r\n    );\r\n  };\r\n\r\n  const onDelete = (msgId: string) => {\r\n    deleteMessage(msgId);\r\n  };\r\n\r\n  const onResend = (message: ChatMessage) => {\r\n    // when it is resending a message\r\n    // 1. for a user's message, find the next bot response\r\n    // 2. for a bot's message, find the last user's input\r\n    // 3. delete original user input and bot's message\r\n    // 4. resend the user's input\r\n\r\n    const resendingIndex = session.messages.findIndex(\r\n      (m) => m.id === message.id,\r\n    );\r\n\r\n    if (resendingIndex < 0 || resendingIndex >= session.messages.length) {\r\n      console.error(\"[Chat] failed to find resending message\", message);\r\n      return;\r\n    }\r\n\r\n    let userMessage: ChatMessage | undefined;\r\n    let botMessage: ChatMessage | undefined;\r\n\r\n    if (message.role === \"assistant\") {\r\n      // if it is resending a bot's message, find the user input for it\r\n      botMessage = message;\r\n      for (let i = resendingIndex; i >= 0; i -= 1) {\r\n        if (session.messages[i].role === \"user\") {\r\n          userMessage = session.messages[i];\r\n          break;\r\n        }\r\n      }\r\n    } else if (message.role === \"user\") {\r\n      // if it is resending a user's input, find the bot's response\r\n      userMessage = message;\r\n      for (let i = resendingIndex; i < session.messages.length; i += 1) {\r\n        if (session.messages[i].role === \"assistant\") {\r\n          botMessage = session.messages[i];\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    if (userMessage === undefined) {\r\n      console.error(\"[Chat] failed to resend\", message);\r\n      return;\r\n    }\r\n\r\n    // delete the original messages\r\n    deleteMessage(userMessage.id);\r\n    deleteMessage(botMessage?.id);\r\n\r\n    // resend the message\r\n    setIsLoading(true);\r\n    const textContent = getMessageTextContent(userMessage);\r\n    const images = getMessageImages(userMessage);\r\n    chatStore.onUserInput(textContent, images).then(() => setIsLoading(false));\r\n    inputRef.current?.focus();\r\n  };\r\n\r\n  const context: RenderMessage[] = useMemo(() => {\r\n    return session.mask.hideContext ? [] : session.mask.context.slice();\r\n  }, [session.mask.context, session.mask.hideContext]);\r\n\r\n  if (\r\n    context.length === 0 &&\r\n    session.messages.at(0)?.content !== BOT_HELLO.content\r\n  ) {\r\n    const copiedHello = Object.assign({}, BOT_HELLO);\r\n\r\n    context.push(copiedHello);\r\n  }\r\n\r\n  // preview messages\r\n  const renderMessages = useMemo(() => {\r\n    return context\r\n      .concat(session.messages as RenderMessage[])\r\n      .concat(\r\n        isLoading\r\n          ? [\r\n            {\r\n              ...createMessage({\r\n                role: \"assistant\",\r\n                content: \"……\",\r\n              }),\r\n              preview: true,\r\n            },\r\n          ]\r\n          : [],\r\n      )\r\n      .concat(\r\n        userInput.length > 0\r\n          ? [\r\n            {\r\n              ...createMessage({\r\n                role: \"user\",\r\n                content: userInput,\r\n              }),\r\n              preview: true,\r\n            },\r\n          ]\r\n          : [],\r\n      );\r\n  }, [\r\n    context,\r\n    isLoading,\r\n    session.messages,\r\n    userInput,\r\n  ]);\r\n\r\n  const [msgRenderIndex, _setMsgRenderIndex] = useState(\r\n    Math.max(0, renderMessages.length - CHAT_PAGE_SIZE),\r\n  );\r\n  function setMsgRenderIndex(newIndex: number) {\r\n    newIndex = Math.min(renderMessages.length - CHAT_PAGE_SIZE, newIndex);\r\n    newIndex = Math.max(0, newIndex);\r\n    _setMsgRenderIndex(newIndex);\r\n  }\r\n\r\n  const messages = useMemo(() => {\r\n    const endRenderIndex = Math.min(\r\n      msgRenderIndex + 3 * CHAT_PAGE_SIZE,\r\n      renderMessages.length,\r\n    );\r\n    return renderMessages.slice(msgRenderIndex, endRenderIndex);\r\n  }, [msgRenderIndex, renderMessages]);\r\n\r\n  const onChatBodyScroll = (e: HTMLElement) => {\r\n    const bottomHeight = e.scrollTop + e.clientHeight;\r\n    const edgeThreshold = e.clientHeight;\r\n\r\n    const isTouchTopEdge = e.scrollTop <= edgeThreshold;\r\n    const isTouchBottomEdge = bottomHeight >= e.scrollHeight - edgeThreshold;\r\n    const isHitBottom =\r\n      bottomHeight >= e.scrollHeight - (isMobileScreen ? 4 : 10);\r\n\r\n    const prevPageMsgIndex = msgRenderIndex - CHAT_PAGE_SIZE;\r\n    const nextPageMsgIndex = msgRenderIndex + CHAT_PAGE_SIZE;\r\n\r\n    if (isTouchTopEdge && !isTouchBottomEdge) {\r\n      setMsgRenderIndex(prevPageMsgIndex);\r\n    } else if (isTouchBottomEdge) {\r\n      setMsgRenderIndex(nextPageMsgIndex);\r\n    }\r\n\r\n    setHitBottom(isHitBottom);\r\n    setAutoScroll(isHitBottom);\r\n  };\r\n  function scrollToBottom() {\r\n    setMsgRenderIndex(renderMessages.length - CHAT_PAGE_SIZE);\r\n    scrollDomToBottom();\r\n  }\r\n\r\n  // clear context index = context length + index in messages\r\n  const clearContextIndex =\r\n    (session.clearContextIndex ?? -1) >= 0\r\n      ? session.clearContextIndex! + context.length - msgRenderIndex\r\n      : -1;\r\n\r\n  const autoFocus = !isMobileScreen; // wont auto focus on mobile screen\r\n\r\n\r\n  // remember unfinished input\r\n  useEffect(() => {\r\n    // try to load from local storage\r\n    const key = UNFINISHED_INPUT(session.id);\r\n    const mayBeUnfinishedInput = localStorage.getItem(key);\r\n    if (mayBeUnfinishedInput && userInput.length === 0) {\r\n      setUserInput(mayBeUnfinishedInput);\r\n      localStorage.removeItem(key);\r\n    }\r\n\r\n    const dom = inputRef.current;\r\n    return () => {\r\n      localStorage.setItem(key, dom?.value ?? \"\");\r\n    };\r\n    // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const handlePaste = useCallback(\r\n    async (event: React.ClipboardEvent<HTMLTextAreaElement>) => {\r\n      const currentModel = chatStore.currentSession().mask.modelConfig.model;\r\n      if (!isVisionModel(currentModel)) {\r\n        return;\r\n      }\r\n      // @ts-expect-error window.clipboardData IE support\r\n      const items = (event.clipboardData || window.clipboardData).items;\r\n      for (const item of items) {\r\n        if (item.kind === \"file\" && item.type.startsWith(\"image/\")) {\r\n          event.preventDefault();\r\n          const file = item.getAsFile();\r\n          if (file) {\r\n            const images: string[] = [];\r\n            images.push(...attachImages);\r\n            images.push(\r\n              ...(await new Promise<string[]>((res, rej) => {\r\n                setUploading(true);\r\n                const imagesData: string[] = [];\r\n                uploadImageRemote(file)\r\n                  .then((dataUrl) => {\r\n                    imagesData.push(dataUrl);\r\n                    setUploading(false);\r\n                    res(imagesData);\r\n                  })\r\n                  .catch((e) => {\r\n                    setUploading(false);\r\n                    rej(e);\r\n                  });\r\n              })),\r\n            );\r\n            const imagesLength = images.length;\r\n\r\n            if (imagesLength > 3) {\r\n              images.splice(3, imagesLength - 3);\r\n            }\r\n            setAttachImages(images);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    [attachImages, chatStore],\r\n  );\r\n\r\n  async function uploadImage() {\r\n    const images: string[] = [];\r\n    images.push(...attachImages);\r\n\r\n    images.push(\r\n      ...(await new Promise<string[]>((res, rej) => {\r\n        const fileInput = document.createElement(\"input\");\r\n        fileInput.type = \"file\";\r\n        fileInput.accept =\r\n          \"image/png, image/jpeg, image/webp, image/heic, image/heif\";\r\n        fileInput.multiple = true;\r\n        fileInput.onchange = (event: any) => {\r\n          setUploading(true);\r\n          const files = event.target.files;\r\n          const imagesData: string[] = [];\r\n          for (let i = 0; i < files.length; i++) {\r\n            const file = event.target.files[i];\r\n            uploadImageRemote(file)\r\n              .then((dataUrl) => {\r\n                imagesData.push(dataUrl);\r\n                if (\r\n                  imagesData.length === 3 ||\r\n                  imagesData.length === files.length\r\n                ) {\r\n                  setUploading(false);\r\n                  res(imagesData);\r\n                }\r\n              })\r\n              .catch((e) => {\r\n                setUploading(false);\r\n                rej(e);\r\n              });\r\n          }\r\n        };\r\n        fileInput.click();\r\n      })),\r\n    );\r\n\r\n    const imagesLength = images.length;\r\n    if (imagesLength > 3) {\r\n      images.splice(3, imagesLength - 3);\r\n    }\r\n    setAttachImages(images);\r\n  }\r\n  const promptStore = usePromptStore();\r\n  const [promptHints, setPromptHints] = useState<RenderPrompt[]>([]);\r\n  const onPromptSelect = (prompt: RenderPrompt) => {\r\n    setTimeout(() => {\r\n      setPromptHints([]);\r\n\r\n      const matchedChatCommand = chatCommands.match(prompt.content);\r\n      if (matchedChatCommand.matched) {\r\n        // if user is selecting a chat command, just trigger it\r\n        matchedChatCommand.invoke();\r\n        setUserInput(\"\");\r\n      } else {\r\n        // or fill the prompt\r\n        setUserInput(prompt.content);\r\n      }\r\n      inputRef.current?.focus();\r\n    }, 30);\r\n  };\r\n  const onSearch = useDebouncedCallback(\r\n    (text: string) => {\r\n      const matchedPrompts = promptStore.search(text);\r\n      setPromptHints(matchedPrompts);\r\n    },\r\n    100,\r\n    { leading: true, trailing: true },\r\n  );\r\n  const onFileClick = (file: FileType) => {\r\n    console.log(\"file\", file)\r\n    if (file.url) {\r\n      if (file.type === \"excel_url\") {\r\n        // Excel预览 - 可以使用在线Office预览或其他Excel预览组件\r\n        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(file.url)}`, '_blank');\r\n      } else if (file.type === \"word_url\") {\r\n        // Word预览 - 同样可以使用在线Office预览\r\n        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(file.url)}`, '_blank');\r\n      } else if (file.type === \"pdf_url\") {\r\n        // PDF预览 - 可以直接在新标签页打开或使用PDF预览组件\r\n        window.open(file.url, '_blank');\r\n      }\r\n    }\r\n  }\r\n\r\n  // 添加缓存逻辑\r\n  const getInteractiveComponent = useCallback((template: string) => {\r\n    console.log('interactiveComponents',interactiveComponents, template)\r\n    const templateName = template.replace('.tsx', '');\r\n    if (!interactiveComponents.has(template)) {\r\n      const Component = dynamic(\r\n        () => import(`../interactive/${templateName}.tsx`),\r\n        {\r\n          loading: () => <div>Loading...</div>,\r\n          ssr: false,\r\n        }\r\n      );\r\n      console.log('Component',Component)\r\n      \r\n      // 包装组件以确保样式正确应用\r\n      const WrappedComponent = (props: any) => {\r\n        useEffect(() => {\r\n          // 触发样式更新\r\n          const styleElement = document.createElement('style');\r\n          document.head.appendChild(styleElement);\r\n          document.head.removeChild(styleElement);\r\n        }, []);\r\n\r\n        return <Component {...props} />;\r\n      };\r\n\r\n      interactiveComponents.set(template, WrappedComponent);\r\n    }\r\n    return interactiveComponents.get(template);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleKeyDown = (event: any) => {\r\n      // 聚焦聊天输入 shift + esc\r\n      if (event.shiftKey && event.key.toLowerCase() === \"escape\") {\r\n        event.preventDefault();\r\n        inputRef.current?.focus();\r\n      }\r\n      // 复制最后一个代码块 command + shift + ;\r\n      else if (\r\n        (event.metaKey || event.ctrlKey) &&\r\n        event.shiftKey &&\r\n        event.code === \"Semicolon\"\r\n      ) {\r\n        event.preventDefault();\r\n        const copyCodeButton =\r\n          document.querySelectorAll<HTMLElement>(\".copy-code-button\");\r\n        if (copyCodeButton.length > 0) {\r\n          copyCodeButton[copyCodeButton.length - 1].click();\r\n        }\r\n      }\r\n      // 复制最后一个回复 command + shift + c\r\n      else if (\r\n        (event.metaKey || event.ctrlKey) &&\r\n        event.shiftKey &&\r\n        event.key.toLowerCase() === \"c\"\r\n      ) {\r\n        event.preventDefault();\r\n        const lastNonUserMessage = messages\r\n          .filter((message) => message.role !== \"user\")\r\n          .pop();\r\n        if (lastNonUserMessage) {\r\n          const lastMessageContent = getMessageTextContent(lastNonUserMessage);\r\n          copyToClipboard(lastMessageContent);\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener(\"keydown\", handleKeyDown);\r\n\r\n    return () => {\r\n      window.removeEventListener(\"keydown\", handleKeyDown);\r\n    };\r\n  }, [messages, chatStore]);\r\n\r\n  // 在组件加载完成后触发样式注入\r\n  useEffect(() => {\r\n    // 触发一次样式更新\r\n    const styleElement = document.createElement('style');\r\n    document.head.appendChild(styleElement);\r\n    document.head.removeChild(styleElement);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      <div className={styles.chat} key={session.id}>\r\n        <div className={styles[\"chat-main\"]}>\r\n          <div className={styles[\"chat-body-container\"]}>\r\n            <div\r\n              className={styles[\"chat-body\"]}\r\n              ref={scrollRef}\r\n              onScroll={(e) => onChatBodyScroll(e.currentTarget)}\r\n              onMouseDown={() => inputRef.current?.blur()}\r\n              onTouchStart={() => {\r\n                inputRef.current?.blur();\r\n                setAutoScroll(false);\r\n              }}\r\n            >\r\n              {messages.map((message, i) => {\r\n                console.log(\"messages\", messages)\r\n                const isUser = message.role === \"user\";\r\n                const isContext = i < context.length;\r\n                const showActions =\r\n                  i > 0 &&\r\n                  !(message.preview || message.content.length === 0) &&\r\n                  !isContext;\r\n                const showTyping = message.preview || message.streaming;\r\n\r\n                const shouldShowClearContextDivider =\r\n                  i === clearContextIndex - 1;\r\n\r\n                return (\r\n                  <Fragment key={message.id}>\r\n                    <div\r\n                      className={\r\n                        isUser\r\n                          ? styles[\"chat-message-user\"]\r\n                          : styles[\"chat-message\"]\r\n                      }\r\n                    >\r\n                      <div className={styles[\"chat-message-container\"]}>\r\n                        <div className={styles[\"chat-message-header\"]}>\r\n                          <div className={styles[\"chat-message-avatar\"]}>\r\n                            {isUser ? (\r\n                              <Avatar avatar={DEFAULT_CONFIG.avatar} />\r\n                            ) : (\r\n                              <>\r\n                                {[\"system\"].includes(message.role) ? (\r\n                                  <Avatar avatar=\"2699-fe0f\" />\r\n                                ) : (\r\n                                  <Avatar model={message.model ||\r\n                                    session.mask.modelConfig.model} />\r\n                                  // <MaskAvatar\r\n                                  //   avatar={session.mask.avatar}\r\n                                  //   model={\r\n                                  //     message.model ||\r\n                                  //     session.mask.modelConfig.model\r\n                                  //   }\r\n                                  // />\r\n                                )}\r\n                              </>\r\n                            )}\r\n                          </div>\r\n                          {!isUser && (\r\n                            <div className={styles[\"chat-model-name\"]}>\r\n                              阿布\r\n                            </div>\r\n                          )}\r\n\r\n                          {showActions && (\r\n                            <div className={styles[\"chat-message-actions\"]}>\r\n                              <div className={styles[\"chat-input-actions\"]}>\r\n                                {/*{message.streaming ? (*/}\r\n                                {/*  <ChatAction*/}\r\n                                {/*    text={Locale.Chat.Actions.Stop}*/}\r\n                                {/*    icon={<StopIcon />}*/}\r\n                                {/*    onClick={() => onUserStop(message.id ?? i)}*/}\r\n                                {/*  />*/}\r\n                                {/*) : (*/}\r\n                                  <>\r\n                                    <ChatAction\r\n                                      text={Locale.Chat.Actions.Retry}\r\n                                      icon={<ResetIcon />}\r\n                                      onClick={() => onResend(message)}\r\n                                    />\r\n\r\n                                    <ChatAction\r\n                                      text={Locale.Chat.Actions.Delete}\r\n                                      icon={<DeleteIcon />}\r\n                                      onClick={() => onDelete(message.id ?? i.toString())}\r\n                                    />\r\n\r\n                                    <ChatAction\r\n                                      text={Locale.Chat.Actions.Copy}\r\n                                      icon={<CopyIcon />}\r\n                                      onClick={() =>\r\n                                        copyToClipboard(\r\n                                          getMessageTextContent(message),\r\n                                        )\r\n                                      }\r\n                                    />\r\n                                  </>\r\n                                {/*)}*/}\r\n                              </div>\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        {/* 加载中 */}\r\n                        {message?.tools?.length == 0 && showTyping && (\r\n                          <div className={styles[\"chat-message-status\"]}>\r\n                            {Locale.Chat.Typing}\r\n                          </div>\r\n                        )}\r\n                        {/* 工具 */}\r\n                        {message?.tools && message?.tools.length > 0 && (\r\n                          <div className={styles[\"chat-message-tools\"]}>\r\n                            {message?.tools?.map((tool) => (\r\n                              <div\r\n                                key={tool.id}\r\n                                title={tool?.errorMsg}\r\n                                className={styles[\"chat-message-tool\"]}\r\n                              >\r\n                                {tool.isError === false ? (\r\n                                  <ConfirmIcon />\r\n                                ) : tool.isError === true ? (\r\n                                  <CloseIcon />\r\n                                ) : (\r\n                                  <LoadingButtonIcon />\r\n                                )}\r\n                                <span>{tool?.function?.name}</span>\r\n                              </div>\r\n                            ))}\r\n                          </div>\r\n                        )}\r\n                        <div className={styles[\"chat-message-item\"]}>\r\n                          {/* 文本 */}\r\n                          <Markdown\r\n                            key={message.streaming ? \"loading\" : \"done\"}\r\n                            content={getMessageTextContent(message)}\r\n                            loading={\r\n                              (message.preview || message.streaming) &&\r\n                              message.content.length === 0 &&\r\n                              !isUser\r\n                            }\r\n                            onContextMenu={(e) => onRightClick(e, message)} // hard to use\r\n                            onDoubleClickCapture={() => {\r\n                              if (!isMobileScreen) return;\r\n                              setUserInput(getMessageTextContent(message));\r\n                            }}\r\n                            fontSize={DEFAULT_CONFIG.fontSize}\r\n                            fontFamily={DEFAULT_CONFIG.fontFamily}\r\n                            parentRef={scrollRef}\r\n                            defaultShow={i >= messages.length - 6}\r\n                          />\r\n                          {\r\n                            getMessageInteractives(message).length > 0 && (\r\n                              getMessageInteractives(message).map((interactive, index) => {\r\n                                if(!interactive) return null;\r\n                                const { template, props, events } = interactive;\r\n                                const InteractiveComponent = getInteractiveComponent(template);\r\n                                return (\r\n                                  <InteractiveComponent\r\n                                    key={`${message.id}-${index}`}\r\n                                    props={props || null}\r\n                                    events={events || null}\r\n                                  />\r\n                                )\r\n                              })\r\n                            )\r\n                          }\r\n                          {/* File */}\r\n                          {getMessageFiles(message).length ? (\r\n                              getMessageFiles(message).map((file, index) => {\r\n                                return (\r\n                                  <div key={index}\r\n                                    className={styles[\"chat-message-item-file\"]}\r\n                                    onClick={() => onFileClick(file)}\r\n                                  >\r\n                                    {\r\n                                      file.type === \"excel_url\" ? <ExcelIcon className={styles[\"chat-message-item-file-icon\"]}/> : file.type === \"word_url\" ? <WordIcon className={styles[\"chat-message-item-file-icon\"]}/> : <PDFIcon className={styles[\"chat-message-item-file-icon\"]} />\r\n                                    }\r\n                                    <div className={styles[\"chat-message-item-file-name\"]}>\r\n                                      {file.name}\r\n                                    </div>\r\n                                  </div>\r\n                                )\r\n                              })\r\n                          ) : null}\r\n                          {/* 单图 */}\r\n                          {getMessageImages(message).length == 1 && (\r\n                            <img\r\n                              className={styles[\"chat-message-item-image\"]}\r\n                              src={getMessageImages(message)[0]}\r\n                              alt=\"\"\r\n                            />\r\n                          )}\r\n                          {/* 多图 */}\r\n                          {getMessageImages(message).length > 1 && (\r\n                            <div\r\n                              className={styles[\"chat-message-item-images\"]}\r\n                              style={\r\n                                {\r\n                                  \"--image-count\":\r\n                                  getMessageImages(message).length,\r\n                                } as React.CSSProperties\r\n                              }\r\n                            >\r\n                              {getMessageImages(message).map((image, index) => {\r\n                                return (\r\n                                  <img\r\n                                    className={\r\n                                      styles[\"chat-message-item-image-multi\"]\r\n                                    }\r\n                                    key={index}\r\n                                    src={image}\r\n                                    alt=\"\"\r\n                                  />\r\n                                );\r\n                              })}\r\n                            </div>\r\n                          )}\r\n                        </div>\r\n                        {/* 音频 */}\r\n                        {message?.audio_url && (\r\n                          <div className={styles[\"chat-message-audio\"]}>\r\n                            <audio src={message.audio_url} controls />\r\n                          </div>\r\n                        )}\r\n                        {/* 时间 */}\r\n                        <div className={styles[\"chat-message-action-date\"]}>\r\n                          {isContext\r\n                            ? Locale.Chat.IsContext\r\n                            : message.date.toLocaleString()}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                    {shouldShowClearContextDivider && <ClearContextDivider />}\r\n                  </Fragment>\r\n                );\r\n              })}\r\n            </div>\r\n            <div className={styles[\"chat-input-panel\"]}>\r\n              <PromptHints\r\n                prompts={promptHints}\r\n                onPromptSelect={onPromptSelect}\r\n              />\r\n\r\n              <ChatActions\r\n                uploadImage={uploadImage}\r\n                setAttachImages={setAttachImages}\r\n                setUploading={setUploading}\r\n                scrollToBottom={scrollToBottom}\r\n                showPromptHints={() => {\r\n                  // Click again to close\r\n                  if (promptHints.length > 0) {\r\n                    setPromptHints([]);\r\n                    return;\r\n                  }\r\n\r\n                  inputRef.current?.focus();\r\n                  setUserInput(\"/\");\r\n                  onSearch(\"\");\r\n                }}\r\n                hitBottom={hitBottom}\r\n                uploading={uploading}\r\n                setUserInput={setUserInput}\r\n              />\r\n              <label\r\n                className={clsx(styles[\"chat-input-panel-inner\"], {\r\n                  [styles[\"chat-input-panel-inner-attach\"]]:\r\n                  attachImages.length !== 0,\r\n                })}\r\n                htmlFor=\"chat-input\"\r\n              >\r\n                <textarea\r\n                  id=\"chat-input\"\r\n                  ref={inputRef}\r\n                  className={styles[\"chat-input\"]}\r\n                  placeholder={Locale.Chat.Input('Enter')}\r\n                  onInput={(e) => onInput(e.currentTarget.value)}\r\n                  value={userInput}\r\n                  onKeyDown={onInputKeyDown}\r\n                  onFocus={scrollToBottom}\r\n                  onClick={scrollToBottom}\r\n                  onPaste={handlePaste}\r\n                  rows={inputRows}\r\n                  autoFocus={autoFocus}\r\n                  style={{\r\n                    fontSize: DEFAULT_CONFIG.fontSize,\r\n                    fontFamily: DEFAULT_CONFIG.fontFamily,\r\n                  }}\r\n                />\r\n                {attachImages.length != 0 && (\r\n                  <div className={styles[\"attach-images\"]}>\r\n                    {attachImages.map((image, index) => {\r\n                      return (\r\n                        <div\r\n                          key={index}\r\n                          className={styles[\"attach-image\"]}\r\n                          style={{ backgroundImage: `url(\"${image}\")` }}\r\n                        >\r\n                          <div className={styles[\"attach-image-mask\"]}>\r\n                            <DeleteImageButton\r\n                              deleteImage={() => {\r\n                                setAttachImages(\r\n                                  attachImages.filter((_, i) => i !== index),\r\n                                );\r\n                              }}\r\n                            />\r\n                          </div>\r\n                        </div>\r\n                      );\r\n                    })}\r\n                  </div>\r\n                )}\r\n                <IconButton\r\n                  icon={<SendWhiteIcon />}\r\n                  text={Locale.Chat.Send}\r\n                  className={styles[\"chat-input-send\"]}\r\n                  type=\"primary\"\r\n                  onClick={() => doSubmit(userInput)}\r\n                />\r\n              </label>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport function Chat() {\r\n  const chatStore = useChatStore();\r\n  const session = chatStore.currentSession();\r\n  return <CChat key={session.id}></CChat>;\r\n}\r\n"], "names": ["SvgSendWhite", "props", "React", "SvgCopy", "SvgLoading", "SvgPrompt", "SvgBreak", "SvgClear", "SvgExcel", "SvgPdf", "SvgWord", "SvgImage", "SvgBottom", "uploadImage", "file", "body", "FormData", "append", "fetch", "UPLOAD_URL", "method", "mode", "credentials", "then", "res", "json", "code", "data", "Error", "msg", "getEmojiUrl", "unified", "style", "Avatar", "model", "jsx", "ABuIcon", "avatar", "EmojiAvat<PERSON>", "<PERSON><PERSON><PERSON>", "size", "ChatCommandPrefix", "useChatCommand", "commands", "extract", "userInput", "match", "slice", "search", "input", "desc", "Locale", "Cha<PERSON>", "Commands", "Object", "keys", "filter", "c", "startsWith", "map", "title", "content", "command", "matched", "invoke", "SearchService", "ready", "builtinEngine", "<PERSON><PERSON>", "userEngine", "count", "builtin", "allPrompts", "builtinPrompts", "init", "userPrompts", "concat", "setCollection", "remove", "id", "doc", "add", "prompt", "text", "userResults", "builtinResults", "v", "item", "usePromptStore", "createPersistStore", "counter", "prompts", "set", "get", "nanoid", "isUser", "createdAt", "Date", "now", "targetPrompt", "find", "entries", "some", "key", "getUserPrompts", "values", "sort", "a", "b", "updatePrompt", "updater", "length", "name", "StoreKey", "Prompt", "version", "migrate", "state", "newState", "JSON", "parse", "stringify", "for<PERSON>ach", "p", "onRehydrateStorage", "window", "PROMPT_URL", "log", "fetchPrompts", "en", "tw", "cn", "includes", "getLang", "promptList", "getState", "allPromptsForSearch", "reduce", "pre", "cur", "HTMLPreview", "forwardRef", "ref", "iframeRef", "useRef", "frameId", "setFrameId", "useState", "iframeHeight", "setIframeHeight", "setTitle", "useEffect", "handleMessage", "e", "height", "addEventListener", "removeEventListener", "useImperativeHandle", "reload", "useMemo", "autoHeight", "parentHeight", "srcDoc", "script", "replace", "handleOnLoad", "onLoad", "styles", "Mermaid", "<PERSON><PERSON><PERSON><PERSON>", "setHasError", "current", "mermaid", "run", "nodes", "suppressErrors", "catch", "error", "message", "viewSvgInNewWindow", "svg", "querySelector", "XMLSerializer", "serializeToString", "blob", "Blob", "type", "URL", "createObjectURL", "clsx", "cursor", "overflow", "PreCode", "previewRef", "mermaidCode", "setMermaidCode", "htmlCode", "setHtmlCode", "useWindowSize", "session", "useChatStore", "currentSession", "renderArtifacts", "useDebouncedCallback", "mermaidDom", "innerText", "htmlDom", "refText", "config", "useAppConfig", "enableArtifacts", "mask", "codeElements", "querySelectorAll", "wrapLanguages", "codeElement", "languageClass", "className", "whiteSpace", "setTimeout", "jsxs", "Fragment", "copyToClipboard", "children", "FullScreen", "IconButton", "position", "right", "top", "ReloadButtonIcon", "document", "fullscreenElement", "CustomCode", "enableCodeFold", "collapsed", "setCollapsed", "showToggle", "setShowToggle", "codeHeight", "scrollHeight", "scrollTop", "toggleCollapsed", "renderShowMoreButton", "expanded", "NewChat", "More", "maxHeight", "overflowY", "escapeBrackets", "pattern", "codeBlock", "squareBracket", "roundBracket", "tryWrapHtmlCode", "quoteStart", "lang", "newLine", "doctype", "bodyEnd", "space", "htmlEnd", "quoteEnd", "_<PERSON><PERSON><PERSON>C<PERSON>nt", "escaped<PERSON><PERSON>nt", "ReactMarkdown", "RemarkMath", "RemarkGfm", "RemarkBreaks", "RehypeKatex", "RehypeHighlight", "detect", "ignoreMissing", "pProps", "aProps", "href", "test", "target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "memo", "<PERSON><PERSON>", "mdRef", "fontSize", "fontFamily", "onContextMenu", "onDoubleClickCapture", "loading", "LoadingIcon", "localStorage", "safeLocalStorage", "useSubmitHandler", "isComposing", "onCompositionStart", "onCompositionEnd", "shouldSubmit", "keyCode", "nativeEvent", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "PromptHints", "noPrompts", "selectIndex", "setSelectIndex", "selected<PERSON>ef", "onKeyDown", "changeIndex", "delta", "stopPropagation", "preventDefault", "nextIndex", "Math", "max", "min", "scrollIntoView", "block", "selected<PERSON><PERSON><PERSON>", "at", "onPromptSelect", "i", "toString", "ClearContextDivider", "chatStore", "updateTargetSession", "clearContextIndex", "undefined", "Context", "Clear", "<PERSON><PERSON>", "ChatAction", "iconRef", "textRef", "width", "<PERSON><PERSON><PERSON><PERSON>", "full", "icon", "updateWidth", "getWidth", "dom", "getBoundingClientRect", "textWidth", "iconWidth", "onClick", "useScrollToBottom", "scrollRef", "detach", "autoScroll", "setAutoScroll", "scrollDomToBottom", "requestAnimationFrame", "scrollTo", "ChatActions", "currentModel", "modelConfig", "showUploadImage", "setShowUploadImage", "show", "isVisionModel", "setAttachImages", "setUploading", "hitBottom", "scrollToBottom", "InputActions", "ToBottom", "BottomIcon", "UploadImage", "uploading", "LoadingButtonIcon", "ImageIcon", "showPromptHints", "PromptIcon", "BreakIcon", "messages", "memoryPrompt", "DeleteImageButton", "deleteImage", "DeleteIcon", "interactiveComponents", "Map", "CChat", "inputRef", "setUserInput", "isLoading", "setIsLoading", "isScrolledToBottom", "abs", "clientHeight", "setHitBottom", "isMobileScreen", "useMobileScreen", "attachImages", "inputRows", "setInputRows", "measure", "rows", "autoGrowTextArea", "Number", "leading", "trailing", "chatCommands", "new", "newSession", "prev", "nextSession", "next", "clear", "fork", "forkSession", "del", "deleteSession", "currentSessionIndex", "SEARCH_TEXT_LIMIT", "onInput", "n", "trim", "setPromptHints", "searchText", "onSearch", "doSubmit", "isEmpty", "matchCommand", "onUserInput", "setLastInput", "focus", "push", "createMessage", "date", "toLocaleString", "role", "excel_url", "url", "interactive", "template", "orderNo", "roll", "weight", "statusName", "time", "price", "paymentStatus", "events", "console", "customerName", "level", "debt", "note", "mostPurchased", "lastOrder", "follow<PERSON><PERSON><PERSON><PERSON>", "salesperson", "monthlyPurchase", "stopTiming", "REQUEST_TIMEOUT_MS", "m", "isError", "getTime", "streaming", "prettyObject", "onInputKeyDown", "lastInput", "promptHints", "onRightClick", "selectOrCopy", "currentTarget", "getMessageTextContent", "deleteMessage", "msgId", "onDelete", "onResend", "resendingIndex", "findIndex", "userMessage", "botMessage", "textContent", "images", "getMessageImages", "context", "hideContext", "BOT_HELLO", "<PERSON><PERSON><PERSON>", "assign", "renderMessages", "preview", "msgRenderIndex", "_setMsgRenderIndex", "CHAT_PAGE_SIZE", "setMsgRenderIndex", "newIndex", "endRenderIndex", "onChatBodyScroll", "bottomHeight", "edgeThreshold", "isTouchTopEdge", "isTouchBottomEdge", "isHitBottom", "prevPageMsgIndex", "nextPageMsgIndex", "autoFocus", "UNFINISHED_INPUT", "mayBeUnfinishedInput", "getItem", "removeItem", "setItem", "value", "handlePaste", "useCallback", "event", "items", "clipboardData", "kind", "getAsFile", "Promise", "rej", "imagesData", "dataUrl", "images<PERSON><PERSON>th", "splice", "fileInput", "createElement", "accept", "multiple", "onchange", "files", "click", "promptStore", "matchedChatCommand", "matchedPrompts", "onFileClick", "open", "encodeURIComponent", "getInteractiveComponent", "templateName", "has", "Component", "dynamic", "__variableDynamicImportRuntimeHelper", "ssr", "WrappedComponent", "styleElement", "head", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handleKeyDown", "toLowerCase", "copyCodeButton", "lastNonUserMessage", "pop", "lastMessageContent", "chat", "blur", "isContext", "showActions", "showTyping", "shouldShowClearContextDivider", "DEFAULT_CONFIG", "Actions", "Retry", "ResetIcon", "Delete", "Copy", "CopyIcon", "tools", "Typing", "tool", "errorMsg", "ConfirmIcon", "CloseIcon", "function", "getMessageInteractives", "index", "InteractiveComponent", "getMessageFiles", "ExcelIcon", "WordIcon", "PDFIcon", "image", "audio_url", "IsContext", "Input", "backgroundImage", "_", "SendWhiteIcon", "Send"], "mappings": ";;;;;;;;;;;;;;;;GACMA,KAAe,CAACC,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAG,CAAA,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,+BAA8B,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAM,GAAoB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,MAAM,kBAAiB,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC3wB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,6CAA6C,WAAW,gFAA+E,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC7N,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,kBAAkB,WAAW,sFAAuF,CAAA,CAAC,CAAC,CAAC,GCVvHC,KAAU,CAACF,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAG,CAAA,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,+BAA8B,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAM,GAAoB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,MAAM,kBAAiB,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACtwB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,6GAA6G,WAAW,gEAA+D,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC7Q,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,oIAAoI,WAAW,gEAAiE,CAAA,CAAC,CAAC,CAAC,GCVnNE,KAAa,CAACH,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,CAAA,GAAI,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,EAAE,IAAI,kBAAkB,OAAO,QAAQ,QAAQ,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM,QAAQ,QAAQ,QAAQ,OAAO,CAAA,GAAI,WAAW,GAAI,CAAA,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,WAAW,gBAAgB,OAAO,CAAE,EAAA,GAAoB,gBAAAA,EAAM,cAAc,SAAS,MAAM,SAAS,GAAmB,gBAAAA,EAAM,cAAc,UAAU,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,OAAO,MAAM,QAAQ,IAAI,SAAS,WAAW,GAAE,GAAoB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,KAAK,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,GAAG,aAAa,cAAc,IAAI,GAAG,QAAQ,UAAW,CAAA,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,gBAAgB,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,GAAG,aAAa,cAAc,IAAI,GAAG,QAAQ,SAAQ,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,UAAU,EAAE,IAAI,GAAG,IAAI,GAAG,GAAG,QAAQ,MAAM,QAAQ,aAAa,KAAK,IAAI,SAAS,WAAW,GAAI,GAAkB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,KAAK,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,KAAK,aAAa,cAAc,IAAI,KAAK,QAAQ,YAAa,CAAA,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,gBAAgB,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,KAAK,aAAa,cAAc,IAAI,KAAK,QAAQ,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,UAAU,EAAE,IAAI,IAAI,IAAI,GAAG,GAAG,OAAO,MAAM,QAAQ,IAAI,SAAS,WAAW,MAAsB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,KAAK,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,GAAG,aAAa,cAAc,IAAI,GAAG,QAAQ,UAAS,CAAE,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,eAAe,gBAAgB,OAAO,MAAM,UAAU,UAAU,KAAK,QAAQ,MAAM,GAAG,aAAa,cAAc,IAAI,GAAG,QAAQ,SAAU,CAAA,CAAC,CAAC,CAAC,GCA37DG,KAAY,CAACJ,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAG,CAAA,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,+BAA8B,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAM,GAAoB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,MAAM,kBAAiB,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACxwB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,kbAAkb,WAAW,gGAA+F,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAClnB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,kBAAkB,WAAW,oFAAqF,CAAA,CAAC,CAAC,CAAC,GCVrHI,KAAW,CAACL,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,KAAK,EAAE,SAAS,EAAG,GAAkB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,kEAAmE,GAAkB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAClf,MAAM;AAAA,EACN,SAAS;AACX,GAAG,GAAG,+mBAAgnB,CAAA,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACpsB,MAAM;AAAA,EACN,SAAS;AACX,GAAG,GAAG,yzGAAyzG,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,uDAAuE,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAQ,GAAkB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAW,CAAA,CAAC,CAAC,CAAC,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAC,CAAE,CAAC,CAAC,GCNjuHK,KAAW,CAACN,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAG,CAAA,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,+BAA8B,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAM,GAAoB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,MAAM,kBAAiB,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACvwB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,yCAAyC,WAAW,gFAA+E,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACzN,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,eAAe,WAAW,gFAA+E,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC/L,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,eAAe,WAAW,+EAA8E,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC9L,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,mBAAmB,WAAW,+BAAgC,CAAA,CAAC,CAAC,CAAC,GCpBjEM,KAAW,CAACP,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,GAAG,eAAe,WAAW,QAAQ,SAAS,iBAAiB,OAAO,8BAA8B,QAAQ,MAAM,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,EAAE,GAAG,8HAA8H,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,25BAA25B,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,wEAAwE,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,sDAAsD,MAAM,WAAW,QAAQ,KAAI,CAAE,CAAC,GCA3uDO,KAAS,CAACR,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,GAAG,eAAe,WAAW,QAAQ,SAAS,iBAAiB,OAAO,8BAA8B,QAAQ,MAAM,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,EAAE,GAAG,8HAA8H,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,wEAAwE,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,0DAA0D,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,ioBAAioB,MAAM,WAAW,QAAQ,KAAI,CAAE,CAAC,GCAn9CQ,KAAU,CAACT,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,GAAG,eAAe,WAAW,QAAQ,SAAS,iBAAiB,OAAO,8BAA8B,QAAQ,MAAM,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,EAAE,GAAG,4HAA4H,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,0EAA0E,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,0DAA0D,MAAM,WAAW,QAAQ,KAAI,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,+uBAA+uB,MAAM,WAAW,QAAQ,KAAI,CAAE,CAAC,GCAlkDS,KAAW,CAACV,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,MAAM,QAAQ,QAAQ,OAAO,OAAO,OAAO,UAAU,YAAY,OAAO,CAAA,GAAI,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,EAAE,IAAI,kBAAkB,OAAO,QAAQ,QAAQ,QAAQ,GAAG,GAAG,GAAG,GAAG,MAAM,QAAQ,QAAQ,OAAQ,CAAA,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,WAAW,gBAAgB,OAAO,CAAE,EAAA,GAAoB,gBAAAA,EAAM,cAAc,SAAS,MAAM,SAAS,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,IAAI,SAAS,WAAW,IAAI,MAAM,QAAQ,aAAa,EAAG,GAAkB,gBAAAA,EAAM,cAAc,WAAW,EAAE,QAAQ,kOAAkO,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAG,CAAA,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,QAAQ,oOAAoO,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAG,CAAA,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,QAAQ,wOAAwO,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAG,CAAA,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,QAAQ,sOAAsO,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAG,CAAA,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,GAAG,2WAA2W,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAC,CAAE,GAAmB,gBAAAA,EAAM,cAAc,WAAW,EAAE,QAAQ,sQAAsQ,IAAI,SAAS,MAAM,QAAQ,aAAa,EAAC,CAAE,CAAC,CAAC,CAAC,GCA5tFU,KAAY,CAACX,MAA0B,gBAAAC,EAAM,cAAc,OAAO,EAAE,OAAO,8BAA8B,YAAY,gCAAgC,OAAO,OAAO,QAAQ,OAAO,MAAM,QAAQ,SAAS,aAAa,GAAGD,EAAO,GAAkB,gBAAAC,EAAM,cAAc,QAAQ,MAAsB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,UAAU,OAAO,IAAI,QAAQ,IAAI,GAAG,GAAG,GAAG,EAAG,CAAA,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,SAAS,GAAG,WAAW,+BAA8B,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,aAAa,MAAM,OAAM,GAAoB,gBAAAA,EAAM,cAAc,OAAO,EAAE,WAAW,UAAS,CAAE,CAAC,GAAmB,gBAAAA,EAAM,cAAc,KAAK,EAAE,MAAM,kBAAiB,GAAoB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EACxwB,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,gBAAgB,WAAW,+BAA8B,CAAE,GAAmB,gBAAAA,EAAM,cAAc,QAAQ,EAAE,IAAI,oBAAoB,OAAO;AAAA,EAC/I,QAAQ;AAAA,EACR,aAAa;AAAA,EACb,eAAe;AAAA,EACf,iBAAiB;AACnB,GAAG,GAAG,gBAAgB,WAAW,+BAAgC,CAAA,CAAC,CAAC,CAAC;ACkH7D,SAASW,GAAYC,GAA6B;AAKjDC,QAAAA,IAAO,IAAIC,SAAS;AACrBC,SAAAA,EAAAA,OAAO,QAAQH,CAAI,GACjBI,MAAMC,IAAY;AAAA,IACvBC,QAAQ;AAAA,IACRL,MAAAA;AAAAA,IACAM,MAAM;AAAA,IACNC,aAAa;AAAA,EAAA,CACd,EACEC,KAAMC,CAAAA,MAAQA,EAAIC,MAAM,EACxBF,KAAMC,CAAQA,MAAA;AAEb,SAAIA,KAAAA,gBAAAA,EAAKE,SAAQ,MAAKF,KAAAA,QAAAA,EAAKG;AACzB,aAAOH,KAAAA,gBAAAA,EAAKG;AAEd,UAAMC,MAAM,iBAAiBJ,KAAAA,gBAAAA,EAAKK,GAAG,EAAE;AAAA,EAAA,CACxC;AACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzIgBC,SAAAA,GAAYC,GAAiBC,GAAmB;AAIvD,SAAA,8DAA8DA,CAAK,OAAOD,CAAO;AAC1F;AAkBO,SAASE,GAAOhC,GAAyC;AAC9D,SAAIA,EAAMiC,QAENC,gBAAAA,MAAC,SAAI,WAAU,WACb,gCAACC,IAAQ,EAAA,WAAU,eAAgB,EACrC,CAAA,IAKFD,gBAAAA,EAAAA,IAAC,OAAI,EAAA,WAAU,eACZlC,UAAAA,EAAMoC,UAAUF,gBAAAA,EAAA,IAACG,IAAY,EAAA,QAAQrC,EAAMoC,WAC9C;AAEJ;AAEO,SAASC,GAAYrC,GAA0C;AAElE,SAAAkC,gBAAAA,MAACI,MACC,SAAStC,EAAMoC,QACf,MAAMpC,EAAMuC,QAAQ,IACpB,aAAAV,GACA,CAAA;AAEN;ACXO,MAAMW,KAAoB;AAEjBC,SAAAA,GAAeC,IAAyB,IAAI;AAC1D,WAASC,EAAQC,GAAmB;AAElC,WADcA,EAAUC,MAAML,EAAiB,IAEtCI,EAAUE,MAAM,CAAC,IAEnBF;AAAAA,EAAAA;AAGT,WAASG,EAAOH,GAAmB;AAC3BI,UAAAA,IAAQL,EAAQC,CAAS,GACzBK,IAAOC,EAAOC,KAAKC;AACzB,WAAOC,OAAOC,KAAKZ,CAAQ,EACxBa,OAAQC,CAAAA,MAAMA,EAAEC,WAAWT,CAAK,CAAC,EACjCU,IAAKF,CAAOA,OAAA;AAAA,MACXG,OAAOV,EAAKO,CAAuB;AAAA,MACnCI,SAAS,MAAMJ;AAAAA,IAAAA,EACf;AAAA,EAAA;AAGN,WAASX,EAAMD,GAAmB;AAC1BiB,UAAAA,IAAUlB,EAAQC,CAAS,GAC3BkB,IAAU,OAAOpB,EAASmB,CAAO,KAAM;AAEtC,WAAA;AAAA,MACLC,SAAAA;AAAAA,MACAC,QAAQA,MAAMD,KAAWpB,EAASmB,CAAO,EAAGjB,CAAS;AAAA,IACvD;AAAA,EAAA;AAGK,SAAA;AAAA,IAAEC,OAAAA;AAAAA,IAAOE,QAAAA;AAAAA,EAAO;AACzB;AChEO,MAAMiB,IAAgB;AAAA,EAC3BC,OAAO;AAAA,EACPC,eAAe,IAAIC,GAAa,IAAI;AAAA,IAAEb,MAAM,CAAC,OAAO;AAAA,EAAA,CAAG;AAAA,EACvDc,YAAY,IAAID,GAAa,IAAI;AAAA,IAAEb,MAAM,CAAC,OAAO;AAAA,EAAA,CAAG;AAAA,EACpDe,OAAO;AAAA,IACLC,SAAS;AAAA,EACX;AAAA,EACAC,YAAY,CAAA;AAAA,EACZC,gBAAgB,CAAA;AAAA,EAEhBC,KAAKD,GAA0BE,GAAuB;AACpD,IAAI,KAAKT,UAGJM,KAAAA,aAAaG,EAAYC,OAAOH,CAAc,GAC9CA,KAAAA,iBAAiBA,EAAe1B,MAAM,GACtCoB,KAAAA,cAAcU,cAAcJ,CAAc,GAC1CJ,KAAAA,WAAWQ,cAAcF,CAAW,GACzC,KAAKT,QAAQ;AAAA,EACf;AAAA,EAEAY,OAAOC,GAAY;AACjB,SAAKV,WAAWS,OAAQE,CAAQA,MAAAA,EAAID,OAAOA,CAAE;AAAA,EAC/C;AAAA,EAEAE,IAAIC,GAAgB;AACbb,SAAAA,WAAWY,IAAIC,CAAM;AAAA,EAC5B;AAAA,EAEAlC,OAAOmC,GAAc;AACnB,UAAMC,IAAc,KAAKf,WAAWrB,OAAOmC,CAAI,GACzCE,IAAiB,KAAKlB,cAAcnB,OAAOmC,CAAI;AACrD,WAAOC,EAAYR,OAAOS,CAAc,EAAE1B,IAAK2B,CAAAA,MAAMA,EAAEC,IAAI;AAAA,EAAA;AAE/D,GAEaC,KAAiBC,GAC5B;AAAA,EACEC,SAAS;AAAA,EACTC,SAAS,CAAA;AACX,GAEA,CAACC,GAAKC,OAAS;AAAA,EACbZ,IAAIC,GAAgB;AACZS,UAAAA,IAAUE,IAAMF;AACtBT,WAAAA,EAAOH,KAAKe,EAAO,GACnBZ,EAAOa,SAAS,IACTC,EAAAA,YAAYC,KAAKC,IAAI,GACpBhB,EAAAA,EAAOH,EAAE,IAAIG,GAErBU,EAAI,OAAO;AAAA,MACTD,SAAAA;AAAAA,IAAAA,EACA,GAEKT,EAAOH;AAAAA,EAChB;AAAA,EAEAc,IAAId,GAAY;AACd,UAAMoB,IAAeN,IAAMF,QAAQZ,CAAE;AAErC,WAAKoB,KACIlC,EAAcQ,eAAe2B,KAAMd,CAAMA,MAAAA,EAAEP,OAAOA,CAAE;AAAA,EAI/D;AAAA,EAEAD,OAAOC,GAAY;AACXY,UAAAA,IAAUE,IAAMF;AACtB,WAAOA,EAAQZ,CAAE,GAEVsB,OAAAA,QAAQV,CAAO,EAAEW,KAAK,CAAC,CAACC,GAAKrB,CAAM,MACpCA,EAAOH,OAAOA,KAChB,OAAOY,EAAQY,CAAG,GACX,MAEF,EACR,GAEDtC,EAAca,OAAOC,CAAE,GAEvBa,EAAI,OAAO;AAAA,MACTD,SAAAA;AAAAA,MACAD,SAASG,EAAI,EAAEH,UAAU;AAAA,IAAA,EACzB;AAAA,EACJ;AAAA,EAEAc,iBAAiB;AACf,UAAM7B,IAAcrB,OAAOmD,OAAOZ,EAAMF,EAAAA,WAAW,EAAE;AACrDhB,WAAAA,EAAY+B,KAAK,CAACC,GAAGC,MACnBA,EAAE7B,MAAM4B,EAAE5B,KAAK6B,EAAEZ,YAAYW,EAAEX,YAAY,CAC7C,GACOrB;AAAAA,EACT;AAAA,EAEAkC,aAAa9B,GAAY+B,GAAmC;AAC1D,UAAM5B,IAASW,EAAAA,EAAMF,QAAQZ,CAAE,KAAK;AAAA,MAClCnB,OAAO;AAAA,MACPC,SAAS;AAAA,MACTkB,IAAAA;AAAAA,IACF;AAEAd,IAAAA,EAAca,OAAOC,CAAE,GACvB+B,EAAQ5B,CAAM;AACRS,UAAAA,IAAUE,IAAMF;AACtBA,IAAAA,EAAQZ,CAAE,IAAIG,GACdU,EAAI,OAAO;AAAA,MAAED,SAAAA;AAAAA,IAAAA,EAAU,GACvB1B,EAAcgB,IAAIC,CAAM;AAAA,EAC1B;AAAA,EAEAlC,OAAOmC,GAAc;AACfA,WAAAA,EAAK4B,WAAW,IAEX,KAAKP,eAAAA,EAAiB5B,OAAOX,EAAcQ,cAAc,IAE3DR,EAAcjB,OAAOmC,CAAI;AAAA,EAAA;AAEpC,IACA;AAAA,EACE6B,MAAMC,GAASC;AAAAA,EACfC,SAAS;AAAA,EAETC,QAAQC,GAAOF,GAAS;AACtB,UAAMG,IAAWC,KAAKC,MAAMD,KAAKE,UAAUJ,CAAK,CAAC;AAIjD,WAAIF,IAAU,KACLV,OAAAA,OAAOa,EAAS3B,OAAO,EAAE+B,QAASC,CAAOA,MAAAA,EAAE5C,KAAKe,GAAS,GAG3DwB;AAAAA,EACT;AAAA,EAEAM,mBAAmBP,GAAO;AAEpB,QAAA,OAAOQ,SAAW;AACpB;AAOIC,UAJa,gBAIH,EACbvG,KAAMC,CAAAA,MAAQA,EAAIC,MAAM,EACxBF,KAAMC,CAAQA,MAAA;AACLuG,cAAAA,IAAI,OAAOvG,CAAG;AAEtB,UAAIwG,IAAe,CAACxG,EAAIyG,IAAIzG,EAAI0G,IAAI1G,EAAI2G,EAAE;AAC1C,MAAI7E,OAAOC,KAAK/B,CAAG,EAAE4G,SAASC,GAAAA,CAAS,MACrCL,IAAe,CAACxG,EAAI6G,GAAQ,CAAC,CAAC;AAEhC,YAAM5D,IAAiBuD,EAAarE,IAAI,CAAC2E,MAChCA,EAAW3E,IAChB,CAAC,CAACC,GAAOC,CAAO,OACb;AAAA,QACCkB,IAAIe,EAAO;AAAA,QACXlC,OAAAA;AAAAA,QACAC,SAAAA;AAAAA,QACAmC,WAAWC,KAAKC,IAAI;AAAA,MAAA,EAE1B,CACD,GAEKvB,IAAca,GAAe+C,SAAAA,EAAW/B,oBAAoB,CAAE,GAE9DgC,IAAsB/D,EACzBgE,OAAO,CAACC,GAAKC,MAAQD,EAAI9D,OAAO+D,CAAG,GAAG,CAAE,CAAA,EACxCnF,OAAQ8B,OAAM,CAAC,CAACA,EAAE1B,SAAS,CAAC,CAAC0B,EAAEzB,OAAO;AAC3BS,MAAAA,EAAAA,MAAMC,UAClB/C,EAAIyG,GAAGlB,SAASvF,EAAI2G,GAAGpB,SAASvF,EAAI0G,GAAGnB,QAC3BrC,EAAAA,KAAK8D,GAAqB7D,CAAW;AAAA,IAAA,CACpD;AAAA,EAAA;AAEP,CACF;;;;;;;;;;GCxKaiE,KAAcC,GACzB,SAAqB5I,GAAO6I,GAAK;AACzBC,QAAAA,IAAYC,EAA0B,IAAI,GAC1C,CAACC,GAASC,CAAU,IAAIC,EAAiBrD,GAAQ,GACjD,CAACsD,GAAcC,CAAe,IAAIF,EAAS,GAAG,GAC9C,CAACvF,GAAO0F,CAAQ,IAAIH,EAAS,EAAE;AASrCI,EAAAA,EAAU,MAAM;AACRC,UAAAA,IAAgBA,CAACC,MAAW;AAC1B,YAAA;AAAA,QAAE1E,IAAAA;AAAAA,QAAI2E,QAAAA;AAAAA,QAAQ9F,OAAAA;AAAAA,UAAU6F,EAAE9H;AAChC2H,MAAAA,EAAS1F,CAAK,GACVmB,KAAMkE,KACRI,EAAgBK,CAAM;AAAA,IAE1B;AACOC,kBAAAA,iBAAiB,WAAWH,CAAa,GACzC,MAAM;AACJI,aAAAA,oBAAoB,WAAWJ,CAAa;AAAA,IACrD;AAAA,EAAA,GACC,CAACP,CAAO,CAAC,GAEZY,GAAoBf,GAAK,OAAO;AAAA,IAC9BgB,QAAQA,MAAM;AACZZ,MAAAA,EAAWpD,GAAQ;AAAA,IAAA;AAAA,EACrB,EACA;AAEI4D,QAAAA,IAASK,EAAQ,MAAM;AAC3B,QAAI,CAAC9J,EAAM+J,WAAY,QAAO/J,EAAMyJ,UAAU;AAC1C,QAAA,OAAOzJ,EAAMyJ,UAAW;AAC1B,aAAOzJ,EAAMyJ;AAETO,UAAAA,IAAehK,EAAMyJ,UAAU;AACrC,WAAON,IAAe,KAAKa,IACvBA,IACAb,IAAe;AAAA,EAAA,GAClB,CAACnJ,EAAM+J,YAAY/J,EAAMyJ,QAAQN,CAAY,CAAC,GAE3Cc,IAASH,EAAQ,MAAM;AACrBI,UAAAA,IAAS,sHAAsHlB,CAAO;AAC5I,WAAIhJ,EAAMyB,KAAK0G,SAAS,iBAAiB,KACvCnI,EAAMyB,KAAK0I,QAAQ,mBAAmB,oBAAoBD,CAAM,GAE3DA,IAASlK,EAAMyB;AAAAA,EACrB,GAAA,CAACzB,EAAMyB,MAAMuH,CAAO,CAAC,GAElBoB,IAAeA,MAAM;AACzB,IAAIpK,KAAAA,QAAAA,EAAOqK,UACTrK,EAAMqK,OAAO1G,CAAK;AAAA,EAEtB;AAGE,SAAAzB,gBAAAA,MAAC,UACC,EAAA,WAAWoI,GAAO,kBAAkB,GAEpC,KAAKxB,GACL,SAAQ,0CACR,OAAO;AAAA,IAAEW,QAAAA;AAAAA,EACT,GAAA,QAAAQ,GACA,QAAQG,EAAAA,GALHpB,CAML;AAEN,CACF;AClEO,SAASuB,GAAQvK,GAAyB;AACzC6I,QAAAA,IAAME,EAAuB,IAAI,GACjC,CAACyB,GAAUC,CAAW,IAAIvB,EAAS,EAAK;AAE9CI,EAAAA,EAAU,MAAM;AACVtJ,IAAAA,EAAMyB,QAAQoH,EAAI6B,WACpBC,GACGC,IAAI;AAAA,MACHC,OAAO,CAAChC,EAAI6B,OAAO;AAAA,MACnBI,gBAAgB;AAAA,IAAA,CACjB,EACAC,MAAOvB,CAAMA,MAAA;AACZiB,MAAAA,EAAY,EAAI,GACRO,QAAAA,MAAM,cAAcxB,EAAEyB,OAAO;AAAA,IAAA,CACtC;AAAA,EACL,GAEC,CAACjL,EAAMyB,IAAI,CAAC;AAEf,WAASyJ,IAAqB;;AAC5B,UAAMC,KAAMtC,IAAAA,EAAI6B,YAAJ7B,gBAAAA,EAAauC,cAAc;AACvC,QAAI,CAACD,EAAK;AACV,UAAMjG,IAAO,IAAImG,gBAAgBC,kBAAkBH,CAAG,GAChDI,IAAO,IAAIC,KAAK,CAACtG,CAAI,GAAG;AAAA,MAAEuG,MAAM;AAAA,IAAA,CAAiB;AACxCC,IAAAA,GAAAA,IAAIC,gBAAgBJ,CAAI,CAAC;AAAA,EAAA;AAG1C,SAAIf,IACK,6BAIN,OACC,EAAA,WAAWoB,EAAK,WAAW,SAAS,GACpC,OAAO;AAAA,IACLC,QAAQ;AAAA,IACRC,UAAU;AAAA,EAAA,GAEZ,KAAAjD,GACA,SAAS,MAAMqC,EAAmB,GAEjClL,YAAMyB,MACT;AAEJ;AAEO,SAASsK,GAAQ/L,GAA0B;;AAC1C6I,QAAAA,IAAME,EAAuB,IAAI,GACjCiD,IAAajD,EAA0B,IAAI,GAC3C,CAACkD,GAAaC,CAAc,IAAIhD,EAAS,EAAE,GAC3C,CAACiD,GAAUC,CAAW,IAAIlD,EAAS,EAAE,GACrC;AAAA,IAAEO,QAAAA;AAAAA,MAAW4C,GAAc,GAE3BC,IADYC,EAAa,EACLC,eAAe,GAEnCC,IAAkBC,GAAqB,MAAM;;AAC7C,QAAA,CAAC7D,EAAI6B,QAAS;AAClB,UAAMiC,IAAa9D,EAAI6B,QAAQU,cAAc,uBAAuB;AACpE,IAAIuB,KACFT,EAAgBS,EAA2BC,SAAS;AAEtD,UAAMC,IAAUhE,EAAI6B,QAAQU,cAAc,oBAAoB,GACxD0B,KAAUjE,IAAAA,EAAI6B,QAAQU,cAAc,MAAM,MAAhCvC,gBAAAA,EAAmC+D;AACnD,IAAIC,IACFT,EAAaS,EAAwBD,SAAS,KAE9CE,KAAAA,QAAAA,EAASrJ,WAAW,gBACpBqJ,KAAAA,QAAAA,EAASrJ,WAAW,WACpBqJ,KAAAA,QAAAA,EAASrJ,WAAW,aAEpB2I,EAAYU,CAAO;AAAA,KAEpB,GAAG,GAEAC,IAASC,GAAa,GACtBC,MACJX,IAAAA,EAAQY,SAARZ,gBAAAA,EAAcW,qBAAoB,MAASF,EAAOE;AAGpD3D,SAAAA,EAAU,MAAM;AACd,QAAIT,EAAI6B,SAAS;AACf,YAAMyC,IAAetE,EAAI6B,QAAQ0C,iBAC/B,MACF,GACMC,IAAgB,CACpB,IACA,MACA,YACA,QACA,OACA,aACA,OACA,OAAO;AAETF,MAAAA,EAAa1F,QAAS6F,CAAgBA,MAAA;AACpC,cAAMC,IAAgBD,EAAYE,UAAU3K,MAAM,gBAAgB,GAC5DkE,IAAOwG,IAAgBA,EAAc,CAAC,IAAI;AAC5CF,QAAAA,EAAclF,SAASpB,CAAI,MAC7BuG,EAAYvL,MAAM0L,aAAa;AAAA,MACjC,CACD,GACDC,WAAWjB,GAAiB,CAAC;AAAA,IAAA;AAAA,EAEjC,GAAG,EAAE,GAIDkB,gBAAAA,EAAA,KAAAC,YAAA,EAAA,UAAA;AAAA,IAAAD,gBAAAA,EAAAA,KAAC,SAAI,KAAA9E,GACH,UAAA;AAAA,MAAA3G,gBAAAA,EAAAA,IAAC,QACC,EAAA,WAAU,oBACV,SAAS,MAAM;;AACb,QAAI2G,EAAI6B,WACNmD,KACEhF,IAAAA,EAAI6B,QAAQU,cAAc,MAAM,MAAhCvC,gBAAAA,EAAmC+D,cAAa,EAClD;AAAA,MACF,GAEH;AAAA,MACA5M,EAAM8N;AAAAA,IAAAA,GACT;AAAA,IACC7B,EAAYnF,SAAS,2BACnByD,IAAQ,EAAA,MAAM0B,KAAkBA,CAAY;AAAA,IAE9CE,EAASrF,SAAS,KAAKmG,4BACrBc,IAAW,EAAA,WAAU,gBAAe,OAAO,IAK1C,UAAA;AAAA,MAAA7L,gBAAAA,MAAC8L,MACC,OAAO;AAAA,QAAEC,UAAU;AAAA,QAAYC,OAAO;AAAA,QAAKC,KAAK;AAAA,MAAA,GAChD,UAAQ,IACR,4BAAOC,IAAmB,CAAA,CAAA,GAC1B,QAAM,IACN,SAAS,MAAMpC;;AAAAA,gBAAAA,IAAAA,EAAWtB,YAAXsB,gBAAAA,EAAoBnC;AAAAA,QACnC,CAAA;AAAA,MACD3H,gBAAAA,EAAA,IAAAyG,IAAA,EACC,KAAKqD,GACL,MAAMG,GACN,YAAY,CAACkC,SAASC,mBACtB,QAASD,SAASC,oBAA0B7E,IAAN,IACtC,CAAA;AAAA,IAAA,EACJ,CAAA;AAAA,EAAA,GAEJ;AAEJ;AAEA,SAAS8E,GAAWvO,GAA8C;;AAE1DsM,QAAAA,IADYC,EAAa,EACLC,eAAe,GACnCO,IAASC,GAAa,GACtBwB,MACJlC,IAAAA,EAAQY,SAARZ,gBAAAA,EAAckC,oBAAmB,MAASzB,EAAOyB,gBAE7C3F,IAAME,EAAuB,IAAI,GACjC,CAAC0F,GAAWC,CAAY,IAAIxF,EAAS,EAAI,GACzC,CAACyF,GAAYC,CAAa,IAAI1F,EAAS,EAAK;AAElDI,EAAAA,EAAU,MAAM;AACd,QAAIT,EAAI6B,SAAS;AACTmE,YAAAA,IAAahG,EAAI6B,QAAQoE;AAC/BF,MAAAA,EAAcC,IAAa,GAAG,GAC1BnE,EAAAA,QAAQqE,YAAYlG,EAAI6B,QAAQoE;AAAAA,IAAAA;AAAAA,EACtC,GACC,CAAC9O,EAAM8N,QAAQ,CAAC;AAEnB,QAAMkB,IAAkBA,MAAM;AACdP,IAAAA,EAAAA,CAAAA,MAAc,CAACA,CAAS;AAAA,EACxC,GACMQ,IAAuBA,MACvBN,KAAcH,KAAkBC,IAE/BvM,gBAAAA,EAAA,IAAA,OAAA,EACC,WAAW0J,EAAK,oBAAoB;AAAA,IAClC6C,WAAAA;AAAAA,IACAS,UAAU,CAACT;AAAAA,EACZ,CAAA,GAED,UAACvM,gBAAAA,MAAA,UAAA,EAAO,SAAS8M,GAAkB9L,UAAAA,EAAOiM,QAAQC,KAAAA,CAAK,EACzD,CAAA,IAGG;AAET,SAEIzB,gBAAAA,EAAA,KAAAC,YAAA,EAAA,UAAA;AAAA,IAAA1L,gBAAAA,MAAC,UACC,WAAW0J,EAAK5L,KAAAA,gBAAAA,EAAOwN,SAAS,GAChC,KAAA3E,GACA,OAAO;AAAA,MACLwG,WAAWb,KAAkBC,IAAY,UAAU;AAAA,MACnDa,WAAW;AAAA,IAAA,GAGZtP,YAAM8N,UACT;AAAA,IAECmB,EAAqB;AAAA,EAAA,GACxB;AAEJ;AAEA,SAASM,GAAerK,GAAc;AACpC,QAAMsK,IACJ;AACF,SAAOtK,EAAKiF,QACVqF,GACA,CAAC3M,GAAO4M,GAAWC,GAAeC,MAC5BF,MAEOC,IACF,KAAKA,CAAa,OAChBC,IACF,IAAIA,CAAY,MAElB9M,EAEX;AACF;AAEA,SAAS+M,GAAgB1K,GAAc;AAE9BA,SAAAA,EACJiF,QACC,6CACA,CAACtH,GAAOgN,GAAYC,GAAMC,GAASC,MACzBH,IAAuChN,IAA1B,gBAAgBmN,CAEzC,EACC7F,QACC,8DACA,CAACtH,GAAOoN,GAASC,GAAOC,GAASJ,GAASK,MAChCA,IAAmDvN,IAAxCoN,IAAUC,IAAQC,IAAU,SAEnD;AACJ;AAEA,SAASE,GAAiBrQ,GAA4B;AAE9CsQ,QAAAA,IAAiBxG,EAAQ,MACtB8F,GAAgBL,GAAevP,EAAM4D,OAAO,CAAC,GACnD,CAAC5D,EAAM4D,OAAO,CAAC;AAElB,SACG1B,gBAAAA,MAAAqO,IAAA,EACC,eAAe,CAACC,IAAYC,IAAWC,EAAY,GACnD,eAAe,CACbC,IACA,CACEC,IACA;AAAA,IACEC,QAAQ;AAAA,IACRC,eAAe;AAAA,EAAA,CAChB,CACF,GAEH,YAAY;AAAA,IACVrI,KAAKsD;AAAAA,IACLtK,MAAM8M;AAAAA,IACN7G,GAAIqJ,CAAWA,OACLjJ,QAAAA,IAAI,UAASiJ,CAAM,GACnB7O,gBAAAA,EAAAA,IAAA,KAAA,EAAM6O,GAAAA,GAAQ,KAAI,QAAS;AAAA,IAErCrK,GAAIsK,CAAWA,MAAA;AACPC,YAAAA,IAAOD,EAAOC,QAAQ;AACxB,UAAA,wBAAwBC,KAAKD,CAAI;AAEjC,eAAA/O,gBAAAA,MAAC,YACC,UAACA,gBAAAA,EAAAA,IAAA,SAAA,EAAM,UAAQ,IAAC,KAAK+O,GAAM,EAC7B,CAAA;AAGA,UAAA,qCAAqCC,KAAKD,CAAI;AAE9C,eAAA/O,gBAAAA,EAAA,IAAC,SAAM,EAAA,UAAQ,IAAC,OAAM,SACpB,UAACA,gBAAAA,EAAAA,IAAA,UAAA,EAAO,KAAK+O,EAAAA,CAAQ,EACvB,CAAA;AAIJ,YAAME,IADa,QAAQD,KAAKD,CAAI,IACR,UAAUD,EAAOG,UAAU;AACvD,aAAQjP,gBAAAA,EAAA,IAAA,KAAA,EAAE,GAAI8O,GAAQ,QAAAG,EAAkB,CAAA;AAAA,IAAA;AAAA,EAC1C,GAGDb,UACHA,GAAA;AAEJ;AAEac,MAAAA,KAAkBnR,GAAMoR,KAAKhB,EAAgB;AAEnD,SAASiB,GACdtR,GAQA;AACMuR,QAAAA,IAAQxI,EAAuB,IAAI;AAEzC,SACG7G,gBAAAA,EAAA,IAAA,OAAA,EACC,WAAU,iBACV,OAAO;AAAA,IACLsP,UAAU,GAAGxR,EAAMwR,YAAY,EAAE;AAAA,IACjCC,YAAYzR,EAAMyR,cAAc;AAAA,EAClC,GACA,KAAKF,GACL,eAAevR,EAAM0R,eACrB,sBAAsB1R,EAAM2R,sBAC5B,KAAI,QAEH3R,UAAM4R,EAAAA,gCACJC,gCAEAT,IAAgB,EAAA,SAASpR,EAAM4D,QAAAA,GAEpC,CAAA;AAEJ;ACpRA,MAAMkO,KAAeC,GAAiB;AAOtC,SAASC,KAAmB;AACpBC,QAAAA,IAAclJ,EAAO,EAAK;AAEhCO,SAAAA,EAAU,MAAM;AACd,UAAM4I,IAAqBA,MAAM;AAC/BD,MAAAA,EAAYvH,UAAU;AAAA,IACxB,GACMyH,IAAmBA,MAAM;AAC7BF,MAAAA,EAAYvH,UAAU;AAAA,IACxB;AAEOhB,kBAAAA,iBAAiB,oBAAoBwI,CAAkB,GACvDxI,OAAAA,iBAAiB,kBAAkByI,CAAgB,GAEnD,MAAM;AACJxI,aAAAA,oBAAoB,oBAAoBuI,CAAkB,GAC1DvI,OAAAA,oBAAoB,kBAAkBwI,CAAgB;AAAA,IAC/D;AAAA,EACF,GAAG,EAAE,GAqBE;AAAA,IACLC,cApBmBA,CAAC5I,MAEhBA,EAAE6I,WAAW,OACb7I,EAAElD,QAAQ,WACVkD,EAAElD,QAAQ,YAAYkD,EAAE8I,YAAYL,eAAeA,EAAYvH,WAC1D,KAEPlB,EAAE+I,UACF/I,EAAEgJ,WACFhJ,EAAEiJ,YACFjJ,EAAEkJ,WAEA,CAAClJ,EAAE+I,UACH,CAAC/I,EAAEgJ,WACH,CAAChJ,EAAEiJ,YACH,CAACjJ,EAAEkJ;AAAAA,EAMT;AACF;AAIO,SAASC,GAAY3S,GAGzB;AACK4S,QAAAA,IAAY5S,EAAM0F,QAAQoB,WAAW,GACrC,CAAC+L,GAAaC,CAAc,IAAI5J,EAAS,CAAC,GAC1C6J,IAAchK,EAAuB,IAAI;AA2C/C,SAzCAO,EAAU,MAAM;AACdwJ,IAAAA,EAAe,CAAC;AAAA,EACf,GAAA,CAAC9S,EAAM0F,QAAQoB,MAAM,CAAC,GAEzBwC,EAAU,MAAM;AACR0J,UAAAA,IAAYA,CAACxJ,MAAqB;AACtC,UAAIoJ,KAAapJ,EAAEkJ,WAAWlJ,EAAE+I,UAAU/I,EAAEgJ;AAC1C;AAGIS,YAAAA,IAAcA,CAACC,MAAkB;;AACrC1J,QAAAA,EAAE2J,gBAAgB,GAClB3J,EAAE4J,eAAe;AACjB,cAAMC,IAAYC,KAAKC,IACrB,GACAD,KAAKE,IAAIxT,EAAM0F,QAAQoB,SAAS,GAAG+L,IAAcK,CAAK,CACxD;AACAJ,QAAAA,EAAeO,CAAS,IACxBN,IAAAA,EAAYrI,YAAZqI,QAAAA,EAAqBU,eAAe;AAAA,UAClCC,OAAO;AAAA,QAAA;AAAA,MAEX;AAEIlK,UAAAA,EAAElD,QAAQ;AACZ2M,QAAAA,EAAY,CAAC;AAAA,eACJzJ,EAAElD,QAAQ;AACnB2M,QAAAA,EAAY,EAAE;AAAA,eACLzJ,EAAElD,QAAQ,SAAS;AAC5B,cAAMqN,IAAiB3T,EAAM0F,QAAQkO,GAAGf,CAAW;AACnD,QAAIc,KACF3T,EAAM6T,eAAeF,CAAc;AAAA,MACrC;AAAA,IAEJ;AAEOjK,kBAAAA,iBAAiB,WAAWsJ,CAAS,GAErC,MAAMpL,OAAO+B,oBAAoB,WAAWqJ,CAAS;AAAA,KAE3D,CAAChT,EAAM0F,QAAQoB,QAAQ+L,CAAW,CAAC,GAElCD,IAAkB,OAEpB1Q,gBAAAA,MAAC,SAAI,WAAWoI,EAAO,cAAc,GAClCtK,UAAAA,EAAM0F,QAAQhC,IAAI,CAACuB,GAAQ6O,MACzBnG,gBAAAA,EAAAA,KAAA,OAAA,EACC,KAAKmG,MAAMjB,IAAcE,IAAc,MACvC,WAAWnH,EAAKtB,EAAO,aAAa,GAAG;AAAA,IACrC,CAACA,EAAO,sBAAsB,CAAC,GAAGwJ,MAAMjB;AAAAA,EACzC,CAAA,GAED,SAAS,MAAM7S,EAAM6T,eAAe5O,CAAM,GAC1C,cAAc,MAAM6N,EAAegB,CAAC,GAEpC,UAAA;AAAA,IAAA5R,gBAAAA,MAAC,SAAI,WAAWoI,EAAO,YAAY,GAAIrF,YAAOtB,OAAM;AAAA,0BACnD,OAAI,EAAA,WAAW2G,EAAO,cAAc,GAAIrF,YAAOrB,QAAQ,CAAA;AAAA,EAAA,EAAA,GALnDqB,EAAOtB,QAAQmQ,EAAEC,SAAS,CAMjC,CACD,GACH;AAEJ;AACA,SAASC,KAAsB;AAC7B,QAAMC,IAAY1H,EAAa,GACzBD,IAAU2H,EAAUzH,eAAe;AAEzC,gCACG,OACC,EAAA,WAAWlC,EAAO,eAAe,GACjC,SAAS,MACP2J,EAAUC,oBACR5H,GACCA,CAAAA,MAAaA,EAAQ6H,oBAAoBC,MAC5C,GAGF,UAAA;AAAA,IAAAlS,gBAAAA,EAAAA,IAAC,SAAI,WAAWoI,EAAO,oBAAoB,GAAIpH,UAAAA,EAAOmR,QAAQC,OAAM;AAAA,IACpEpS,gBAAAA,MAAC,SAAI,WAAWoI,EAAO,0BAA0B,GAC9CpH,UAAAA,EAAOmR,QAAQE,OAClB,CAAA;AAAA,EAAA,GACF;AAEJ;AAEO,SAASC,EAAWxU,GAIxB;AACKyU,QAAAA,IAAU1L,EAAuB,IAAI,GACrC2L,IAAU3L,EAAuB,IAAI,GACrC,CAAC4L,GAAOC,CAAQ,IAAI1L,EAAS;AAAA,IACjC2L,MAAM;AAAA,IACNC,MAAM;AAAA,EAAA,CACP;AAED,WAASC,IAAc;AACrB,QAAI,CAACN,EAAQ/J,WAAW,CAACgK,EAAQhK,QAAS;AAC1C,UAAMsK,IAAWA,CAACC,MAAwBA,EAAIC,sBAAwBP,EAAAA,OAChEQ,IAAYH,EAASN,EAAQhK,OAAO,GACpC0K,IAAYJ,EAASP,EAAQ/J,OAAO;AACjC,IAAAkK,EAAA;AAAA,MACPC,MAAMM,IAAYC;AAAAA,MAClBN,MAAMM;AAAAA,IAAAA,CACP;AAAA,EAAA;AAID,SAAAzH,gBAAAA,OAAC,OACC,EAAA,WAAW/B,EAAKtB,EAAO,mBAAmB,GAAG,WAAW,GACxD,SAAS,MAAM;AACbtK,IAAAA,EAAMqV,QAAQ,GACd3H,WAAWqH,GAAa,CAAC;AAAA,EAE3B,GAAA,cAAcA,GACd,cAAcA,GACd,OACE;AAAA,IACE,gBAAgB,GAAGJ,EAAMG,IAAI;AAAA,IAC7B,gBAAgB,GAAGH,EAAME,IAAI;AAAA,EAIjC,GAAA,UAAA;AAAA,IAAC3S,gBAAAA,EAAAA,IAAA,OAAA,EAAI,KAAKuS,GAAS,WAAWnK,EAAO,MAClCtK,YAAM8U,KACT,CAAA;AAAA,IACA5S,gBAAAA,EAAAA,IAAC,SAAI,WAAWoI,EAAO,MAAS,KAAKoK,GAClC1U,UAAAA,EAAMkF,KACT,CAAA;AAAA,EAAA,GACF;AAEJ;AAEA,SAASoQ,GACPC,GACAC,IAAkB,IAClB;AAGA,QAAM,CAACC,GAAYC,CAAa,IAAIxM,EAAS,EAAI;AACjD,WAASyM,IAAoB;AAC3B,UAAMV,IAAMM,EAAU7K;AACtB,IAAIuK,KACFW,sBAAsB,MAAM;AAC1BF,MAAAA,EAAc,EAAI,GACdG,EAAAA,SAAS,GAAGZ,EAAInG,YAAY;AAAA,IAAA,CACjC;AAAA,EACH;AAIFxF,SAAAA,EAAU,MAAM;AACVmM,IAAAA,KAAc,CAACD,KACCG,EAAA;AAAA,EACpB,CACD,GAEM;AAAA,IACLJ,WAAAA;AAAAA,IACAE,YAAAA;AAAAA,IACAC,eAAAA;AAAAA,IACAC,mBAAAA;AAAAA,EACF;AACF;AAEO,SAASG,GAAY9V,GASzB;AACD,QAAMiU,IAAY1H,EAAa,GACzBD,IAAU2H,EAAUzH,eAAe,GAGnCuJ,IAAezJ,EAAQY,KAAK8I,YAAY/T,OACxC,CAACgU,GAAiBC,CAAkB,IAAIhN,EAAS,EAAK;AAE5DI,SAAAA,EAAU,MAAM;AACR6M,UAAAA,IAAOC,GAAcL,CAAY;AACvCG,IAAAA,EAAmBC,CAAI,GAClBA,MACGE,EAAAA,gBAAgB,EAAE,GACxBrW,EAAMsW,aAAa,EAAK;AAAA,EAGzB,GAAA,CAACrC,GAAW8B,GAAczJ,CAAO,CAAC,yBAGlC,OAAI,EAAA,WAAWhC,EAAO,oBAAoB,GACzC,UACGqD,gBAAAA,EAAAA,KAAAC,EAAA,UAAA,EAAA,UAAA;AAAA,IAAA,CAAC5N,EAAMuW,aACLrU,gBAAAA,EAAA,IAAAsS,GAAA,EACC,SAASxU,EAAMwW,gBACf,MAAMtT,EAAOC,KAAKsT,aAAaC,UAC/B,MAAMxU,gBAAAA,EAAAA,IAACyU,KAAa,CAAA;IAIvBV,KACE/T,gBAAAA,EAAAA,IAAAsS,GAAA,EACC,SAASxU,EAAMY,aACf,MAAMsC,EAAOC,KAAKsT,aAAaG,aAC/B,MAAM5W,EAAM6W,YAAY3U,gBAAAA,EAAAA,IAAC4U,UAAuB5U,gBAAAA,MAAC6U,KAAY,CAAA;IAGhE7U,gBAAAA,EAAA,IAAAsS,GAAA,EACC,SAASxU,EAAMgX,iBACf,MAAM9T,EAAOC,KAAKsT,aAAaxP,QAC/B,MAAM/E,gBAAAA,EAAA,IAAC+U,KAAU,CAAA,GACjB;AAAA,IACD/U,gBAAAA,EAAAA,IAAAsS,GAAA,EACC,MAAMtR,EAAOC,KAAKsT,aAAanC,OAC/B,MAAOpS,gBAAAA,EAAAA,IAAAgV,IAAA,CAAA,CAAY,GACnB,SAAS,MAAM;AACHhD,MAAAA,EAAAA,oBAAoB5H,GAAUA,CAAAA,MAAY;AAClD,QAAIA,EAAQ6H,sBAAsB7H,EAAQ6K,SAASrQ,SACjDwF,EAAQ6H,oBAAoBC,UAE5B9H,EAAQ6H,oBAAoB7H,EAAQ6K,SAASrQ,QAC7CwF,EAAQ8K,eAAe;AAAA,MACzB,CACD;AAAA,IAAA,EAEH,CAAA;AAAA,EAAA,EAAA,CACJ,EACF,CAAA;AAEJ;AAEO,SAASC,GAAkBrX,GAAoC;AAElE,SAAAkC,gBAAAA,EAAAA,IAAC,OAAI,EAAA,WAAWoI,EAAO,cAAc,GAAG,SAAStK,EAAMsX,aACrD,UAACpV,gBAAAA,EAAAA,IAAAqV,IAAA,CAAA,CAAa,EAChB,CAAA;AAEJ;AAGA,MAAMC,wBAA4BC,IAAI;AAEtC,SAASC,KAAQ;;AAGf,QAAMzD,IAAY1H,EAAa,GACzBD,IAAU2H,EAAUzH,eAAe;AACjC1E,UAAAA,IAAI,WAAUwE,CAAO;AACvBqL,QAAAA,IAAW5O,EAA4B,IAAI,GAC3C,CAACnG,GAAWgV,CAAY,IAAI1O,EAAS,EAAE,GACvC,CAAC2O,GAAWC,CAAY,IAAI5O,EAAS,EAAK,GAC1C;AAAA,IAAEkJ,cAAAA;AAAAA,MAAiBJ,GAAiB,GACpCuD,IAAYxM,EAAuB,IAAI,GACvCgP,IAAqBxC,KAAAA,QAAAA,EAAW7K,UAClC4I,KAAK0E,IACPzC,EAAU7K,QAAQoE,gBACjByG,EAAU7K,QAAQqE,YAAYwG,EAAU7K,QAAQuN,aACnD,KAAK,IACD,IACE;AAAA,IAAEvC,eAAAA;AAAAA,IAAeC,mBAAAA;AAAAA,EAAAA,IAAsBL,GAC3CC,GACAwC,CACF,GACM,CAACxB,GAAW2B,CAAY,IAAIhP,EAAS,EAAI,GACzCiP,IAAiBC,GAAgB,GACjC,CAACC,GAAchC,CAAe,IAAInN,EAAmB,CAAA,CAAE,GACvD,CAAC2N,GAAWP,CAAY,IAAIpN,EAAS,EAAK,GAG1C,CAACoP,IAAWC,EAAY,IAAIrP,EAAS,CAAC,GACtCsP,KAAU9L,GACd,MAAM;AACJ,UAAM+L,IAAOd,EAASjN,UAAUgO,GAAiBf,EAASjN,OAAO,IAAI,GAC/D4N,IAAYhF,KAAKE,IACrB,IACAF,KAAKC,IAAI,IAAIoF,CAAO,CAACR,GAAiBM,CAAI,CAC5C;AACAF,IAAAA,GAAaD,CAAS;AAAA,KAExB,KACA;AAAA,IACEM,SAAS;AAAA,IACTC,UAAU;AAAA,EAAA,CAEd;AAGUL,EAAAA,EAAAA,IAAS,CAAC5V,CAAS,CAAC;AAG9B,QAAMkW,KAAerW,GAAe;AAAA,IAClCsW,KAAKA,MAAM9E,EAAU+E,WAAW;AAAA,IAChCC,MAAMA,MAAMhF,EAAUiF,YAAY,EAAE;AAAA,IACpCC,MAAMA,MAAMlF,EAAUiF,YAAY,CAAC;AAAA,IACnCE,OAAOA,MACLnF,EAAUC,oBACR5H,GACCA,CAAAA,MAAaA,EAAQ6H,oBAAoB7H,EAAQ6K,SAASrQ,MAC7D;AAAA,IACFuS,MAAMA,MAAMpF,EAAUqF,YAAY;AAAA,IAClCC,KAAKA,MAAMtF,EAAUuF,cAAcvF,EAAUwF,mBAAmB;AAAA,EAAA,CACjE,GAGKC,KAAoB,IACpBC,KAAUA,CAACzU,MAAiB;AAChC0S,IAAAA,EAAa1S,CAAI;AACX0U,UAAAA,IAAI1U,EAAK2U,KAAAA,EAAO/S;AAGtB,QAAI8S,MAAM;AACRE,MAAAA,EAAe,CAAA,CAAE;AAAA,aACR5U,EAAKrC,MAAML,EAAiB;AACtBsW,MAAAA,EAAAA,GAAa/V,OAAOmC,CAAI,CAAC;AAAA,aACM0U,IAAIF,MAE9CxU,EAAKzB,WAAW,GAAG,GAAG;AAClBsW,YAAAA,IAAa7U,EAAKpC,MAAM,CAAC;AAC/BkX,MAAAA,GAASD,CAAU;AAAA,IAAA;AAAA,EAGzB,GAEME,KAAWA,CAACrX,MAAsB;;AACtC,QAAIA,EAAUiX,KAAK,MAAM,MAAMK,GAAQ7B,CAAY,EAAG;AAChD8B,UAAAA,IAAerB,GAAajW,MAAMD,CAAS;AACjD,QAAIuX,EAAarW,SAAS;AACxB8T,MAAAA,EAAa,EAAE,GACfuC,EAAapW,OAAO;AACpB;AAAA,IAAA;AAEF+T,IAAAA,EAAa,EAAI,GAEdsC,EAAAA,YAAYxX,GAAWyV,CAAY,EACnC/W,KAAK,MAAMwW,EAAa,EAAK,CAAC,GACjCzB,EAAgB,CAAA,CAAE,GAClBpC,EAAUoG,aAAazX,CAAS,GAChCgV,EAAa,EAAE,GACVO,MAAyBzN,IAAAA,EAAAA,YAAAA,QAAAA,EAAS4P,SACvC5E,EAAc,EAAI;AAAA,EACpB;AAEApM,EAAAA,EAAU,MAAM;AAEN6N,IAAAA,EAAAA,SAASoD,KAAKC,EAAc;AAAA,MAClC1V,IAAIe,EAAO;AAAA,MACX4U,OAAM,oBAAIzU,KAAK,GAAE0U,eAAe;AAAA,MAChCC,MAAM;AAAA,MACN/W,SAAS,CACP;AAAA,QACE6H,MAAM;AAAA,QACNmP,WAAW;AAAA,UACT7T,MAAM;AAAA,UACN8T,KAAK;AAAA,QAAA;AAAA,MAER,CAAA;AAAA,IAAA,CAEJ,CAAC,GAEM1D,EAAAA,SAASoD,KAAKC,EAAc;AAAA,MAClC1V,IAAIe,EAAO;AAAA,MACX4U,OAAM,oBAAIzU,KAAK,GAAE0U,eAAe;AAAA,MAChCC,MAAM;AAAA,MACN/W,SAAS,CACP;AAAA,QACE6H,MAAM;AAAA,QACNqP,aAAa;AAAA,UACXC,UAAU;AAAA,UACV/a,OAAO;AAAA,YACLgb,SAAS;AAAA,YACTC,MAAM;AAAA,YACNC,QAAQ;AAAA,YACRpU,QAAQ;AAAA,YACRqU,YAAY;AAAA,YACZC,MAAM;AAAA,YACNC,OAAO;AAAA,YACPC,eAAe;AAAA,UACjB;AAAA,UACAC,QAAQ;AAAA,YACNlG,SAASA,MAAM;AAEbmG,sBAAQ1T,IAAI,SAAS;AAAA,YAAA;AAAA,UACvB;AAAA,QACF;AAAA,MAEH,CAAA;AAAA,IAAA,CAEJ,CAAC,GAEMqP,EAAAA,SAASoD,KAAKC,EAAc;AAAA,MAClC1V,IAAIe,EAAO;AAAA,MACX4U,OAAM,oBAAIzU,KAAK,GAAE0U,eAAe;AAAA,MAChCC,MAAM;AAAA,MACN/W,SAAS,CACP;AAAA,QACE6H,MAAM;AAAA,QACNqP,aAAa;AAAA,UACXC,UAAU;AAAA,UACV/a,OAAO;AAAA,YACLyb,cAAc;AAAA,YACdC,OAAO;AAAA,YACPC,MAAM;AAAA,YACNC,MAAM;AAAA,YACNC,eAAe;AAAA,YACfC,WAAW;AAAA,YACXC,gBAAgB;AAAA,YAChBC,aAAa;AAAA,UACf;AAAA,UACAT,QAAQ;AAAA,YACNlG,SAASA,MAAM;AAEbmG,sBAAQ1T,IAAI,SAAS;AAAA,YAAA;AAAA,UACvB;AAAA,QACF;AAAA,MAEH,CAAA;AAAA,IAAA,CAEJ,CAAC,GAEMqP,EAAAA,SAASoD,KAAKC,EAAc;AAAA,MAClC1V,IAAIe,EAAO;AAAA,MACX4U,OAAM,oBAAIzU,KAAK,GAAE0U,eAAe;AAAA,MAChCC,MAAM;AAAA,MACN/W,SAAS,CACP;AAAA,QACE6H,MAAM;AAAA,QACNqP,aAAa;AAAA,UACXC,UAAU;AAAA,UACV/a,OAAO;AAAA,YACLic,iBAAiB;AAAA,YACjBN,MAAM;AAAA,UACR;AAAA,UACAJ,QAAQ;AAAA,YACNlG,SAASA,MAAM;AAEbmG,sBAAQ1T,IAAI,SAAS;AAAA,YAAA;AAAA,UACvB;AAAA,QACF;AAAA,MAEH,CAAA;AAAA,IAAA,CAEJ,CAAC;AAAA,EACJ,GAAG,EAAE,GAELwB,EAAU,MAAM;AACJ4K,IAAAA,EAAAA,oBAAoB5H,GAAUA,CAAAA,MAAY;AAC5C4P,YAAAA,IAAalW,KAAKC,IAAAA,IAAQkW;AAExBrU,cAAAA,IAAI,WAAUwE,CAAO,GAC7BA,EAAQ6K,SAAS1P,QAAS2U,CAAMA,MAAA;AAE1BA,SAAAA,EAAEC,WAAW,IAAIrW,KAAKoW,EAAE3B,IAAI,EAAE6B,QAAQ,IAAIJ,OACxCE,EAAEG,cACJH,EAAEG,YAAY,KAGZH,EAAExY,QAAQkD,WAAW,MACvBsV,EAAEC,UAAU,IACZD,EAAExY,UAAU4Y,GAAa;AAAA,UACvBxR,OAAO;AAAA,UACPC,SAAS;AAAA,QAAA,CACV;AAAA,MAEL,CACD;AAAA,IAAA,CAEF;AAAA,EAAA,GAEA,CAACqB,CAAO,CAAC;AAGNmQ,QAAAA,KAAiBA,CAACjT,MAAgD;AAEtE,QACEA,EAAElD,QAAQ,aACV1D,EAAUkE,UAAU,KACpB,EAAE0C,EAAEkJ,WAAWlJ,EAAE+I,UAAU/I,EAAEgJ,UAC7B;AACayB,MAAAA,EAAAA,EAAUyI,aAAa,EAAE,GACtClT,EAAE4J,eAAe;AACjB;AAAA,IAAA;AAEF,IAAIhB,EAAa5I,CAAC,KAAKmT,GAAY7V,WAAW,MAC5CmT,GAASrX,CAAS,GAClB4G,EAAE4J,eAAe;AAAA,EAErB,GACMwJ,KAAeA,CAACpT,GAAQyB,MAAyB;AAErD,IAAI4R,GAAarT,EAAEsT,eAAeC,EAAsB9R,CAAO,CAAC,MAC1DrI,EAAUkE,WAAW,KACViW,EAAAA,EAAsB9R,CAAO,CAAC,GAG7CzB,EAAE4J,eAAe;AAAA,EAErB,GAEM4J,KAAgBA,CAACC,MAAmB;AACxChJ,IAAAA,EAAUC,oBACR5H,GACCA,CAAAA,MACEA,EAAQ6K,WAAW7K,EAAQ6K,SAAS5T,OAAQ6Y,CAAAA,MAAMA,EAAEtX,OAAOmY,CAAK,CACrE;AAAA,EACF,GAEMC,KAAWA,CAACD,MAAkB;AAClCD,IAAAA,GAAcC,CAAK;AAAA,EACrB,GAEME,KAAWA,CAAClS,MAAyB;;AAOnCmS,UAAAA,IAAiB9Q,EAAQ6K,SAASkG,UACrCjB,OAAMA,EAAEtX,OAAOmG,EAAQnG,EAC1B;AAEA,QAAIsY,IAAiB,KAAKA,KAAkB9Q,EAAQ6K,SAASrQ,QAAQ;AAC3DkE,cAAAA,MAAM,2CAA2CC,CAAO;AAChE;AAAA,IAAA;AAGEqS,QAAAA,GACAC;AAEAtS,QAAAA,EAAQ0P,SAAS,aAAa;AAEnB1P,MAAAA,IAAAA;AACb,eAAS6I,IAAIsJ,GAAgBtJ,KAAK,GAAGA,KAAK;AACxC,YAAIxH,EAAQ6K,SAASrD,CAAC,EAAE6G,SAAS,QAAQ;AACzBrO,UAAAA,IAAAA,EAAQ6K,SAASrD,CAAC;AAChC;AAAA,QAAA;AAAA,IAEJ,WACS7I,EAAQ0P,SAAS,QAAQ;AAEpB1P,MAAAA,IAAAA;AACd,eAAS6I,IAAIsJ,GAAgBtJ,IAAIxH,EAAQ6K,SAASrQ,QAAQgN,KAAK;AAC7D,YAAIxH,EAAQ6K,SAASrD,CAAC,EAAE6G,SAAS,aAAa;AAC/BrO,UAAAA,IAAAA,EAAQ6K,SAASrD,CAAC;AAC/B;AAAA,QAAA;AAAA,IAEJ;AAGF,QAAIwJ,MAAgBlJ,QAAW;AACrBpJ,cAAAA,MAAM,2BAA2BC,CAAO;AAChD;AAAA,IAAA;AAIF+R,IAAAA,GAAcM,EAAYxY,EAAE,GAC5BkY,GAAcO,KAAAA,gBAAAA,EAAYzY,EAAE,GAG5BgT,EAAa,EAAI;AACX0F,UAAAA,IAAcT,EAAsBO,CAAW,GAC/CG,IAASC,EAAiBJ,CAAW;AACjClD,IAAAA,EAAAA,YAAYoD,GAAaC,CAAM,EAAEnc,KAAK,MAAMwW,EAAa,EAAK,CAAC,IACzEH,IAAAA,EAASjN,YAATiN,QAAAA,EAAkB2C;AAAAA,EACpB,GAEMqD,IAA2B7T,EAAQ,MAChCwC,EAAQY,KAAK0Q,cAAc,CAAA,IAAKtR,EAAQY,KAAKyQ,QAAQ7a,MAAM,GACjE,CAACwJ,EAAQY,KAAKyQ,SAASrR,EAAQY,KAAK0Q,WAAW,CAAC;AAGjDD,MAAAA,EAAQ7W,WAAW,OACnBwF,KAAAA,EAAQ6K,SAASvD,GAAG,CAAC,MAArBtH,gBAAAA,GAAwB1I,aAAYia,GAAUja,SAC9C;AACA,UAAMka,IAAcza,OAAO0a,OAAO,CAAA,GAAIF,EAAS;AAE/CF,IAAAA,EAAQpD,KAAKuD,CAAW;AAAA,EAAA;AAIpBE,QAAAA,IAAiBlU,EAAQ,MACtB6T,EACJhZ,OAAO2H,EAAQ6K,QAA2B,EAC1CxS,OACCkT,IACI,CACA;AAAA,IACE,GAAG2C,EAAc;AAAA,MACfG,MAAM;AAAA,MACN/W,SAAS;AAAA,IAAA,CACV;AAAA,IACDqa,SAAS;AAAA,EAAA,CACV,IAED,CACN,CAAA,EACCtZ,OACC/B,EAAUkE,SAAS,IACf,CACA;AAAA,IACE,GAAG0T,EAAc;AAAA,MACfG,MAAM;AAAA,MACN/W,SAAShB;AAAAA,IAAAA,CACV;AAAA,IACDqb,SAAS;AAAA,EACV,CAAA,IAED,CAAA,CACN,GACD,CACDN,GACA9F,GACAvL,EAAQ6K,UACRvU,CAAS,CACV,GAEK,CAACsb,GAAgBC,EAAkB,IAAIjV,EAC3CoK,KAAKC,IAAI,GAAGyK,EAAelX,SAASsX,CAAc,CACpD;AACA,WAASC,GAAkBC,GAAkB;AAC3CA,IAAAA,IAAWhL,KAAKE,IAAIwK,EAAelX,SAASsX,GAAgBE,CAAQ,GACzDhL,IAAAA,KAAKC,IAAI,GAAG+K,CAAQ,GAC/BH,GAAmBG,CAAQ;AAAA,EAAA;AAGvBnH,QAAAA,IAAWrN,EAAQ,MAAM;AAC7B,UAAMyU,IAAiBjL,KAAKE,IAC1B0K,IAAiB,IAAIE,GACrBJ,EAAelX,MACjB;AACOkX,WAAAA,EAAelb,MAAMob,GAAgBK,CAAc;AAAA,EAAA,GACzD,CAACL,GAAgBF,CAAc,CAAC,GAE7BQ,KAAmBA,CAAChV,MAAmB;AACrCiV,UAAAA,IAAejV,EAAEuF,YAAYvF,EAAEyO,cAC/ByG,IAAgBlV,EAAEyO,cAElB0G,IAAiBnV,EAAEuF,aAAa2P,GAChCE,IAAoBH,KAAgBjV,EAAEsF,eAAe4P,GACrDG,IACJJ,KAAgBjV,EAAEsF,gBAAgBqJ,IAAiB,IAAI,KAEnD2G,IAAmBZ,IAAiBE,GACpCW,IAAmBb,IAAiBE;AAEtCO,IAAAA,KAAkB,CAACC,IACrBP,GAAkBS,CAAgB,IACzBF,KACTP,GAAkBU,CAAgB,GAGpC7G,EAAa2G,CAAW,GACxBnJ,EAAcmJ,CAAW;AAAA,EAC3B;AACA,WAASrI,KAAiB;AACNwH,IAAAA,GAAAA,EAAelX,SAASsX,CAAc,GACtCzI,EAAA;AAAA,EAAA;AAIdxB,QAAAA,MACH7H,EAAQ6H,qBAAqB,OAAO,IACjC7H,EAAQ6H,oBAAqBwJ,EAAQ7W,SAASoX,IAC9C,IAEAc,KAAY,CAAC7G;AAInB7O,EAAAA,EAAU,MAAM;AAERhD,UAAAA,IAAM2Y,GAAiB3S,EAAQxH,EAAE,GACjCoa,IAAuBpN,GAAaqN,QAAQ7Y,CAAG;AACjD4Y,IAAAA,KAAwBtc,EAAUkE,WAAW,MAC/C8Q,EAAasH,CAAoB,GACjCpN,GAAasN,WAAW9Y,CAAG;AAG7B,UAAM2O,IAAM0C,EAASjN;AACrB,WAAO,MAAM;AACXoH,MAAAA,GAAauN,QAAQ/Y,IAAK2O,KAAAA,gBAAAA,EAAKqK,UAAS,EAAE;AAAA,IAC5C;AAAA,EAEF,GAAG,EAAE;AAECC,QAAAA,KAAcC,GAClB,OAAOC,MAAqD;AAC1D,UAAM1J,IAAe9B,EAAUzH,eAAe,EAAEU,KAAK8I,YAAY/T;AAC7D,QAAA,CAACmU,GAAcL,CAAY;AAC7B;AAGF,UAAM2J,KAASD,EAAME,iBAAiB/X,OAAO+X,eAAeD;AAC5D,eAAWpa,KAAQoa;AACjB,UAAIpa,EAAKsa,SAAS,UAAUta,EAAKmG,KAAKhI,WAAW,QAAQ,GAAG;AAC1Dgc,QAAAA,EAAMrM,eAAe;AACfvS,cAAAA,IAAOyE,EAAKua,UAAU;AAC5B,YAAIhf,GAAM;AACR,gBAAM4c,IAAmB,CAAE;AACpBlD,UAAAA,EAAAA,KAAK,GAAGlC,CAAY,GAC3BoF,EAAOlD,KACL,GAAI,MAAM,IAAIuF,QAAkB,CAACve,GAAKwe,MAAQ;AAC5CzJ,YAAAA,EAAa,EAAI;AACjB,kBAAM0J,IAAuB,CAAE;AACbnf,YAAAA,GAAAA,CAAI,EACnBS,KAAM2e,CAAYA,MAAA;AACjBD,cAAAA,EAAWzF,KAAK0F,CAAO,GACvB3J,EAAa,EAAK,GAClB/U,EAAIye,CAAU;AAAA,YAAA,CACf,EACAjV,MAAOvB,CAAMA,MAAA;AACZ8M,cAAAA,EAAa,EAAK,GAClByJ,EAAIvW,CAAC;AAAA,YAAA,CACN;AAAA,UAAA,CACJ,CACH;AACA,gBAAM0W,IAAezC,EAAO3W;AAE5B,UAAIoZ,IAAe,KACVC,EAAAA,OAAO,GAAGD,IAAe,CAAC,GAEnC7J,EAAgBoH,CAAM;AAAA,QAAA;AAAA,MACxB;AAAA,EAEJ,GAEF,CAACpF,GAAcpE,CAAS,CAC1B;AAEA,iBAAerT,KAAc;AAC3B,UAAM6c,IAAmB,CAAE;AACpBlD,IAAAA,EAAAA,KAAK,GAAGlC,CAAY,GAE3BoF,EAAOlD,KACL,GAAI,MAAM,IAAIuF,QAAkB,CAACve,GAAKwe,MAAQ;AACtCK,YAAAA,IAAY/R,SAASgS,cAAc,OAAO;AAChDD,MAAAA,EAAU3U,OAAO,QACjB2U,EAAUE,SACR,6DACFF,EAAUG,WAAW,IACXC,EAAAA,WAAW,CAACf,MAAe;AACnCnJ,QAAAA,EAAa,EAAI;AACXmK,cAAAA,IAAQhB,EAAMtO,OAAOsP,OACrBT,IAAuB,CAAE;AAC/B,iBAASlM,IAAI,GAAGA,IAAI2M,EAAM3Z,QAAQgN,KAAK;AACrC,gBAAMjT,IAAO4e,EAAMtO,OAAOsP,MAAM3M,CAAC;AACfjT,UAAAA,GAAAA,CAAI,EACnBS,KAAM2e,CAAYA,MAAA;AACjBD,YAAAA,EAAWzF,KAAK0F,CAAO,IAErBD,EAAWlZ,WAAW,KACtBkZ,EAAWlZ,WAAW2Z,EAAM3Z,YAE5BwP,EAAa,EAAK,GAClB/U,EAAIye,CAAU;AAAA,UAChB,CACD,EACAjV,MAAOvB,CAAMA,MAAA;AACZ8M,YAAAA,EAAa,EAAK,GAClByJ,EAAIvW,CAAC;AAAA,UAAA,CACN;AAAA,QAAA;AAAA,MAEP,GACA4W,EAAUM,MAAM;AAAA,IAAA,CACjB,CACH;AAEA,UAAMR,IAAezC,EAAO3W;AAC5B,IAAIoZ,IAAe,KACVC,EAAAA,OAAO,GAAGD,IAAe,CAAC,GAEnC7J,EAAgBoH,CAAM;AAAA,EAAA;AAExB,QAAMkD,KAAcpb,GAAe,GAC7B,CAACoX,IAAa7C,CAAc,IAAI5Q,EAAyB,CAAA,CAAE,GAC3D2K,KAAiBA,CAAC5O,MAAyB;AAC/CyI,eAAW,MAAM;;AACfoM,MAAAA,EAAe,CAAA,CAAE;AAEjB,YAAM8G,IAAqB9H,GAAajW,MAAMoC,EAAOrB,OAAO;AAC5D,MAAIgd,EAAmB9c,WAErB8c,EAAmB7c,OAAO,GAC1B6T,EAAa,EAAE,KAGfA,EAAa3S,EAAOrB,OAAO,IAE7B+T,IAAAA,EAASjN,YAATiN,QAAAA,EAAkB2C;AAAAA,OACjB,EAAE;AAAA,EACP,GACMN,KAAWtN,GACf,CAACxH,MAAiB;AACV2b,UAAAA,IAAiBF,GAAY5d,OAAOmC,CAAI;AAC9C4U,IAAAA,EAAe+G,CAAc;AAAA,KAE/B,KACA;AAAA,IAAEjI,SAAS;AAAA,IAAMC,UAAU;AAAA,EAAA,CAC7B,GACMiI,KAAcA,CAACjgB,MAAmB;AAC9BiH,YAAAA,IAAI,QAAQjH,CAAI,GACpBA,EAAKga,QACHha,EAAK4K,SAAS,cAEhB7D,OAAOmZ,KAAK,qDAAqDC,mBAAmBngB,EAAKga,GAAG,CAAC,IAAI,QAAQ,IAChGha,EAAK4K,SAAS,aAEvB7D,OAAOmZ,KAAK,qDAAqDC,mBAAmBngB,EAAKga,GAAG,CAAC,IAAI,QAAQ,IAChGha,EAAK4K,SAAS,aAEhBsV,OAAAA,KAAKlgB,EAAKga,KAAK,QAAQ;AAAA,EAGpC,GAGMoG,KAA0BzB,GAAY,CAACzE,MAAqB;AACxDjT,YAAAA,IAAI,yBAAwB0P,GAAuBuD,CAAQ;AACnE,UAAMmG,IAAenG,EAAS5Q,QAAQ,QAAQ,EAAE;AAChD,QAAI,CAACqN,EAAsB2J,IAAIpG,CAAQ,GAAG;AACxC,YAAMqG,IAAYC,GAChB,MAAMC,yXAAA,kBAAAJ,CAAA,QAAA,CAAA,GACN;AAAA,QACEtP,SAASA,MAAO1P,gBAAAA,EAAAA,IAAA,OAAA,EAAI,UAAU,aAAA,CAAA;AAAA,QAC9Bqf,KAAK;AAAA,MAAA,CAET;AACQzZ,cAAAA,IAAI,aAAYsZ,CAAS;AAG3BI,YAAAA,IAAmBA,CAACxhB,OACxBsJ,EAAU,MAAM;AAERmY,cAAAA,IAAepT,SAASgS,cAAc,OAAO;AAC1CqB,iBAAAA,KAAKC,YAAYF,CAAY,GAC7BC,SAAAA,KAAKE,YAAYH,CAAY;AAAA,MACxC,GAAG,EAAE,GAEEvf,gBAAAA,MAACkf,GAAcphB,EAAAA,GAAAA,EAAS,CAAA;AAGX2F,MAAAA,EAAAA,IAAIoV,GAAUyG,CAAgB;AAAA,IAAA;AAE/ChK,WAAAA,EAAsB5R,IAAImV,CAAQ;AAAA,EAC3C,GAAG,EAAE;AAELzR,SAAAA,EAAU,MAAM;AACRuY,UAAAA,IAAgBA,CAACpC,MAAe;;AAEpC,UAAIA,EAAMhN,YAAYgN,EAAMnZ,IAAIwb,kBAAkB;AAChDrC,QAAAA,EAAMrM,eAAe,IACrBuE,IAAAA,EAASjN,YAATiN,QAAAA,EAAkB2C;AAAAA,gBAIjBmF,EAAM/M,WAAW+M,EAAMjN,YACxBiN,EAAMhN,YACNgN,EAAMhe,SAAS,aACf;AACAge,QAAAA,EAAMrM,eAAe;AACf2O,cAAAA,IACJ1T,SAASjB,iBAA8B,mBAAmB;AACxD2U,QAAAA,EAAejb,SAAS,KAC1Bib,EAAeA,EAAejb,SAAS,CAAC,EAAE4Z,MAAM;AAAA,MAEpD,YAGGjB,EAAM/M,WAAW+M,EAAMjN,YACxBiN,EAAMhN,YACNgN,EAAMnZ,IAAIwb,YAAY,MAAM,KAC5B;AACArC,QAAAA,EAAMrM,eAAe;AACf4O,cAAAA,IAAqB7K,EACxB5T,OAAQ0H,CAAAA,MAAYA,EAAQ0P,SAAS,MAAM,EAC3CsH,IAAI;AACP,YAAID,GAAoB;AAChBE,gBAAAA,IAAqBnF,EAAsBiF,CAAkB;AACnEnU,UAAAA,GAAgBqU,CAAkB;AAAA,QAAA;AAAA,MACpC;AAAA,IAEJ;AAEOxY,kBAAAA,iBAAiB,WAAWmY,CAAa,GAEzC,MAAM;AACJlY,aAAAA,oBAAoB,WAAWkY,CAAa;AAAA,IACrD;AAAA,EAAA,GACC,CAAC1K,GAAUlD,CAAS,CAAC,GAGxB3K,EAAU,MAAM;AAERmY,UAAAA,IAAepT,SAASgS,cAAc,OAAO;AAC1CqB,aAAAA,KAAKC,YAAYF,CAAY,GAC7BC,SAAAA,KAAKE,YAAYH,CAAY;AAAA,EACxC,GAAG,EAAE,uCAID,UAACvf,gBAAAA,EAAAA,IAAA,OAAA,EAAI,WAAWoI,EAAO6X,MACrB,UAACjgB,gBAAAA,EAAAA,IAAA,OAAA,EAAI,WAAWoI,EAAO,WAAW,GAChC,UAAAqD,gBAAAA,EAAA,KAAC,SAAI,WAAWrD,EAAO,qBAAqB,GAC1C,UAAA;AAAA,IAACpI,gBAAAA,MAAA,OAAA,EACC,WAAWoI,EAAO,WAAW,GAC7B,KAAKiL,GACL,UAAW/L,CAAMgV,MAAAA,GAAiBhV,EAAEsT,aAAa,GACjD,aAAa,MAAA;;AAAMnF,cAAAA,IAAAA,EAASjN,YAATiN,gBAAAA,EAAkByK;AAAAA,OACrC,cAAc,MAAM;;AAClBzK,OAAAA,IAAAA,EAASjN,YAATiN,QAAAA,EAAkByK,QAClB1M,EAAc,EAAK;AAAA,IAGpByB,GAAAA,UAAAA,EAASzT,IAAI,CAACuH,GAAS6I,MAAM;;AACpBhM,cAAAA,IAAI,YAAYqP,CAAQ;AAC1BrR,YAAAA,IAASmF,EAAQ0P,SAAS,QAC1B0H,IAAYvO,IAAI6J,EAAQ7W,QACxBwb,IACJxO,IAAI,KACJ,EAAE7I,EAAQgT,WAAWhT,EAAQrH,QAAQkD,WAAW,MAChD,CAACub,GACGE,IAAatX,EAAQgT,WAAWhT,EAAQsR,WAExCiG,IACJ1O,MAAMK,KAAoB;AAG1B,aAAAxG,gBAAAA,EAAA,KAACC,IAAA,EACC,UAAA;AAAA,QAAA1L,gBAAAA,EAAA,IAAC,OACC,EAAA,WACE4D,IACIwE,EAAO,mBAAmB,IAC1BA,EAAO,cAAc,GAG3B,UAACqD,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWrD,EAAO,wBAAwB,GAC7C,UAAA;AAAA,UAAAqD,gBAAAA,EAAA,KAAC,OAAI,EAAA,WAAWrD,EAAO,qBAAqB,GAC1C,UAAA;AAAA,YAAApI,gBAAAA,MAAC,OAAI,EAAA,WAAWoI,EAAO,qBAAqB,GACzCxE,UACCA,IAAA5D,gBAAAA,MAACF,IAAO,EAAA,QAAQygB,EAAergB,QAAO,IAGnCF,gBAAAA,EAAA,IAAA0L,YAAA,EAAA,UAAA,CAAC,QAAQ,EAAEzF,SAAS8C,EAAQ0P,IAAI,IAC9BzY,gBAAAA,MAAAF,IAAA,EAAO,QAAO,aAAc,IAE5BE,gBAAAA,MAAAF,IAAA,EAAO,OAAOiJ,EAAQhJ,SACrBqK,EAAQY,KAAK8I,YAAY/T,MAAAA,CAAM,EASrC,CAAA,GAEJ;AAAA,YACC,CAAC6D,KACC5D,gBAAAA,MAAA,OAAA,EAAI,WAAWoI,EAAO,iBAAiB,GAAG,UAE3C,MAAA;AAAA,YAGDgY,KACCpgB,gBAAAA,EAAAA,IAAC,OAAI,EAAA,WAAWoI,EAAO,sBAAsB,GAC3C,UAAApI,gBAAAA,EAAA,IAAC,OAAI,EAAA,WAAWoI,EAAO,oBAAoB,GAQvC,UACEqD,gBAAAA,OAAAC,EAAAA,UAAA,EAAA,UAAA;AAAA,cAAA1L,gBAAAA,EAAA,IAACsS,GACC,EAAA,MAAMtR,EAAOC,KAAKuf,QAAQC,OAC1B,MAAOzgB,gBAAAA,EAAAA,IAAA0gB,IAAA,CAAA,CAAY,GACnB,SAAS,MAAMzF,GAASlS,CAAO,GAC/B;AAAA,oCAEDuJ,GACC,EAAA,MAAMtR,EAAOC,KAAKuf,QAAQG,QAC1B,MAAM3gB,gBAAAA,EAAA,IAACqV,MAAa,GACpB,SAAS,MAAM2F,GAASjS,EAAQnG,MAAMgP,EAAEC,SAAU,CAAA,GAClD;AAAA,oCAEDS,GACC,EAAA,MAAMtR,EAAOC,KAAKuf,QAAQI,MAC1B,MAAO5gB,gBAAAA,EAAA,IAAA6gB,IAAA,CAAW,CAAA,GAClB,SAAS,MACPlV,GACEkP,EAAsB9R,CAAO,CAC/B,EAEF,CAAA;AAAA,YAAA,EACJ,CAAA,EAEJ,CAAA,EACF,CAAA;AAAA,UAAA,GAEJ;AAAA,YAECA,IAAAA,KAAAA,gBAAAA,EAAS+X,UAAT/X,gBAAAA,EAAgBnE,WAAU,KAAKyb,KAC9BrgB,gBAAAA,EAAA,IAAC,OAAI,EAAA,WAAWoI,EAAO,qBAAqB,GACzCpH,UAAAA,EAAOC,KAAK8f,QACf;AAAA,WAGDhY,KAAAA,gBAAAA,EAAS+X,WAAS/X,KAAAA,gBAAAA,EAAS+X,MAAMlc,UAAS,KACxC5E,gBAAAA,MAAA,OAAA,EAAI,WAAWoI,EAAO,oBAAoB,GACxCW,sCAAS+X,4BAAOtf,IAAKwf,CACpBA;;AAAAvV,mCAAAA,EAAA,KAAC,OAEC,EAAA,OAAOuV,KAAAA,gBAAAA,EAAMC,UACb,WAAW7Y,EAAO,mBAAmB,GAEpC4Y,UAAAA;AAAAA,cAAAA,EAAK7G,YAAY,KACfna,gBAAAA,EAAA,IAAAkhB,IAAA,CAAW,CAAA,IACVF,EAAK7G,YAAY,KAClBna,gBAAAA,EAAA,IAAAmhB,IAAA,CAAA,KAEDnhB,gBAAAA,EAAA,IAAC4U,IAAiB,EAAA;AAAA,cAEnB5U,gBAAAA,EAAA,IAAA,QAAA,EAAMghB,WAAMI,IAAAA,KAAAA,gBAAAA,EAAAA,aAAAA,gBAAAA,EAAUvc,KAAK,CAAA;AAAA,YAXvBmc,EAAAA,GAAAA,EAAKpe,EAYZ;AAAA,cAEJ;AAAA,UAED6I,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWrD,EAAO,mBAAmB,GAExC,UAAA;AAAA,YAAApI,gBAAAA,EAAA;AAAA,cAACoP;AAAA,cAAA;AAAA,gBAEC,SAASyL,EAAsB9R,CAAO;AAAA,gBACtC,UACGA,EAAQgT,WAAWhT,EAAQsR,cAC5BtR,EAAQrH,QAAQkD,WAAW,KAC3B,CAAChB;AAAAA,gBAEH,eAAgB0D,CAAAA,MAAMoT,GAAapT,GAAGyB,CAAO;AAAA,gBAC7C,sBAAsB,MAAM;AAC1B,kBAAKkN,KACQ4E,EAAAA,EAAsB9R,CAAO,CAAC;AAAA,gBAC7C;AAAA,gBACA,UAAUwX,EAAejR;AAAAA,gBACzB,YAAYiR,EAAehR;AAAAA,gBAC3B,WAAW8D;AAAAA,gBACX,aAAazB,KAAKqD,EAASrQ,SAAS;AAAA,cAAA;AAAA,cAf/BmE,EAAQsR,YAAY,YAAY;AAAA,YAgBrC;AAAA,YAEAgH,GAAuBtY,CAAO,EAAEnE,SAAS,KACvCyc,GAAuBtY,CAAO,EAAEvH,IAAI,CAACoX,GAAa0I,MAAU;AACvD,kBAAA,CAAC1I,EAAoB,QAAA;AAClB,oBAAA;AAAA,gBAAEC,UAAAA;AAAAA,gBAAU/a,OAAAA;AAAAA,gBAAOub,QAAAA;AAAAA,cAAAA,IAAWT,GAC9B2I,KAAuBxC,GAAwBlG,EAAQ;AAC7D,qBACG7Y,gBAAAA,EAAAA,IAAAuhB,IAAA,EAEC,OAAOzjB,MAAS,MAChB,QAAQub,MAAU,KAAA,GAFb,GAAGtQ,EAAQnG,EAAE,IAAI0e,CAAK,EAG3B;AAAA,YAAA,CAEL;AAAA,YAIJE,GAAgBzY,CAAO,EAAEnE,SACtB4c,GAAgBzY,CAAO,EAAEvH,IAAI,CAAC7C,GAAM2iB,MAEhC7V,gBAAAA,OAAC,OACC,EAAA,WAAWrD,EAAO,wBAAwB,GAC1C,SAAS,MAAMwW,GAAYjgB,CAAI,GAG7BA,UAAAA;AAAAA,cAAK4K,EAAAA,SAAS,cAAcvJ,gBAAAA,EAAA,IAACyhB,IAAU,EAAA,WAAWrZ,EAAO,6BAA6B,OAAOzJ,EAAK4K,SAAS,mCAAcmY,IAAS,EAAA,WAAWtZ,EAAO,6BAA6B,EAAI,CAAA,0BAAIuZ,IAAQ,EAAA,WAAWvZ,EAAO,6BAA6B,EAAE,CAAA;AAAA,oCAEnP,OAAI,EAAA,WAAWA,EAAO,6BAA6B,GACjDzJ,YAAKkG,KACR,CAAA;AAAA,YAAA,EAAA,GATQyc,CAUV,CAEH,IACD;AAAA,YAEH9F,EAAiBzS,CAAO,EAAEnE,UAAU,KACnC5E,gBAAAA,MAAC,SACC,WAAWoI,EAAO,yBAAyB,GAC3C,KAAKoT,EAAiBzS,CAAO,EAAE,CAAC,GAChC,KAAI;YAIPyS,EAAiBzS,CAAO,EAAEnE,SAAS,KACjC5E,gBAAAA,EAAA,IAAA,OAAA,EACC,WAAWoI,EAAO,0BAA0B,GAC5C,OACE;AAAA,cACE,iBACAoT,EAAiBzS,CAAO,EAAEnE;AAAAA,YAAAA,GAI7B4W,UAAiBzS,EAAAA,CAAO,EAAEvH,IAAI,CAACogB,GAAON,MAEnCthB,gBAAAA,MAAC,OACC,EAAA,WACEoI,EAAO,+BAA+B,GAGxC,KAAKwZ,GACL,KAAI,GAAA,GAFCN,CAGL,CAEL,EACH,CAAA;AAAA,UAAA,GAEJ;AAAA,WAECvY,KAAAA,gBAAAA,EAAS8Y,cACP7hB,gBAAAA,MAAA,OAAA,EAAI,WAAWoI,EAAO,oBAAoB,GACzC,UAAApI,gBAAAA,EAAAA,IAAC,WAAM,KAAK+I,EAAQ8Y,WAAW,UAAQ,GAAG,CAAA,GAC5C;AAAA,UAGD7hB,gBAAAA,EAAA,IAAA,OAAA,EAAI,WAAWoI,EAAO,0BAA0B,GAC9C+X,UACGnf,IAAAA,EAAOC,KAAK6gB,YACZ/Y,EAAQwP,KAAKC,iBACnB,CAAA;AAAA,QAAA,EAAA,CACF,EACF,CAAA;AAAA,QACC8H,2BAAkCxO;WA5MtB/I,EAAQnG,EA6MvB;AAAA,IAEH,CAAA,GACH;AAAA,IACC6I,gBAAAA,EAAA,KAAA,OAAA,EAAI,WAAWrD,EAAO,kBAAkB,GACvC,UAAA;AAAA,MAACpI,gBAAAA,EAAAA,IAAAyQ,IAAA,EACC,SAASgK,IACT,gBAAA9I,GACA,CAAA;AAAA,4BAEDiC,IACC,eAAAlV,IACA,iBAAAyV,GACA,cAAAC,GACA,gBAAAE,IACA,iBAAiB,MAAM;;AAEjBmG,YAAAA,GAAY7V,SAAS,GAAG;AAC1BgT,UAAAA,EAAe,CAAA,CAAE;AACjB;AAAA,QAAA;AAGFnC,SAAAA,IAAAA,EAASjN,YAATiN,QAAAA,EAAkB2C,SAClB1C,EAAa,GAAG,GAChBoC,GAAS,EAAE;AAAA,MAAA,GAEb,WAAAzD,GACA,WAAAM,GACA,cAAAe,GACA;AAAA,6BACD,SACC,EAAA,WAAWhM,EAAKtB,EAAO,wBAAwB,GAAG;AAAA,QAChD,CAACA,EAAO,+BAA+B,CAAC,GACxC+N,EAAavR,WAAW;AAAA,MAAA,CACzB,GACD,SAAQ,cAER,UAAA;AAAA,QAAA5E,gBAAAA,EAAA,IAAC,YACC,EAAA,IAAG,cACH,KAAKyV,GACL,WAAWrN,EAAO,YAAY,GAC9B,aAAapH,EAAOC,KAAK8gB,MAAM,OAAO,GACtC,SAAUza,CAAAA,MAAMmQ,GAAQnQ,EAAEsT,cAAcwC,KAAK,GAC7C,OAAO1c,GACP,WAAW6Z,IACX,SAASjG,IACT,SAASA,IACT,SAAS+I,IACT,MAAMjH,IACN,WAAA0G,IACA,OAAO;AAAA,UACLxN,UAAUiR,EAAejR;AAAAA,UACzBC,YAAYgR,EAAehR;AAAAA,QAAAA,GAE7B;AAAA,QACD4G,EAAavR,UAAU,KACtB5E,gBAAAA,EAAA,IAAC,OAAI,EAAA,WAAWoI,EAAO,eAAe,GACnC+N,UAAAA,EAAa3U,IAAI,CAACogB,GAAON,4BAErB,OAEC,EAAA,WAAWlZ,EAAO,cAAc,GAChC,OAAO;AAAA,UAAE4Z,iBAAiB,QAAQJ,CAAK;AAAA,QAAK,GAE5C,UAAC5hB,gBAAAA,EAAA,IAAA,OAAA,EAAI,WAAWoI,EAAO,mBAAmB,GACxC,UAAApI,gBAAAA,EAAA,IAACmV,IACC,EAAA,aAAa,MAAM;AACjBhB,UAAAA,EACEgC,EAAa9U,OAAO,CAAC4gB,GAAGrQ,MAAMA,MAAM0P,CAAK,CAC3C;AAAA,QAAA,GAEF,EACJ,CAAA,EAAA,GAZKA,CAaP,CAEH,GACH;AAAA,QAEFthB,gBAAAA,MAAC8L,MACC,MAAM9L,gBAAAA,EAAA,IAACkiB,MAAa,GACpB,MAAMlhB,EAAOC,KAAKkhB,MAClB,WAAW/Z,EAAO,iBAAiB,GACnC,MAAK,WACL,SAAS,MAAM2P,GAASrX,CAAS,EACjC,CAAA;AAAA,MAAA,EACJ,CAAA;AAAA,IAAA,EACF,CAAA;AAAA,EAAA,EAAA,CACF,EACF,CAAA,EAAA,GAhUgC0J,EAAQxH,EAiU1C,EACF,CAAA;AAEJ;AAEO,SAAS3B,KAAO;AAEfmJ,QAAAA,IADYC,EAAa,EACLC,eAAe;AAClC,SAAAtK,gBAAAA,EAAA,IAACwV,IAAWpL,IAAAA,EAAQxH,EAAI;AACjC;"}