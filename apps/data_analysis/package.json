{"name": "data_analysis", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite --host", "dev:pre": "vite --host --mode pre", "dev:test": "vite --host --mode test", "dev:zqx": "vite --host --mode zqx", "dev:cjt": "vite --host --mode cjt", "dev:xmh": "vite --host --mode xmh", "build": "vite build", "build:test": "vite build --mode test", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview"}, "dependencies": {"@ant-design/cssinjs": "^1.22.1", "@ant-design/icons": "^5.5.2", "@ant-design/pro-components": "^2.8.2", "@antfu/eslint-config": "^3.12.1", "@fortaine/fetch-event-source": "^3.0.6", "@ly/abu-ai": "workspace:*", "@ly/utils": "workspace:*", "@ly/wecom": "workspace:*", "@ohh-889/react-auto-route": "^0.3.5", "@reduxjs/toolkit": "^2.5.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/react-query": "^5.62.10", "@tanstack/react-table": "^8.20.6", "ahooks": "^3.8.4", "antd": "^5.25.3", "antd-mobile": "^5.38.1", "classnames": "^2.5.1", "currency.js": "^2.0.4", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-stat": "^1.2.0", "highlight.js": "^11.11.1", "keepalive-for-react": "^4.0.2", "keepalive-for-react-router": "1.x.x", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-highlightjs": "^4.2.0", "pinyin-pro": "^3.26.0", "postcss-pxtorem": "^6.1.0", "qs": "^6.13.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-error-boundary": "^5.0.0", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-redux": "^9.2.0", "react-router-dom": "6.20.0", "redux-persist": "^6.0.0", "rehype-highlight": "^7.0.1", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "swiper": "^11.1.15", "vconsole": "^3.15.1"}, "devDependencies": {"@eslint-react/eslint-plugin": "^1.23.0", "@eslint/js": "^9.17.0", "@types/lodash-es": "^4.17.12", "@types/node": "^22.10.2", "@types/qs": "^6.9.17", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.13.0", "postcss": "^8.4.18", "sass": "^1.83.0", "tailwindcss": "^4.1.8", "typescript": "~5.6.2", "typescript-eslint": "^8.18.1", "vite": "^6.0.3", "vite-plugin-svgr": "^4.3.0"}}