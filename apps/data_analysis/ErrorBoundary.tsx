import type { FC } from 'react'
import type { FallbackProps } from 'react-error-boundary'
import styles from '@/styles/errPage.module.scss'
import { ArrowLeftOutlined, HomeOutlined, ReloadOutlined } from '@ant-design/icons'
import { Button, Result } from 'antd'
import { useNavigate } from 'react-router-dom'

type Props = Partial<FallbackProps>

const ErrorPage: FC<Props> = ({ error, resetErrorBoundary }) => {
  const navigate = useNavigate()

  const handleBackHome = () => {
    navigate('/')
  }

  const handleBack = () => {
    navigate(-1)
  }

  const handleRefresh = () => {
    resetErrorBoundary?.()
    // window.location.reload()
  }

  return (
    <div className={styles.errorPage}>
      <Result
        status={error?.status === 404 ? '404' : 'error'}
        title={error?.status || '500'}
        subTitle={(
          <div className={styles.errorInfo}>
            <p className={styles.mainText}>
              {error.statusText || '抱歉，服务器开小差了'}
            </p>
            <p className={styles.subText}>
              {error.message || '请稍后再试或返回上一页'}
            </p>
          </div>
        )}
        extra={[
          <Button
            key="back"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
          >
            返回上页
          </Button>,
          <Button
            key="home"
            type="primary"
            icon={<HomeOutlined />}
            onClick={handleBackHome}
          >
            返回首页
          </Button>,
          <Button
            key="refresh"
            icon={<ReloadOutlined />}
            onClick={handleRefresh}
          >
            刷新页面
          </Button>,
        ]}
      />
    </div>
  )
}

export default ErrorPage
