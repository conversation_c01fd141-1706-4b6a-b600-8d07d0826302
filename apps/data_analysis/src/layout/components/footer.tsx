import { useMobileScreen } from '@/utils'
import { Layout } from 'antd'
import { Footer } from 'antd-mobile'

function LayoutFooter() {
  const isMobile = useMobileScreen()

  const context = (
    <span>
      {CURRENT_VERSION}
      {' '}
      commit hash
      {' '}
      {CURRENT_GITHASH}
    </span>
  )

  return isMobile
    ? <Footer content={context}></Footer>
    : (
        <Layout.Footer className="text-[12px] text-[#c9c9c9] flex justify-center items-center mt-2">
          {context}
        </Layout.Footer>
      )
}

export default LayoutFooter
