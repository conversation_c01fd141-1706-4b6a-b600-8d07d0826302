import type { MenuProps } from 'antd'
import { Component as AbuIframe } from '@/pages/abu-iframe'
import { useMobileScreen } from '@/utils'
import { BarChartOutlined, FunctionOutlined, LoadingOutlined, MessageOutlined } from '@ant-design/icons'
import { Menu, Spin } from 'antd'
import { TabBar } from 'antd-mobile'
import KeepAlive, { useKeepAliveRef } from 'keepalive-for-react'
import { Suspense, useEffect, useMemo, useState } from 'react'
import { Outlet, useLocation, useNavigate, useOutlet } from 'react-router-dom'
import styles from './index.module.scss'

export function Component() {
  const navigate = useNavigate()
  const location = useLocation()
  const [showChatIframe, setShowChatIframe] = useState(false)
  const isMobile = useMobileScreen()
  // 判断是否为登录页面或其他不需要菜单的页面
  const shouldHideMenu = useMemo(() => {
    const hideMenuPaths = ['/login', '/auth', '/register'] // 可以根据需要添加更多路径
    return hideMenuPaths.some(path => location.pathname.startsWith(path))
  }, [location.pathname])

  const menuItems: MenuProps['items'] = [
    {
      key: 'chat',
      // icon: <MessageOutlined />,
      label: (
        <div className="flex flex-col items-center">
          <MessageOutlined style={{ fontSize: '20px', marginBottom: '4px' }} />
          <span style={{ fontSize: '12px', marginInlineStart: 'unset' }}>对话</span>
        </div>
      ),
      onClick: () => {
        setShowChatIframe(true)
      },
    },
    {
      key: 'function',
      // icon: <FunctionOutlined />,
      label: (
        <div className="flex flex-col items-center">
          <FunctionOutlined style={{ fontSize: '20px', marginBottom: '4px' }} />
          <span style={{ fontSize: '12px', marginInlineStart: 'unset' }}>功能</span>
        </div>
      ),
      onClick: () => {
        setShowChatIframe(false)
        navigate('/function-dashboard')
      },
    },
    {
      key: 'statistics',
      // icon: <BarChartOutlined />,
      label: (
        <div className="flex flex-col items-center">
          <BarChartOutlined style={{ fontSize: '20px', marginBottom: '4px' }} />
          <span style={{ fontSize: '12px', marginInlineStart: 'unset' }}>统计</span>
        </div>
      ),
      onClick: () => {
        setShowChatIframe(false)
        navigate('/dashboard')
      },
    },
  ]
  const tabBarItems = [
    {
      key: 'chat',
      title: '对话',
      icon: <MessageOutlined />,
    },
    {
      key: 'function',
      title: '功能',
      icon: <FunctionOutlined />,
    },
    {
      key: 'statistics',
      title: '统计',
      icon: <BarChartOutlined />,
    },
  ]

  const handleTabChange = (key: string) => {
    switch (key) {
      case 'chat':
        setShowChatIframe(true)
        break
      case 'function':
        setShowChatIframe(false)
        navigate('/function-dashboard')
        break
      case 'statistics':
        setShowChatIframe(false)
        navigate('/dashboard')
        break
    }
  }
  // 根据当前路径设置选中的菜单项
  const selectedKey = useMemo(() => {
    if (showChatIframe)
      return 'chat'
    if (location.pathname === '/function-dashboard')
      return 'function'
    if (location.pathname === '/dashboard')
      return 'statistics'
    return ''
  }, [location.pathname, showChatIframe])
  // 如果是登录页面等，直接返回内容区域，不显示菜单
  if (shouldHideMenu) {
    return (
      <div className="h-screen">
        <ComponentContainer />
      </div>
    )
  }
  if (isMobile) {
    // 移动端布局：菜单在底部
    return (
      <div className="flex flex-col h-screen">
        {/* 主内容区域 */}
        <div className="flex-1 overflow-hidden">
          {showChatIframe
            ? (
                <AbuIframe />
              )
            : (
                <ComponentContainer />
              )}
        </div>

        {/* 底部菜单栏 */}
        <TabBar
          activeKey={selectedKey}
          onChange={handleTabChange}
        >
          {tabBarItems.map(item => (
            <TabBar.Item key={item.key} icon={item.icon} title={item.title} />
          ))}
        </TabBar>
      </div>
    )
  }
  return (
    <div className="flex h-screen">
      {/* 左侧边栏 */}
      <div className="w-28 bg-white shadow-md border-r border-gray-200">
        <div className="h-full flex flex-col justify-center">
          <Menu
            mode="vertical"
            selectedKeys={[selectedKey]}
            items={menuItems}
            className={styles.menu}
            style={{
              padding: '0 16px',
            }}
          />
        </div>
      </div>

      {/* 右侧内容区域 */}
      <div className="flex-1 overflow-hidden">
        {showChatIframe
          ? (
              <AbuIframe />
            )
          : (
              <ComponentContainer />
            )}
      </div>
    </div>
  )
}

export function ComponentContainer() {
  const location = useLocation()
  const aliveRef = useKeepAliveRef()
  // 确定哪个路由组件处于活动状态
  const currentCacheKey = useMemo(() => {
    return location.pathname + location.search
  }, [location.pathname, location.search])
  const outlet = useOutlet()
  return (

    <KeepAlive aliveRef={aliveRef} activeCacheKey={currentCacheKey} max={18}>
      <Suspense fallback={(
        <Spin
          indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />}
          className="flex justify-center items-center h-screen"
        />
      )}
      >
        {outlet}
      </Suspense>
    </KeepAlive>
  )
}
