.menu{
  :global {
    .ant-menu-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px !important;
      line-height: 1 !important;
      margin-bottom: 20px;
    }
  }
}
.mobileMenu {
  // 移动端底部菜单样式
  display: flex;
  justify-content: space-evenly;
  :global {
    .ant-menu-item {
      // flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0 !important;
      padding: 8px 4px !important;
      &:hover {
        background-color: #f0f0f0;
      }
      
      &.ant-menu-item-selected {
        background-color: #e6f7ff;
        color: #1890ff;
        
        &::after {
          display: none;
        }
      }
    }
  }
}
