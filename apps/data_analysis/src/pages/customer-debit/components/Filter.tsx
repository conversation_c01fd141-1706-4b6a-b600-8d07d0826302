import type { TableColumnsType, TablePaginationConfig } from 'antd'
import type { SearchBarRef } from 'antd-mobile'
import type { TableRowSelection } from 'antd/es/table/interface'

import { FetchEmployeeList } from '@/service/api/employee'
import { FetchSaleSystemList } from '@/service/api/saleSystem'
import { EmployeeType } from '@/service/request/type'
import { useMessage } from '@/utils/message'
import { Flex, Table, Typography } from 'antd'
import { Button, Popup, SearchBar, Space } from 'antd-mobile'
import { useEffect, useRef, useState } from 'react'

export interface FilterOptions {
  businessPerson: string // 销售员
  salesOrder: string // 销售跟单
  marketingSystem: string // 营销体系
  businessPersonId?: number // 销售员id
  salesOrderId?: number // 销售跟单id
  marketingSystemId?: number // 营销体系id
}
const { Title } = Typography
export interface FilterProps {
  visible: boolean
  onClose: () => void
  onConfirm: (options: FilterOptions) => void
}
interface PopupTableProps {
  title: string
  type: 'employee' | 'saleSystem'
  duty?: EmployeeType
  visible: boolean
  onChange?: (visible: boolean) => void
  onSelect?: (selected: { id: number, name: string }[]) => void
}
function PopupTable(props: PopupTableProps) {
  const { showMessage, contextHolder } = useMessage()
  const { type, duty, visible, onChange, title, onSelect } = props
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<any[]>([])
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])

  // 添加表格参数状态
  const [tableParams, setTableParams] = useState({
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
    } as TablePaginationConfig,
  })
  // 获取销售员或销售跟单数据
  const { mutate: fetchEmployee } = FetchEmployeeList({
    onSuccess: (res) => {
      const processedData = res.list?.map(item => ({
        id: item.id,
        key: item.id,
        name: item.name,
        department_name: item.department_name,
      })) || []
      setTableParams(prev => ({
        ...prev,
        pagination: {
          ...prev.pagination,
          total: res.total ?? 0,
        },
      }))
      setData(processedData)
      setLoading(false)
    },
    onError: (error) => {
      showMessage.info(error.message)
      setLoading(false)
    },
  })

  // 获取营销体系数据
  const { mutate: fetchSaleSystem } = FetchSaleSystemList({
    onSuccess: (res) => {
      const processedData = res.list.map(item => ({
        id: item.id,
        key: item.id,
        name: item.name,
        code: item.code,
      }))
      setTableParams(prev => ({
        ...prev,
        pagination: {
          ...prev.pagination,
          total: res.total,
        },
      }))
      setData(processedData)
      setLoading(false)
    },
    onError: (error) => {
      showMessage.info(error.message)
      setLoading(false)
    },
  })

  useEffect(() => {
    if (visible) {
      setLoading(true)
      if (type === 'employee') {
        fetchEmployee({ duty: duty! })
      }
      else {
        fetchSaleSystem({})
      }
    }
  }, [visible, type, duty])

  const columns: TableColumnsType<any> = type === 'employee'
    ? [
        {
          title: '姓名',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '部门',
          dataIndex: 'department_name',
          key: 'department_name',
        },
      ]
    : [
        {
          title: '营销体系',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '编号',
          dataIndex: 'code',
          key: 'code',
        },
      ]

  const [innerValue, setInnerValue] = useState(false)
  const onClose = () => {
    setInnerValue(false)
    onChange?.(false)
    setSelectedRowKeys([])
  }

  const handleSearch = (val: string) => {
    setLoading(true)
    if (type === 'employee') {
      fetchEmployee({
        duty: duty!,
        name: val || undefined,
      })
    }
    else {
      fetchSaleSystem({
        name: val || undefined,
      })
    }
  }

  const rowSelection: TableRowSelection<any> = {
    type: 'radio',
    columnWidth: 50,
    selectedRowKeys,
    onChange: (selectedKeys) => {
      setSelectedRowKeys(selectedKeys)
    },
  }
  const [tableHeight, setTableHeight] = useState(0)
  const [tableWidth, setTableWidth] = useState(0)
  const setLayout = () => {
    const tableDom = document.querySelector('#filter-table-container') as HTMLElement
    if (tableDom) {
      const availableHeight = tableDom.offsetHeight - 90
      setTableWidth(tableDom.offsetWidth)
      console.log('availableHeight', availableHeight)
      setTableHeight(availableHeight)
    }
  }
  useEffect(() => {
    if (innerValue) {
      setTimeout(() => {
        setLayout()
      }, 100)
      // 监听窗口大小变化
      window.addEventListener('resize', setLayout)
      return () => {
        window.removeEventListener('resize', setLayout)
      }
    }
  }, [innerValue])
  function onConfirm() {
    const selectedItem = data.find(item => selectedRowKeys.includes(item.key))
    if (selectedItem) {
      onSelect?.([{ id: selectedItem.id, name: selectedItem.name }])
    }
    onClose()
  }

  // 添加表格变化处理函数
  const handleTableChange = (pagination: TablePaginationConfig) => {
    setTableParams({
      pagination: {
        ...pagination,
      },
    })
  }
  const searchRef = useRef<SearchBarRef>(null)
  const handleSearchBtn = () => {
    handleSearch(searchRef.current?.nativeElement?.value || '')
  }
  // 修复 visible 状态同步
  useEffect(() => {
    setInnerValue(visible)
  }, [visible])

  return (
    <>
      {contextHolder}
      <Popup
        bodyStyle={{ width: '80vw' }}
        visible={innerValue}
        onMaskClick={onClose}
        position="left"
      >
        <Flex vertical className="w-full h-full p-2">
          <Title level={4}>
            筛选
            {title}
          </Title>
          <Flex className="w-full mt-2">
            <SearchBar className="flex-1 mr-2" ref={searchRef} placeholder={`请输入${title}`} onSearch={handleSearch} onClear={() => handleSearch('')} />
            <Button color="primary" onClick={handleSearchBtn} size="small">搜索</Button>
          </Flex>
          <div id="filter-table-container" className="flex-1 mt-2 overflow-hidden">
            <Table<any>
              scroll={{ x: tableWidth, y: tableHeight }}
              virtual
              rowSelection={rowSelection}
              loading={loading}
              onChange={handleTableChange}
              pagination={tableParams.pagination}
              dataSource={data}
              columns={columns}
            />
          </div>
          <Space wrap className="w-full mt-2" justify="end">
            <Button color="default" onClick={onClose} size="small">取消</Button>
            <Button color="primary" disabled={!selectedRowKeys.length} loading={loading} onClick={onConfirm} size="small">确定</Button>
          </Space>
        </Flex>
      </Popup>
    </>
  )
}

function Filter({ visible, onClose, onConfirm }: FilterProps) {
  const [showFilter, setShowFilter] = useState(false)

  useEffect(() => {
    setShowFilter(visible)
  }, [visible])

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    businessPersonId: undefined,
    businessPerson: '',
    salesOrderId: undefined,
    salesOrder: '',
    marketingSystemId: undefined,
    marketingSystem: '',
  })
  const [leftPopupVisible, setLeftPopupVisible] = useState(false)
  const [leftPopupTitle, setLeftPopupTitle] = useState('')

  const handleSelect = (selected: { id: number, name: string }[]) => {
    if (selected.length > 0) {
      const selectedItem = selected[0]
      const key = (() => {
        switch (leftPopupTitle) {
          case '销售员':
            return 'businessPerson'
          case '销售跟单':
            return 'salesOrder'
          case '营销体系':
            return 'marketingSystem'
          default:
            return ''
        }
      })()

      if (key) {
        setFilterOptions(prev => ({
          ...prev,
          [key]: selectedItem.name,
          [`${key}Id`]: selectedItem.id,
        }))
      }
    }
  }

  // 添加按钮点击处理函数
  const handleButtonClick = (title: string) => {
    setLeftPopupTitle(title)
    setLeftPopupVisible(true)
  }

  const getButtonText = (type: keyof FilterOptions) => {
    const value = filterOptions[type]
    switch (type) {
      case 'businessPerson':
        return value || '选择销售员'
      case 'salesOrder':
        return value || '选择销售跟单'
      case 'marketingSystem':
        return value || '选择营销体系'
      default:
        return '请选择'
    }
  }

  const handleReset = () => {
    const reset = {
      businessPerson: '',
      salesOrder: '',
      marketingSystem: '',
      businessPersonId: undefined,
      salesOrderId: undefined,
      marketingSystemId: undefined,
    }
    setFilterOptions(reset)
    onConfirm(reset)
    onClose()
  }

  return (
    <>
      <Popup
        visible={showFilter}
        onMaskClick={onClose}
        position="bottom"
      >
        <Flex vertical className="p-4 h-full">
          <div className="flex-1">

            <div className="mb-4 text-center">
              <Title level={4}>筛选</Title>
            </div>

            <div className="mb-4">
              <Title level={5}>销售员</Title>
              <Button
                color="default"
                onClick={() => handleButtonClick('销售员')}
                className="w-full mt-2 text-gray-400 cursor-pointer bg-[#f2f2f2]"
              >
                {getButtonText('businessPerson')}
              </Button>
            </div>

            <div className="mb-4">
              <Title level={5}>销售跟单</Title>
              <Button
                color="default"
                onClick={() => handleButtonClick('销售跟单')}
                className="w-full mt-2 text-gray-400 cursor-pointer bg-[#f2f2f2]"
              >
                {getButtonText('salesOrder')}
              </Button>
            </div>

            <div className="mb-4">
              <Title level={5}>营销体系</Title>
              <Button
                color="default"
                onClick={() => handleButtonClick('营销体系')}
                className="w-full mt-2 text-gray-400 cursor-pointer bg-[#f2f2f2]"
              >
                {getButtonText('marketingSystem')}
              </Button>
            </div>
          </div>

          <div className="flex gap-4 mt-8">
            <Button
              block
              onClick={handleReset}
            >
              重置
            </Button>
            <Button
              block
              color="primary"
              disabled={!filterOptions.businessPerson && !filterOptions.salesOrder && !filterOptions.marketingSystem}
              onClick={() => {
                onConfirm({
                  businessPerson: filterOptions.businessPerson,
                  salesOrder: filterOptions.salesOrder,
                  marketingSystem: filterOptions.marketingSystem,
                  businessPersonId: filterOptions.businessPersonId,
                  salesOrderId: filterOptions.salesOrderId,
                  marketingSystemId: filterOptions.marketingSystemId,
                })
                onClose()
              }}
            >
              确定
            </Button>
          </div>
        </Flex>
      </Popup>
      <PopupTable
        type={leftPopupTitle === '营销体系' ? 'saleSystem' : 'employee'}
        duty={leftPopupTitle === '销售员' ? EmployeeType.salesman : EmployeeType.follower}
        title={leftPopupTitle}
        visible={leftPopupVisible}
        onChange={setLeftPopupVisible}
        onSelect={handleSelect}
      />
    </>
  )
}

export default Filter
