import type { FetchSaleSystemListParams } from '@/service/api'
import type { PaginationResponse } from '@/service/request/type'
import type { GetProp, TableColumnsType, TablePaginationConfig, TableProps } from 'antd'
import type { RangePickerProps } from 'antd/es/date-picker'
import type { SorterResult } from 'antd/es/table/interface'
import type { Key } from 'react'
import type { FilterOptions } from './components/Filter'
import DateRangePicker from '@/components/dateRangePicker'
import HeaderBox from '@/components/headerBox'
import IconText from '@/components/iconText'
import SelectComponent from '@/components/selectComponent'
import { useRouterPush } from '@/hooks/routerPush'
import { FetchCustomerBillList, FetchCustomerList, FetchEmployeeList, FetchSaleSystemList } from '@/service/api'
import { PAGE, SIZE } from '@/service/request/share'
import { EmployeeType } from '@/service/request/type'
import { useMobileScreen } from '@/utils'
import { processDataOut } from '@/utils/handBinary'
import { useMessage } from '@/utils/message'
import { formatPriceDiv, getFilterData } from '@ly/utils'
import { Card, DatePicker, Descriptions, Flex, Splitter, Table, Tooltip } from 'antd'
import { SearchBar } from 'antd-mobile'
import classnames from 'classnames'
import dayjs from 'dayjs'
import { useEffectOnActive } from 'keepalive-for-react'
import { uniqueId as _uniqueId } from 'lodash-es'
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react'
import { FaRegCalendar } from 'react-icons/fa'
import { RiFilterLine } from 'react-icons/ri'
import { CustomerReconciliationContainer } from '../customer-reconciliation'
import Filter from './components/Filter'
import styles from './index.module.scss'

type DataType = Api.ShouldCollectOrder.CustomerBill

interface LeftPanelProps {
  filterParams: Api.ShouldCollectOrder.FetchCustomerBillListParams
  originData: Api.ShouldCollectOrder.CustomerBill
  data: DataType[]
  isLoading: boolean
  onTableChange: (params: TableParams) => void
  onChangeParams: (params: Api.ShouldCollectOrder.FetchCustomerBillListParams) => void
}

function LeftPanel(props: LeftPanelProps) {
  const [filterParams, setFilterParams] = useState<Api.ShouldCollectOrder.FetchCustomerBillListParams>(props.filterParams)
  useEffect(() => {
    setFilterParams(props.filterParams)
  }, [props.filterParams])

  const [searchBar, setSearchBar] = useState({
    customer_name: '',
    sale_user_name: '',
    order_follower_name: '',
    department_name: '',
  })

  const handleDateChange: RangePickerProps['onChange'] = (dates) => {
    if (dates) {
      const tempFilterParams = { ...filterParams, start_time: dayjs(dates[0]).format('YYYY-MM-DD'), end_time: dayjs(dates[1]).format('YYYY-MM-DD') }
      setFilterParams(tempFilterParams)
      props.onChangeParams(tempFilterParams)
    }
    else {
      const tempFilterParams = { ...filterParams, start_time: undefined, end_time: undefined }
      setFilterParams(tempFilterParams)
      props.onChangeParams(tempFilterParams)
    }
  }
  const handleCustomerChange = (value: number) => {
    const tempFilterParams = { ...filterParams, customer_id: value }
    setFilterParams(tempFilterParams)
    props.onChangeParams(tempFilterParams)
  }

  const handleSalesmanChange = (value: number) => {
    const tempFilterParams = { ...filterParams, sale_user_id: value }
    setFilterParams(tempFilterParams)
    props.onChangeParams(tempFilterParams)
  }

  const handleFollowerChange = (value: number) => {
    const tempFilterParams = { ...filterParams, order_follower_id: value }
    setFilterParams(tempFilterParams)
    props.onChangeParams(tempFilterParams)
  }

  function handleDepartmentChange(value: number) {
    const tempFilterParams = { ...filterParams, sale_system_id: value }
    setFilterParams(tempFilterParams)
    props.onChangeParams(tempFilterParams)
  }
  function handleSearch(field: string, value: string) {
    setSearchBar(prev => ({ ...prev, [field]: value }))
  }
  return (
    <Flex vertical style={{ height: '100%' }}>
      <Card className={classnames(styles['left-panel-filter'], 'mx-2')}>
        <Descriptions styles={{ label: { display: 'flex', alignItems: 'center' } }} column={{ xs: 1, sm: 3, md: 3, lg: 3, xl: 3, xxl: 4 }}>
          <Descriptions.Item label="时间" span={2}>
            <DatePicker.RangePicker className="w-full" allowClear={false} value={filterParams.start_time && filterParams.end_time ? [dayjs(filterParams.start_time), dayjs(filterParams.end_time)] : undefined} onChange={handleDateChange} />
          </Descriptions.Item>
          <Descriptions.Item label="客户">
            <SelectComponent<PaginationResponse<Api.Customer.Response>, Api.Customer.Request>
              api={FetchCustomerList}
              showSearch
              onSearch={value => handleSearch('customer_name', value)}
              params={{ name: searchBar.customer_name, page: 1, size: 200 }}
              placeholder="请选择客户"
              labelField="name"
              valueField="id"
              onChange={handleCustomerChange}
            />
            {/* <Input placeholder="请输入客户名称" onChange={handleCustomerChange} /> */}
          </Descriptions.Item>
          <Descriptions.Item label="销售员">
            <SelectComponent<PaginationResponse<Api.Employee.Response>, Api.Employee.FetchEmployeeListParams>
              api={FetchEmployeeList}
              showSearch
              onSearch={value => handleSearch('sale_user_name', value)}
              params={{ duty: EmployeeType.salesman, name: searchBar.sale_user_name }}
              placeholder="请选择员工"
              labelField="name"
              valueField="id"
              onChange={handleSalesmanChange}
            />
          </Descriptions.Item>
          <Descriptions.Item label="跟单员">
            <SelectComponent<PaginationResponse<Api.Employee.Response>, Api.Employee.FetchEmployeeListParams>
              api={FetchEmployeeList}
              showSearch
              onSearch={value => handleSearch('order_follower_name', value)}
              params={{ duty: EmployeeType.follower, name: searchBar.order_follower_name }}
              placeholder="请选择跟单员"
              labelField="name"
              valueField="id"
              onChange={handleFollowerChange}
            />
          </Descriptions.Item>
          <Descriptions.Item label="部门">
            <SelectComponent<PaginationResponse<Api.SaleSystem.Response>, FetchSaleSystemListParams>
              api={FetchSaleSystemList}
              showSearch
              onSearch={value => handleSearch('department_name', value)}
              params={{ name: searchBar.department_name }}
              placeholder="请选择营销体系"
              labelField="name"
              valueField="id"
              onChange={handleDepartmentChange}
            />
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card id="customer-table-card" className="h-full mt-2 mx-2">
        <CustomerTable filterParams={filterParams} total={props.originData?.total || 0} data={props.data} isLoading={props.isLoading} onTableChange={props.onTableChange}></CustomerTable>
      </Card>
    </Flex>
  )
}
function RightPanel() {
  return (
    <Flex justify="center" align="center">
      <Card title="客户对账单" bordered className={classnames('w-full h-full mx-2', styles['right-panel-card'])}>
        <CustomerReconciliationContainer />
      </Card>
    </Flex>
  )
}

interface DesktopPageProps {
  filterParams: Api.ShouldCollectOrder.FetchCustomerBillListParams
  originData: Api.ShouldCollectOrder.CustomerBill
  data: DataType[]
  isLoading: boolean
  onTableChange: (params: TableParams) => void
  onChangeParams: (params: Api.ShouldCollectOrder.FetchCustomerBillListParams) => void
}
function DesktopPage(props: DesktopPageProps) {
  const { contextHolder } = useMessage()
  return (
    <>
      {contextHolder}
      <Flex vertical className="h-screen overflow-hidden">
        <HeaderBox />
        {/* 分隔板 */}
        <Splitter lazy className="flex-1 mt-2">
          <Splitter.Panel defaultSize="50%" min="20%" max="70%">
            <LeftPanel {...props} />
          </Splitter.Panel>
          <Splitter.Panel>
            <RightPanel />
          </Splitter.Panel>
        </Splitter>
      </Flex>
    </>
  )
}
interface CustomerTableProps {
  filterParams: Api.ShouldCollectOrder.FetchCustomerBillListParams
  isLoading?: boolean
  data?: DataType[]
  total: number
  onTableChange?: (params: TableParams) => void
}
export interface CustomerTableRef {
  getWidth: () => void
}
interface TableParams {
  pagination?: TablePaginationConfig | false
  sortField?: SorterResult<any>['field']
  sortOrder?: SorterResult<any>['order']
  filters?: Parameters<GetProp<TableProps, 'onChange'>>[1]
}
// 客户欠款表格 手机端 PC端复用
const CustomerTable = forwardRef<CustomerTableRef, CustomerTableProps>((props, ref) => {
  const { isLoading, data, onTableChange, filterParams } = props
  const [tableData, setTableData] = useState<DataType[]>([])

  useEffect(() => {
    if (data) {
      setTableData(data.map((item, index) => ({ ...item, index })))
    }
  }, [data])

  // 设置tabs高度
  const [scrollDivHeight, setHeight] = useState(0)
  const [tableWidth, setTableWidth] = useState(0)
  const getWidth = () => {
    const tableDom = document.querySelector('.ant-table-header') as HTMLElement
    if (tableDom) {
      setTableWidth(tableDom.offsetWidth)
    }
  }
  const isMobile = useMobileScreen()
  console.log('useMobileScreen', isMobile)
  const getHeight = () => {
    if (isMobile) {
      const searchBarDom = document.querySelector(`#customer-table`) as HTMLElement
      const tableHeaderDom = document.querySelector(`#customer-table .ant-table-header`) as HTMLElement
      if (searchBarDom) {
        // 计算可用高度 = 视窗高度 - 搜索栏高度 - Tabs头部高度
        const availableHeight = window.innerHeight - searchBarDom.offsetTop - tableHeaderDom.offsetHeight
        setHeight(availableHeight)
      }
    }
    else {
      const scrollableDiv2Dom = document.querySelector(`#customer-table-card`) as HTMLElement
      console.log('scrollableDiv2Dom', scrollableDiv2Dom)
      if (scrollableDiv2Dom) {
        // 计算可用高度 = 视窗高度 - table头部高度
        const availableHeight = window.innerHeight - scrollableDiv2Dom.offsetTop - 100
        console.log('availableHeight', availableHeight)
        setHeight(availableHeight)
      }
    }
  }

  const [tableParams, setTableParams] = useState<TableParams>({
    pagination: isMobile
      ? { current: PAGE, pageSize: SIZE }
      : {
          current: PAGE,
          pageSize: SIZE,
        },
  })

  // 添加排序状态
  const [sortInfo, setSortInfo] = useState<{
    field?: string
    order?: 'ascend' | 'descend'
  }>({})

  // 恢复表格布局相关代码
  const getTableLayout = () => {
    if (isMobile) {
      setTableParams(prev => ({ ...prev, pagination: false }))
    }
    else {
      setTableParams(prev => ({ ...prev, pagination: { current: 1, pageSize: 100 } }))
    }
    setTimeout(() => {
      getWidth()
      getHeight()
    }, 100)
  }

  useEffectOnActive(() => {
    getTableLayout()
    // 监听窗口大小变化
    window.addEventListener('resize', getTableLayout)
    return () => {
      window.removeEventListener('resize', getTableLayout)
    }
  }, [])

  // 优化排序逻辑
  const sortedData = useMemo(() => {
    if (!sortInfo.field || !sortInfo.order || !tableData) {
      return tableData
    }

    return [...tableData].sort((a, b) => {
      const aValue = a[sortInfo.field as keyof DataType]
      const bValue = b[sortInfo.field as keyof DataType]

      // 处理不同类型的数据比较
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortInfo.order === 'ascend' ? aValue - bValue : bValue - aValue
      }

      // 字符串比较
      const aStr = String(aValue || '')
      const bStr = String(bValue || '')
      return sortInfo.order === 'ascend'
        ? aStr.localeCompare(bStr)
        : bStr.localeCompare(aStr)
    })
  }, [tableData, sortInfo])

  // 修改表格变化处理函数
  const handleTableChange: TableProps<DataType>['onChange'] = (
    pagination,
    filters,
    sorter,
  ) => {
    // 处理排序
    if ('field' in sorter) {
      setSortInfo({
        field: sorter.field as string,
        order: sorter.order as 'ascend' | 'descend',
      })
    }

    // 只传递分页信息给父组件
    onTableChange?.({
      pagination,
      filters,
    })
  }
  useImperativeHandle(ref, () => ({
    getWidth,
  }), [])
  const columns: TableColumnsType<DataType> = [
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 100,
      ellipsis: {
        showTitle: false,
      },
      render: customer_name => (
        <Tooltip trigger="click" placement="topLeft" title={customer_name}>
          {customer_name}
        </Tooltip>
      ),
      sorter: (a: any, b: any) => a.customer_name - b.customer_name,
    },
    {
      title: '销售员',
      dataIndex: 'sale_user_name',
      key: 'sale_user_name',
      width: 100,
      ellipsis: {
        showTitle: false,
      },
      sorter: (a: any, b: any) => a.sale_user_name - b.sale_user_name,
      render: sale_user_name => (
        <Tooltip trigger="click" placement="topLeft" title={sale_user_name}>
          {sale_user_name}
        </Tooltip>
      ),
    },
    {
      title: '欠款金额',
      dataIndex: 'end_period',
      key: 'end_period',
      width: 100,
      ellipsis: {
        showTitle: true,
      },
      sorter: (a: any, b: any) => a.end_period - b.end_period,
      render: end_period => (
        <Tooltip trigger="click" placement="topLeft" title={end_period}>
          ¥
          {end_period?.toLocaleString()}
        </Tooltip>
      ),
    },
  ]
  const { routerPushByKey } = useRouterPush()
  const [highlightedRowIndex, setHighlightedRowIndex] = useState<number | undefined>(undefined)
  const handleRowClick = (record: DataType, index: number) => {
    setHighlightedRowIndex(index)
    if (isMobile) {
      routerPushByKey('/customer-reconciliation', {
        state: {
          customer_id: record.customer_id,
          customer_name: record.customer_name,
          start_time: filterParams.start_time,
          end_time: filterParams.end_time,
        },
      })
      return
    }

    // PC端处理逻辑
    const customerChangeEvent = new CustomEvent('customerChanged', {
      detail: {
        customer_id: record.customer_id,
        customer_name: record.customer_name,
        start_time: filterParams.start_time,
        end_time: filterParams.end_time,
      },
    })
    window.dispatchEvent(customerChangeEvent)
  }
  return (
    <div id="customer-table">
      <Table<DataType>
        rootClassName={styles.tableContainer}
        onRow={(record, index) => ({
          onClick: () => handleRowClick(record, index!),
          className: index === highlightedRowIndex ? styles['highlighted-row'] : '',
        })}
        rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white! cursor-pointer' : 'bg-gray-50! cursor-pointer')}
        virtual
        rowKey={(record) => {
          return `${record.customer_id}_${record.index}`
        }}
        loading={isLoading}
        onChange={handleTableChange}
        scroll={{ x: tableWidth, y: scrollDivHeight }}
        pagination={isMobile ? false : tableParams.pagination}
        dataSource={sortedData} // 使用排序后的数据
        columns={columns}
      />
    </div>
  )
})

export function Component() {
  const [filterParams, setFilterParams] = useState<Api.ShouldCollectOrder.FetchCustomerBillListParams>({
    customer_name: undefined,
    sale_user_id: undefined,
    order_follower_id: undefined,
    sale_system_id: undefined,
    start_time: dayjs().startOf('month').format('YYYY-MM-DD'),
    end_time: dayjs().endOf('month').format('YYYY-MM-DD'),
  })
  const [filterVisible, setFilterVisible] = useState(false)

  const handleFilterConfirm = (options: FilterOptions) => {
    // 这里处理筛选逻辑
    setFilterParams(prev => ({ ...prev, sale_user_id: options.businessPersonId, order_follower_id: options.salesOrderId, sale_system_id: options.marketingSystemId,
    }))
  }
  function handleClickFilter() {
    setFilterVisible(true)
  }
  const isMobile = useMobileScreen()

  const [data, setData] = useState<DataType[]>()
  const [pagination, setPagination] = useState({ page: PAGE, size: SIZE })
  const { showMessage, contextHolder } = useMessage()
  // 添加获取客户欠款单列表的接口调用
  const { mutate: fetchBillList, isPending, data: originData } = FetchCustomerBillList({
    onSuccess: (res) => {
      setData(() => processDataOut(res.list))
    },
    onError: (error) => {
      showMessage.error(error.message)
    },
  })
  const fetchData = () => {
    const params: Api.ShouldCollectOrder.FetchCustomerBillListParams = {
      page: pagination.page,
      size: pagination.size,
      ...filterParams,
    }
    fetchBillList(getFilterData(params))
  }
  useEffect(fetchData, [
    filterParams,
    pagination.page,
    pagination.size,
  ])
  const onTableChange = (params: TableParams) => {
    // setTableParams(params)
    if (params.pagination && typeof params.pagination === 'object') {
      setPagination({
        page: params.pagination?.current || PAGE,
        size: params.pagination?.pageSize || SIZE,
      })
    }
  }
  function handleSearchChange(val: string) {
    if (val.trim() === '') {
      setFilterParams(prev => ({ ...prev, customer_name: undefined }))
    }
  }
  function handleSearch(val: string) {
    setFilterParams(prev => ({ ...prev, customer_name: val }))
  }
  const prevCustomerName = useRef(filterParams.customer_name)
  function handleBlur(e: React.ChangeEvent<HTMLInputElement>) {
    if (e.target.value.trim() === prevCustomerName.current) {
      return
    }
    prevCustomerName.current = e.target.value
    handleSearch(e.target.value)
  }
  function handleChangeParams(params: Api.ShouldCollectOrder.FetchCustomerBillListParams) {
    setFilterParams(prev => ({ ...prev, ...params }))
  }
  function handleDateChange(dates: [Date, Date]) {
    setFilterParams(prev => ({ ...prev, start_time: dayjs(dates[0]).format('YYYY-MM-DD'), end_time: dayjs(dates[1]).format('YYYY-MM-DD') }))
  }

  return isMobile
    ? (
        <>
          {contextHolder}
          <Flex vertical className="h-screen">
            <HeaderBox />
            <Flex vertical className={styles['search-bar']}>
              <Flex align="center">
                <SearchBar
                  placeholder="请输入客户名称"
                  style={{
                    'flex': '1 1 auto',
                    '--border-radius': '100px',
                    '--background': '#ffffff',
                    '--height': '32px',
                    '--padding-left': '12px',
                  }}
                  onChange={handleSearchChange}
                  onSearch={handleSearch}
                  onBlur={handleBlur}
                />
                <div className="ml-2 whitespace-nowrap" onClick={handleClickFilter}>
                  <IconText className="text-black text-base" icon={RiFilterLine} text="筛选" />
                </div>
              </Flex>
              <div className="mt-2 flex items-center justify-between">
                <div className="flex items-center">
                  <FaRegCalendar className={styles.icon} />
                  <DateRangePicker
                    border={false}
                    value={filterParams.start_time && filterParams.end_time ? [dayjs(filterParams.start_time).toDate(), dayjs(filterParams.end_time).toDate()] : undefined}
                    onChange={handleDateChange}
                  />
                </div>
                <span className="text-black">
                  累欠：￥
                  {formatPriceDiv(originData?.summary?.end_period || 0).toLocaleString()}
                </span>
              </div>
            </Flex>
            <Filter
              visible={filterVisible}
              onClose={() => setFilterVisible(false)}
              onConfirm={handleFilterConfirm}
            />
            <CustomerTable filterParams={filterParams} total={originData?.total || 0} data={data} isLoading={isPending} onTableChange={onTableChange}></CustomerTable>
          </Flex>
        </>
      )
    : <DesktopPage filterParams={filterParams} onChangeParams={handleChangeParams} originData={originData!} data={data!} isLoading={isPending} onTableChange={onTableChange} />
}
