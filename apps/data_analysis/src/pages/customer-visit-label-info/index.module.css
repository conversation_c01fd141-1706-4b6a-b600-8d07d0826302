.searchBox {
  position: sticky;
  top: 0;
  width: 100%;
  height: 42px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
}
.searchBox .goBack {
  width: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.nav_container {
  background-color: #4581ff;
  color: white;
  flex-shrink: 0;
}

.tag_list {
  display: flex;
  flex-flow: row wrap;
  gap: 12px;
  padding: 8px 0;
}
.tag_list :global(.adm-tag) {
  font-size: 14px;
  padding: 8px 16px;
  border-radius: 4px;
  margin: 0;
}
.tag_list :global(.adm-tag):global(.adm-tag-outline) {
  background-color: #f6f6f6;
  border-color: transparent;
}
.tag_list :global(.adm-tag):global(.adm-tag-primary.adm-tag-solid) {
  background-color: #337fff;
  border-color: #337fff;
}