import type { RootState } from '@/store'
import ListFrame from '@/components/listFrame'
import { GetCorpTagList } from '@/service/api/customerVisit'
import { LeftOutlined } from '@ant-design/icons'
import { getFilterData } from '@ly/utils'
import { useUnmount } from 'ahooks'
import { message } from 'antd'
import { Tag as AntdTag, Button, Card, ErrorBlock, NavBar, Selector, Space } from 'antd-mobile'
import classnames from 'classnames'
import { useEffectOnActive, useKeepAliveRef } from 'keepalive-for-react'
import { useEffect, useState } from 'react'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

export interface Label {
  id?: number
  name?: string
  pid?: number
  label_id?: string
  checked?: boolean
}

interface Form {
  id: number
  title: string
  list: Label[]
}

interface LocationState {
  labelList: string
  purchaser_id: string
  external_user_id: string
  requireValidate: string
}

// 标签选择页面
export function Component() {
  const [messageApi, contextHolder] = message.useMessage()
  const [formData, setFormData] = useState<Api.GetCorpTagList.Response[]>([])
  const location = useLocation()
  const navigate = useNavigate()
  const params = location.state as LocationState
  const userInfo = useSelector((state: RootState) => state.auth.userInfo)

  const { mutateAsync: getTagList } = GetCorpTagList({
    onSuccess: (res) => {
      let temp: Label[] = []
      if (params.labelList) {
        temp = JSON.parse(decodeURIComponent(params.labelList))
      }
      console.log('temp', temp)
      setFormData(res.list?.map(item => ({
        id: item.id,
        title: item.name,
        list: item.corp_tags?.map(it => ({
          pid: item.id,
          id: it.id,
          label_id: it.tag_id,
          name: it.tag_name,
          checked: it.is_select || temp?.some(item => item.label_id === it.tag_id) || false,
        })) || [],
      })) || [])
    },
    onError: (err) => {
      messageApi.info(err.message)
    },
  })

  const getData = async () => {
    try {
      await getTagList(getFilterData({
        external_user_id: params.external_user_id,
        follow_user_id: userInfo?.follow_user_id,
      }))
      // if (params.labelList) {
      //   temp = JSON.parse(decodeURIComponent(params.labelList))
      // }
    }
    catch (err) {
      console.log('err :>> ', err)
      messageApi.info(err.msg)
    }
  }
  const aliveRef = useKeepAliveRef()

  useEffectOnActive(() => {
    getData()
  }, [])
  useUnmount(() => {
    aliveRef.current?.destroy()
  })
  const handleReset = () => {
    setFormData(prev => prev.map((item) => {
      item.list.forEach((it) => {
        it.checked = false
      })
      return item
    }))
  }

  const handleSave = () => {
    if (params?.requireValidate) {
      const validate = Object.values(formData).every((value: Form) => value.list.some(label => label.checked))
      if (!validate) {
        messageApi.info('请选择对应的标签信息')
        return
      }
    }

    // 使用sessionStorage替代事件通道
    const selectedLabels = formData.reduce((acc, item) => [...acc, ...item.list.reduce((a, it) => it.checked ? [...a, it] : a, [])], [])
    sessionStorage.setItem('labelInfoData', JSON.stringify(selectedLabels))
    navigate(-1)
  }

  const selectedCount = formData?.reduce((acc, curr) => acc += curr.list?.reduce((a, c) => c.checked ? a += 1 : a, 0), 0)

  return (
    <ListFrame
      header={(
        <NavBar
          onBack={() => navigate(-1)}
          className={styles.nav_container}
        >
          标签列表
        </NavBar>
      )}
      footer={(
        <div className="flex justify-between">
          <Button
            fill="outline"
            shape="rounded"
            className="w-full flex-1 mx-2!"
            block
            onClick={handleReset}
          >
            重置
          </Button>
          <Button
            shape="rounded"
            color="primary"
            className="w-full flex-1 mx-2!"

            block
            onClick={handleSave}
          >
            保存（
            {selectedCount}
            个）
          </Button>
        </div>
      )}
      customContainerClassName={styles.container}
    >
      {contextHolder}

      <div className="mx-2 my-2">
        {formData.length > 0
          ? formData.map((form, index) => (
              <Card key={index} className="mb-2">
                <div className="flex items-center">
                  <span className="text-base font-medium">
                    {form.title}
                    {params?.requireValidate && <span className="text-red-500 ml-1">*</span>}
                  </span>
                </div>
                <div className={classnames(styles.tag_list)}>
                  <Selector
                    options={form.list?.map(item => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    style={{ '--padding': '6px' }}
                    value={form.list?.filter(item => item.checked).map(item => item.id)}
                    multiple={form.id !== 7}
                    onChange={(values) => {
                      if (form.id === 7) {
                        // 单选模式：直接设置选中项，其他项取消选中
                        form.list?.forEach((item) => {
                          item.checked = values[0] === item.id
                        })
                      }
                      else {
                        // 多选模式：保持原有逻辑
                        form.list?.forEach((item) => {
                          item.checked = values.includes(item.id)
                        })
                      }
                      setFormData([...formData])
                    }}
                  />
                </div>
              </Card>
            ))
          : <ErrorBlock status="empty" description="用户可能未绑定企微"></ErrorBlock>}
      </div>

    </ListFrame>
  )
}
