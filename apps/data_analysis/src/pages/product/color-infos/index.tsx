import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { BarChartData } from '@/components/echartsComponents'
import type { CardData } from '@/components/swiperCard'
import type { ColumnType } from 'antd/es/table'
import WeightSvg from '@/assets/I-2.svg?react'
import RollSvg from '@/assets/I-3.svg?react'
import PriceSvg from '@/assets/I-4.svg?react'
import CustomerSvg from '@/assets/I.svg?react'
import AIAnalysis from '@/components/AIAnalysis'
import { RangeDate } from '@/components/dateComponents'
import { <PERSON><PERSON><PERSON>, SmallLineChart } from '@/components/echartsComponents'
import HeaderBox from '@/components/headerBox'
import LabelBox from '@/components/LabelBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { Selects } from '@/components/selectComponents/index'
import SwiperCard from '@/components/swiperCard'
import TrendBox, { DATE_CONFIG, getAnalysisDateRange } from '@/pages/customer/components/TrendBox'
import { FetchEmployeeList, FetchGetTopCustomerData, FetchProductColorList, FetchProductList, FetchSaleSystemList } from '@/service/api'
import { EmployeeType } from '@/service/request/type'
import { useMobileScreen } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { formatDate, formatHashTag, getFilterData } from '@ly/utils'
import { Affix, Card, Checkbox, Col, Flex, message, Row, Table, Tooltip, Typography } from 'antd'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { FetchGetProductAnalysisCustomer } from '../index.config'
import styles from './index.module.scss'

const { Title } = Typography

const cards: CardData[] = [
  {
    id: 1,
    title: '总匹数',
    key: 'total_roll',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><RollSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 2,
    title: '总数量',
    key: 'total_settle_weight',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><WeightSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 3,
    title: '客户数',
    key: 'total_customer_count',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><CustomerSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 4,
    title: '总销售金额',
    key: 'total_sale_amount',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><PriceSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
] as const
interface CheckboxState {
  big: boolean
  plate: boolean
}
// 添加获取 sale_mode_iden 的工具函数
function getSaleModeIden(states: CheckboxState): number | undefined {
  // 使用对象映射简化逻辑
  const modeMap = {
    'true,false': 1, // 只勾选大货
    'false,true': 2, // 只勾选剪板
    'true,true': undefined, // 都勾选
    'false,false': undefined, // 都不勾选
  }

  const key = `${states.big},${states.plate}`
  return modeMap[key as keyof typeof modeMap]
}
export function Component() {
  const [searchParams] = useSearchParams()
  const [product, setProduct] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('product_id')) || '',
    label: searchParams.get('product_name') !== 'undefined' ? searchParams.get('product_name') : '',
  }) // 产品id
  const [color, setColor] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('color_id')) || '',
    label: searchParams.get('color_name') !== 'undefined' ? (searchParams.get('color_name')) : '',
  }) // 颜色i
  const [saleSystem, setSaleSystem] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_system_id')) || '',
    label: searchParams.get('sale_system_name') !== 'undefined' ? (searchParams.get('sale_system_name')) : '',
  }) // 部门
  const [saleUser, setSaleUser] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_user_id')) || '',
    label: searchParams.get('sale_user_name') !== 'undefined' ? (searchParams.get('sale_user_name')) : '',
  }) // 销售员
  const start_time = searchParams.get('start_time') // 上一个路由传入的开始时间
  const end_time = searchParams.get('end_time') // 上一个路由传入的结束时间
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const navigate = useNavigate() // 路由跳转
  const [dateRange, setDateRange] = useState<DateRange>([
    new Date(start_time || new Date()),
    new Date(end_time || new Date()),
  ]) // 日期范围
  const isMobile = useMobileScreen()

  const customBreadcrumbs = [ // 头标签
    {
      title: '工作台',
      onClick: () => navigate('/'),
    },
    {
      title: '产品分析',
      onClick: () => navigate('/product/analysis'),
    },
    {
      title: '产品详情',
      onClick: () => {
        const params = {
          product_id: product.value,
          product_name: encodeURIComponent(product.label || ''),
          color_id: color.value,
          color_name: encodeURIComponent(color.label || ''),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          sale_system_id: saleSystem.value || undefined,
          sale_system_name: encodeURIComponent(saleSystem.label || '') || undefined,
          sale_user_id: saleUser.value || undefined,
          sale_user_name: encodeURIComponent(saleUser.label || '') || undefined,
        }
        const queryString = Object.entries(getFilterData(params))
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')
        navigate(`/product/infos?mode=1&${queryString}`)
      },
    },
    {
      title: '颜色详情',
      onClick: () => {},
    },
  ]
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)
  const [customerData, setCustomerData] = useState<Api.GetTopCustomerData.GetTopCustomerResForTop[]>([])
  const { mutateAsync: fetchTopCustomer } = FetchGetTopCustomerData()
  const [currentSelectArea, setCurrentSelectArea] = useState('')
  const [checkboxStates, setCheckboxStates] = useState({
    big: true, // 大货
    plate: true, // 剪板
  })
  const states = checkboxStates
  const sale_mode_iden = getSaleModeIden(states)
  const [dateType, setDateType] = useState(DATE_CONFIG.DAILY_ANALYSIS.TYPE)
  // 获取趋势分析的日期范围
  const trendDateRange = useMemo(() => {
    if (!dateRange)
      return { trend_start_time: '', trend_end_time: '' }
    const [start, end] = getAnalysisDateRange(dateType, dateRange)
    return {
      trend_start_time: start,
      trend_end_time: end,
    }
  }, [dateRange, dateType])

  function handleChangeType(dateType: 1 | 2) {
    setDateType(dateType)
  }
  const [isChatPending, setChatPending] = useState(false)
  const [productAnalysisData, setProductAnalysisData] = useState<Api.ProductAnalysis.ProductAnalysisDataResponse>() // 产品分析数据
  const preAreaStrings = useRef('null')
  // 点击地区
  function handleBarClick(e: any) {
    if (e.name === currentSelectArea) {
      setCurrentSelectArea('')
      return
    }
    if (e.name === '其他') {
      if (currentSelectArea === preAreaStrings.current) {
        setCurrentSelectArea('')
      }
      else {
        const otherArea = productAnalysisData?.sales_area_stat.map(item => item.name).join(',') || ''
        preAreaStrings.current = otherArea
        setCurrentSelectArea(otherArea)
      }
      return
    }
    setCurrentSelectArea(e.name)
  }
  const fetchCustomerData = useCallback(async (currentPage: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return
    // 使用传入的 currentCheckboxStates 或当前的 checkboxStates
    const states = currentCheckboxStates || checkboxStates

    // 根据复选框状态确定 sale_mode_iden
    let sale_mode_iden: number | undefined
    if (states.big && !states.plate) {
      sale_mode_iden = 1 // 只勾选大货
    }
    else if (!states.big && states.plate) {
      sale_mode_iden = 2 // 只勾选剪板
    }
    setLoading(true)
    try {
      const res = await fetchTopCustomer({
        page: currentPage,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        product_id: product.value,
        location: currentSelectArea,
        product_color_id: color.value,
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
      })

      if (currentPage === 1) {
        setCustomerData(res.top_data_list || [])
      }
      else {
        setCustomerData(prev => [...prev, ...(res.top_data_list || [])])
      }

      setHasMore((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取客户排行数据失败:', error)
      messageApi.error('获取数据失败')
    }
    finally {
      setLoading(false)
    }
  }, [dateRange, product.value, color.value, saleSystem.value, currentSelectArea, saleUser.value, fetchTopCustomer, messageApi])
  const handleLoadMore = useCallback(() => {
    if (loading || !hasMore)
      return
    setPage(prev => prev + 1)
    fetchCustomerData(page + 1)
  }, [loading, hasMore, page, fetchCustomerData])
  const columns = useMemo<ColumnType<Api.GetTopCustomerData.GetTopCustomerResForTop>[]>(() => [
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 120,
      fixed: 'left',
      render: (_: string, record: Api.GetTopCustomerData.GetTopCustomerResForTop) => {
        const params = {
          customer_id: record.customer_id,
          customer_name: encodeURIComponent(record.customer_name),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          sale_system_id: saleSystem.value || undefined,
          sale_system_name: encodeURIComponent(saleSystem.label) || undefined,
          sale_user_id: saleUser.value || undefined,
          sale_user_name: encodeURIComponent(saleUser.label) || undefined,
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return <Links to={`/customer/infos?${queryString}`}>{record.customer_name}</Links>
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      key: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [dateRange, saleSystem, saleUser, isMobile])
  const { mutateAsync: fetchProductAnalysis, isPending } = FetchGetProductAnalysisCustomer() // 获取产品分析数据
  const [chartData, setChartData] = useState<BarChartData[]>([]) // 图表数据

  // 添加处理函数
  const handleCheckboxChange = (type: 'big' | 'plate') => {
    const newState = {
      ...checkboxStates,
      [type]: !checkboxStates[type],
    }

    // 确保至少有一个选中
    if (newState.big || newState.plate) {
      setCheckboxStates(newState)
      setPage(1) // 重置页码
      fetchCustomerData(1, newState) // 传入新的状态
    }
  }

  const fetchData = useCallback(async (params: Api.ProductAnalysis.ProductAnalysisDataRequest) => {
    try {
      const res = await fetchProductAnalysis(params)
      setProductAnalysisData(res as any)
    }
    catch (error) {
      console.error('获取数据失败:', error)
      messageApi.error('发生意外，请稍后再试')
    }
  }, [fetchProductAnalysis, messageApi])

  useEffect(() => { // 获取数据
    if (!product) // 产品id
      return
    if (!color) // 颜色id
      return
    if (!dateRange) // 日期范围
      return
    fetchData({
      start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      product_id: product.value, // 路由地址上的customer_id
      product_color_id: color.value, // 路由地址上的color_id
      location: currentSelectArea,
      type: 1,
      sale_system_id: saleSystem.value,
      sale_user_id: saleUser.value,
    })
  }, [product, color, dateRange, fetchData, saleSystem, saleUser, currentSelectArea])
  useEffect(() => { // 组合数据
    if (!currentSelectArea) {
      setChatPending(true)
      const chartData: any = productAnalysisData && productAnalysisData?.sales_area_stat?.map(item => ({
        name: item.name,
        series: [
          {
            name: '销量',
            value: item.value,
          },
        ],
      }))
      setChartData(chartData)
      setTimeout(() => {
        setChatPending(false)
      }, 0)
    }
  }, [productAnalysisData, currentSelectArea])

  useEffect(() => {
    if (!dateRange)
      return
    setPage(1)
    fetchCustomerData(1)
  }, [product.value, color.value, dateRange, saleSystem.value, currentSelectArea, saleUser.value])
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const [tableWidth, setTableWidth] = useState(0)
  const [tableHeight, setTableHeight] = useState(0)
  useEffect(() => {
    // 监听窗口大小变化
    setTimeout(() => {
      setTableWidth(tableContainer.current?.offsetWidth || 0)
      setTableHeight(tableContainer.current?.offsetHeight || 0)
    }, 0)
  }, [isMobile])
  return (
    <ListFrame
      header={(
        <HeaderBox
          showUserInfo
          breadcrumbs={customBreadcrumbs}
          rightSlot={(
            <AIAnalysis
              fixed={false}
              url="/h5/v1/ai/analysis/productColorDetailProAnalysisData"
              outputParams={{
                type: 1,
                start_time: formatDate(dateRange![0].toDateString()),
                end_time: formatDate(dateRange![1].toDateString()),
                sale_mode_iden,
                product_id: product.value, // 产品id
                product_color_id: `${color.value}`,
                sale_system_id: saleSystem.value, // 部门id
                sale_user_id: saleUser.value, // 销售员id
                location: currentSelectArea,
                trend_start_time: trendDateRange.trend_start_time,
                trend_end_time: trendDateRange.trend_end_time,
              } as Api.ProductColorDetailAi.Request}
              type="output"
              text="AI报表分析"
            />
          )}
        />
      )}
      className={styles.page}
    >
      {contextHolder}
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.leftContent}>
            <Card
              classNames={{
                body: styles.overviewCard,
              }}
            >
              <Row gutter={[16, 16]} className="mb-2">
                <Col xs={24} sm={24} md={12} lg={8}>
                  <LabelBox label="产品" fullWidth>
                    <Selects
                      fetchApi={FetchProductList}
                      disabled={isPending}
                      placeholder="选择产品"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      renderOption={(option: any) => {
                        return (
                          <div className="flex items-center">
                            <div>{formatHashTag(option.data.finish_product_code, option.label)}</div>
                          </div>
                        )
                      }}
                      fieldNames={{ label: 'finish_product_name', value: 'id' }}
                      isSearch
                      keywordFile="finish_product_code_or_name"
                      value={product}
                      onChange={(value: any, _option: any) => {
                        setProduct({ value, label: _option?.finish_product_name })
                        setColor({ value: 0, label: '' })
                        setCurrentSelectArea('')
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8}>
                  <LabelBox label="颜色名称" fullWidth>
                    <Selects
                      fetchApi={FetchProductColorList}
                      disabled={isPending}
                      placeholder="选择颜色"
                      query={{ finish_product_id: product.value }}
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'product_color_name', value: 'id' }}
                      renderOption={(option: any) => {
                        return (
                          <div className="flex items-center">
                            <div>{formatHashTag(option.data.product_color_code, option.label)}</div>
                          </div>
                        )
                      }}
                      isSearch
                      keywordFile="product_color_code_or_name"
                      value={color}
                      onChange={(value: any, _option: any) => {
                        setColor({ value, label: _option?.name })
                        setCurrentSelectArea('')
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8}>
                  <LabelBox label="日&nbsp;&nbsp;&nbsp;&nbsp;期" fullWidth>
                    <RangeDate
                      value={dateRange}
                      onChange={(e) => {
                        setDateRange(e)
                        setCurrentSelectArea('')
                      }}
                      loading={false}
                      format="YYYY-MM-DD"
                      // minDate={new Date('2010-01-01')}
                      // maxDate={new Date()}
                      allowClear={false}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8}>
                  <LabelBox label="部&nbsp;&nbsp;&nbsp;&nbsp;门" fullWidth>
                    <Selects
                      fetchApi={FetchSaleSystemList}
                      disabled={isPending}
                      placeholder="选择部门"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleSystem}
                      onChange={(value: any, _option: any) => {
                        setSaleSystem({ value, label: _option?.name })
                        setCurrentSelectArea('')
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8}>
                  <LabelBox label="销售员" fullWidth>
                    <Selects
                      fetchApi={FetchEmployeeList}
                      disabled={isPending}
                      placeholder="选择销售员"
                      query={{ duty: EmployeeType.salesman }}
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleUser}
                      onChange={(value: any, _option: any) => {
                        setSaleUser({ value, label: _option?.name })
                        setCurrentSelectArea('')
                      }}
                    />
                  </LabelBox>
                </Col>
              </Row>
              <SwiperCard cards={cards} data={productAnalysisData} loading={isPending} />

            </Card>
            <Card
              className={classNames('flex-1')}
              classNames={{
                body: styles.tableCard,
              }}
            >
              <div className="flex flex-col h-full">
                <Title level={4}>
                  <Tooltip placement="bottomRight" title="不含退货数据">
                    销售地区
                    <QuestionCircleOutlined className="ml-2" />
                  </Tooltip>
                </Title>
                <div className="h-1/3">
                  <BarChart
                    height="100%"
                    loading={isChatPending}
                    data={chartData}
                    config={{
                      gridLeft: isMobile ? '-10%' : '-3%',
                      colors: ['#1677ff', '#52c41a', '#faad14'],
                      negativeColors: ['#ff4d4f', '#f5222d', '#cf1322'],
                      onBarClick: handleBarClick,
                      isStack: false,
                      showLegend: false,
                      showGrid: false,
                      showYAxis: false,
                    }}
                  />
                </div>
                <div className="flex-1 flex flex-col">
                  <Flex justify="space-between" align="center">
                    <Title level={4}>客户排行</Title>
                    <Flex>
                      <Checkbox
                        checked={checkboxStates.big}
                        onChange={() => handleCheckboxChange('big')}
                      >
                        大货
                      </Checkbox>
                      <Checkbox
                        checked={checkboxStates.plate}
                        onChange={() => handleCheckboxChange('plate')}
                      >
                        剪板
                        {tableContainer.current?.clientHeight}
                      </Checkbox>
                    </Flex>
                  </Flex>
                  <div className={styles.tableContainer} ref={tableContainer}>
                    <Table
                      rootClassName={styles.tableContainer}
                      columns={columns}
                      dataSource={customerData}
                      loading={loading}
                      scroll={{
                        x: tableWidth,
                        y: tableContainer.current?.clientHeight || 0,
                      }}
                      pagination={false}
                      rowKey="customer_id"
                      size="small"
                      rowClassName={(_, index) => `${index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!'}`}
                      style={{ width: tableWidth, height: '100%' }}
                      onRow={() => ({
                        onMouseEnter: () => {
                          // 当滚动到最后一行时加载更多数据
                          const scrollElement = document.querySelector('.ant-table-body')
                          if (!scrollElement)
                            return

                          const { scrollTop, scrollHeight, clientHeight } = scrollElement
                          if (scrollHeight - scrollTop <= clientHeight + 50 && !loading && hasMore) {
                            handleLoadMore()
                          }
                        },
                      })}
                    />
                  </div>
                </div>
              </div>

            </Card>
          </div>
          <div className={styles.rightContent}>
            <TrendBox
              chartHeight="100%"
              onChangeType={handleChangeType}
              saleArea={currentSelectArea}
              product_id={product?.value}
              dateRange={dateRange}
              saleSystemId={saleSystem.value}
              saleUserId={saleUser.value}
              color_id={color?.value}
            />
          </div>
        </div>
      </div>
    </ListFrame>
  )
}
