@reference 'tailwindcss';
.page {
  overflow-y: auto!important;
  overflow-x: hidden!important;
  @apply bg-[#f5f6f7] flex flex-col p-3;
  // height: 100vh;
  overflow: hidden;
  @media (max-width: 767px) {
    min-height: unset;
  }
  .container {
    @apply w-full flex-1;
    overflow-y: auto;
    
    // 基础样式和性能优化
    & {
      contain: content;
      isolation: isolate;
      --header-height: 64px;
      --content-padding: 12px;
    }

    // 滚动条样式
    & {
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 3px;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.3);
        }
      }
    }

    // 响应式布局
    // & {
    //   @media (min-width: 1920px) {
    //     @apply w-[1800px] mx-auto;
    //   }

    //   @media (min-width: 1600px) and (max-width: 1919px) {
    //     @apply w-[98vw] mx-auto px-5;
    //   }

    //   @media (min-width: 1280px) and (max-width: 1599px) {
    //     @apply w-[98vw] mx-auto px-4;
    //   }

    //   @media (min-width: 1024px) and (max-width: 1279px) {
    //     @apply w-[98vw] mx-auto px-4;
    //   }

    //   @media (min-width: 768px) and (max-width: 1023px) {
    //     @apply w-[98vw] mx-auto px-3;
    //   }

    //   @media (max-width: 767px) {
    //     @apply w-full px-[12px];
    //   }
    // }

    // 内容区域
    .content {
      height: 100%;
      @apply flex gap-3;
      // min-height: calc(100vh - var(--header-height));
      // padding: var(--content-padding) 0;
      // overflow: hidden;
      
      // 响应式布局
      @media (max-width: 1024px) {
        @apply flex-col;
        
        .leftContent,
        .rightContent {
          @apply w-full;
        }
      }

      // 左侧内容
      .leftContent {
        @apply flex flex-col gap-3 flex-[1.5]  h-full;
        
        .overviewCard {
          @apply bg-white rounded-lg p-3;
          // min-height: 200px;
          // .headerBox{
          //   @apply grid gap-4 mb-4;
          //   grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          // }
        }
        
        .tableCard {
          height: 100%;
          // flex: 1 1 0;
          // min-height: 500px;
          display: flex;
          flex-direction: column;
          .tableContainer {
            flex: 1 1 0;
            overflow: hidden;
          }
        }
      }
      
      // 右侧内容
      .rightContent {
        @apply flex-1 bg-white rounded-lg p-3;
        min-height: 100%;
      }

      // 动画效果
      .leftContent > div,
      .rightContent {
        opacity: 0;
        animation: slideIn 0.5s ease forwards;
        will-change: opacity, transform;
      }
      
      .leftContent > div:nth-child(1) { animation-delay: 0.1s; }
      .leftContent > div:nth-child(2) { animation-delay: 0.2s; }
      .rightContent { animation-delay: 0.3s; }
    }
  }
}

// 动画定义
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.tableContainer {
  :global {
    .ant-spin-nested-loading,.ant-spin-container,.ant-table,.ant-table-container{
      height: 100%;
    }
    .ant-table-body {
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 4px;
      }
    }

    .ant-table-body:hover {
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
      
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        
        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
