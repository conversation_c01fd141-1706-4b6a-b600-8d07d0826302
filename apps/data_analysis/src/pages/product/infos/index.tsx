import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { BarChartData } from '@/components/echartsComponents'
import type { CardData } from '@/components/swiperCard'
import WeightSvg from '@/assets/I-2.svg?react'
import RollSvg from '@/assets/I-3.svg?react'
import PriceSvg from '@/assets/I-4.svg?react'
import CustomerSvg from '@/assets/I.svg?react'
import AIAnalysis from '@/components/AIAnalysis'
import { RangeDate } from '@/components/dateComponents'
import { <PERSON><PERSON><PERSON>, Pie<PERSON>hart, SmallLineChart } from '@/components/echartsComponents'
import HeaderBox from '@/components/headerBox'
import LabelBox from '@/components/LabelBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { Selects } from '@/components/selectComponents'
import SwiperCard from '@/components/swiperCard'
import { OldOrNew } from '@/pages/customer/analysis'
import TrendBox, { DATE_CONFIG, getAnalysisDateRange } from '@/pages/customer/components/TrendBox'
import { FetchEmployeeList, FetchGetTopColorData, FetchGetTopCustomerData, FetchProductList, FetchSaleSystemList } from '@/service/api'
import { EmployeeType } from '@/service/request/type'
import { useMobileScreen } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { formatDate, formatHashTag, getFilterData } from '@ly/utils'
import { Affix, Card, Checkbox, Col, Flex, message, Row, Table, Tooltip, Typography } from 'antd'
import classNames from 'classnames'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { FetchGetProductAnalysisDetail } from '../index.config'
import styles from './index.module.scss'

const { Title } = Typography

const cards: CardData[] = [
  {
    id: 1,
    title: '总匹数',
    key: 'total_roll',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><RollSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 2,
    title: '总数量',
    key: 'total_settle_weight',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><WeightSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 3,
    title: '客户数',
    key: 'total_customer_count',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><CustomerSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 4,
    title: '总销售额',
    key: 'total_sale_amount',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><PriceSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
] as const
interface CheckboxState {
  big: boolean
  plate: boolean
}
// 添加获取 sale_mode_iden 的工具函数
function getSaleModeIden(states: CheckboxState): number | undefined {
  // 使用对象映射简化逻辑
  const modeMap = {
    'true,false': 1, // 只勾选大货
    'false,true': 2, // 只勾选剪板
    'true,true': undefined, // 都勾选
    'false,false': undefined, // 都不勾选
  }

  const key = `${states.big},${states.plate}`
  return modeMap[key as keyof typeof modeMap]
}
export function Component() {
  const [searchParams] = useSearchParams()
  const productName = searchParams.get('product_name')
  const productCode = searchParams.get('product_code')
  const [product, setProduct] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('product_id')) || '',
    label: productName !== 'undefined' ? productName : '',
  }) // 产品id
  const [saleSystem, setSaleSystem] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_system_id')) || '',
    label: searchParams.get('sale_system_name') !== 'undefined' ? searchParams.get('sale_system_name') : '',
  }) // 部门id
  const [saleUser, setSaleUser] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_user_id')) || '',
    label: searchParams.get('sale_user_name') !== 'undefined' ? searchParams.get('sale_user_name') : '',
  }) // 销售员id
  const start_time = searchParams.get('start_time') // 上一个路由传入的开始时间
  const end_time = searchParams.get('end_time') // 上一个路由传入的结束时间
  const mode = Number(searchParams.get('mode')) // 模式
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const navigate = useNavigate() // 路由跳转
  const [dateRange, setDateRange] = useState<DateRange>([
    new Date(start_time || new Date()),
    new Date(end_time || new Date()),
  ]) // 日期范围
  const customBreadcrumbs = [ // 头标签
    {
      title: '工作台',
      onClick: () => navigate('/'),
    },
    {
      title: '产品分析',
      onClick: () => navigate(`/product/analysis?&start_time=${dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')}&end_time=${dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')}`),
    },
    {
      title: '产品详情',
      onClick: () => {},
    },
  ]

  const [colorRankData, setColorRankData] = useState<Api.GetTopColorData.GetTopColorResForTop[]>([])
  const [customerRankData, setCustomerRankData] = useState<Api.GetTopCustomerData.GetTopCustomerResForTop[]>([])
  const [colorRankPage, setColorRankPage] = useState(1)
  const [customerRankPage, setCustomerRankPage] = useState(1)
  const [hasMoreColor, setHasMoreColor] = useState(true)
  const [hasMoreCustomer, setHasMoreCustomer] = useState(true)
  const { mutateAsync: fetchTopColor, isPending: isTopColorLoading } = FetchGetTopColorData()
  const { mutateAsync: fetchTopCustomer, isPending: isTopCustomerLoading } = FetchGetTopCustomerData()
  const [checkboxStates, setCheckboxStates] = useState({
    big: true, // 大货
    plate: true, // 剪板
  })
  const [currentSelectArea, setCurrentSelectArea] = useState('')
  const [currentColor, setColor] = useState('')
  const [currentSaleGroupRatio, setSaleGroupRatio] = useState('')
  const [currentOldOrNew, setOldOrNewClick] = useState(0 as OldOrNew)
  const [productAnalysisData, setProductAnalysisData] = useState<Api.ProductAnalysis.ProductAnalysisDataResponse>() // 产品分析数据
  const preAreaStrings = useRef('null')
  const states = checkboxStates
  const sale_mode_iden = getSaleModeIden(states)
  const [dateType, setDateType] = useState(DATE_CONFIG.DAILY_ANALYSIS.TYPE)
  // 获取趋势分析的日期范围
  const trendDateRange = useMemo(() => {
    if (!dateRange)
      return { trend_start_time: '', trend_end_time: '' }
    const [start, end] = getAnalysisDateRange(dateType, dateRange)
    return {
      trend_start_time: start,
      trend_end_time: end,
    }
  }, [dateRange, dateType])

  function handleChangeType(dateType: 1 | 2) {
    setDateType(dateType)
  }
  // 点击地区
  function handleBarClick(e: any) {
    if (e.name === currentSelectArea) {
      setCurrentSelectArea('')
      return
    }
    if (e.name === '其他') {
      if (currentSelectArea === preAreaStrings.current) {
        setCurrentSelectArea('')
      }
      else {
        const otherArea = productAnalysisData?.sales_area_stat.filter(item => item.name !== '其他').map(item => item.value).join(',') || ''
        preAreaStrings.current = otherArea
        setCurrentSelectArea(otherArea)
      }
      return
    }
    setCurrentSelectArea(e.name)
  }
  const preColor = useRef('null')
  function handlePieClick(e: any) {
    console.log('handlePieClick', e.data, productAnalysisData?.color_ratio)
    if (e.data.color_id === currentColor) {
      setColor('')
      return
    }
    if (e.data.name === '其他') {
      if (currentColor === preColor.current) {
        setColor('')
      }
      else {
        const otherArea = productAnalysisData?.color_ratio.map(item => item.color_id).filter(item => item).join(',') || ''
        preColor.current = otherArea
        setColor(otherArea)
      }
      return
    }
    setColor(e.data.color_id)
  }
  const preSaleGroupRatio = useRef('null')
  function handleSaleGroupRatioClick(e: any) {
    if (e.data.id === currentSaleGroupRatio) {
      setSaleGroupRatio('')
      return
    }
    if (e.data.name === '其他') {
      if (currentSaleGroupRatio === preSaleGroupRatio.current) {
        setSaleGroupRatio('')
      }
      else {
        const otherArea = productAnalysisData?.top_10_sale_group_ratio.map(item => item.sale_group_id).filter(item => item).join(',') || ''
        preSaleGroupRatio.current = otherArea
        setSaleGroupRatio(otherArea)
      }
      return
    }
    setSaleGroupRatio(e.data.id)
  }
  function handleOldOrNewClick(e: any) {
    if (e.data.old_customer && OldOrNew.Old === currentOldOrNew) {
      setOldOrNewClick(0)
      return
    }
    if (e.data.new_customer && OldOrNew.New === currentOldOrNew) {
      setOldOrNewClick(0)
      return
    }
    setOldOrNewClick(e.data.old_customer ? OldOrNew.Old : OldOrNew.New)
  }
  // 获取颜色排行数据
  const fetchColorRank = useCallback(async (page: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return
    // 使用传入的 currentCheckboxStates 或当前的 checkboxStates
    const states = currentCheckboxStates || checkboxStates
    const sale_mode_iden = getSaleModeIden(states)

    try {
      const res = await fetchTopColor({
        page,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        product_id: product.value,
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
        location: currentSelectArea,
        product_color_id: currentColor,
        sale_group_id: currentSaleGroupRatio,
        is_show_new_customer: currentOldOrNew === OldOrNew.New,
        is_show_old_customer: currentOldOrNew === OldOrNew.Old,
      })

      if (page === 1) {
        setColorRankData(res.top_data_list || [])
      }
      else {
        setColorRankData(prev => [...prev, ...(res.top_data_list || [])])
      }

      setHasMoreColor((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取颜色排行数据失败:', error)
      messageApi.error('获取颜色排行数据失败')
    }
  }, [dateRange, product.value, saleSystem.value, saleUser.value, currentSaleGroupRatio, fetchTopColor, messageApi, currentSelectArea, currentColor, currentSaleGroupRatio, currentOldOrNew])

  // 获取客户排行数据
  const fetchCustomerRank = useCallback(async (page: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return
    // 使用传入的 currentCheckboxStates 或当前的 checkboxStates
    const states = currentCheckboxStates || checkboxStates
    const sale_mode_iden = getSaleModeIden(states)

    try {
      const res = await fetchTopCustomer({
        page,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        product_id: product.value,
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
        sale_group_id: currentSaleGroupRatio,
        location: currentSelectArea,
        product_color_id: currentColor,
        is_show_new_customer: currentOldOrNew === OldOrNew.New,
        is_show_old_customer: currentOldOrNew === OldOrNew.Old,
      })

      if (page === 1) {
        setCustomerRankData(res.top_data_list || [])
      }
      else {
        setCustomerRankData(prev => [...prev, ...(res.top_data_list || [])])
      }

      setHasMoreCustomer((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取客户排行数据失败:', error)
      messageApi.error('获取客户排行数据失败')
    }
  }, [dateRange, product.value, saleSystem.value, saleUser.value, currentSaleGroupRatio, fetchTopCustomer, messageApi, currentSelectArea, currentColor, currentSaleGroupRatio, currentOldOrNew])
  const isMobile = useMobileScreen()
  // 监听筛选条件变化，重置并获取数据
  useEffect(() => {
    if (mode === 1) {
      setColorRankPage(1)
      fetchColorRank(1)
    }
    else if (mode === 2) {
      setCustomerRankPage(1)
      fetchCustomerRank(1)
    }
  }, [mode, dateRange, product.value, saleSystem.value, saleUser.value, fetchColorRank, fetchCustomerRank])
  // 修改颜色排行列定义
  const columns_color = useMemo(() => [
    {
      title: '颜色名称',
      dataIndex: 'color_name',
      key: 'color_name',
      width: 120,
      fixed: 'left',
      render: (_: string, record: Api.GetTopColorData.GetTopColorResForTop, index: number) => {
        const params = {
          product_id: record.product_id,
          product_name: encodeURIComponent(record.product_name || ''),
          color_id: record.product_color_id,
          color_name: encodeURIComponent(record.product_color_name || ''),
          sale_system_id: saleSystem.value,
          sale_system_name: saleSystem.label,
          sale_user_id: saleUser.value,
          sale_user_name: saleUser.label,
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(getFilterData(params))
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')
        return (
          <Links to={`/product/color-infos?${queryString}`}>
            {formatHashTag(record.product_color_code, record.product_color_name)}
          </Links>
        )
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '客户数',
      dataIndex: 'customer_count',
      key: 'customer_count',
      width: 80,
      render: (_: number, record: Api.GetTopColorData.GetTopColorResForTop, index: number) => {
        const params = {
          product_id: product.value,
          color_id: record?.product_color_id || 0,
          product_name: product.label,
          color_name: record.product_color_name,
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(params)
          .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
          .join('&')

        return <Links key={index} to={`/product/color-infos?${queryString}`}>{record.customer_count}</Links>
      },
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      key: 'last_15_day_sale_amount',
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [dateRange, product, isMobile])

  // 修改客户排行列定义
  const columns_customer = useMemo(() => [
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 120,
      fixed: 'left',
      render: (_: string, record: Api.GetTopCustomerData.GetTopCustomerResForTop) => {
        const params = {
          customer_id: record.customer_id,
          customer_name: encodeURIComponent(record.customer_name),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          sale_system_id: saleSystem.value || undefined,
          sale_system_name: encodeURIComponent(saleSystem.label) || undefined,
          sale_user_id: saleUser.value || undefined,
          sale_user_name: encodeURIComponent(saleUser.label) || undefined,
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return <Links to={`/customer/infos?${queryString}`}>{record.customer_name}</Links>
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '颜色数量',
      dataIndex: 'product_color_count',
      key: 'product_color_count',
      width: 80,
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      key: 'last_15_day_sale_amount',
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [dateRange, saleSystem, saleUser, isMobile])
  const { mutateAsync: fetchProductAnalysis, isPending } = FetchGetProductAnalysisDetail() // 获取产品分析数据
  const [pieOneData, setPieOneData] = useState<any>(null) // 产品类别
  const [pieTwoData, setPieTwoData] = useState<any>(null) // 客户类别
  const [pieThreeData, setPieThreeData] = useState<any>(null) // 近30天新老客户占比
  const [chartData, setChartData] = useState<BarChartData[]>([]) // 图表数据

  // 添加处理函数
  const handleCheckboxChange = (type: 'big' | 'plate') => {
    const newState = {
      ...checkboxStates,
      [type]: !checkboxStates[type],
    }

    // 确保至少有一个选中
    if (newState.big || newState.plate) {
      setCheckboxStates(newState)
      if (mode === 1) {
        setColorRankPage(1)
        fetchColorRank(1, newState)
      }
      else if (mode === 2) {
        setCustomerRankPage(1)
        fetchCustomerRank(1, newState)
      }
    }
  }

  const fetchData = async (params: Api.ProductAnalysis.ProductAnalysisDataRequest) => { // 获取数据的请求方法
    try {
      const res = await fetchProductAnalysis(params)
      setProductAnalysisData(res as any)
    }
    catch (error) {
      console.error('获取数据失败:', error)
      messageApi.error('发生意外，请稍后再试')
    }
  }

  useEffect(() => { // 获取数据
    if (!product)
      return
    if (!mode)
      return
    if (!dateRange)
      return
    fetchData({
      start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      type: mode,
      product_id: product.value,
      sale_group_id: currentSaleGroupRatio,
      sale_system_id: saleSystem.value,
      sale_user_id: saleUser.value,
      location: currentSelectArea,
      product_color_id: currentColor,
      is_show_new_customer: currentOldOrNew === OldOrNew.New,
      is_show_old_customer: currentOldOrNew === OldOrNew.Old,
    })
  }, [dateRange, product, mode, saleSystem, saleUser, currentSelectArea, currentSaleGroupRatio, currentColor, currentSaleGroupRatio, currentOldOrNew])
  const [isChatPending, setChatPending] = useState(false)
  useEffect(() => { // 组合数据
    if (!currentColor && mode === 1 && productAnalysisData) { // 色号占比
      const list1 = productAnalysisData.color_ratio?.map((item: any) => {
        const value = Number(item.sale_ratio.replace('%', ''))
        return { name: item.color_name ? formatHashTag(item.color_code, item.color_name) : '未知', value, color_id: item.color_id }
      })
      setPieOneData(list1)
    }
    if (!currentSaleGroupRatio && productAnalysisData) { // 客户类别
      const list2 = productAnalysisData.top_10_sale_group_ratio?.map((item: any) => {
        const value = Number(item.sale_ratio.replace('%', ''))
        return { name: item.sale_group_name, value, id: item.sale_group_id }
      })
      setPieTwoData(list2)
    }
    if (!currentOldOrNew && productAnalysisData) {
      const list3 = [
        { name: '新客户', value: Number(productAnalysisData.new_customer_ratio.replace('%', '')), color: '#6eb088', new_customer: true },
        { name: '老客户', value: Number(productAnalysisData.old_customer_ratio.replace('%', '')), color: '#3875f7', old_customer: true },
      ]
      setPieThreeData(list3)
    }
    if (!currentSelectArea && productAnalysisData?.sales_area_stat) {
      setChatPending(true)
      const chartData: any = productAnalysisData && productAnalysisData?.sales_area_stat?.map(item => ({
        name: item.name,
        series: [
          {
            name: '销量',
            value: item.value,
          },
        ],
      }))
      setChartData(chartData)
      setTimeout(() => {
        setChatPending(false)
      }, 0)
    }
  }, [mode, productAnalysisData])
  const [bottomRowHeight, setBottomRowHeight] = useState<string | number>(0)

  useEffect(() => {
    if (isMobile) {
      setBottomRowHeight('unset')
      return
    }
    const handleResize = () => {
      const bottomRow = document.getElementById('bottomRow')
      setBottomRowHeight(bottomRow?.clientHeight || 0)
    }
    setTimeout(() => {
      handleResize()
    }, 60)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isMobile])
  return (
    <ListFrame
      header={(
        <HeaderBox
          showUserInfo
          breadcrumbs={customBreadcrumbs}
          rightSlot={(
            <AIAnalysis
              fixed={false}
              url="/h5/v1/ai/analysis/productDetailProAnalysisData"
              outputParams={{
                type: 1,
                start_time: formatDate(dateRange![0].toDateString()),
                end_time: formatDate(dateRange![1].toDateString()),
                sale_mode_iden,
                product_id: product.value, // 产品id
                product_color_id: currentColor,
                sale_system_id: saleSystem.value, // 部门id
                sale_user_id: saleUser.value, // 销售员id
                location: currentSelectArea,
                sale_group_id: currentSaleGroupRatio,
                is_show_new_customer: currentOldOrNew === OldOrNew.New,
                is_show_old_customer: currentOldOrNew === OldOrNew.Old,
                trend_start_time: trendDateRange.trend_start_time,
                trend_end_time: trendDateRange.trend_end_time,
              } as Api.ProductDetailAi.Request}
              type="output"
              text="AI报表分析"
            />
          )}
        />
      )}
      className={styles.page}
    >
      {contextHolder}
      <div className={styles.container}>
        <div className={styles.content}>

          {/* 上部分 */}
          <Row gutter={[10, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              {/* 日期+部门+销售员 */}
              <Card
                className="h-full"
                classNames={{
                  body: styles.overviewCard,
                }}
              >
                <Row gutter={[16, 16]} className="mb-2">
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="产品名称" fullWidth>
                      <Selects
                        fetchApi={FetchProductList}
                        disabled={isPending}
                        placeholder="选择产品"
                        pagination={{ defaultPage: 1, defaultSize: 50 }}
                        renderOption={(option: any) => {
                          return (
                            <div className="flex items-center">
                              <div>{formatHashTag(option.data?.finish_product_code, option.data?.finish_product_name)}</div>
                            </div>
                          )
                        }}
                        fieldNames={{ label: 'finish_product_name', value: 'id' }}
                        isSearch
                        keywordFile="finish_product_code_or_name"
                        value={product}
                        onChange={(value: any, _option: any) => {
                          setProduct({ value, label: _option?.finish_product_name })
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setColor('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                      />
                    </LabelBox>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="日&nbsp;&nbsp;&nbsp;&nbsp;期" fullWidth>
                      <RangeDate
                        value={dateRange}
                        onChange={(e) => {
                          setDateRange(e)
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setColor('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                        loading={false}
                        format="YYYY-MM-DD"
                        // minDate={new Date('2010-01-01')}
                        // maxDate={new Date()}
                        allowClear={false}
                      />
                    </LabelBox>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="部&nbsp;&nbsp;&nbsp;&nbsp;门" fullWidth>
                      <Selects
                        fetchApi={FetchSaleSystemList}
                        disabled={isPending}
                        placeholder="选择部门"
                        pagination={{ defaultPage: 1, defaultSize: 50 }}
                        fieldNames={{ label: 'name', value: 'id' }}
                        isSearch
                        keywordFile="name"
                        value={saleSystem}
                        onChange={(value: any, _option: any) => {
                          setSaleSystem({ value, label: _option?.name })
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setColor('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                      />
                    </LabelBox>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="销售员" fullWidth>
                      <Selects
                        fetchApi={FetchEmployeeList}
                        disabled={isPending}
                        placeholder="选择销售员"
                        query={{ duty: EmployeeType.salesman }}
                        pagination={{ defaultPage: 1, defaultSize: 50 }}
                        fieldNames={{ label: 'name', value: 'id' }}
                        isSearch
                        keywordFile="name"
                        value={saleUser}
                        onChange={(value: any, _option: any) => {
                          setSaleUser({ value, label: _option?.name })
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setColor('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                      />
                    </LabelBox>
                  </Col>
                </Row>
                {/* 切换图 */}
                <SwiperCard cards={cards} data={productAnalysisData} loading={isPending} />
              </Card>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card className={styles.pieChartsCard}>
                <Row gutter={[0, 0]} className={styles.pieChartsCard}>
                  {
                    mode === 1 && (
                      <Col xs={24} sm={24} md={12} lg={12}>
                        <Tooltip placement="bottomRight" title="不含退款数据">
                          <Title level={4}>
                            色号占比
                            <QuestionCircleOutlined className="ml-2" />
                          </Title>
                        </Tooltip>
                        <PieChart
                          height={200}
                          className="flex-1 flex justify-center items-center"
                          type="pie"
                          labelPosition="inside"
                          loading={false}
                          defaultData={[{ name: '暂无数据', value: 1, color: '#f4b352' }]}
                          onPieClick={handlePieClick}
                          data={pieOneData}
                        />
                      </Col>
                    )
                  }
                  <Col xs={24} sm={24} md={mode === 1 ? 12 : 24} lg={mode === 1 ? 12 : 24}>
                    <Tooltip placement="bottomRight" title="当前时间30天内首次下单的客户为新客户，不含退款数据">
                      <Title level={4}>
                        新老客户占比
                        <QuestionCircleOutlined className="ml-2" />
                      </Title>
                    </Tooltip>
                    <PieChart
                      className="flex-1 flex justify-center items-center"
                      height={200}
                      type="pie"
                      labelPosition="inside"
                      loading={false}
                      defaultData={[{ name: '新客户', value: 1, color: '#6eb088' }, { name: '老客户', value: 1, color: '#3875f7' }]}
                      onPieClick={handleOldOrNewClick}
                      data={pieThreeData}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>

          {/* 下部分 */}
          <Row gutter={[10, 16]} id="bottomRow" className={classNames(styles.row, 'flex-1 overflow-hidden')}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card
                classNames={{
                  body: 'flex flex-col h-full',
                }}
                className={styles.leftCard}
                style={{ height: bottomRowHeight }}
              >
                <div className="flex flex-col h-full">

                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={24} md={16} lg={16}>

                      <Title level={4}>
                        <Title level={4}>
                          <Tooltip placement="bottomRight" title="不含退货数据">
                            销售地区
                            <QuestionCircleOutlined className="ml-2" />
                          </Tooltip>
                        </Title>
                      </Title>

                      <BarChart
                        height={isMobile ? 300 : (bottomRowHeight as number) / 2 - 100}
                        loading={isChatPending}
                        data={chartData}
                        config={{
                          gridLeft: isMobile ? '-10%' : '-3%',
                          colors: ['#1677ff', '#52c41a', '#faad14'],
                          negativeColors: ['#ff4d4f', '#f5222d', '#cf1322'],
                          onBarClick: handleBarClick,
                          isStack: false,
                          showLegend: false,
                          showGrid: false,
                          showYAxis: false,
                        }}
                      />
                    </Col>
                    <Col xs={24} sm={24} md={8} lg={8}>
                      <Tooltip placement="bottomLeft" title="不含退款数据">
                        <Title level={4}>
                          客户类别占比
                          <QuestionCircleOutlined className="ml-2" />
                        </Title>

                      </Tooltip>

                      <PieChart
                        height={((bottomRowHeight as number) / 2) - 100}
                        loading={false}
                        labelPosition="inside"
                        className="flex-1 flex justify-center items-center"
                        type="pie"
                        defaultData={[{ name: '暂无数据', value: 1, color: '#68aff9' }]}
                        onPieClick={handleSaleGroupRatioClick}
                        data={pieTwoData}
                      />
                    </Col>
                  </Row>
                  <div className="flex-1 flex flex-col">
                    <Flex justify="space-between" align="center" style={{ marginTop: 10, marginBottom: 10 }}>
                      <Title level={4}>{mode === 1 ? '颜色排行' : '客户排行'}</Title>
                      <Flex justify="center" align="center">
                        <Checkbox
                          checked={checkboxStates.big}
                          onChange={() => handleCheckboxChange('big')}
                        >
                          大货
                        </Checkbox>
                        <Checkbox
                          checked={checkboxStates.plate}
                          onChange={() => handleCheckboxChange('plate')}
                        >
                          剪板
                        </Checkbox>
                      </Flex>
                    </Flex>

                    {mode === 1
                      ? (
                          <Table
                            rootClassName={styles.tableContainer}
                            columns={columns_color}
                            dataSource={colorRankData}
                            loading={isTopColorLoading}
                            scroll={{
                              x: 'max-content',
                              y: isMobile ? 300 : (bottomRowHeight as number) / 2 - 50,
                            }}
                            pagination={false}
                            rowKey="product_color_id"
                            size="small"
                            style={{ height: isMobile ? 330 : (bottomRowHeight as number) / 2 - 50 }}
                            onRow={() => ({
                              style: { background: '#fff' },
                            })}
                            rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!')}
                            onScroll={({ target }: any) => {
                              const { scrollTop, scrollHeight, clientHeight } = target
                              if (scrollHeight - scrollTop - clientHeight < 20 && hasMoreColor && !isTopColorLoading) {
                                const nextPage = colorRankPage + 1
                                setColorRankPage(nextPage)
                                fetchColorRank(nextPage)
                              }
                            }}
                          />
                        )
                      : (
                          <Table
                            columns={columns_customer}
                            rootClassName={styles.tableContainer}
                            dataSource={customerRankData}
                            loading={isTopCustomerLoading}
                            scroll={{
                              x: 'max-content',
                              y: isMobile ? 300 : (bottomRowHeight as number) / 2 - 50,
                            }}
                            pagination={false}
                            rowKey="customer_id"
                            size="small"
                            style={{ height: isMobile ? 330 : (bottomRowHeight as number) / 2 - 50 }}
                            rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!')}
                            onScroll={({ target }: any) => {
                              const { scrollTop, scrollHeight, clientHeight } = target
                              if (scrollHeight - scrollTop - clientHeight < 20 && hasMoreCustomer && !isTopCustomerLoading) {
                                const nextPage = customerRankPage + 1
                                setCustomerRankPage(nextPage)
                                fetchCustomerRank(nextPage)
                              }
                            }}
                          />
                        )}
                  </div>
                </div>
              </Card>

            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card
                classNames={{
                  body: styles.rightCard,
                }}
                style={{ height: bottomRowHeight }}
              >

                <TrendBox
                  onChangeType={handleChangeType}
                  chartHeight={isMobile ? undefined : '100%'}
                  product_id={product?.value}
                  oleOrNew={currentOldOrNew}
                  saleGroupRatio={currentSaleGroupRatio}
                  saleArea={currentSelectArea}
                  color_id={currentColor}
                  dateRange={dateRange}
                  saleSystemId={saleSystem.value}
                  saleUserId={saleUser.value}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </div>
      {/* {productAnalysisData?.product_id && (

      )} */}
    </ListFrame>
  )
}
