@reference 'tailwindcss';
.page {
  overflow-y: auto!important;
  overflow-x: hidden!important;
  @apply bg-[#f5f6f7] flex flex-col overflow-hidden p-3;
  // 移动端（<768px）：最小留白
  @media (max-width: 767px) {
    min-height: unset;
  }
  .container {
    height: 100%;
    @apply w-full flex-1;

    // 大屏（≥1920px）：固定宽度 + 最大留白
    // @media (min-width: 1920px) {
    //   @apply w-[1800px] mx-auto;
    // }

    // // 1600px - 1919px：较大留白
    // @media (min-width: 1600px) and (max-width: 1919px) {
    //   @apply w-[98vw] mx-auto px-5;
    // }

    // // 1280px - 1599px：中等留白
    // @media (min-width: 1280px) and (max-width: 1599px) {
    //   @apply w-[98vw] mx-auto px-4;
    // }

    // // 1024px - 1279px：较小留白
    // @media (min-width: 1024px) and (max-width: 1279px) {
    //   @apply w-[98vw] mx-auto px-4;
    // }

    // // 768px - 1023px：小留白
    // @media (min-width: 768px) and (max-width: 1023px) {
    //   @apply w-[98vw] mx-auto px-3;
    // }

    // // 移动端（<768px）：最小留白
    // @media (max-width: 767px) {
    //   @apply w-full px-[12px];
    // }

    .content {
      height: 100%;
      @apply flex flex-col gap-3;
      // height: 89vh;
      // 移动端（<768px）：最小留白
      @media (max-width: 780px) {
        height: unset;
      }

      @media (max-width: 480px) {
        height: unset;
      }
      .headerBox {
        // @apply grid gap-4 mb-4;
        // grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

        .headerBoxItem {
          @apply w-full;
        }
      }
      .overviewCard {
        @apply flex flex-col;
      }
      // 卡片高度设置
      .overviewCard,
      .pieChartsCard {
        @apply bg-white rounded-lg h-full;
        // min-height: 150px;
        
      }

      .leftCard,
      .rightCard {
        height: 100%;
        // min-height: 480px;
        background-color: white;
        // min-height: 480px;
      }
      // .row {
      //   @apply flex gap-3;

      //   > div {
      //     @apply bg-white rounded-lg p-3;
      //     opacity: 0;
      //     transform: translateY(20px);
      //     animation: fadeInUp 0.5s ease forwards;

      //     &:nth-child(1) {
      //       animation-delay: 0.1s;
      //     }
      //     &:nth-child(2) {
      //       animation-delay: 0.2s;
      //     }
      //   }

      //   // 大屏双列（≥1260px）
      //   @media (min-width: 1260px) {
      //     @apply flex-wrap justify-between;

      //     > div {
      //       min-width: 580px;
      //       width: calc(50% - 6px);
      //     }
      //   }

      //   // 小于1260px时单列
      //   @media (max-width: 1259px) {
      //     @apply flex-col;

      //     > div {
      //       width: 100%;
      //     }
      //   }

        
      // }
    }
  }
  .pieChartsContainer {
    @apply flex flex-wrap justify-center gap-4 h-full;

    .chartCard {
      @apply rounded-lg transition-all duration-300 flex-grow flex flex-col;
      min-height: 240px;
      min-width: 179px;
      width: calc(33.333% - 16px);
      max-width: 400px;

      @media (max-width: 768px) {
        width: calc(50% - 8px);
      }

      @media (max-width: 480px) {
        width: 100%;
      }
    }
  }
}

// 定义淡入上升动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
:global {
  .ant-table-wrapper {
    height: 330px;
    .ant-table-container {
      height: 100%;
    }
    .ant-table-body {
      // min-height: 280px !important;
    }
  }
}

.nameCell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  display: block;
}
.tableContainer {
  :global {
    .ant-table-body {
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 4px;
      }
    }

    .ant-table-body:hover {
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
      
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        
        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}
