@reference "tailwindcss";
.page {
  @apply min-h-screen bg-[#f5f6f7] flex flex-col overflow-hidden;
}
.page .container {
  @apply w-full flex-auto overflow-hidden py-3;
}
@media (min-width: 1920px) {
  .page .container {
    @apply w-[1800px] mx-auto;
  }
}
@media (min-width: 1600px) and (max-width: 1919px) {
  .page .container {
    @apply w-[98vw] mx-auto px-5;
  }
}
@media (min-width: 1280px) and (max-width: 1599px) {
  .page .container {
    @apply w-[98vw] mx-auto px-4;
  }
}
@media (min-width: 1024px) and (max-width: 1279px) {
  .page .container {
    @apply w-[98vw] mx-auto px-4;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .page .container {
    @apply w-[98vw] mx-auto px-3;
  }
}
@media (max-width: 767px) {
  .page .container {
    @apply w-full px-[12px];
  }
}
.page .container .content {
  @apply flex flex-col gap-3 h-full;
  height: 89vh;
}
@media (max-width: 768px) {
  .page .container .content {
    height: unset;
  }
}
@media (max-width: 480px) {
  .page .container .content {
    height: unset;
  }
}
.page .container .content .row {
  @apply flex gap-3;
}
.page .container .content .row > div {
  @apply bg-white rounded-lg p-3;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.5s ease forwards;
}
.page .container .content .row > div:nth-child(1) {
  animation-delay: 0.1s;
}
.page .container .content .row > div:nth-child(2) {
  animation-delay: 0.2s;
}
@media (min-width: 1260px) {
  .page .container .content .row {
    @apply flex-wrap justify-between;
  }
  .page .container .content .row > div {
    min-width: 580px;
    width: calc(50% - 6px);
  }
}
@media (max-width: 1259px) {
  .page .container .content .row {
    @apply flex-col;
  }
  .page .container .content .row > div {
    width: 100%;
  }
}
.page .container .content .row .overviewCard {
  @apply flex flex-col justify-between;
}
.page .container .content .row .overviewCard,
.page .container .content .row .pieChartsCard {
  min-height: 200px;
}
.page .container .content .row .overviewCard .headerBox,
.page .container .content .row .pieChartsCard .headerBox {
  @apply grid gap-4 mb-4;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.page .container .content .row .overviewCard .headerBox .headerBoxItem,
.page .container .content .row .pieChartsCard .headerBox .headerBoxItem {
  @apply w-full;
}
.page .container .content .row .leftCard,
.page .container .content .row .rightCard {
  min-height: 480px;
}
.page .pieChartsContainer {
  @apply flex flex-wrap justify-center gap-2 h-full;
}
.page .pieChartsContainer .chartCard {
  @apply rounded-lg transition-all duration-300 flex-grow flex flex-col;
  min-height: 250px;
  min-width: 179px;
  width: calc(33.333% - 16px);
  max-width: 400px;
}
@media (max-width: 768px) {
  .page .pieChartsContainer .chartCard {
    width: calc(50% - 8px);
  }
}
@media (max-width: 480px) {
  .page .pieChartsContainer .chartCard {
    width: 100%;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.nameCell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 160px;
  display: block;
}

:global .ant-table-wrapper {
  height: 330px;
}
:global .ant-table-wrapper .ant-table-container {
  height: 100%;
}
:global .ant-table-wrapper .ant-table-body {
  min-height: 280px !important;
}

.tableContainer :global .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar-track {
  background: transparent;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}
.tableContainer :global .ant-table-body:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
.tableContainer :global .ant-table-body:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}
.tableContainer :global .ant-table-body:hover::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}/*# sourceMappingURL=index.module.css.map */