import type { MutationOptions } from '@/service/request'
// import type { PaginationResponse } from '@/service/request/type' // 分页需要
import { useCustomMutation } from '@/service/request'

// 获取产品分析的数据  http://192.168.1.28:50001/hcscm/admin/v1/analysis/productAnalysis/home?type=1
export function FetchGetProductAnalysisData(options?: MutationOptions<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>) {
  return useCustomMutation<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/productAnalysis/home',
      method: 'GET',
    },
    options,
  )
}

// 获取产品维度的详情 /hcscm/admin/v1/analysis/productAnalysis/product
export function FetchGetProductAnalysisDetail(options?: MutationOptions<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>) {
  return useCustomMutation<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/productAnalysis/product',
      method: 'GET',
    },
    options,
  )
}

// 获取 产品详情+颜色详情 http://192.168.1.28:50002/hcscm/h5/v1/analysis/productAnalysis/customer?start_time=2024-01-01&end_time=2025-01-31&product_id=1799929081319424&product_color_id=1799929119207424&type=1
export function FetchGetProductAnalysisCustomer(options?: MutationOptions<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>) {
  return useCustomMutation<Api.ProductAnalysis.ProductAnalysisDataResponse, Api.ProductAnalysis.ProductAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/productAnalysis/customer',
      method: 'GET',
    },
    options,
  )
}
