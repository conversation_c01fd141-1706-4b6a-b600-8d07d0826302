.main {
  background-color: #f7f7f7;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.container{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.goBack{
  width: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.nav_container {
  background-color: var(--adm-color-primary);
  color: white;
  flex-shrink: 0;
}
.search {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 12px 16px;
  box-sizing: border-box;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #e5e5e5;

  &_collect {
    font-size: 14px;
    color: #007aff;
    margin-right: 12px;
    white-space: nowrap;
  }
}

.context {
  flex: 1;
  overflow-y: auto;
  background-color: white;
}

.bottomBar {
  position: sticky;
  bottom: 0;
  z-index: 99;
  box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
  padding: 12px 16px;
  background-color: white;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
}

.colorCard {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  &__image {
    width: 72px;
    height: 72px;
    border-radius: 4px;
    overflow: hidden;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .num {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 4px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      font-size: 12px;
      text-align: center;
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__title {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .tag_list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;

    .tag,
    .tag_g {
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 12px;
    }

    .tag {
      background-color: #e8f3ff;
      color: #1677ff;
    }

    .tag_g {
      background-color: #fff3e8;
      color: #f77234;
    }
  }

  .introduce {
    font-size: 13px;
    color: #666;
  }

  .des {
    font-size: 13px;
    color: #999;
    margin-top: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .addButton {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.scrollContainer {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
