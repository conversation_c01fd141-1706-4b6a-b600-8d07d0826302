import ListFrame from '@/components/listFrame'
import { FetchProductList } from '@/service/api/product'
import { formatUrl } from '@/utils/format'
import { LeftOutlined } from '@ant-design/icons'
import { getFilterData } from '@ly/utils'
import { useUnmount } from 'ahooks'
import { message } from 'antd'
import { Button, Card, Checkbox, Image, InfiniteScroll, NavBar, PullToRefresh, SearchBar } from 'antd-mobile'
import { useEffectOnActive, useKeepAliveRef } from 'keepalive-for-react'
import { useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

interface ItemProps {
  data: Api.Product.Response
  onSelect: (data: ItemProps['data']) => void
  onUnSelect: (data: ItemProps['data']) => void
}

function Item({ data: item, onSelect, onUnSelect }: ItemProps) {
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (item.checked) {
      onUnSelect(item)
    }
    else {
      onSelect(item)
    }
  }

  return (
    <Card bodyClassName={styles.colorCard} onClick={handleClick}>
      <div className={styles.colorCard__image}>
        <Image src={formatUrl(item.texture_url?.[0])} className="w-full! h-full!" fit="cover" alt={item.finish_product_name} />
        <div className={styles.num}>
          {item.sum_colors}
          色
        </div>
      </div>
      <div className={styles.colorCard__content}>
        <div className={styles.colorCard__title}>
          #
          {item.finish_product_code}
          {' '}
          {item.finish_product_name}
        </div>
        <div className={styles.tag_list}>
          {item.finish_product_width_and_unit_name ? <span className={styles.tag}>{item.finish_product_width_and_unit_name}</span> : null}
          {item.finish_product_gram_weight_and_unit_name ? <span className={styles.tag_g}>{item.finish_product_gram_weight_and_unit_name}</span> : null}
        </div>
        <div className={styles.introduce}>{item.finish_product_ingredient}</div>
        {item.remark && <div className={styles.des}>{item.remark}</div>}
      </div>
      <div className={styles.addButton} onClick={e => e.stopPropagation()}>
        <Checkbox
          checked={item.checked}
          onChange={checked => checked ? onSelect(item) : onUnSelect(item)}
        />
      </div>
    </Card>
  )
}

export function Component() {
  const { mutateAsync: fetchData } = FetchProductList()
  const location = useLocation()
  const navigate = useNavigate()
  const [messageApi, contextHolder] = message.useMessage()
  const [searchValue, setSearchValue] = useState('')
  const [list, setList] = useState<(Api.Product.Response & { checked?: boolean })[]>([])
  const [total, setTotal] = useState(0)
  const selectedIds = useRef<Api.Product.Response[]>([])
  const pageSize = 20
  const [page, setPage] = useState(1)
  const [loading, setLoading] = useState(false)
  const aliveRef = useKeepAliveRef()

  const getData = async (searchText: string, pageNum?: number) => {
    try {
      setLoading(true)
      const currentPage = pageNum || page
      const res = await fetchData(getFilterData({
        finish_product_code_or_name: searchText,
        page: currentPage,
        size: pageSize,
      }))
      const ids = selectedIds.current.map(item => item.id)
      const newList = res.list?.map(item => ({
        ...item,
        checked: ids.includes(item.id || 0),
      })) || []

      setList(currentPage === 1 ? newList : [...list, ...newList])
      setTotal(res.total)
    }
    catch (e) {
      messageApi.error('获取产品列表失败')
      console.error('获取产品列表失败:', e)
    }
    finally {
      setLoading(false)
    }
  }

  useEffectOnActive(() => {
    const state = location.state as { selectedIds?: Api.Product.Response[] }
    console.log('state', state)
    if (state?.selectedIds) {
      selectedIds.current = state.selectedIds
    }
    getData('', 1)
  }, [])

  useUnmount(() => {
    aliveRef.current?.destroy()
  })

  const handleSearch = (value: string) => {
    console.log('value', value)
    setSearchValue(value)
    setPage(1)
    getData(value, 1)
  }

  const handleSelect = (item: Api.Product.Response & { checked?: boolean }) => {
    item.checked = true
    selectedIds.current = [...selectedIds.current, item]
    setList([...list])
  }

  const handleUnSelect = (item: Api.Product.Response & { checked?: boolean }) => {
    item.checked = false
    selectedIds.current = selectedIds.current.filter(it => it.id !== item.id)
    setList([...list])
  }

  const handleConfirm = () => {
    sessionStorage.setItem('productSelectorData', JSON.stringify(selectedIds.current))
    navigate(-1)
  }

  return (
    <ListFrame
      header={(
        <NavBar
        // back={<LeftOutlined />}
          onBack={() => navigate(-1)}
          className={styles.nav_container}
        >
          选择产品
        </NavBar>
      )}
      footer={(
        <Button
          block
          color="primary"
          disabled={selectedIds.current.length === 0}
          onClick={handleConfirm}
        >
          确认
          {selectedIds.current.length > 0 ? `（已选 ${selectedIds.current.length} 个）` : ''}
        </Button>
      )}
      className={styles.main}
      customContainerClassName={styles.container}
    >
      {contextHolder}
      <div className={styles.search}>
        <SearchBar
          placeholder="请输入搜索产品名称或编号"
          className="flex-1"
          value={searchValue}
          onChange={handleSearch}
        />
      </div>
      <div className={styles.scrollContainer}>

        <PullToRefresh
          onRefresh={async () => {
            setPage(1)
            await getData(searchValue, 1)
          }}
        >
          <div className={styles.context}>
            {list.map(item => (
              <Item
                key={item.id}
                data={item}
                onSelect={handleSelect}
                onUnSelect={handleUnSelect}
              />
            ))}
            <InfiniteScroll
              loadMore={async () => {
                if (list.length < total && !loading) {
                  const nextPage = page + 1
                  setPage(nextPage)
                  await getData(searchValue, nextPage)
                }
              }}
              hasMore={list.length < total && !loading}
            />
          </div>
        </PullToRefresh>
      </div>

    </ListFrame>
  )
}
