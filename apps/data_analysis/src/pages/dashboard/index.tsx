// import 'https://hcscmtest.zzfzyc.com/chat/.widget/abu-ai.css'
import type { DateRange } from '@/components/dateComponents/RangeDate'
import HeaderBox from '@/components/headerBox'
import ListFrame from '@/components/listFrame'
import { GetMatrixData } from '@/service/api/dashboard'
import { useMobileScreen } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Col, message, Modal, Row, Typography } from 'antd'
import dayjs from 'dayjs'
import { useEffectOnActive } from 'keepalive-for-react'
// 导入组件和样式
// import { destroy, init } from 'https://hcscmtest.zzfzyc.com/chat/.widget/abu-ai.es.js'
import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import ClusteringProcess, { transformMatrixDataToChart } from './components/ClusteringProcess'
import DataOverview from './components/DataOverview'
import clusteringProcessStyles from './components/index.module.scss'
import { TreeChart } from './components/TreeChart'
import styles from './index.module.scss'

const { Title } = Typography
// 使用组件
export function Component() {
  const isMobile = useMobileScreen()
  const [messageApi, contextHolder] = message.useMessage()
  const [dateRange, setDateRange] = useState<DateRange>()
  const handleChangeDateRange = (dateRange: DateRange) => {
    setDateRange(dateRange)
  }
  const { mutateAsync, data: matrixData } = GetMatrixData({
    onError: (e) => {
      messageApi.error(e.message)
    },
  })
  const getData = async () => {
    const res = await mutateAsync({
      start_time: dayjs(dateRange?.[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange?.[1]).format('YYYY-MM-DD'),
    })
    // 转换数据
    console.log(res)
  }
  useEffectOnActive(() => {
    getData()
  }, [dateRange])

  // 使用接口返回的真实数据
  const chartData = matrixData?.product_matrix ? transformMatrixDataToChart(matrixData.product_matrix) : []
  // 动态生成图例标签
  const getCustomPieces = () => {
    const avgProfit = matrixData?.profit_margin ? Number.parseFloat(matrixData.profit_margin).toFixed(1) : '平均'
    const avgGrowth = matrixData?.sale_growth_rate ? Number.parseFloat(matrixData.sale_growth_rate).toFixed(1) : '平均'

    return [
      { value: 0, label: `明星产品${isMobile ? '\n' : ''}(≥${avgProfit}%利润,${isMobile ? '\n' : ''}≥${avgGrowth}%增长)`, color: 'rgba(82, 196, 26, 0.8)' },
      { value: 1, label: `现金牛${isMobile ? '\n' : ''}(≥${avgProfit}%利润,${isMobile ? '\n' : ''}<${avgGrowth}%增长)`, color: 'rgba(250, 173, 20, 0.8)' },
      { value: 2, label: `潜力产品${isMobile ? '\n' : ''}(<${avgProfit}%利润,${isMobile ? '\n' : ''}≥${avgGrowth}%增长)`, color: 'rgba(24, 144, 255, 0.8)' },
      { value: 3, label: `淘汰区${isMobile ? '\n' : ''}(<${avgProfit}%利润,${isMobile ? '\n' : ''}<${avgGrowth}%增长)`, color: 'rgba(245, 34, 45, 0.8)' },
    ]
  }

  const customPieces1 = getCustomPieces()

  const navigate = useNavigate() // 路由跳转
  function handleClickTips1() {
    const avgProfit = matrixData?.profit_margin ? Number.parseFloat(matrixData.profit_margin).toFixed(1) : '平均'
    const avgGrowth = matrixData?.sale_growth_rate ? Number.parseFloat(matrixData.sale_growth_rate).toFixed(1) : '平均'

    Modal.info({
      title: '提示',
      okText: '我知道了',
      maskClosable: true,
      cancelText: '',
      content: (
        <p>
          <ul>
            <li className={styles.starPrefix}>
              明星产品：利润率≥
              {avgProfit}
              %， 增长率≥
              {avgGrowth}
              %
            </li>
            <li className={styles.cashCowPrefix}>
              现金牛：利润率≥
              {avgProfit}
              %， 增长率＜
              {avgGrowth}
              %
            </li>
            <li className={styles.potentialPrefix}>
              潜力产品：利润率＜
              {avgProfit}
              %， 增长率≥
              {avgGrowth}
              %
            </li>
            <li className={styles.eliminatePrefix}>
              淘汰区：利润率＜
              {avgProfit}
              %， 增长率＜
              {avgGrowth}
              %
            </li>
          </ul>
        </p>
      ),
    })
  }
  function handleClickTips2() {
    Modal.info({
      title: '提示',
      okText: '我知道了',
      maskClosable: true,
      content: (
        <p>
          <ul>
            <li className={styles.bluePrefix}>
              普通客户：购买频率≥
              {matrixData?.customer_purchase_rate}
              %
              ， 金额＜
              ￥
              {matrixData?.customer_purchase_price}
            </li>
            <li className={styles.bluePrefix}>
              活跃低价值：购买频率＜
              {matrixData?.customer_purchase_rate}
              %
              ， 金额＜
              ￥
              {matrixData?.customer_purchase_price}
            </li>
            <li className={styles.bluePrefix}>
              潜力客户：购买频率＜
              {matrixData?.customer_purchase_rate}
              %
              ， 金额≥
              ￥
              {matrixData?.customer_purchase_price}
            </li>
            <li className={styles.bluePrefix}>
              核心客户：购买频率≥
              {matrixData?.customer_purchase_rate}
              %
              ， 金额≥
              ￥
              {matrixData?.customer_purchase_price}
            </li>
          </ul>
        </p>
      ),
    })
  }
  function handleClickArea(data) {
    const params = new URLSearchParams()
    params.set('startDate', dayjs(dateRange?.[0]).format('YYYY-MM-DD'))
    params.set('endDate', dayjs(dateRange?.[1]).format('YYYY-MM-DD'))

    navigate({
      pathname: '/customer-classification-detail',
      search: params.toString(),
    })
  }
  // const [matrixData, setMatrixData] = useState<Api.GetMatrixData.HcscmStructureShouldCollectOrderGetProductMatrixData[]>([])
  // const chartData = transformMatrixDataToChart(matrixData?.product_matrix || [])
  console.log('chartData', chartData)
  return (
    <ListFrame header={<HeaderBox showUserInfo />} className={styles.dashboardPage}>
      <div className={styles.container}>
        {contextHolder}
        <div className={styles.content}>
          <DataOverview onDateRangeChange={handleChangeDateRange} />
          <Row gutter={[10, 10]} className={clusteringProcessStyles.clusteringProcessBox} id="clusteringProcessBox">
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card>
                <Title level={5} className="mb-2!">
                  近
                  {matrixData?.product_cycle}
                  天产品矩阵
                  <QuestionCircleOutlined onClick={handleClickTips1} />
                </Title>
                <ClusteringProcess
                  height="60vh"
                  data={chartData}
                  matrixData={matrixData}
                  config={{
                    title: '',
                    xAxisName: '产品利润率(%)',
                    yAxisName: '销量增长率(%)',
                    pieces: customPieces1,
                    grid: {
                      top: '5%',
                      bottom: isMobile ? '11%' : '10%',
                      left: isMobile ? '10%' : '11%',
                      right: isMobile ? '5%' : '3%',
                    },
                    clusterCount: 4,
                    name: {
                      firstQuadrant: '明星产品',
                      secondQuadrant: '潜力产品',
                      thirdQuadrant: '淘汰区',
                      fourthQuadrant: '现金牛',
                    },
                    colors: {
                      firstQuadrant: 'rgba(82, 196, 26, 0.08)',
                      secondQuadrant: 'rgba(24, 144, 255, 0.08)',
                      thirdQuadrant: 'rgba(245, 34, 45, 0.08)',
                      fourthQuadrant: 'rgba(250, 173, 20, 0.08)',
                    },
                  }}
                  onPointClick={(data) => {
                    // data是echarts点击事件返回的数据，包含散点的完整信息
                    // data数组结构: [x, y, quadrant, name, detailNum, productId]
                    if (data && Array.isArray(data) && data.length >= 6) {
                      const productId = data[5] // product_id在数组的第6个位置（索引5）
                      const productName = data[3] // product_name在数组的第4个位置（索引3）
                      const startTime = dayjs(dateRange?.[0]).format('YYYY-MM-DD')
                      const endTime = dayjs(dateRange?.[1]).format('YYYY-MM-DD')
                      navigate(`/product/infos?mode=1&product_id=${productId}&product_name=${encodeURIComponent(productName)}&start_time=${startTime}&end_time=${endTime}`)
                    }
                  }}
                />
              </Card>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card>
                <Title level={5} className="mb-2!">
                  近
                  {matrixData?.customer_cycle}
                  天客户矩阵
                  <QuestionCircleOutlined onClick={handleClickTips2} />
                </Title>
                <TreeChart
                  data={matrixData?.customer_matrix || []}
                  height="60vh"
                  onClickArea={handleClickArea}
                />
              </Card>
            </Col>
          </Row>
          {/* <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <AIMulDimAnalysis />
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <FunctionBox dateRange={dateRange} />
            </Col>
          </Row> */}
        </div>
      </div>
    </ListFrame>
  )
}
