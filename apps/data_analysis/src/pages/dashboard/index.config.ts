import type { MutationOptions } from '@/service/request'
// import type { PaginationResponse } from '@/service/request/type' // 分页需要
import { useCustomMutation } from '@/service/request'

// 获取工作台的数据  http://192.168.1.28:50001/hcscm/admin/v1/analysis/getWorkbenchData
export function FetchGetWorkbenchData(options?: MutationOptions<Api.Dashboard.WorkbenchDataResponse, Api.Dashboard.WorkbenchDataRequest>) {
  return useCustomMutation<Api.Dashboard.WorkbenchDataResponse, Api.Dashboard.WorkbenchDataRequest>(
    {
      url: '@/h5/v1/analysis/getWorkbenchData',
      method: 'GET',
    },
    options,
  )
}
