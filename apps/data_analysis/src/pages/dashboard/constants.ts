// 定义本地卡片配置
export interface MetricCardType {
  readonly title: string // 指标标题
  readonly key: string // 指标key
  readonly unit: any // 单位
  readonly lastIssue?: string // 上期指标
  readonly dailyAvg?: string // 日均
}

export const METRIC_CARDS: MetricCardType[] = [
  {
    title: '销售额',
    key: 'sale_amount',
    lastIssue: 'sale_amount_last_rate',
    dailyAvg: 'sale_amount_daily_avg',
    unit: '¥',
  },
  {
    title: '匹数',
    key: 'roll',
    lastIssue: 'roll_last_rate',
    dailyAvg: 'roll_daily_avg',
    unit: '',
  },
  {
    title: '大货客户数',
    key: 'big_customer_count',
    lastIssue: 'big_customer_last_rate',
    dailyAvg: 'big_customer_daily_avg',
    unit: '',
  },
  {
    title: '大货数量',
    key: 'big_order_weight',
    lastIssue: 'big_order_last_rate',
    dailyAvg: 'big_order_daily_avg',
    unit: '',
  },
  {
    title: '剪版数量',
    key: 'plate_order_weight',
    lastIssue: 'plate_order_last_rate',
    dailyAvg: 'plate_order_daily_avg',
    unit: '',
  },
  {
    title: '剪版客户数',
    key: 'small_customer_count',
    lastIssue: 'small_customer_last_rate',
    dailyAvg: 'small_customer_daily_avg',
    unit: '',
  },
  {
    title: '总客户数',
    key: 'total_count',
    lastIssue: 'total_count_last_rate',
    dailyAvg: 'total_count_daily_avg',
    unit: '',
  },
  {
    key: 'total_debt',
    title: '总欠款',
    unit: '',
    // lastIssue: '',
    // dailyAvg: '',
  },
] as const

// export type MetricKey = typeof METRIC_CARDS[number]['key']
