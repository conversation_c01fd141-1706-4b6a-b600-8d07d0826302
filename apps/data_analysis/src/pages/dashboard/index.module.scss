@reference 'tailwindcss';
.dashboardPage {
  overflow-y: auto !important;
  overflow-x: hidden !important;
  @apply flex flex-col min-h-screen bg-[#f5f6f7];

  .container {
    @apply w-full flex justify-center px-3 pt-3;

    .content {
      @apply w-full flex flex-col gap-4;
      
      // 如果页面大于1200px
      @screen xl {
        @apply min-w-[1250px] max-w-[1920px] w-[96vw];
      }
    }
  }
}
// 响应式设计
@media (max-width: 768px) {
  .dashboardPage {
    min-height: unset;
  }
}
.starPrefix{
  @apply relative pl-5 before:content-[''] before:absolute before:left-0 before:top-[0.5em] before:w-3 before:h-3 before:rounded-full;
}
.starPrefix::before {
  background-color: rgba(82, 196, 26, 0.8);
}

.cashCowPrefix{
  @apply relative pl-5 before:content-[''] before:absolute before:left-0 before:top-[0.5em] before:w-3 before:h-3 before:rounded-full;
}
.cashCowPrefix::before {
  background-color: rgba(250, 173, 20, 0.8);
}

.potentialPrefix{
  @apply relative pl-5 before:content-[''] before:absolute before:left-0 before:top-[0.5em] before:w-3 before:h-3 before:rounded-full;
}
.potentialPrefix::before {
  background-color: rgba(24, 144, 255, 0.8);
}

.eliminatePrefix{
  @apply relative pl-5 before:content-[''] before:absolute before:left-0 before:top-[0.5em] before:w-3 before:h-3 before:rounded-full;
}
.eliminatePrefix::before {
  background-color: rgba(245, 34, 45, 0.8);
}
