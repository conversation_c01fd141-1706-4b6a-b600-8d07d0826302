@reference 'tailwindcss';
.ai {
  @apply bg-white rounded-lg p-4;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
  opacity: 0;
  animation: fadeIn 0.25s ease-out forwards;

  .title {
    @apply text-lg font-medium mb-3;
  }

  .filters {
    @apply flex flex-col md:flex-row items-stretch md:items-end gap-4 mb-6 p-4 rounded-sm;
    background-color: #e8f4ff;

    .filterItem {
      @apply w-full md:flex-1;

      label {
        @apply block text-sm text-gray-500 mb-2;
      }
    }

    .analyzeBtn {
      @apply w-full md:w-auto md:min-w-[120px] h-[32px];
    }
  }

  .result {
    @apply bg-[#f9fafb] rounded-lg p-4;
    .resultHeader {
      @apply flex flex-col md:flex-row justify-start md:justify-between items-start md:items-center gap-4 md:gap-0 mb-4 md:mb-2;

      h3 {
        @apply text-base font-medium;
      }

      .actions {
        @apply flex flex-wrap gap-2;
      }
    }

    .resultContent {
      @apply flex flex-col rounded-lg gap-3;
      .resultContentItem{
        @apply bg-white p-4;
      }
      h4 {
        @apply text-base font-medium mb-2;
      }

      p {
        @apply text-gray-600 text-sm leading-relaxed;
      }

      .actionButtons {
        @apply flex gap-4 mt-4;

        // button {
        //   @apply text-blue-500 hover:text-blue-600 p-0;
        // }
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
