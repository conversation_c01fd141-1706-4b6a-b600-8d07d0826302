// 数据总览
@reference 'tailwindcss';
.overview {
  @apply w-full bg-white p-4 rounded-lg;
  animation: fadeInUp 0.25s ease-out;

  .header {
    // @apply mb-3;

    .title {
      @apply m-0 !text-base shrink-0;
      animation: fadeIn 0.25s ease-out;
    }
    .datePicker {
      @apply min-w-0; // 允许日期选择器缩小

      :global {
        .ant-picker-input {
          input {
            @apply text-xs; // 调小日期文字大小
          }
        }

        // 在小屏幕上缩小日期选择器的内边距
        @media screen and (max-width: 576px) {
          .ant-picker-input {
            @apply px-1;
          }
        }
      }
    }
  }

  .cardsContainer {
    @apply w-full;
    margin: 0 -6px; // 抵消父容器的内边距
    padding: 0 6px; // 添加内边距以显示完整阴影
    overflow: hidden;

    .swiper {
      @apply overflow-visible;
      padding: 8px 0; // 添加上下内边距以显示完整阴影
    }

    .slide {
      @apply w-[280px] shrink-0;
      opacity: 0;
      animation: slideIn 0.5s ease-out forwards;

      @for $i from 1 through 7 {
        &:nth-child(#{$i}) {
          animation-delay: #{$i * 0.1}s;
        }
      }
    }

    .metricCard {
      @apply w-full;
      transition: all 0.3s ease;

      &:hover {
        @apply shadow-lg transform -translate-y-1;
        cursor: pointer;
      }

      .cardTitle {
        @apply text-sm text-gray-500 mb-2;
      }

      .value {
        @apply text-xl font-medium mb-0 min-h-[32px];
      }

      .footer {
        @apply flex justify-between items-center text-sm;

        .trend, .dailyAvg {
          @apply flex items-center gap-1;
        }

        .label {
          @apply text-gray-500;
        }

        .up {
          @apply text-red-500;
        }

        .down {
          @apply text-green-500;
        }
      }
    }
  }
}
.functionBox{
  overflow-y: auto !important;
  overflow-x: hidden;
}
.categorySection {
  // margin-bottom: 32px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  background: #fff;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.categoryTitle {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  gap: 8px;
}

.categoryLine {
  width: 4px;
  height: 16px;
  background-color: #1890ff;
  border-radius: 2px;
}

.categoryText {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.categoryCards {
  margin-bottom: 0;
}
.functionGrid{
  min-height: 200px;
  padding: 20px;
  gap: 20px;
}
.functionCard {
  // height: 60px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: #fff;
  padding: 12px 12px;
  
  &:hover {
    background-color: #eff6ff;
    border-color: #155dfc;
    
    .cardTitle {
      color: #155dfc;
    }
    
    .cardArrow {
      color: #155dfc;
    }
  }
}

.cardContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.cardTitle {
  font-size: 14px;
  font-weight: 400;
  color: #333;
  flex: 1;
  text-align: left;
  transition: color 0.3s ease;
}

.cardArrow {
  color: #bfbfbf;
  font-size: 12px;
  transition: color 0.3s ease;
}

.noResults {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.noResultsText {
  font-size: 16px;
  margin-bottom: 8px;
}

.noResultsSubtext {
  font-size: 14px;
  color: #ccc;
}
.tabs{
  width: 100%;
  margin: 0 20px;
}
.tabHeader{
  width: 100%;
  background-color: white;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.searchContainer{
  display: flex;
  align-items: center;
  background-color: white;
}
// 响应式设计
@media (max-width: 768px) {
  .tabHeader {
    width: 100vw;
    flex-direction: column;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 99;
  }
  
  .searchContainer {
    width: 100%;
    padding-bottom: 16px;
    
    .ant-input-search {
      width: 100% !important;
    }
  }
}
.clusteringProcessBox{
  @apply relative z-10 ;
  // animation: fadeInUp 0.25s ease-out 0.2s both;
}
// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes scaleIn {
  from {
    transform: translateY(-50%) scaleY(0);
  }
  to {
    transform: translateY(-50%) scaleY(1);
  }
}

.trend {
  @apply flex items-center gap-1;

  .up {
    @apply text-[#f5222d];
  }

  .down {
    @apply text-[#52c41a];
  }

  .equal {
    @apply text-[#1677ff];
  }

  .label {
    @apply text-[#00000073];
  }
}
.processChart{
  :global{
    canvas{
      width: 100% !important;
      height: 100% !important;
    }
  }
}
