@reference "tailwindcss";
.ai {
  @apply bg-white rounded-lg p-4;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.05);
  opacity: 0;
  animation: fadeIn 0.25s ease-out forwards;
}
.ai .title {
  @apply text-lg font-medium mb-3;
}
.ai .filters {
  @apply flex flex-col md:flex-row items-stretch md:items-end gap-4 mb-6 p-4 rounded-sm;
  background-color: #e8f4ff;
}
.ai .filters .filterItem {
  @apply w-full md:flex-1;
}
.ai .filters .filterItem label {
  @apply block text-sm text-gray-500 mb-2;
}
.ai .filters .analyzeBtn {
  @apply w-full md:w-auto md:min-w-[120px] h-[32px];
}
.ai .result {
  @apply bg-[#f9fafb] rounded-lg p-4;
}
.ai .result .resultHeader {
  @apply flex flex-col md:flex-row justify-start md:justify-between items-start md:items-center gap-4 md:gap-0 mb-4 md:mb-2;
}
.ai .result .resultHeader h3 {
  @apply text-base font-medium;
}
.ai .result .resultHeader .actions {
  @apply flex flex-wrap gap-2;
}
.ai .result .resultContent {
  @apply flex flex-col rounded-lg gap-3;
}
.ai .result .resultContent .resultContentItem {
  @apply bg-white p-4;
}
.ai .result .resultContent h4 {
  @apply text-base font-medium mb-2;
}
.ai .result .resultContent p {
  @apply text-gray-600 text-sm leading-relaxed;
}
.ai .result .resultContent .actionButtons {
  @apply flex gap-4 mt-4;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}/*# sourceMappingURL=AIMulDimAnalysis.module.css.map */