import { useMobileScreen } from '@/utils'
import * as echarts from 'echarts'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

interface CustomerData {
  name: string
  value: number
  itemStyle?: {
    color: string
  }
  label?: {
    formatter: string | Function
    rich?: Record<string, any>
  }
  trend?: 'up' | 'down' | 'stable'
  trendValue?: number
}
interface Props {
  data: Api.GetMatrixData.HcscmStructureShouldCollectOrderGetCustomerMatrixData
  onClickArea?: (params: any) => void
  height?: string | number
}
export function TreeChart(props: Props) {
  const { data, onClickArea, height } = props
  const isMobile = useMobileScreen()
  const [isFullscreen, setIsFullscreen] = useState(false)
  const chartInstance = useRef<echarts.ECharts>()
  const chartRef = useRef<HTMLDivElement>(null)
  const [rotateDirection, setRotateDirection] = useState<'left' | 'right'>('left')

  useEffect(() => {
    const chartDom = chartRef.current
    if (!chartDom)
      return
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }
    chartInstance.current = echarts.init(chartDom)

    // 使用真实数据构建客户数据
    const customerData: CustomerData[] = [
      {
        name: '普通客户',
        value: data.un_active || 0,
        itemStyle: {
          color: '#bfdbfe', // 浅蓝色
        },
        trend: (data.un_active_growth || 0) > 0 ? 'up' : (data.un_active_growth || 0) < 0 ? 'down' : 'stable',
        trendValue: Math.abs(data.un_active_growth || 0),
      },
      {
        name: '核心客户',
        value: data.core || 0,
        itemStyle: {
          color: '#e9d5ff', // 浅紫色
        },
        trend: (data.core_growth || 0) > 0 ? 'up' : (data.core_growth || 0) < 0 ? 'down' : 'stable',
        trendValue: Math.abs(data.core_growth || 0),
      },
      {
        name: '潜力客户',
        value: data.potential || 0,
        itemStyle: {
          color: '#fed7aa', // 浅橙色
        },
        trend: (data.potential_growth || 0) > 0 ? 'up' : (data.potential_growth || 0) < 0 ? 'down' : 'stable',
        trendValue: Math.abs(data.potential_growth || 0),
      },
      {
        name: '活跃低价值',
        value: data.ordinary || 0,
        itemStyle: {
          color: '#bbf7d0', // 浅绿色
        },
        trend: (data.ordinary_growth || 0) > 0 ? 'up' : (data.ordinary_growth || 0) < 0 ? 'down' : 'stable',
        trendValue: Math.abs(data.ordinary_growth || 0),
      },
    ].filter(item => item.value > 0) // 过滤掉值为0的项

    // 为每个数据项添加标签格式化函数
    customerData.forEach((item) => {
      item.label = {
        formatter: (params: any) => {
          const trend = params.data.trend
          const trendValue = params.data.trendValue
          const trendIcon = trend === 'up'
            ? '{upArrow|}'
            : trend === 'down'
              ? '{downArrow|}'
              : ''
          const trendText = trendValue && trendValue > 0 ? trend === 'up' ? ` {upText|+${trendValue}}` : ` {downText|-${trendValue}}` : ''

          return `{name|${params.name}}\n{value|${params.value}人} ${trendIcon}${trendText}`
        },
        rich: {
          upText: {
            fontSize: 12,
            fontWeight: 'bold',
            color: 'red',
          },
          downText: {
            fontSize: 12,
            fontWeight: 'bold',
            color: 'green',
          },
          name: {
            fontSize: 16,
            color: '#333',
            align: 'center',
            verticalAlign: 'middle',
          },
          value: {
            fontSize: 12,
            color: '#333',
            align: 'center',
            verticalAlign: 'middle',
          },
          upArrow: {
            width: 16,
            height: 16,
            align: 'center',
            verticalAlign: 'middle',
            backgroundColor: {
              image: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmY0NDQ0IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1hcnJvdy11cCI+PHBhdGggZD0iTTEyIDV2MTRNNSAxMmw3LTcgNyA3Ii8+PC9zdmc+',
            },
          },
          downArrow: {
            width: 16,
            height: 16,
            align: 'center',
            verticalAlign: 'middle',
            backgroundColor: {
              image: 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMDBjYzY2IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1hcnJvdy1kb3duIj48cGF0aCBkPSJNMTIgMTl2LTE0TTUgMTJsNyA3IDctNyIvPjwvc3ZnPg==',
            },
          },
        },
      }
    })

    const option = {
      toolbox: {
        show: isMobile,
        feature: {
          myFullScreen: {
            show: true,
            title: '全屏',
            icon: 'path://M919.920093 725.414549q3.014188 26.122962 7.033105 58.776664t7.53547 66.814498 7.53547 67.819227 7.033105 60.786122q6.028376 47.222277-41.193901 44.208089-25.118232-2.009459-56.767205-5.526011t-64.805039-7.53547-65.809769-8.037834-59.781393-7.033105q-29.137149-3.014188-37.174984-16.578033t9.042564-30.644243q11.052022-10.047293 27.127691-27.630056t27.127691-28.634785q11.052022-12.056752 7.033105-22.104044t-16.075669-23.108774q-28.13242-27.127691-51.241194-49.231735t-51.241194-51.241194q-6.028376-6.028376-12.056752-13.061481t-9.042564-15.573304-1.004729-18.085127 13.061481-20.59695q6.028376-6.028376 10.047293-10.549658t8.037834-8.037834 8.540199-8.037834 11.554387-12.559116q20.094586-20.094586 37.174984-17.080398t37.174984 23.108774 41.193901 40.691536 47.222277 46.719912q19.089857 18.085127 32.653702 25.118232t26.625326-6.028376q9.042564-9.042564 22.606409-21.60168t23.611138-22.606409q17.080398-17.080398 30.644243-13.061481t16.578033 30.141879zM43.79615 383.80659q-3.014188-26.122962-7.033105-58.776664t-7.53547-66.814498-7.53547-67.819227-7.033105-60.786122q-3.014188-26.122962 6.53074-36.170255t33.658431-8.037834q25.118232 2.009459 56.767205 5.526011t64.805039 7.53547 65.809769 8.037834 59.781393 7.033105q30.141879 3.014188 37.677348 16.578033t-9.544928 30.644243q-10.047293 10.047293-24.615868 26.122962t-25.620597 27.127691q-12.056752 12.056752-8.037834 22.104044t17.080398 23.108774q13.061481 14.06621 24.615868 24.615868t22.606409 21.099315 23.108774 22.606409l25.118232 25.118232q6.028376 6.028376 11.554387 14.06621t8.037834 17.080398-0.502365 19.089857-13.061481 20.094586l-11.052022 11.052022q-4.018917 4.018917-7.53547 8.037834t-8.540199 8.037834l-11.052022 12.056752q-20.094586 20.094586-34.663161 15.070939t-34.663161-25.118232-38.179713-37.677348-44.208089-43.705724q-18.085127-18.085127-32.151337-25.118232t-27.127691 6.028376q-9.042564 10.047293-25.118232 24.615868t-26.122962 24.615868q-17.080398 17.080398-30.141879 13.061481t-16.075669-30.141879zM905.853883 84.397261q26.122962-3.014188 36.170255 6.53074t8.037834 34.663161-5.526011 56.767205-7.53547 64.805039-8.037834 65.809769-7.033105 59.781393q-3.014188 29.137149-16.578033 37.174984t-30.644243-10.047293q-10.047293-10.047293-26.122962-24.615868t-27.127691-25.620597q-12.056752-11.052022-22.104044-7.53547t-23.108774 16.578033q-27.127691 27.127691-47.724641 49.231735t-48.729371 50.236465q-6.028376 6.028376-14.06621 11.554387t-17.080398 8.037834-19.089857-0.502365-20.094586-14.06621q-6.028376-6.028376-10.549658-10.047293t-8.540199-8.037834-8.540199-8.037834-11.554387-12.056752q-20.094586-20.094586-16.075669-35.165525t25.118232-35.165525l38.179713-40.189172q19.089857-20.094586 45.212818-46.217547 19.089857-18.085127 26.122962-32.151337t-7.033105-26.122962q-9.042564-9.042564-23.108774-24.615868t-24.113503-25.620597q-17.080398-17.080398-13.061481-30.141879t30.141879-16.075669 58.776664-7.033105 67.316863-7.53547 67.819227-7.53547 60.283758-7.033105zM350.238584 640.012559q6.028376 6.028376 10.549658 10.047293t8.540199 8.037834l8.037834 9.042564 12.056752 11.052022q20.094586 20.094586 17.582763 36.672619t-23.611138 37.677348q-19.089857 19.089857-40.189172 40.691536t-47.222277 47.724641q-18.085127 18.085127-22.606409 29.639514t8.540199 24.615868q10.047293 9.042564 22.606409 22.606409t22.606409 23.611138q17.080398 17.080398 12.559116 30.141879t-30.644243 16.075669-58.274299 7.033105-66.814498 8.037834-68.321592 8.037834-60.786122 7.033105q-25.118232 2.009459-35.66789-7.53547t-8.540199-33.658431q2.009459-25.118232 5.526011-56.767205t7.53547-64.805039 8.037834-65.809769 7.033105-59.781393q3.014188-30.141879 16.578033-37.677348t30.644243 9.544928q10.047293 10.047293 27.630056 26.122962t28.634785 27.127691q12.056752 12.056752 20.094586 10.549658t20.094586-14.568575q13.061481-13.061481 25.118232-25.620597t24.113503-24.615868 24.615868-25.118232 26.625326-27.127691q6.028376-6.028376 13.061481-12.056752t15.573304-9.042564 18.085127-0.502365 20.59695 13.563845z',
            onclick() {
              if (chartDom) {
                // console.log('getFullscreenElement', getFullscreenElement())
                if (isFullscreen) {
                  // exitFullscreen()
                  setIsFullscreen(false)
                }
                else {
                  // full(chartDom)
                  setIsFullscreen(true)
                }
              }
            },
          },
        },
        right: 10,
        top: 10,
        iconStyle: {
          color: '#666', // 图标颜色
          borderColor: '#666', // 图标边框颜色
          borderWidth: 1, // 图标边框宽度
        },
        emphasis: {
          iconStyle: {
            color: '#333', // 鼠标悬停时的颜色
          },
        },
      },
      tooltip: {},
      series: [
        {
          type: 'treemap',
          data: customerData,
          width: '100%',
          height: '100%',
          roam: false,
          nodeClick: false,
          label: {
            // overflow: 'truncate',
            // ellipsis: 'truncate',
          },
          breadcrumb: {
            show: false,
          },
          // 解决 treemap 设置 rich 后整体label不垂直居中的问题
          labelLayout(params: any) {
            return {
              x: params.rect.x,
              y: params.labelRect.y,
            }
          },
          emphasis: {
            label: {
              show: true,
            },
          },
          upperLabel: {
            show: false,
          },
        },
      ],
      grid: {
        top: '10%',
        bottom: 70,
      },
      backgroundColor: 'rgba(243, 244, 246, 1)',
    }

    chartInstance.current.setOption(option)
    if (onClickArea) {
      chartInstance.current.on('click', (params: any) => {
        onClickArea(params.data)
      })
    }
    // 响应式调整
    const handleResize = () => {
      chartInstance.current?.resize()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        onClickArea && chartInstance.current.off('click')
        chartInstance.current.dispose()
      }
    }
  }, [height, isMobile, onClickArea, isFullscreen])
  useEffect(() => {
    setTimeout(() => {
      chartInstance.current?.resize()
    }, 60)
  }, [])
  // 检测设备方向
  useEffect(() => {
    const checkOrientation = () => {
      // 优先使用 Screen Orientation API
      if (window.screen?.orientation?.angle !== undefined) {
        setRotateDirection(window.screen.orientation.angle === 90 ? 'left' : 'right')
      }
      // 回退到 window.orientation
      else if (window.orientation !== undefined) {
        setRotateDirection(window.orientation === 90 ? 'right' : 'left')
      }
      // 如果都不支持，使用视口尺寸判断
      else {
        const isLandscape = window.innerWidth > window.innerHeight
        setRotateDirection(isLandscape ? 'left' : 'right')
      }
    }

    checkOrientation()
    window.addEventListener('orientationchange', checkOrientation)
    // 监听 Screen Orientation API 的变化
    if (window.screen?.orientation) {
      window.screen.orientation.addEventListener('change', checkOrientation)
    }
    return () => {
      window.removeEventListener('orientationchange', checkOrientation)
      if (window.screen?.orientation) {
        window.screen.orientation.removeEventListener('change', checkOrientation)
      }
    }
  }, [])
  useEffect(() => {
    if (isFullscreen) {
      // 禁止页面滚动
      document.body.style.overflow = 'hidden'
    }
    else {
      // 恢复页面滚动
      document.body.style.overflow = ''
    }

    return () => {
      // 组件卸载时恢复滚动
      document.body.style.overflow = ''
    }
  }, [isFullscreen])
  return (
    <div
      className={isFullscreen ? 'fixed inset-0 w-screen h-screen z-9999 bg-white' : 'w-full'}
      style={isFullscreen
        ? {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100dvw',
            height: '100dvh',
            overflow: 'hidden',
          }
        : {}}
    >
      <div
        className={isFullscreen ? `absolute inset-0 ${rotateDirection === 'left' ? 'rotate-90' : 'rotate-[-90]'} origin-center` : 'w-full'}
        style={isFullscreen
          ? {
              width: '100dvh',
              height: '100dvw',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%) rotate(90deg)',
            }
          : { }}
      >
        <div
          ref={chartRef}
          className={styles.processChart}
          style={{ width: 'auto', height: isFullscreen ? '100%' : height }}
        >
        </div>
      </div>
    </div>
  )
}
