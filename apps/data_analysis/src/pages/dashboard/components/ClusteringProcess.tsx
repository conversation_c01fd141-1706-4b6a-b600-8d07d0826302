import { useMobileScreen } from '@/utils'
import currency from 'currency.js'
import * as echarts from 'echarts'
import ecStat from 'echarts-stat'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'
// 注册聚类分析转换器
echarts.registerTransform(ecStat.transform.clustering)
// 类型定义
interface DataPoint {
  name: string
  value: [number, number, string]
  symbolSize: number
  category: 'highValue' | 'potentialValue' | 'generalValue' | 'lowValue'
}

interface ChartConfig {
  title?: string
  xAxisName?: string
  yAxisName?: string
  xAxisSplitValue?: number
  yAxisSplitValue?: number
  clusterCount?: number
  name?: {
    firstQuadrant?: string
    secondQuadrant?: string
    thirdQuadrant?: string
    fourthQuadrant?: string
  }
  pieces?: Array<{
    value: number
    label: string
    color: string
  }>
  colors?: {
    firstQuadrant?: string
    secondQuadrant?: string
    thirdQuadrant?: string
    fourthQuadrant?: string
  }
  grid?: {
    top?: string | number
    right?: string | number
    bottom?: string | number
    left?: string | number
  }
}

interface ClusteringProcessProps {
  height?: string | number
  width?: string | number
  data?: [number, number, string, number, number][]
  config?: ChartConfig
  onPointClick?: (data: DataPoint) => void
  onChartReady?: (instance: echarts.ECharts) => void
  matrixData?: {
    max_profit_margin?: string
    min_profit_margin?: string
    max_sale_growth_rate?: string
    min_sale_growth_rate?: string
    profit_margin?: string
    sale_growth_rate?: string
    detail_num?: string
  }
}

// 默认配置
const defaultConfig: ChartConfig = {
  title: '产品价值矩阵',
  xAxisName: '产品利润率(%)', // 更新x轴名称
  yAxisName: '销售增长率(%)', // 更新y轴名称
  xAxisSplitValue: 25,
  yAxisSplitValue: 5,
  clusterCount: 4,
  name: {
    firstQuadrant: '明星产品', // 高利润率，高增长率
    secondQuadrant: '潜力产品', // 低利润率，高增长率
    thirdQuadrant: '淘汰区', // 低利润率，低增长率
    fourthQuadrant: '现金牛', // 高利润率，低增长率
  },
  colors: {
    firstQuadrant: '#91cc75',
    secondQuadrant: '#73c0de',
    thirdQuadrant: '#fac858',
    fourthQuadrant: '#ee6666',
  },
  grid: {
    top: '10%',
    right: '10%',
    bottom: '15%',
    left: '10%',
  },
}
// 聚类颜色配置
const CLUSTER_COLORS = [
  '#91cc75',
  '#73c0de',
  '#fac858',
  '#ee6666',
]
/**
 * 将API数据转换为聚类图表所需的格式
 * @param apiData API返回的产品矩阵数据
 * @returns 转换后的图表数据 [利润率, 销售增长率, 产品名称, detail_num, product_id]
 */
export function transformMatrixDataToChart(
  apiData: Api.GetMatrixData.HcscmStructureShouldCollectOrderGetProductMatrixData[],
): [number, number, string, number, number][] {
  return apiData
    .filter(item => item.profit_margin && item.sale_growth_rate && item.product)
    .map(item => [
      Number.parseFloat(item.profit_margin || '0'), // x坐标：利润率
      Number.parseFloat(item.sale_growth_rate || '0'), // y坐标：销售增长率
      item.product || '未知产品', // 产品名称
      Number.parseFloat(item.detail_num || '1'), // detail_num用于散点大小
      item.product_id || 0, // product_id用于跳转
    ])
}
const ClusteringProcess: React.FC<ClusteringProcessProps> = ({
  data = [],
  height,
  config = {},
  onPointClick,
  onChartReady,
  matrixData,
}) => {
  const isMobile = useMobileScreen()
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const mergedConfig = { ...defaultConfig, ...config }
  const [isFullscreen, setIsFullscreen] = useState(false)

  // 计算坐标轴范围和象限分界线
  const getAxisConfig = () => {
    if (!matrixData) {
      return {
        xMin: -160,
        xMax: 150,
        yMin: -100,
        yMax: 100,
        xSplit: 0,
        ySplit: 0,
      }
    }

    // 最大产品利润率
    const maxProfit = Number.parseFloat(matrixData.max_profit_margin || '0')
    // 最小产品利润率
    const minProfit = Number.parseFloat(matrixData.min_profit_margin || '0')
    // 最大增长率
    const maxGrowth = Number.parseFloat(matrixData.max_sale_growth_rate || '0')
    // 最小增长率
    const minGrowth = Number.parseFloat(matrixData.min_sale_growth_rate || '0')
    const avgProfit = Number.parseFloat(matrixData.profit_margin || '0')
    const avgGrowth = Number.parseFloat(matrixData.sale_growth_rate || '0')

    // 计算坐标轴范围，留出一定的边距
    const profitRange = currency(maxProfit).subtract(minProfit).value
    const growthRange = currency(maxGrowth).subtract(minGrowth).value
    const profitMargin = currency(profitRange).multiply(0.2).value // 10%的边距
    const growthMargin = currency(growthRange).multiply(0.2).value // 10%的边距

    return {
      xMin: minProfit - profitMargin,
      xMax: maxProfit + profitMargin,
      yMin: minGrowth - growthMargin,
      yMax: maxGrowth + growthMargin,
      xSplit: avgProfit, // 使用平均利润率作为x轴分界线
      ySplit: avgGrowth, // 使用平均增长率作为y轴分界线
    }
  }

  const axisConfig = getAxisConfig()
  // 如果没有传入 pieces，则使用默认的聚类配置
  const defaultPieces = Array.from({ length: mergedConfig.clusterCount || 4 }, (_, i) => ({
    value: i,
    label: `聚类 ${i + 1}`,
    color: CLUSTER_COLORS[i],
  }))

  const pieces = mergedConfig.pieces || defaultPieces
  const [rotateDirection, setRotateDirection] = useState<'left' | 'right'>('left')
  useEffect(() => {
    const chartDom = chartRef.current
    if (!chartDom)
      return
    if (chartInstance.current) {
      chartInstance.current.dispose()
    }
    chartInstance.current = echarts.init(chartDom)

    const option = {
      toolbox: {
        show: isMobile,
        feature: {
          myFullScreen: {
            show: true,
            title: '全屏',
            icon: 'path://M919.**********.414549q3.014188 26.122962 7.033105 58.776664t7.53547 66.814498 7.53547 67.819227 7.033105 60.786122q6.028376 47.222277-41.193901 44.208089-25.118232-2.009459-56.767205-5.526011t-64.805039-7.53547-65.809769-8.037834-59.781393-7.033105q-29.137149-3.014188-37.174984-16.578033t9.042564-30.644243q11.052022-10.047293 27.127691-27.630056t27.127691-28.634785q11.052022-12.056752 7.033105-22.104044t-16.075669-23.108774q-28.13242-27.127691-51.241194-49.231735t-51.241194-51.241194q-6.028376-6.028376-12.056752-13.061481t-9.042564-15.573304-1.004729-18.085127 13.061481-20.59695q6.028376-6.028376 10.047293-10.549658t8.037834-8.037834 8.540199-8.037834 11.554387-12.559116q20.094586-20.094586 37.174984-17.080398t37.174984 23.108774 41.193901 40.691536 47.222277 46.719912q19.089857 18.085127 32.653702 25.118232t26.625326-6.028376q9.042564-9.042564 22.606409-21.60168t23.611138-22.606409q17.080398-17.080398 30.644243-13.061481t16.578033 30.141879zM43.79615 383.80659q-3.014188-26.122962-7.033105-58.776664t-7.53547-66.814498-7.53547-67.819227-7.033105-60.786122q-3.014188-26.122962 6.53074-36.170255t33.658431-8.037834q25.118232 2.009459 56.767205 5.526011t64.805039 7.53547 65.809769 8.037834 59.781393 7.033105q30.141879 3.014188 37.677348 16.578033t-9.544928 30.644243q-10.047293 10.047293-24.615868 26.122962t-25.620597 27.127691q-12.056752 12.056752-8.037834 22.104044t17.080398 23.108774q13.061481 14.06621 24.615868 24.615868t22.606409 21.099315 23.108774 22.606409l25.118232 25.118232q6.028376 6.028376 11.554387 14.06621t8.037834 17.080398-0.502365 19.089857-13.061481 20.094586l-11.052022 11.052022q-4.018917 4.018917-7.53547 8.037834t-8.540199 8.037834l-11.052022 12.056752q-20.094586 20.094586-34.663161 15.070939t-34.663161-25.118232-38.179713-37.677348-44.208089-43.705724q-18.085127-18.085127-32.151337-25.118232t-27.127691 6.028376q-9.042564 10.047293-25.118232 24.615868t-26.122962 24.615868q-17.080398 17.080398-30.141879 13.061481t-16.075669-30.141879zM905.853883 84.397261q26.122962-3.014188 36.170255 6.53074t8.037834 34.663161-5.526011 56.767205-7.53547 64.805039-8.037834 65.809769-7.033105 59.781393q-3.014188 29.137149-16.578033 37.174984t-30.644243-10.047293q-10.047293-10.047293-26.122962-24.615868t-27.127691-25.620597q-12.056752-11.052022-22.104044-7.53547t-23.108774 16.578033q-27.127691 27.127691-47.724641 49.231735t-48.729371 50.236465q-6.028376 6.028376-14.06621 11.554387t-17.080398 8.037834-19.089857-0.502365-20.094586-14.06621q-6.028376-6.028376-10.549658-10.047293t-8.540199-8.037834-8.540199-8.037834-11.554387-12.056752q-20.094586-20.094586-16.075669-35.165525t25.118232-35.165525l38.179713-40.189172q19.089857-20.094586 45.212818-46.217547 19.089857-18.085127 26.122962-32.151337t-7.033105-26.122962q-9.042564-9.042564-23.108774-24.615868t-24.113503-25.620597q-17.080398-17.080398-13.061481-30.141879t30.141879-16.075669 58.776664-7.033105 67.316863-7.53547 67.819227-7.53547 60.283758-7.033105zM350.238584 640.012559q6.028376 6.028376 10.549658 10.047293t8.540199 8.037834l8.037834 9.042564 12.056752 11.052022q20.094586 20.094586 17.582763 36.672619t-23.611138 37.677348q-19.089857 19.089857-40.189172 40.691536t-47.222277 47.724641q-18.085127 18.085127-22.606409 29.639514t8.540199 24.615868q10.047293 9.042564 22.606409 22.606409t22.606409 23.611138q17.080398 17.080398 12.559116 30.141879t-30.644243 16.075669-58.274299 7.033105-66.814498 8.037834-68.321592 8.037834-60.786122 7.033105q-25.118232 2.009459-35.66789-7.53547t-8.540199-33.658431q2.009459-25.118232 5.526011-56.767205t7.53547-64.805039 8.037834-65.809769 7.033105-59.781393q3.014188-30.141879 16.578033-37.677348t30.644243 9.544928q10.047293 10.047293 27.630056 26.122962t28.634785 27.127691q12.056752 12.056752 20.094586 10.549658t20.094586-14.568575q13.061481-13.061481 25.118232-25.620597t24.113503-24.615868 24.615868-25.118232 26.625326-27.127691q6.028376-6.028376 13.061481-12.056752t15.573304-9.042564 18.085127-0.502365 20.59695 13.563845z',
            onclick() {
              if (chartDom) {
                // console.log('getFullscreenElement', getFullscreenElement())
                if (isFullscreen) {
                  // exitFullscreen()
                  setIsFullscreen(false)
                }
                else {
                  // full(chartDom)
                  setIsFullscreen(true)
                }
              }
            },
          },
        },
        right: 10,
        top: 10,
        iconStyle: {
          color: '#666', // 图标颜色
          borderColor: '#666', // 图标边框颜色
          borderWidth: 1, // 图标边框宽度
        },
        emphasis: {
          iconStyle: {
            color: '#333', // 鼠标悬停时的颜色
          },
        },
      },
      dataset: {
        source: data.map(([x, y, name, detailNum, productId]) => {
          // 根据象限位置分配聚类索引，使用动态分界线
          let quadrant = 0
          if (x >= axisConfig.xSplit && y >= axisConfig.ySplit) {
            quadrant = 0 // 明星产品 - 第一象限 (右上)
          }
          else if (x < axisConfig.xSplit && y >= axisConfig.ySplit) {
            quadrant = 2 // 潜力产品 - 第二象限 (左上)
          }
          else if (x < axisConfig.xSplit && y < axisConfig.ySplit) {
            quadrant = 3 // 淘汰区 - 第三象限 (左下)
          }
          else if (x >= axisConfig.xSplit && y < axisConfig.ySplit) {
            quadrant = 1 // 现金牛 - 第四象限 (右下)
          }
          return [x, y, quadrant, name, detailNum, productId]
        }),
        dimensions: ['x', 'y', 'cluster', 'name', 'detail_num', 'product_id'],
      },
      title: {
        text: mergedConfig.title,
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          // 从传入的data中获取对应的数据项
          const dataIndex = params.dataIndex
          if (data && data[dataIndex]) {
            const [x, y, name, detailNum, productId] = data[dataIndex]

            // 根据坐标位置动态确定象限（使用平均值作为分界线）
            let quadrantName = ''
            if (x >= axisConfig.xSplit && y >= axisConfig.ySplit) {
              quadrantName = '明星产品' // 第一象限：高利润率，高增长率
            }
            else if (x >= axisConfig.xSplit && y < axisConfig.ySplit) {
              quadrantName = '现金牛' // 第四象限：高利润率，低增长率
            }
            else if (x < axisConfig.xSplit && y >= axisConfig.ySplit) {
              quadrantName = '潜力产品' // 第二象限：低利润率，高增长率
            }
            else {
              quadrantName = '淘汰区' // 第三象限：低利润率，低增长率
            }

            return `${name}<br/>利润率：${x.toFixed(2)}%<br/>增长率：${y.toFixed(2)}%<br/>象限：${quadrantName}<br/>订单数：${detailNum}`
          }
          // 如果没有找到对应数据，使用默认显示
          return `数据点: ${Math.abs(params.data[0] * params.data[1]) * 10}`
        },
      },
      xAxis: {
        name: mergedConfig.xAxisName,
        type: 'value',
        nameLocation: 'middle',
        min: axisConfig.xMin, // 动态设置最小值
        max: axisConfig.xMax, // 动态设置最大值
        nameGap: 25,
        axisLine: {
          onZero: false, // 不在0位置显示轴线
          lineStyle: {
            color: '#333',
          },
        },
        splitLine: {
          show: true,
          lineStyle: { type: 'dashed' },
        },
      },
      yAxis: {
        name: mergedConfig.yAxisName,
        type: 'value',
        nameLocation: 'middle',
        min: axisConfig.yMin, // 动态设置最小值
        max: axisConfig.yMax, // 动态设置最大值
        nameGap: 35,
        axisLine: {
          onZero: false, // 不在0位置显示轴线
          lineStyle: {
            color: '#333',
          },
        },
        splitLine: {
          show: true,
          lineStyle: { type: 'dashed' },
        },
      },
      visualMap: {
        show: false,
        type: 'piecewise',
        dimension: 2,
        pieces,
        orient: 'horizontal',
        min: 0,
        max: 4,
        bottom: 0, // 添加此行，设置在底部
        left: 'center',
        itemWidth: isMobile ? 12 : 15, // 图例标记的宽度
        itemHeight: isMobile ? 12 : 15, // 图例标记的高度
        itemGap: isMobile ? 5 : 10, // 图例项之间的间
        textGap: isMobile ? 5 : 10,
        minOpen: true,
        maxOpen: true,
        // inRange: { symbolSize: [10, 50] },
        itemSymbol: 'circle',
        textStyle: {
          lineHeight: 16,
          overflow: 'break',
          fontSize: 10,
          width: 'auto',
        },
      },
      series: [{
        type: 'scatter',
        // data,
        encode: {
          x: 0,
          y: 1,
          tooltip: [2],
        },
        symbolSize: (value, params) => {
          // 使用每个数据点的detail_num作为散点大小
          const detailNum = value[4] || 1 // detail_num在数组的第5个位置（索引4）
          const baseSize = Math.sqrt(detailNum) * (isMobile ? 5 : 8)
          return Math.max(8, Math.min(baseSize, 60))
        },
        itemStyle: {
          borderColor: '#555',
          borderWidth: 2,
        },
        datasetIndex: 0,
        markLine: {
          silent: true,
          lineStyle: { color: '#333', width: 2 },
          data: [
            { xAxis: axisConfig.xSplit }, // 在平均利润率位置显示垂直线
            { yAxis: axisConfig.ySplit }, // 在平均增长率位置显示水平线
          ],
          label: { formatter: '' },
        },
        markArea: {
          silent: true,
          itemStyle: { color: 'transparent' },
          data: [
            [{
              name: mergedConfig.name?.firstQuadrant,
              xAxis: axisConfig.xSplit,
              yAxis: axisConfig.ySplit,
              itemStyle: { color: mergedConfig.colors?.firstQuadrant || 'transparent' },
              label: {
                position: 'insideTopRight',
                color: '#c2c2c2',
                fontSize: 14,
              },
            }, {
              x: 'max',
              y: 'max',
            }],
            [{
              name: mergedConfig.name?.secondQuadrant,
              x: 'min',
              yAxis: axisConfig.ySplit,
              itemStyle: { color: mergedConfig.colors?.secondQuadrant || 'transparent' },
              label: {
                position: 'insideTopLeft',
                color: '#c2c2c2',
                fontSize: 14,
              },
            }, {
              xAxis: axisConfig.xSplit,
              y: 'max',
            }],
            [{
              name: mergedConfig.name?.fourthQuadrant,
              xAxis: axisConfig.xSplit,
              y: 'min',
              itemStyle: { color: mergedConfig.colors?.fourthQuadrant || 'transparent' },
              label: {
                position: 'insideBottomRight',
                color: '#c2c2c2',
                fontSize: 14,
              },
            }, {
              x: 'max',
              yAxis: axisConfig.ySplit,
            }],
            [{
              name: mergedConfig.name?.thirdQuadrant,
              x: 'min',
              y: 'min',
              itemStyle: { color: mergedConfig.colors?.thirdQuadrant || 'transparent' },
              label: {
                position: 'insideBottomLeft',
                color: '#c2c2c2',
                fontSize: 14,
              },
            }, {
              xAxis: axisConfig.xSplit,
              yAxis: axisConfig.ySplit,
            }],
          ],
        },
      }],
      grid: {
        ...mergedConfig.grid,
      },
      backgroundColor: 'rgba(243, 244, 246, 1)',
    }
    chartInstance.current.setOption(option)
    onChartReady?.(chartInstance.current)

    // 添加调试信息
    console.log('Chart instance created:', !!chartInstance.current)
    console.log('onPointClick function exists:', !!onPointClick)

    if (onPointClick) {
      // 先移除可能存在的事件监听器
      chartInstance.current.off('dblclick')
      chartInstance.current.off('click')

      // 使用单击模拟双击事件
      let clickTimeout: NodeJS.Timeout | null = null
      let clickCount = 0

      chartInstance.current.on('click', (params: any) => {
        console.log('Single click triggered!', params)
        clickCount++

        if (clickTimeout) {
          clearTimeout(clickTimeout)
          clickTimeout = null
        }

        if (clickCount === 1) {
          // 第一次点击，等待300ms看是否有第二次点击
          clickTimeout = setTimeout(() => {
            console.log('Single click confirmed')
            clickCount = 0
            clickTimeout = null
          }, 300)
        }
        else if (clickCount === 2) {
          // 第二次点击，触发双击事件
          console.log('Double click detected!', params)
          if (params && params.data) {
            onPointClick(params.data)
          }
          clickCount = 0
          clickTimeout = null
        }
      })

      console.log('Click event listener attached with double-click simulation')
    }

    const handleResize = () => chartInstance.current?.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        // 移除所有事件监听器
        chartInstance.current.off('dblclick')
        chartInstance.current.off('click')
        chartInstance.current.dispose()
      }
    }
  }, [data, config, height, onPointClick, onChartReady, isMobile, isFullscreen])
  useEffect(() => {
    setTimeout(() => {
      chartInstance.current?.resize()
    }, 60)
  }, [])
  // 检测设备方向
  useEffect(() => {
    const checkOrientation = () => {
      // 优先使用 Screen Orientation API
      if (window.screen?.orientation?.angle !== undefined) {
        setRotateDirection(window.screen.orientation.angle === 90 ? 'left' : 'right')
      }
      // 回退到 window.orientation
      else if (window.orientation !== undefined) {
        setRotateDirection(window.orientation === 90 ? 'right' : 'left')
      }
      // 如果都不支持，使用视口尺寸判断
      else {
        const isLandscape = window.innerWidth > window.innerHeight
        setRotateDirection(isLandscape ? 'left' : 'right')
      }
    }

    checkOrientation()
    window.addEventListener('orientationchange', checkOrientation)
    // 监听 Screen Orientation API 的变化
    if (window.screen?.orientation) {
      window.screen.orientation.addEventListener('change', checkOrientation)
    }
    return () => {
      window.removeEventListener('orientationchange', checkOrientation)
      if (window.screen?.orientation) {
        window.screen.orientation.removeEventListener('change', checkOrientation)
      }
    }
  }, [])
  useEffect(() => {
    if (isFullscreen) {
      // 禁止页面滚动
      document.body.style.overflow = 'hidden'
    }
    else {
      // 恢复页面滚动
      document.body.style.overflow = ''
    }

    return () => {
      // 组件卸载时恢复滚动
      document.body.style.overflow = ''
    }
  }, [isFullscreen])

  return (
    <div
      className={isFullscreen ? 'fixed inset-0 w-screen h-screen z-9999 bg-white' : ''}
      style={isFullscreen
        ? {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            width: '100dvw',
            height: '100dvh',
            overflow: 'hidden',
          }
        : {}}
    >
      <div
        className={isFullscreen ? `absolute inset-0 ${rotateDirection === 'left' ? 'rotate-90' : 'rotate-[-90]'} origin-center` : ''}
        style={isFullscreen
          ? {
              width: '100dvh',
              height: '100dvw',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%) rotate(90deg)',
            }
          : { }}
      >
        <div
          ref={chartRef}
          className={styles.processChart}
          style={{
            height: isFullscreen ? '100%' : height,
            backgroundColor: '#fff',
          }}
        />
      </div>
    </div>
  )
}

export default ClusteringProcess
