import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { RoutePath } from '@elegant-router/types'
import ListFrame from '@/components/listFrame'
import { useRouterPush } from '@/hooks/routerPush'
import { useMobileScreen } from '@/utils'
import { RightOutlined, SearchOutlined } from '@ant-design/icons'
import { Affix, Col, Input, Row, Select, Tabs, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useMemo, useState } from 'react'
import styles from './index.module.scss'

interface FunctionItem {
  title: string
  key: string
  path?: RoutePath
  link?: string
  category: string
  query?: Record<string, string> // 新增 query 属性
}

interface FunctionBoxProps {
  dateRange?: DateRange
}

const { Title } = Typography
const { Search } = Input

function FunctionBox(props: FunctionBoxProps) {
  const { dateRange } = props
  const { routerPushByKey } = useRouterPush()
  const [activeTab, setActiveTab] = useState('all')
  const [searchValue, setSearchValue] = useState('')

  function handleClick(item: FunctionItem) {
    if (item.link) {
      window.open(item.link, '_self')
    }
    else if (item.path) {
      // 构建基础查询参数
      const baseQuery = dateRange
        ? `start_time=${dayjs(dateRange[0]).format('YYYY-MM-DD')}&end_time=${dayjs(dateRange[1]).format('YYYY-MM-DD')}`
        : ''
      
      // 合并自定义查询参数
      const customQuery = item.query 
        ? Object.entries(item.query)
            .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
            .join('&')
        : ''
      
      // 组合所有查询参数
      const queryString = [baseQuery, customQuery]
        .filter(Boolean)
        .join('&')
      const finalPath = queryString ? `${item.path}?${queryString}` : item.path
      routerPushByKey(finalPath)
    }
  }

  const ALL_FUNCTIONS: FunctionItem[] = [
    // 数据分析
    { title: '产品分析', key: 'product-analysis', path: '/product/analysis' as RoutePath, category: 'analysis' },
    { title: '客户分析', key: 'customer-analysis', path: '/customer/analysis' as RoutePath, category: 'analysis' },

    // 销售管理
    { title: '新增销售单', key: 'add-sale-order', path: '/finish-product-sale-order-add' as RoutePath, category: 'sales' },
    { title: '成品销售单', key: 'sale-order', path: '/finish-product-sale-order' as RoutePath, category: 'sales' },
    { title: '客户对账单', key: 'customer-debit', path: '/customer-debit' as RoutePath, category: 'sales' },
    { title: '客户画像', key: 'customer-profile', path: '/customer/infos' as RoutePath,
      query: {
        customer_id: '0',
      },
      category: 'sales' },

    // 员工管理
    { title: '线下拜访', key: 'customer-visit', path: '/customer-visit' as RoutePath, category: 'employee' },

    // 其他功能
    { title: '电子商城', key: 'e-commerce', path: '/color-card-iframe', category: 'other' },
  ]

  const filteredFunctions = useMemo(() => {
    let functions = ALL_FUNCTIONS

    // 按tab筛选
    if (activeTab !== 'all') {
      functions = functions.filter(item => item.category === activeTab)
    }

    // 按选择的功能筛选
    if (searchValue) {
      functions = functions.filter(item => item.title === searchValue)
    }

    return functions
  }, [activeTab, searchValue])

  const tabItems = [
    { key: 'all', label: '全部应用' },
    { key: 'analysis', label: '数据分析' },
    { key: 'sales', label: '销售管理' },
    { key: 'employee', label: '员工管理' },
    { key: 'other', label: '其他功能' },
  ]
  // 按分类分组功能
  const groupedFunctions = useMemo(() => {
    const groups: { [key: string]: { title: string, items: FunctionItem[] } } = {}

    filteredFunctions.forEach((item) => {
      const categoryMap = {
        analysis: '数据分析',
        sales: '销售管理',
        employee: '员工管理',
        other: '其他功能',
      }

      const categoryTitle = categoryMap[item.category as keyof typeof categoryMap] || '其他功能'

      if (!groups[item.category]) {
        groups[item.category] = {
          title: categoryTitle,
          items: [],
        }
      }
      groups[item.category].items.push(item)
    })

    return Object.values(groups)
  }, [filteredFunctions])
  const isMobile = useMobileScreen()
  // eslint-disable-next-line react/no-nested-components
  function SearchBar() {
    return (
      <Select
        placeholder="选择应用功能"
        value={searchValue || undefined}
        onChange={value => setSearchValue(value || '')}
        style={{ width: isMobile ? '100%' : 300 }}
        allowClear
        showSearch
        filterOption={(input, option) =>
          (option?.label ?? '').toLowerCase().includes(input.toLowerCase())}
        options={[
          { value: '', label: '全部功能' },
          ...ALL_FUNCTIONS.map(item => ({
            value: item.title,
            label: item.title,
          })),
        ]}
      />
    )
  }
  return (
    <ListFrame className={styles.functionBox}>
      <Affix>
        {/* 顶部Tab导航 */}
        <div className={styles.tabHeader}>
          <Tabs
            activeKey={activeTab}
            onChange={setActiveTab}
            items={tabItems}
            className={styles.tabs}
            tabBarExtraContent={!isMobile
              ? (
                  <div className={styles.searchContainer}>
                    <SearchOutlined />
                    <SearchBar />
                  </div>
                )
              : null}
          />

          {
            isMobile
              ? <div className={styles.searchContainer}><SearchBar /></div>
              : null
          }
        </div>
      </Affix>

      {/* 功能分组显示 */}
      <Row gutter={[16, 16]} className={styles.functionGrid}>
        {groupedFunctions.map(group => (
          <Col key={group.title} xs={24} sm={12} md={8} lg={6} xl={4} className={styles.categorySection}>
            {/* 分类标题 */}
            <div className={styles.categoryTitle}>
              <div className={styles.categoryLine}></div>
              <span className={styles.categoryText}>{group.title}</span>
            </div>

            {/* 该分类下的功能卡片 */}
            <Row gutter={[16, 16]} className={styles.categoryCards}>
              {group.items.map(item => (
                <Col key={item.key} span={24}>
                  <div
                    className={styles.functionCard}
                    onClick={() => handleClick(item)}
                  >
                    <div className={styles.cardContent}>
                      <div className={styles.cardTitle}>{item.title}</div>
                      <RightOutlined className={styles.cardArrow} />
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Col>
        ))}
      </Row>

      {/* 无搜索结果提示 */}
      {filteredFunctions.length === 0 && (
        <div className={styles.noResults}>
          <div className={styles.noResultsText}>未找到相关功能</div>
          <div className={styles.noResultsSubtext}>请尝试其他关键词或切换分类</div>
        </div>
      )}
    </ListFrame>
  )
}

export default FunctionBox
