import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { MetricCardType } from '../constants'
import { RangeDate } from '@/components/dateComponents'
import LabelBox from '@/components/LabelBox'
import Skeleton from '@/components/skeleton'
import { formatValue } from '@/utils/math'
import { getLastPeriodRange } from '@/utils/times'
import { ArrowDownOutlined, ArrowUpOutlined, BulbFilled } from '@ant-design/icons'
import { Card, Flex, message, Typography } from 'antd'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import React, { useEffect, useState } from 'react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { METRIC_CARDS } from '../constants'
import { FetchGetWorkbenchData } from '../index.config'
import styles from './index.module.scss'
import 'swiper/css'

const { Title, Paragraph } = Typography

function MetricCard({ constKey, data, loading }: {
  constKey: MetricCardType // 指标key
  data?: Api.Dashboard.WorkbenchDataResponse // 数据
  loading: boolean // 是否加载中
}) {
  if (loading) {
    return (
      <Card bordered={false} className={styles.metricCard}>
        <Flex vertical justify="space-between">
          <div className={styles.cardTitle}>{constKey.title}</div>
          <div className={styles.value}>
            <Skeleton width={140} height={20} />
          </div>
          <div className={styles.footer}>
            <div className={styles.trend}>
              <ArrowUpOutlined className={styles.up} />
              <div className={styles.dailyAvg}>
                <span className={styles.label}>较上期</span>
                <Skeleton width={40} height={16} className={styles.trendValue} />
              </div>
            </div>
            <div className={styles.dailyAvg}>
              <span className={styles.label}>日均</span>
              <Skeleton width={40} height={16} className={styles.avgValue} />
            </div>
          </div>
        </Flex>
      </Card>
    )
  }

  if (!data) {
    return (
      <Card bordered={false} className={styles.metricCard}>
        <Flex vertical justify="space-between">
          <div className={styles.cardTitle}>{constKey.title}</div>
          <div className={styles.value}>--</div>
          <div className={styles.footer}>
            <div className={styles.trend}>
              <span className={styles.label}>较上期</span>
              <span>--</span>
            </div>
            <div className={styles.dailyAvg}>
              <span className={styles.label}>日均</span>
              <span>--</span>
            </div>
          </div>
        </Flex>
      </Card>
    )
  }

  return (
    <Card bordered={false} className={styles.metricCard}>
      <Flex vertical justify="space-between">
        <div className={styles.cardTitle}>{constKey.title}</div>
        <Paragraph className={styles.value} ellipsis={{ rows: 1, tooltip: true }}>{formatValue(data[constKey.key], '--')}</Paragraph>
        <div className={styles.footer}>
          <div className={styles.trend}>
            {(() => {
              if (!constKey?.lastIssue) {
                return <div className="opacity-0">占位符</div>
              }
              const value = Number(data[constKey.lastIssue])
              const displayValue = Number.isNaN(value) ? 0 : value

              return (
                <>
                  {displayValue > 0
                    ? (
                        <ArrowUpOutlined className={styles.up} />
                      )
                    : displayValue < 0
                      ? (
                          <ArrowDownOutlined className={styles.down} />
                        )
                      : (
                          <BulbFilled className={styles.equal} />
                        )}
                  <span className={styles.label}>较上期</span>
                  <span>
                    {Number.isNaN(value) ? '--' : Math.abs(displayValue)}
                    %
                  </span>
                </>
              )
            })()}
          </div>
          {constKey?.dailyAvg
            ? (
                <div className={styles.dailyAvg}>
                  <span className={styles.label}>日均</span>
                  {formatValue(data[constKey.dailyAvg], '--')}
                </div>
              )
            : <div>{' '}</div>}
        </div>
      </Flex>
    </Card>
  )
}
interface DataOverviewProps {
  onDateRangeChange?: (dateRange: DateRange) => void
}
function DataOverview(props: DataOverviewProps) {
  const { onDateRangeChange } = props
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const [metricsData, setMetricsData] = useState<Api.Dashboard.WorkbenchDataResponse>() // 请求回来的数据载体
  const [dateRange, setDateRange] = useState<DateRange>([new Date(), new Date()]) // 日期范围
  const { mutateAsync: fetchWorkbenchData, isPending } = FetchGetWorkbenchData() // 请求数据的hook
  const startTimeString = dayjs(dateRange[0]).format('YYYY-MM-DD')
  const endTimeString = dayjs(dateRange[1]).format('YYYY-MM-DD')
  console.log('time', endTimeString, startTimeString)
  let TEMP_METRIC_CARDS = cloneDeep(METRIC_CARDS)
  if (startTimeString === endTimeString) {
    TEMP_METRIC_CARDS.forEach((card) => {
      delete card.dailyAvg
    })
  }
  else {
    TEMP_METRIC_CARDS = cloneDeep(METRIC_CARDS)
  }
  useEffect(() => {
    const fetchData = async () => {
      try {
        const { lastStartTime, lastEndTime } = getLastPeriodRange(dateRange[0], dateRange[1])

        const res = await fetchWorkbenchData({
          start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
          end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
          last_start_time: lastStartTime,
          last_end_time: lastEndTime,
        })
        setMetricsData(res)
      }
      catch (error) {
        // setMetricsData({
        //   sale_amount: '', // 销售金额
        //   sale_amount_last_rate: '', // 销售金额较上期
        //   sale_amount_daily_avg: '', // 销售金额日均
        //   roll: '', // 匹数
        //   roll_last_rate: '', // 匹数较上期
        //   roll_daily_avg: '', // 匹数日均
        //   big_customer_count: '', // 大货客户数
        //   big_customer_last_rate: '', // 大货客户数较上期
        //   big_customer_daily_avg: '', // 大货客户数日均
        //   big_order_weight: '', // 大货数量(带单位)
        //   big_order_last_rate: '', // 大货数量较上期
        //   big_order_daily_avg: '', // 大货数量日均
        //   plate_order_weight: '', // 剪板数量(带单位)
        //   plate_order_last_rate: '', // 剪板数量较上期
        //   plate_order_daily_avg: '', // 剪板数量日均
        //   small_customer_count: '', // 小样客户数
        //   small_customer_last_rate: '', // 小样客户数较上期
        //   small_customer_daily_avg: '', // 小样客户数日均
        //   total_count: '', // 总客户数
        //   total_count_last_rate: '', // 总客户数较上期
        //   total_count_daily_avg: '', // 总客户数日均
        //   total_debt: '', // 总欠款
        //   advance_collect_amount: '', // 预收金额
        //   actually_collect_amount: '', // 实收金额
        // }) // 设置为undefined
        console.error('获取数据失败:', error) // 打印错误
        messageApi.open({
          type: 'error',
          content: '发生意外，请稍后再试',
          key: 'data-overview',
        })
      }
    }

    fetchData()
  }, [dateRange, fetchWorkbenchData, messageApi])

  return (
    <>
      {contextHolder}
      <div className={styles.overview}>
        <Flex justify="space-between" align="center" className={styles.header}>
          <Title level={5} className={styles.title}>数据总览</Title>
          <LabelBox label={undefined} width={260}>
            <RangeDate
              value={dateRange}
              onChange={(newDateRange) => {
                setDateRange(newDateRange)
                onDateRangeChange?.(newDateRange)
              }}
              onConfirm={(newDateRange) => {
                setDateRange(newDateRange)
                onDateRangeChange?.(newDateRange)
              }}
              loading={isPending}
              format="YYYY-MM-DD"
              // minDate={new Date('2010-01-01')}
              maxDate={new Date(dayjs().format('YYYY-MM-DD 23:59:59'))}
              allowClear
            />
          </LabelBox>
        </Flex>

        <div className={styles.cardsContainer}>
          <Swiper slidesPerView="auto" spaceBetween={16} className={styles.swiper}>
            {TEMP_METRIC_CARDS.map(card => (
              <SwiperSlide key={card.key} className={styles.slide}>
                <MetricCard
                  constKey={card}
                  data={metricsData}
                  loading={isPending}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </div>
      </div>
    </>
  )
}

export default DataOverview
