import { BarChartOutlined, CaretRightOutlined, CloudDownloadOutlined, ShareAltOutlined, UserOutlined } from '@ant-design/icons'
import { Button, Divider, Select } from 'antd'
import { useState } from 'react'
import styles from './AIMulDimAnalysis.module.scss'

// 分析维度选项
const dimensionOptions = [
  { value: 'salesForecast', label: '销售趋势预测' },
  { value: 'customerBehavior', label: '客户行为分析' },
  { value: 'productMix', label: '产品组合优化' },
  { value: 'inventory', label: '库存周转分析' },
  { value: 'seasonal', label: '季节性影响评估' },
]

// 时间范围选项
const timeRangeOptions = [
  { value: '3months', label: '最近3个月' },
  { value: '6months', label: '最近6个月' },
  { value: '12months', label: '最近12个月' },
]
interface AnalysisResult {
  id: number
  title: string
  description: string
  actions: {
    type: 'link' | 'primary' | ''
    name: string
    icon: any
  }[]
}
export function AIMulDimAnalysis() {
  const [dimension, setDimension] = useState(dimensionOptions[0].value)
  const [timeRange, setTimeRange] = useState(timeRangeOptions[0].value)
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult[]>()

  const handleAnalysis = () => {
    // 模拟分析结果
    setAnalysisResult([
      {
        id: 1,
        title: '1. 销售趋势预测',
        description: '基于历史数据和多个因素的分析，预计下季度销售额将增长12-15%，其中华东区域增长潜力最大，预计可达18%。',
        actions: [
          { type: 'primary', name: '查看详细预测', icon: <BarChartOutlined /> },
          { type: '', name: '设置增长目标', icon: <UserOutlined /> },
        ],
      },
      {
        id: 2,
        title: '2. 产品组合建议',
        description: '建议红外测温仪的组合比例由现在的40%提高到45%，同时考虑增加出新款产品的库存比例。',
        actions: [
          { type: 'primary', name: '调整库存', icon: <CloudDownloadOutlined /> },
          { type: '', name: '新品开发', icon: <ShareAltOutlined /> },
        ],
      },
      {
        id: 3,
        title: '3. 客户行为洞察',
        description: '老客户复购率呈现上升趋势，建议对老客户推出专属优惠活动，提升客户粘性。',
        actions: [
          { type: 'primary', name: '查看客户画像', icon: <UserOutlined /> },
          { type: '', name: '设置增长目标', icon: <BarChartOutlined /> },
        ],
      },
    ])
  }

  return (
    <div className={styles.ai}>
      <h2 className={styles.title}>AI 多维分析</h2>

      <div className={styles.filters}>
        <div className={styles.filterItem}>
          <label>分析维度</label>
          <Select
            value={dimension}
            onChange={setDimension}
            options={dimensionOptions}
            style={{ width: '100%' }}
          />
        </div>

        <div className={styles.filterItem}>
          <label>时间范围</label>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            options={timeRangeOptions}
            style={{ width: '100%' }}
          />
        </div>

        <Button type="primary" onClick={handleAnalysis} icon={<CaretRightOutlined />} className={styles.analyzeBtn}>
          开始分析
        </Button>
      </div>
      {analysisResult && <Divider></Divider>}
      {analysisResult && (
        <div className={styles.result}>

          <div className={styles.resultHeader}>
            <h3>分析结果</h3>
            <div className={styles.actions}>
              <Button type="link">历史分析记录</Button>
              <Button icon={<CloudDownloadOutlined />}>导出</Button>
              <Button icon={<ShareAltOutlined />}>分享</Button>
            </div>
          </div>

          <div className={styles.resultContent}>
            {analysisResult.map(result => (
              <div key={result.id} className={styles.resultContentItem}>
                <h4>{result.title}</h4>
                <p>{result.description}</p>

                <div className={styles.actionButtons}>
                  {result.actions.map((action, index) => (
                    <Button
                      key={index}
                      type={action.type}
                      icon={action.icon}
                    >
                      {action.name}
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
