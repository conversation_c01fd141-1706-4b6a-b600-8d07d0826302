import type { MutationOptions } from '@/service/request'
// import type { PaginationResponse } from '@/service/request/type' // 分页需要
import { useCustomMutation } from '@/service/request'

// 获取产品分析的数据  http://192.168.1.28:50001/hcscm/admin/v1/analysis/productAnalysis/home?type=1
export function FetchGetCustomerAnalysisData(options?: MutationOptions<Api.CustomerAnalysis.CustomerAnalysisDataResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>) {
  return useCustomMutation<Api.CustomerAnalysis.CustomerAnalysisDataResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/productAnalysis/home',
      method: 'GET',
    },
    options,
  )
}

// 获取客户详情 http://192.168.1.28:50001/hcscm/admin/v1/analysis/customerAnalysis/customer
export function FetchGetCustomerInfo(options?: MutationOptions<Api.CustomerAnalysis.CustomerAnalysisDataResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>) {
  return useCustomMutation<Api.CustomerAnalysis.CustomerAnalysisDataResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/customerAnalysis/customer',
      method: 'GET',
    },
    options,
  )
}

// 获取客户+产品详情 http://192.168.1.28:50001/hcscm/admin/v1/analysis/customerAnalysis/product
export function FetchGetCustomerProductInfo(options?: MutationOptions<Api.CustomerAnalysis.CustomerProductDetailResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>) {
  return useCustomMutation<Api.CustomerAnalysis.CustomerProductDetailResponse, Api.CustomerAnalysis.CustomerAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/customerAnalysis/product',
      method: 'GET',
    },
    options,
  )
}
