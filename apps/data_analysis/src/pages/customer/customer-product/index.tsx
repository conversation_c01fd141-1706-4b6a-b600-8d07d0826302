import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { CardData } from '@/components/swiperCard'
import WeightSvg from '@/assets/I-2.svg?react'
import RollSvg from '@/assets/I-3.svg?react'
import PriceSvg from '@/assets/I-4.svg?react'
import AIAnalysis from '@/components/AIAnalysis'
import { RangeDate } from '@/components/dateComponents'
import { SmallLineChart } from '@/components/echartsComponents'
import HeaderBox from '@/components/headerBox'
import LabelBox from '@/components/LabelBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { Selects } from '@/components/selectComponents'
import SwiperCard from '@/components/swiperCard'
import { FetchCustomerList, FetchEmployeeList, FetchGetTopColorData, FetchProductList, FetchSaleSystemList } from '@/service/api'
import { EmployeeType } from '@/service/request/type'
import { useMobileScreen } from '@/utils'
import { formatDate, formatHashTag } from '@ly/utils'
import { Affix, Checkbox, Col, Flex, message, Row, Table, Typography } from 'antd'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import TrendBox, { DATE_CONFIG, getAnalysisDateRange } from '../components/TrendBox'
import { FetchGetCustomerProductInfo } from '../index.config'
import styles from './index.module.scss'

const { Title } = Typography

const cards: CardData[] = [
  {
    id: 1,
    title: '总匹数',
    key: 'total_roll',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><RollSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 2,
    title: '总数量',
    key: 'total_settle_weight',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><WeightSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 3,
    title: '总销售金额',
    key: 'total_sale_amount',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><PriceSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
] as const
interface CheckboxState {
  big: boolean
  plate: boolean
}
// 添加获取 sale_mode_iden 的工具函数
function getSaleModeIden(states: CheckboxState): number | undefined {
  // 使用对象映射简化逻辑
  const modeMap = {
    'true,false': 1, // 只勾选大货
    'false,true': 2, // 只勾选剪板
    'true,true': undefined, // 都勾选
    'false,false': undefined, // 都不勾选
  }

  const key = `${states.big},${states.plate}`
  return modeMap[key as keyof typeof modeMap]
}
export function Component() {
  const isMobile = useMobileScreen()
  const [searchParams] = useSearchParams()
  const [saleSystem, setSaleSystem] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_system_id')) || 0,
    label: searchParams.get('sale_system_name') !== 'undefined' ? searchParams.get('sale_system_name') : '',
  }) // 部门
  const [saleUser, setSaleUser] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_user_id')) || 0,
    label: searchParams.get('sale_user_name') !== 'undefined' ? searchParams.get('sale_user_name') : '',
  }) // 销售员
  const productNameParams = decodeURIComponent(searchParams.get('product_name') || '') // 产品名称
  const customerNameParams = decodeURIComponent(searchParams.get('customer_name') || '') // 产品名称
  // const productCodeParams = decodeURIComponent(searchParams.get('product_code') || '') // 产品名称
  const [customer, setCustomer] = useState<{
    value: string
    label: string
  }>({
    value: Number(searchParams.get('customer_id')),
    label: customerNameParams !== 'undefined' ? customerNameParams : '',
  }) // 客户id
  const [product, setProduct] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('product_id')),
    label: productNameParams !== 'undefined' ? productNameParams : '',
  }) // 产品id
  const start_time = searchParams.get('start_time') // 上一个路由传入的开始时间
  const end_time = searchParams.get('end_time') // 上一个路由传入的结束时间
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const navigate = useNavigate() // 路由跳转
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)
  const [colorData, setColorData] = useState<Api.GetTopColorData.GetTopColorResForTop[]>([])
  const { mutateAsync: fetchTopColor } = FetchGetTopColorData()
  const [dateRange, setDateRange] = useState<DateRange>([
    new Date(start_time || new Date()),
    new Date(end_time || new Date()),
  ]) // 日期范围
  const [checkboxStates, setCheckboxStates] = useState({
    big: true, // 大货
    plate: true, // 剪板
  })
  const states = checkboxStates
  const sale_mode_iden = getSaleModeIden(states)
  const [dateType, setDateType] = useState(DATE_CONFIG.DAILY_ANALYSIS.TYPE)
  // 获取趋势分析的日期范围
  const trendDateRange = useMemo(() => {
    if (!dateRange)
      return { trend_start_time: '', trend_end_time: '' }
    const [start, end] = getAnalysisDateRange(dateType, dateRange)
    return {
      trend_start_time: start,
      trend_end_time: end,
    }
  }, [dateRange, dateType])

  function handleChangeType(dateType: 1 | 2) {
    setDateType(dateType)
  }
  // 修改数据获取方法
  const fetchColorData = useCallback(async (currentPage: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return
    // 使用传入的 currentCheckboxStates 或当前的 checkboxStates
    const states = currentCheckboxStates || checkboxStates

    // 根据复选框状态确定 sale_mode_iden
    let sale_mode_iden: number | undefined
    if (states.big && !states.plate) {
      sale_mode_iden = 1 // 只勾选大货
    }
    else if (!states.big && states.plate) {
      sale_mode_iden = 2 // 只勾选剪板
    }
    setLoading(true)
    try {
      const res = await fetchTopColor({
        page: currentPage,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        customer_id: customer.value,
        product_id: product.value,
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
      })

      if (currentPage === 1) {
        setColorData(res.top_data_list || [])
      }
      else {
        setColorData(prev => [...prev, ...(res.top_data_list || [])])
      }

      setHasMore((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取颜色排行数据失败:', error)
      messageApi.error('获取数据失败')
    }
    finally {
      setLoading(false)
    }
  }, [dateRange, customer.value, product.value, saleSystem.value, saleUser.value, fetchTopColor, messageApi])

  const customBreadcrumbs = [
    {
      title: '工作台',
      onClick: () => navigate('/'),
    },
    {
      title: '客户分析',
      onClick: () => {
        const params = {
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        navigate(`/customer/analysis?${queryString}`)
      },
    },
    {
      title: '客户详情',
      onClick: () => {
        const params = {
          customer_id: customer.value,
          customer_name: encodeURIComponent(customer.label || ''),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        navigate(`/customer/infos?${queryString}`)
      },
    },
    {
      title: '产品详情',
      onClick: () => {},
    },
  ]

  // 添加加载更多处理函数
  const handleLoadMore = useCallback(() => {
    if (loading || !hasMore)
      return
    setPage(prev => prev + 1)
    fetchColorData(page + 1)
  }, [loading, hasMore, page, fetchColorData])

  // 修改列定义
  const columns = useMemo(() => [
    {
      title: '颜色名称',
      dataIndex: 'product_color_name',
      key: 'product_color_name',
      width: 120,
      fixed: 'left',
      render: (_: string, record: Api.GetTopColorData.GetTopColorResForTop) => {
        const params = {
          customer_id: customer.value,
          customer_name: encodeURIComponent(customer.label || ''),
          product_id: product.value,
          product_name: encodeURIComponent(product.label || ''),
          color_id: record.product_color_id,
          color_name: encodeURIComponent(record.product_color_name || ''),
          sale_system_id: saleSystem.value,
          sale_system_name: saleSystem.label,
          sale_user_id: saleUser.value,
          sale_user_name: saleUser.label,
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return (
          <Links to={`/product/color-infos?${queryString}`}>
            {formatHashTag(record.product_color_code, record.product_color_name)}
          </Links>
        )
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      key: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [isMobile, customer, product, saleSystem, saleUser, dateRange])
  const { mutateAsync: fetchProductAnalysis, isPending } = FetchGetCustomerProductInfo() // 获取产品分析数据
  const [productAnalysisData, setProductAnalysisData] = useState<Api.CustomerAnalysis.CustomerProductDetailResponse>() // 产品分析数据

  // 添加处理函数
  const handleCheckboxChange = (type: 'big' | 'plate') => {
    const newState = {
      ...checkboxStates,
      [type]: !checkboxStates[type],
    }

    // 确保至少有一个选中
    if (newState.big || newState.plate) {
      setCheckboxStates(newState)
      setPage(1) // 重置页码
      fetchColorData(1, newState) // 传入新的状态
    }
  }
  const fetchData = useCallback(async (params: Api.CustomerAnalysis.CustomerAnalysisDataRequest) => {
    try {
      const res = await fetchProductAnalysis(params)
      setProductAnalysisData(res as any)
    }
    catch (error) {
      console.error('获取数据失败:', error)
      messageApi.error('发生意外，请稍后再试')
    }
  }, [fetchProductAnalysis, messageApi])

  useEffect(() => { // 获取数据
    if (!dateRange)
      return
    fetchData({
      start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      customer_id: customer.value, // 路由地址上的customer_id
      product_id: product.value, // 路由地址上的product_id
      sale_system_id: saleSystem.value,
      sale_user_id: saleUser.value,
    })
  }, [customer.value, dateRange, fetchData, product.value, saleSystem, saleUser])
  useEffect(() => {
    if (!dateRange)
      return
    setPage(1)
    fetchColorData(1)
  }, [customer.value, product.value, dateRange, saleSystem.value, saleUser.value])
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const [width, setWidth] = useState(0)
  useEffect(() => {
    setTimeout(() => {
      setWidth(tableContainer.current?.offsetWidth || 0)
    }, 0)
  }, [isMobile])
  return (
    <ListFrame
      header={(
        <HeaderBox
          showUserInfo
          breadcrumbs={customBreadcrumbs}
          rightSlot={(
            <AIAnalysis
              fixed={false}
              url="/h5/v1/ai/analysis/productDetailCusAnalysisData"
              outputParams={{
                type: 1,
                start_time: formatDate(dateRange![0].toDateString()),
                end_time: formatDate(dateRange![1].toDateString()),
                sale_mode_iden,
                product_id: product.value,
                customer_id: `${customer.value}`,
                sale_system_id: saleSystem.value, // 部门id
                sale_user_id: saleUser.value, // 销售员id
                trend_start_time: trendDateRange.trend_start_time,
                trend_end_time: trendDateRange.trend_end_time,
              } as Api.ProductDetailAi.Request}
              type="output"
              text="AI报表分析"
            />
          )}
        />
      )}
      className={styles.page}
    >
      {contextHolder}
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.leftContent}>
            <div className={styles.overviewCard}>
              <Row gutter={[10, 16]} className={styles.headerBox}>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="客户" fullWidth>
                    <Selects
                      fetchApi={FetchCustomerList}
                      disabled={isPending}
                      placeholder="选择客户"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={customer}
                      onChange={(value: any, _option: any) => {
                        setCustomer({ value, label: _option?.name })
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="产品名称" fullWidth>
                    <Selects
                      defaultValue={productNameParams}
                      fetchApi={FetchProductList}
                      disabled={isPending}
                      placeholder="选择产品"
                      renderOption={(option: any) => {
                        return (
                          <div className="flex items-center">
                            <div>{formatHashTag(option.data.finish_product_code, option.label)}</div>
                          </div>
                        )
                      }}
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'finish_product_name', value: 'id' }}
                      isSearch
                      keywordFile="finish_product_code_or_name"
                      value={product}
                      onChange={(value: any, _option: any) => {
                        setProduct({ value, label: _option?.finish_product_name || '' })
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="日&nbsp;&nbsp;&nbsp;&nbsp;期" fullWidth>
                    <RangeDate
                      value={dateRange}
                      onChange={setDateRange}
                      loading={false}
                      format="YYYY-MM-DD"
                      // minDate={new Date('2010-01-01')}
                      // maxDate={new Date()}
                      allowClear={false}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="部&nbsp;&nbsp;&nbsp;&nbsp;门" fullWidth>
                    <Selects
                      fetchApi={FetchSaleSystemList}
                      disabled={isPending}
                      placeholder="选择部门"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleSystem}
                      onChange={(value: any, _option: any) => {
                        setSaleSystem({ value, label: _option?.name || '' })
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="销售员" fullWidth>
                    <Selects
                      fetchApi={FetchEmployeeList}
                      disabled={isPending}
                      placeholder="选择销售员"
                      query={{ duty: EmployeeType.salesman }}
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleUser}
                      onChange={(value: any, _option: any) => {
                        setSaleUser({ value, label: _option?.name || '' })
                      }}
                    />
                  </LabelBox>
                </Col>
              </Row>
              <SwiperCard cards={cards} data={productAnalysisData} loading={isPending} />

            </div>
            <div className={styles.tableCard}>
              <Flex justify="space-between" align="center" style={{ marginBottom: 10 }}>
                <Title level={4}>颜色排行</Title>
                <Flex>
                  <Checkbox
                    checked={checkboxStates.big}
                    onChange={() => handleCheckboxChange('big')}
                  >
                    大货
                  </Checkbox>
                  <Checkbox
                    checked={checkboxStates.plate}
                    onChange={() => handleCheckboxChange('plate')}
                  >
                    剪板
                  </Checkbox>
                </Flex>
              </Flex>
              {/* 表格内容 */}
              <div ref={tableContainer} className="flex-1">
                <Table
                  columns={columns}
                  rootClassName={styles.tableContainer}
                  dataSource={colorData}
                  loading={loading}
                  scroll={{ x: width, y: isMobile ? 300 : (tableContainer.current?.clientHeight) - 40 }}
                  pagination={false}
                  rowKey="product_color_id"
                  style={{ width, height: (tableContainer.current?.clientHeight) }}
                  size="small"
                  rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!')}
                  onRow={() => ({
                    onMouseEnter: () => {
                    // 当滚动到最后一行时加载更多数据
                      const scrollElement = document.querySelector('.ant-table-body')
                      if (!scrollElement)
                        return

                      const { scrollTop, scrollHeight, clientHeight } = scrollElement
                      if (scrollHeight - scrollTop <= clientHeight + 50 && !loading && hasMore) {
                        handleLoadMore()
                      }
                    },
                  })}
                />
              </div>
            </div>
          </div>
          <div className={styles.rightContent}>
            <TrendBox
              chartHeight="100%"
              onChangeType={handleChangeType}
              product_id={product?.value}
              dateRange={dateRange}
              saleSystemId={saleSystem.value}
              saleUserId={saleUser.value}
              customerId={customer.value}
            />
          </div>
        </div>
      </div>
    </ListFrame>
  )
}
