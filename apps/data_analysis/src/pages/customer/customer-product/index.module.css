@reference "tailwindcss";
.page {
  @apply min-h-screen bg-[#f5f6f7] flex flex-col;
  height: 100vh;
  overflow: hidden;
}
.page .container {
  @apply w-full flex-1;
  overflow-y: auto;
}
.page .container {
  contain: content;
  isolation: isolate;
  --header-height: 64px;
  --content-padding: 12px;
}
.page .container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.page .container::-webkit-scrollbar-track {
  background: transparent;
}
.page .container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
.page .container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);
}
@media (min-width: 1920px) {
  .page .container {
    @apply w-[1800px] mx-auto;
  }
}
@media (min-width: 1600px) and (max-width: 1919px) {
  .page .container {
    @apply w-[98vw] mx-auto px-5;
  }
}
@media (min-width: 1280px) and (max-width: 1599px) {
  .page .container {
    @apply w-[98vw] mx-auto px-4;
  }
}
@media (min-width: 1024px) and (max-width: 1279px) {
  .page .container {
    @apply w-[98vw] mx-auto px-4;
  }
}
@media (min-width: 768px) and (max-width: 1023px) {
  .page .container {
    @apply w-[98vw] mx-auto px-3;
  }
}
@media (max-width: 767px) {
  .page .container {
    @apply w-full px-[12px];
  }
}
.page .container .content {
  @apply flex gap-3;
  min-height: calc(100vh - var(--header-height));
  padding: var(--content-padding) 0;
}
@media (max-width: 1024px) {
  .page .container .content {
    @apply flex-col;
  }
  .page .container .content .leftContent,
  .page .container .content .rightContent {
    @apply w-full;
  }
}
.page .container .content .leftContent {
  @apply flex flex-col gap-3 flex-[2];
}
.page .container .content .leftContent .overviewCard {
  @apply bg-white rounded-lg p-3;
  min-height: 200px;
}
.page .container .content .leftContent .overviewCard .headerBox {
  @apply grid gap-4 mb-4;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.page .container .content .leftContent .tableCard {
  @apply bg-white rounded-lg p-3 flex-1;
  min-height: 500px;
}
.page .container .content .rightContent {
  @apply flex-1 bg-white rounded-lg p-3;
  min-height: 100%;
}
.page .container .content .leftContent > div,
.page .container .content .rightContent {
  opacity: 0;
  animation: slideIn 0.5s ease forwards;
  will-change: opacity, transform;
}
.page .container .content .leftContent > div:nth-child(1) {
  animation-delay: 0.1s;
}
.page .container .content .leftContent > div:nth-child(2) {
  animation-delay: 0.2s;
}
.page .container .content .rightContent {
  animation-delay: 0.3s;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
.tableContainer :global .ant-table-body {
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar-track {
  background: transparent;
}
.tableContainer :global .ant-table-body::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 4px;
}
.tableContainer :global .ant-table-body:hover {
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}
.tableContainer :global .ant-table-body:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}
.tableContainer :global .ant-table-body:hover::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}/*# sourceMappingURL=index.module.css.map */