import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { RadioChangeEvent } from 'antd'
import { <PERSON><PERSON><PERSON>, LineChart } from '@/components/echartsComponents'
import LabelBox from '@/components/LabelBox'
import { FetchGetTrendAnalysisData } from '@/service/api/trendAnalysis'
import { Bar<PERSON>hartOutlined, CalendarOutlined, InfoCircleOutlined, StockOutlined } from '@ant-design/icons'
import { getFilterData } from '@ly/utils'
import { Alert, Button, Checkbox, Col, Flex, message, Modal, Radio, Row, Space, Typography } from 'antd'
import classNames from 'classnames'
import currency from 'currency.js'
import dayjs from 'dayjs'
import { type FC, useCallback, useEffect, useMemo, useState } from 'react'
import { OldOrNew } from '../../analysis'
import styles from './index.module.scss'

const { Title, Link } = Typography

// 添加月份选择的常量
const MONTH_OPTIONS = [
  { label: '1月', value: 1 },
  { label: '2月', value: 2 },
  { label: '3月', value: 3 },
  { label: '4月', value: 4 },
  { label: '5月', value: 5 },
  { label: '6月', value: 6 },
  { label: '7月', value: 7 },
  { label: '8月', value: 8 },
  { label: '9月', value: 9 },
  { label: '10月', value: 10 },
  { label: '11月', value: 11 },
  { label: '12月', value: 12 },
]
// 提取常量配置
const DIMENSION_OPTIONS = [
  { label: '匹数', value: 1 },
  { label: '销售金额', value: 2 },
  { label: '数量', value: 3 },
]
export type DateType = 1 | 2
export const DATE_CONFIG = {
  DAILY_ANALYSIS: {
    DAYS_RANGE: 14, // 改成 14 只需要改这里
    TYPE: 1 as DateType,
  },
  MONTHLY_ANALYSIS: {
    TYPE: 2 as DateType,
  },
} as const
export function getAnalysisDateRange(type: DateType, dateRange: DateRange): [string, string] {
  if (type === DATE_CONFIG.DAILY_ANALYSIS.TYPE) {
    return [
      dayjs(dateRange?.[1]).subtract(DATE_CONFIG.DAILY_ANALYSIS.DAYS_RANGE - 1, 'day').format('YYYY-MM-DD'),
      dayjs(dateRange?.[1]).format('YYYY-MM-DD'),
    ]
  }
  return [
    dayjs(dateRange?.[0]).startOf('month').format('YYYY-MM-DD'),
    dayjs(dateRange?.[1]).endOf('month').format('YYYY-MM-DD'),
  ]
}
interface TrendBoxProps {
  product_id?: number
  color_id?: string
  dateRange: DateRange // 添加日期范围属性
  saleSystemId?: number // 添加部门ID
  saleUserId?: number // 添加销售员ID
  customerId?: string
  saleArea?: string // 销售地区
  oleOrNew?: OldOrNew
  saleGroupRatio?: string
  productKindId?: string
  chartHeight?: number | string
  onChangeType?: (type: DateType) => void
}
const TrendBox: FC<TrendBoxProps> = (props) => {
  const { product_id, chartHeight = 600, saleArea, color_id, productKindId, dateRange, saleSystemId, saleUserId, customerId, oleOrNew, saleGroupRatio } = props
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const [types, setTypes] = useState(true) // 是否显示柱状图
  const [type, setType] = useState<DateType>(2) // 分析类型 - 1 日分析 2 月分析
  const [dimension, setDimension] = useState<1 | 2 | 3>(1) // 维度 1: 匹数，2: 销售金额，3: 数量
  const { mutateAsync: fetchTrendAnalysis, isPending, data } = FetchGetTrendAnalysisData() // 获取趋势分析数据
  const [chartData, setChartData] = useState<Api.TrendAnalysis.ProductAnalysisDataResponse>() // 图表源数据
  const [currentShowData, setCurrentShowData] = useState<any>() // 当前显示数据 根据伟纬度拆分数据

  const fetchData = useCallback(async () => {
    if (!dateRange)
      return

    try {
      const analysisDateRange = getAnalysisDateRange(type, dateRange)
      const res = await fetchTrendAnalysis(getFilterData({
        start_time: analysisDateRange[0],
        end_time: analysisDateRange[1],
        type,
        product_id,
        product_color_id: color_id,
        customer_id: customerId,
        sale_system_id: saleSystemId,
        sale_user_id: saleUserId,
        location: saleArea,
        sale_group_id: saleGroupRatio,
        type_fabric_id: productKindId,
        is_show_new_customer: oleOrNew === OldOrNew.New,
        is_show_old_customer: oleOrNew === OldOrNew.Old,
      }))
      setChartData(res)
    }
    catch (error) {
      console.error(error)
      messageApi.error('发生意外，请稍后再试')
    }
  }, [dateRange, oleOrNew, customerId, productKindId, saleGroupRatio, type, product_id, saleArea, color_id, saleSystemId, saleUserId, fetchTrendAnalysis, messageApi])

  // 处理数据转换
  const getStackData = useCallback((
    data: Api.TrendAnalysis.ThreeYearRoll | Api.TrendAnalysis.ThreeYearSaleAmount | Api.TrendAnalysis.ThreeYearSettleWeight,
    dateType: 1 | 2, // 1 日分析 2 月分析
  ) => {
    if (!dateRange)
      return

    // 近四天
    console.log('dateRange', dateRange)
    if (dateType === 1) {
      const DAYS = DATE_CONFIG.DAILY_ANALYSIS.DAYS_RANGE - 1
      const startDate = dayjs(dateRange[1]).subtract(DAYS, 'day').format('YYYY-MM-DD')
      const dates = []
      // 生成14天的日期数组
      for (let i = 0; i <= DAYS; i++) {
        dates.push(dayjs(startDate).add(i, 'day').format('YYYY-MM-DD'))
      }
      const newList = dates.map((item: string, index: number) => ({
        name: dayjs(item).format('MM-DD'),
        // 添加预估值到数据中
        estimateValue: index === DAYS
          ? (
              dimension === 1
                ? data.this_day_estimate_roll
                : dimension === 2
                  ? data.this_day_estimate_sale_amount
                  : data.this_day_estimate_settle_weight
            )
          : undefined,
        series: [
          { name: '上上期', value: data.before_year?.[index] },
          { name: '上期', value: data.last_year?.[index] },
          { name: '本期', value: data.this_year?.[index] },
        ],
      }))
      setCurrentShowData(newList)
    }
    else {
      // 月分析
      const startMonth = dayjs(dateRange[0]).startOf('month')
      const endMonth = dayjs(dateRange[1]).startOf('month')
      const monthCount = endMonth.diff(startMonth, 'month')
      // 根据月份数量决定显示逻辑
      let actualStartMonth, actualMonthCount
      if (monthCount < 6) {
        // 小于6个月时，取结束日期的近6个月数据（含结束月份）
        actualStartMonth = endMonth.subtract(5, 'month')
        actualMonthCount = 5
      }
      else {
        // 超过6个月时，按用户选择的月份展示
        actualStartMonth = startMonth
        actualMonthCount = monthCount
      }
      const dates = []
      for (let i = 0; i <= actualMonthCount; i++) {
        dates.push(dayjs(actualStartMonth).add(i, 'month').format('YYYY-MM'))
      }
      const newList = dates.map((item: string, index: number) => ({
        name: dayjs(item).format('YYYY-MM'),
        estimateValue: index === dates.length - 1
          ? (
              dimension === 1
                ? data.this_month_estimate_roll
                : dimension === 2
                  ? data.this_month_estimate_sale_amount
                  : data.this_month_estimate_settle_weight
            )
          : undefined,
        series: [
          { name: '上上期', value: data.before_year[index] },
          { name: '上期', value: data.last_year[index] },
          { name: '本期', value: data.this_year[index] },
        ],
      }))
      setCurrentShowData(newList)
    }
  }, [dateRange, dimension, type])

  useEffect(() => {
    if (dateRange) {
      // console.log('触发了', [dayjs(dateRange[0]).format('YYYY-MM-DD'), dayjs(dateRange[1]).format('YYYY-MM-DD')])
      fetchData()
    }
  }, [dateRange, fetchData])

  // 处理数据转换
  useEffect(() => {
    if (!chartData)
      return

    const dataMap = {
      1: chartData.three_year_roll,
      2: chartData.three_year_sale_amount,
      3: chartData.three_year_settle_weight,
    }

    getStackData(dataMap[dimension], type)
  }, [chartData, type, dimension, getStackData])
  // 添加图例选中状态
  const [selectedSeries, setSelectedSeries] = useState<string[]>(['本期', '上期'])
  // 图表配置
  const chartConfig = useMemo(() => ({
    bar: {
      isStack: false,
      showLegend: true,
      showGrid: true,
      showYAxis: true,
      colors: ['#218aea', '#70b603', '#8080ff'],
      defaultSeries: selectedSeries, // 使用保存的图例状态
      onLegendClick: (name: string) => {
        setSelectedSeries((prev) => {
          if (prev.includes(name)) {
            return prev.filter(item => item !== name)
          }
          return [...prev, name]
        })
      },
      itemStyle: (seriesName: string, index: number) => {
        return {
          color: (params: any) => {
            const value = params.data
            const colorIndex = index % 3
            // 如果是"本期"且是最后一个数据点，使用特殊颜色
            if (seriesName === '本期' && params.dataIndex === currentShowData?.length - 1
              && dayjs(dateRange?.[1]).isSame(dayjs(), type === 1 ? 'day' : 'month')) {
              return '#f59a23'
            }
            return value >= 0
              ? ['#218aea', '#70b603', '#8080ff']?.[colorIndex]
              : (['#ff4d4f', '#f5222d', '#cf1322']?.[colorIndex] || '#ff4d4f')
          },
          borderRadius: [2, 2, 0, 0],
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' },
        formatter: (params: any[]) => {
          return params.map((param) => {
            const value = param.data
            const marker = `<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${param.color}"></span>`

            if (param.seriesName === '本期' && param.dataIndex === currentShowData.length - 1
              && dayjs(dateRange?.[1]).isSame(dayjs(), type === 1 ? 'day' : 'month')) {
              const estimateValue = currentShowData[param.dataIndex].estimateValue
              return estimateValue !== undefined
                ? `${marker}${param.seriesName} ${value} (预估: ${estimateValue})`
                : `${marker}${param.seriesName} ${value}`
            }
            return `${marker}${param.seriesName} ${value}`
          }).join('<br/>')
        },
      },
      negativeColors: ['#ff4d4f', '#f5222d', '#cf1322'],
      gridLeft: '0%',
    },
    line: {
      showGrid: true,
      smooth: true,
      showSymbol: true,
      showLegend: true,
    },
  }), [currentShowData, selectedSeries])
  function handleChangeDateType(value: DateType) {
    setType(value)
    props.onChangeType?.(value)
  }
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedMonths, setSelectedMonths] = useState<number[]>([1, 6, 7])

  // 处理设置按钮点击
  function handleSetting() {
    setIsModalOpen(true)
  }
  // 处理月份选择
  const handleMonthChange = (checkedValues: number[]) => {
    setSelectedMonths(checkedValues)
  }

  // 处理确认设置
  const handleConfirm = () => {
    if (selectedMonths.length === 0) {
      messageApi.warning('请至少选择一个月份')
      return
    }
    // TODO: 这里可以添加保存季节设置的接口调用
    setIsModalOpen(false)
  }
  return (
    <>
      {contextHolder}
      <div className={styles.trendBox}>
        <Flex justify="space-between" align="center" className="mb-4!">
          <Title level={4} style={{ margin: 0 }}>趋势分析</Title>
          <Space align="center" size={20}>
            <Button
              type={types ? 'primary' : 'text'}
              icon={<BarChartOutlined />}
              onClick={() => setTypes(true)}
            />
            <Button
              type={!types ? 'primary' : 'default'}
              icon={<StockOutlined />}
              onClick={() => setTypes(false)}
            />
          </Space>
        </Flex>

        <Row>
          <Col span={12}>
            <Radio.Group
              options={[{ label: `近${DATE_CONFIG.DAILY_ANALYSIS.DAYS_RANGE}天`, value: 1 }, { label: '月分析', value: 2 }]}
              value={type}
              onChange={(e: RadioChangeEvent) => handleChangeDateType(e.target.value)}
              optionType="button"
              buttonStyle="solid"
              className="mb-4"
            />
          </Col>
          <Col span={12}>
            <div className="flex justify-end items-center">
              <Radio.Group
                options={DIMENSION_OPTIONS}
                value={dimension}
                onChange={(e: RadioChangeEvent) => setDimension(e.target.value)}
                optionType="button"
                buttonStyle="solid"
                size="small"
              />
            </div>
          </Col>
        </Row>

        <div className={styles.contentBox}>
          {types
            ? (
                <BarChart
                  height={chartHeight}
                  data={currentShowData}
                  loading={isPending}
                  config={chartConfig.bar}
                />
              )
            : (
                <LineChart
                  height={chartHeight}
                  data={currentShowData}
                  loading={isPending}
                  config={chartConfig.bar}
                />
              )}
        </div>
        <div className="bg-[#e8f4ff] p-2 rounded flex justify-between items-center">
          <div>
            <div className="font-bold text-lg">
              {dayjs().month() + 1}
              月销售预测
            </div>
            <div className="flex">
              基于历史数据和季节性因素，
              <Link onClick={handleSetting}>
                <CalendarOutlined />
                季节设置
              </Link>
            </div>
            <div className="flex">
              <InfoCircleOutlined />
              AI预期区间：
              {currency(data?.may_predict_data?.predict_data).subtract(data?.may_predict_data?.range).value}
              ~
              {currency(data?.may_predict_data?.predict_data).add(data?.may_predict_data?.range).value}
              匹（95%置信度））
            </div>
          </div>
          <div className="text-[#1890ff] inline-flex items-center align-baseline">
            <span className="text-lg align-baseline mr-1">{data?.may_predict_data?.predict_data}</span>
            <span className="text-xs align-baseline">
              ±
              {data?.may_predict_data?.range}
            </span>
          </div>
          <Modal
            title="旺季月份设置"
            open={isModalOpen}
            onCancel={() => setIsModalOpen(false)}
            onOk={handleConfirm}
            okText="保存设置"
            cancelText="取消"
          >
            <div className="p-4">
              <div className="mb-4 text-gray-500 text-sm">选择旺季月份，系统将自动计算季节性因素</div>
              <Checkbox.Group
                options={MONTH_OPTIONS}
                value={selectedMonths}
                onChange={handleMonthChange as any}
                className="grid grid-cols-3 gap-4"
              />
            </div>
          </Modal>
        </div>

      </div>

    </>
  )
}

export default TrendBox
