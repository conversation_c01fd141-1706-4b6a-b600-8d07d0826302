@reference 'tailwindcss';
.page {
  overflow-y: auto!important;
  overflow-x: hidden!important;
  @apply bg-[#f5f6f7] flex flex-col overflow-hidden;

  .container {
    height: 100%;
    @apply w-full flex-1 p-3;

    // // 大屏（≥1920px）：固定宽度 + 最大留白
    // @media (min-width: 1920px) {
    //   @apply w-[1800px] mx-auto;
    // }

    // // 1600px - 1919px：较大留白
    // @media (min-width: 1600px) and (max-width: 1919px) {
    //   @apply w-[98vw] mx-auto px-5;
    // }

    // // 1280px - 1599px：中等留白
    // @media (min-width: 1280px) and (max-width: 1599px) {
    //   @apply w-[98vw] mx-auto px-4;
    // }

    // // 1024px - 1279px：较小留白
    // @media (min-width: 1024px) and (max-width: 1279px) {
    //   @apply w-[98vw] mx-auto px-4;
    // }

    // // 768px - 1023px：小留白
    // @media (min-width: 768px) and (max-width: 1023px) {
    //   @apply w-[98vw] mx-auto px-3;
    // }

    // 移动端（<768px）：最小留白
    @media (max-width: 767px) {
      @apply w-full px-[12px];
    }

    .content {
      height: 100%;
      @apply flex flex-col gap-3 h-full;
      @media (max-width: 768px) {
        height: unset;
      }

      @media (max-width: 480px) {
        height: unset;
      }
      .headerBox {
        @apply grid gap-4 mb-2;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

        .headerBoxItem {
          @apply w-full;
        }
      }

      
      .leftCard,
      .rightCard {
        height: 100%;
        // min-height: 480px;
        background-color: white;
        
        // iPad Pro 11" 卡片高度优化
        // @media (min-width: 834px) and (max-width: 1194px) and (-webkit-min-device-pixel-ratio: 2) {
        //   min-height: 400px;
        // }
      }
      .overviewCard {
        height: 100%;
        @apply flex flex-col;
      }
      // 卡片高度设置
      .overviewCard,
      .pieChartsCard {
        // min-height: 200px;
        @apply bg-white rounded-lg;
        
      }
      .leftCard{
        display: flex;
        flex-direction: column;
      }
    }
  }
  .pieChartsContainer {
    @apply flex flex-wrap justify-center h-full;

    .chartCard {
      @apply rounded-lg transition-all duration-300 flex-grow flex flex-col;
      // min-height: 240px;
      min-width: 179px;
      width: calc(33.333% - 16px);
      max-width: 400px;

      @media (max-width: 768px) {
        width: calc(50% - 8px);
      }

      @media (max-width: 480px) {
        width: 100%;
      }
    }
  }
}

// 定义淡入上升动画
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tableContainer {
  height: 100%;
  :global {
    .ant-spin-nested-loading, .ant-spin-container {
      height: 100%;
      overflow: hidden;
    }
    .ant-table-body {
      scrollbar-width: thin;
      scrollbar-color: transparent transparent;
      
      &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      &::-webkit-scrollbar-track {
        background: transparent;
      }
      
      &::-webkit-scrollbar-thumb {
        background: transparent;
        border-radius: 4px;
      }
    }
    .ant-spin-container{
      display: flex;
      flex-direction: column;

    }
    .ant-table-body:hover {
      scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
      
      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        
        &:hover {
          background: rgba(0, 0, 0, 0.3);
        }
      }
    }
    .ant-table {
      height: 100%;
      flex: 1;
      overflow-y: scroll;
    }
  }
}
