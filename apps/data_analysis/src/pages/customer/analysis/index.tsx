import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { BarChartData } from '@/components/echartsComponents'
import type { CardData } from '@/components/swiperCard'
import type { CheckboxChangeEvent } from 'antd/lib'
import WeightSvg from '@/assets/I-2.svg?react'
import RollSvg from '@/assets/I-3.svg?react'
import PriceSvg from '@/assets/I-4.svg?react'
import CustomerSvg from '@/assets/I.svg?react'
import AIAnalysis from '@/components/AIAnalysis'
import { RangeDate } from '@/components/dateComponents'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ine<PERSON>hart } from '@/components/echartsComponents'
import HeaderBox from '@/components/headerBox'
import LabelBox from '@/components/LabelBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { Selects } from '@/components/selectComponents'
import SwiperCard from '@/components/swiperCard'
import { FetchEmployeeList, FetchGetTopCustomerData, FetchSaleSystemList } from '@/service/api'
import { EmployeeType } from '@/service/request/type'
import { useMobileScreen, useWindowSize } from '@/utils'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { formatDate, getFilterData } from '@ly/utils'
import { Affix, Card, Checkbox, Col, Flex, message, Row, Table, Tooltip, Typography } from 'antd'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import TrendBox, { DATE_CONFIG, getAnalysisDateRange } from '../components/TrendBox'
import { FetchGetCustomerAnalysisData } from '../index.config'
import styles from './index.module.scss'

const { Title } = Typography
export enum OldOrNew {
  Old = 1,
  New = 2,
  Both = 0,
}
const cards: CardData[] = [
  {
    id: 1,
    title: '总匹数',
    key: 'total_roll',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><RollSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 2,
    title: '总数量',
    key: 'total_settle_weight',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><WeightSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 3,
    title: '客户数',
    key: 'total_customer_count',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><CustomerSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 4,
    title: '总销售金额',
    key: 'total_sale_amount',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><PriceSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
] as const
interface CheckboxState {
  big: boolean
  plate: boolean
}
// 添加获取 sale_mode_iden 的工具函数
function getSaleModeIden(states: CheckboxState): number | undefined {
  // 使用对象映射简化逻辑
  const modeMap = {
    'true,false': 1, // 只勾选大货
    'false,true': 2, // 只勾选剪板
    'true,true': undefined, // 都勾选
    'false,false': undefined, // 都不勾选
  }

  const key = `${states.big},${states.plate}`
  return modeMap[key as keyof typeof modeMap]
}
export function Component() {
  const isMobile = useMobileScreen()
  const [searchParams] = useSearchParams()
  const [saleSystem, setSaleSystem] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_system_id')) || 0,
    label: searchParams.get('sale_system_name') || '',
  }) // 部门id
  const [saleUser, setSaleUser] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_user_id')) || 0,
    label: searchParams.get('sale_user_name') || '',
  }) // 销售员id
  const start_time = searchParams.get('start_time') // 上一个路由传入的开始时间
  const end_time = searchParams.get('end_time') // 上一个路由传入的结束时间
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const navigate = useNavigate() // 路由跳转
  const customBreadcrumbs = [ // 头标签
    {
      title: '工作台',
      onClick: () => navigate('/'),
    },
    {
      title: '客户分析',
      onClick: () => {},
    },
  ]
  const [dateRange, setDateRange] = useState<DateRange>([
    new Date(start_time || new Date()),
    new Date(end_time || new Date()),
  ]) // 日期范围
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [customerData, setCustomerData] = useState<Api.GetTopCustomerData.GetTopCustomerResForTop[]>([])
  const [loading, setLoading] = useState(false)
  const { mutateAsync: fetchTopCustomer } = FetchGetTopCustomerData()
  const [currentSelectArea, setCurrentSelectArea] = useState('')
  const [currentCustomer, setCustomer] = useState('')
  const [currentSaleGroupRatio, setSaleGroupRatio] = useState('')
  const [currentOldOrNew, setOldOrNewClick] = useState(0 as OldOrNew)
  const [checkboxStates, setCheckboxStates] = useState({
    big: true, // 大货
    plate: true, // 剪板
  })
  // 在原来的代码中使用这个函数
  const states = checkboxStates
  const sale_mode_iden = getSaleModeIden(states)
  const [dateType, setDateType] = useState(DATE_CONFIG.DAILY_ANALYSIS.TYPE)
  // 获取趋势分析的日期范围
  const trendDateRange = useMemo(() => {
    if (!dateRange)
      return { trend_start_time: '', trend_end_time: '' }
    const [start, end] = getAnalysisDateRange(dateType, dateRange)
    return {
      trend_start_time: start,
      trend_end_time: end,
    }
  }, [dateRange, dateType])
  function handleChangeType(dateType: 1 | 2) {
    setDateType(dateType)
  }
  // 请求客户排行
  const fetchData = useCallback(async (currentPage: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return
    const states = currentCheckboxStates || checkboxStates
    const sale_mode_iden = getSaleModeIden(states)
    setLoading(true)
    try {
      const res = await fetchTopCustomer(getFilterData({
        page: currentPage,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
        location: currentSelectArea,
        customer_id: currentCustomer,
        sale_group_id: currentSaleGroupRatio,
        is_show_new_customer: currentOldOrNew === OldOrNew.New,
        is_show_old_customer: currentOldOrNew === OldOrNew.Old,
      }))

      if (currentPage === 1) {
        setCustomerData(res.top_data_list || [])
      }
      else {
        setCustomerData(prev => [...prev, ...(res.top_data_list || [])])
      }

      // 判断是否还有更多数据
      setHasMore((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取客户排行数据失败:', error)
      messageApi.error('获取数据失败')
    }
    finally {
      setLoading(false)
    }
  }, [dateRange, saleSystem.value, saleUser.value, fetchTopCustomer, messageApi, currentSaleGroupRatio, currentSelectArea, currentCustomer, currentOldOrNew])
  // 修改列定义为 antd Table 的格式
  const columns = useMemo(() => [
    {
      title: '客户名称',
      dataIndex: 'customer_name',
      key: 'customer_name',
      width: 120,
      fixed: 'left',
      render: (_: string, record: Api.GetTopCustomerData.GetTopCustomerResForTop) => {
        const params = {
          customer_id: record.customer_id,
          customer_name: encodeURIComponent(record.customer_name || ''),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          sale_system_id: saleSystem.value || '',
          sale_system_name: encodeURIComponent(saleSystem.label || '') || undefined,
          sale_user_id: saleUser.value || '',
          sale_user_name: encodeURIComponent(saleUser.label || '') || undefined,
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return <Links to={`/customer/infos?${queryString}`}>{record.customer_name}</Links>
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '面料种类',
      dataIndex: 'product_count',
      key: 'product_count',
      width: 80,
    },
    {
      title: '颜色数量',
      dataIndex: 'product_color_count',
      key: 'product_color_count',
      width: 80,
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      key: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [dateRange, saleSystem, saleUser, isMobile])
  const { mutateAsync: fetchCustomerAnalysis, isPending } = FetchGetCustomerAnalysisData() // 获取产品分析数据
  const [productAnalysisData, setProductAnalysisData] = useState<Api.CustomerAnalysis.CustomerAnalysisDataResponse>() // 产品分析数据
  const [pieOneData, setPieOneData] = useState<any>(null) // 客户销售占比
  const [pieTwoData, setPieTwoData] = useState<any>(null) // 客户类别占比
  const [pieThreeData, setPieThreeData] = useState<any>(null) // 近30天新老客户占比
  const [chartData, setChartData] = useState<BarChartData[]>([]) // 图表数据
  const [isChatPending, setChatPending] = useState(false)
  // 添加处理函数
  const handleCheckboxChange = (type: 'big' | 'plate', checkedValue: CheckboxChangeEvent) => {
    console.log('checkedValue', type, checkedValue.target.checked)
    const newState = {
      ...checkboxStates,
      [type]: checkedValue.target.checked,
    }
    console.log('newState', newState)
    // 确保至少有一个选中
    if (newState.big || newState.plate) {
      setCheckboxStates(newState)
      setPage(1) // 重置页码
      fetchData(1, newState) // 重新加载数据
    }
  }
  //
  const fetchData1 = async (params: Api.CustomerAnalysis.CustomerAnalysisDataRequest) => {
    try {
      const res = await fetchCustomerAnalysis(getFilterData(params))
      setProductAnalysisData(res)
    }
    catch (error) {
      console.error('获取数据失败:', error)
      messageApi.error('发生意外，请稍后再试')
    }
  }

  const handleLoadMore = useCallback(() => {
    if (loading || !hasMore)
      return
    setPage(prev => prev + 1)
    fetchData(page + 1)
  }, [loading, hasMore, page, fetchData])
  const preAreaStrings = useRef('null')
  // 点击地区
  function handleBarClick(e: any) {
    if (e.name === currentSelectArea) {
      setCurrentSelectArea('')
      return
    }
    if (e.name === '其他') {
      console.log('otherArea', currentSelectArea, preAreaStrings.current)
      if (currentSelectArea === preAreaStrings.current) {
        setCurrentSelectArea('')
      }
      else {
        const otherArea = productAnalysisData?.sales_area_stat.filter(item => item.name !== '其他').map(item => item.value).join(',') || ''
        preAreaStrings.current = otherArea
        setCurrentSelectArea(otherArea)
      }
      return
    }
    setCurrentSelectArea(e.name)
  }
  const preCustomer = useRef('null')
  function handlePieClick(e: any) {
    if (e.data.customer_id === currentCustomer) {
      setCustomer('')
      return
    }
    if (e.data.name === '其他') {
      console.log('otherArea', currentCustomer, preCustomer.current)
      if (currentCustomer === preCustomer.current) {
        setCustomer('')
      }
      else {
        const otherArea = productAnalysisData?.customer_sale_ratio.filter(item => item.customer_name !== '其他').map(item => item.customer_id).join(',') || ''
        preCustomer.current = otherArea
        setCustomer(otherArea)
      }
      return
    }
    setCustomer(e.data.customer_id)
  }
  const preSaleGroupRatio = useRef('null')
  function handleSaleGroupRatioClick(e: any) {
    if (e.data.id === currentSaleGroupRatio) {
      setSaleGroupRatio('')
      return
    }
    if (e.data.name === '其他') {
      if (currentSaleGroupRatio === preSaleGroupRatio.current) {
        setSaleGroupRatio('')
      }
      else {
        const otherArea = productAnalysisData?.top_10_sale_group_ratio.map(item => item.sale_group_id).filter(item => item).join(',') || ''
        preSaleGroupRatio.current = otherArea
        setSaleGroupRatio(otherArea)
      }
      return
    }
    setSaleGroupRatio(e.data.id)
  }
  function handleOldOrNewClick(e: any) {
    if (e.data.old_customer && OldOrNew.Old === currentOldOrNew) {
      setOldOrNewClick(0)
      return
    }
    if (e.data.new_customer && OldOrNew.New === currentOldOrNew) {
      setOldOrNewClick(0)
      return
    }
    setOldOrNewClick(e.data.old_customer ? OldOrNew.Old : OldOrNew.New)
  }
  // 修改初始加载数据的 useEffect
  useEffect(() => {
    setPage(1)
    console.log('currentCustomer', currentCustomer)
    if (dateRange) {
      // 请求所有数据（除了排行）
      fetchData1({
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        type: 2,
        location: currentSelectArea,
        customer_id: currentCustomer,
        sale_group_id: currentSaleGroupRatio,
        is_show_new_customer: currentOldOrNew === OldOrNew.New,
        is_show_old_customer: currentOldOrNew === OldOrNew.Old,
      })
    }
    fetchData(1)
  }, [dateRange, saleSystem.value, saleUser.value, currentSelectArea, currentSaleGroupRatio, currentCustomer, currentOldOrNew])
  useEffect(() => { // 组合数据
    if (!currentCustomer && productAnalysisData) { // 客户销售占比
      const list1 = productAnalysisData.customer_sale_ratio?.map((item: any) => {
        const value = Number(item.sale_ratio.replace('%', ''))
        return { name: item.customer_name, value, customer_id: item.customer_id }
      })
      setPieOneData(list1)
    }
    if (!currentSaleGroupRatio && productAnalysisData) { // 客户类别占比
      const list2 = productAnalysisData.top_10_sale_group_ratio?.map((item: any) => {
        const value = Number(item.sale_ratio.replace('%', ''))
        return { name: item.sale_group_name, value, id: item.sale_group_id }
      })
      setPieTwoData(list2)
    }
    if (!currentOldOrNew && productAnalysisData) { // 近30天新老客户占比
      const list3 = [
        { name: '新客户', value: Number(productAnalysisData.new_customer_ratio.replace('%', '')), color: '#6eb088', new_customer: true },
        { name: '老客户', value: Number(productAnalysisData.old_customer_ratio.replace('%', '')), color: '#3875f7', old_customer: true },
      ]
      setPieThreeData(list3)
    }
    // 只在非地区筛选时更新销售地区数据
    if (!currentSelectArea && productAnalysisData?.sales_area_stat) {
      setChatPending(true)
      const chartData: any = productAnalysisData.sales_area_stat.map(item => ({
        name: item.name,
        series: [
          {
            name: '销量',
            value: item.value,
          },
        ],
      }))
      setChartData(chartData)
      setTimeout(() => {
        setChatPending(false)
      }, 0)
    }
  }, [productAnalysisData, currentSelectArea, currentSaleGroupRatio, currentOldOrNew])
  const [bottomRowHeight, setBottomRowHeight] = useState<string | number>(0)

  useEffect(() => {
    if (isMobile) {
      setBottomRowHeight('unset')
      return
    }
    const handleResize = () => {
      const bottomRow = document.getElementById('bottomRow')
      console.log('bottomRow', bottomRow)
      setBottomRowHeight(bottomRow?.clientHeight || 0)
    }
    setTimeout(() => {
      handleResize()
    }, 60)
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [isMobile])
  const tableContiainerRef = useRef<HTMLDivElement>(null)
  return (
    <ListFrame
      header={(
        <HeaderBox
          showUserInfo
          breadcrumbs={customBreadcrumbs}
          rightSlot={(
            <AIAnalysis
              url="/h5/v1/ai/analysis/productAnalysisData"
              fixed={false}
              outputParams={{
                type: 2,
                customer_id: currentCustomer,
                start_time: formatDate(dateRange![0].toDateString()),
                end_time: formatDate(dateRange![1].toDateString()),
                sale_mode_iden,
                sale_system_id: saleSystem.value, // 部门id
                sale_user_id: saleUser.value, // 销售员id
                location: currentSelectArea,
                sale_group_id: currentSaleGroupRatio,
                is_show_new_customer: currentOldOrNew === OldOrNew.New,
                is_show_old_customer: currentOldOrNew === OldOrNew.Old,
                trend_start_time: trendDateRange.trend_start_time,
                trend_end_time: trendDateRange.trend_end_time,
              } as Api.ProductAi.Request}
              type="output"
              text="AI报表分析"
            />
          )}
        />
      )}
      className={styles.page}
    >
      {contextHolder}
      <div className={styles.container}>
        <div className={styles.content}>
          {/* 上部分 */}
          <Row gutter={[16, 16]} align="top">
            <Col xs={24} sm={24} md={12} lg={12} className="h-full">
              <Card
                className="h-full"
                classNames={{
                  body: styles.overviewCard,
                }}
              >
                {/* 日期+部门+销售员 */}
                <Row gutter={[16, 16]} className=" mb-3">
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="日&nbsp;&nbsp;&nbsp;&nbsp;期" fullWidth>
                      <RangeDate
                        value={dateRange}
                        onChange={(e) => {
                          setDateRange(e)
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setCustomer('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                        loading={false}
                        format="YYYY-MM-DD"
                        minDate={new Date('2010-01-01')}
                        maxDate={new Date()}
                        allowClear={false}
                      />
                    </LabelBox>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="部&nbsp;&nbsp;&nbsp;&nbsp;门" fullWidth>
                      <Selects
                        fetchApi={FetchSaleSystemList}
                        disabled={isPending}
                        placeholder="选择部门"
                        pagination={{ defaultPage: 1, defaultSize: 50 }}
                        fieldNames={{ label: 'name', value: 'id' }}
                        isSearch
                        keywordFile="name"
                        value={saleSystem}
                        onChange={(value: any, _option: any) => {
                          setSaleSystem({ value, label: _option?.name || '' })
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setCustomer('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                      />
                    </LabelBox>
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={8}>
                    <LabelBox label="销售员" fullWidth>
                      <Selects
                        fetchApi={FetchEmployeeList}
                        disabled={isPending}
                        placeholder="选择销售员"
                        query={{ duty: EmployeeType.salesman }}
                        pagination={{ defaultPage: 1, defaultSize: 50 }}
                        fieldNames={{ label: 'name', value: 'id' }}
                        isSearch
                        keywordFile="name"
                        value={saleUser}
                        onChange={(value: any, _option: any) => {
                          setSaleUser({ value, label: _option?.name || '' })
                          setCurrentSelectArea('')
                          setSaleGroupRatio('')
                          setCustomer('')
                          setOldOrNewClick(OldOrNew.Both)
                        }}
                      />
                    </LabelBox>
                  </Col>
                </Row>
                {/* 切换图 */}
                <SwiperCard cards={cards} data={productAnalysisData} loading={isPending} />
              </Card>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Card>
                <Row className={styles.pieChartsContainer}>
                  <Col xs={24} sm={24} md={12} lg={12}>
                    <Tooltip placement="bottomRight" title="不含退货数据">
                      <Title level={4}>
                        客户销售占比
                        <QuestionCircleOutlined className="ml-2" />
                      </Title>

                    </Tooltip>
                    <PieChart
                      className="flex-1 flex justify-center items-center"
                      labelPosition="inside"
                      type="pie"
                      loading={false}
                      defaultData={[{ name: '暂无数据', value: 1, color: '#f4b352' }]}
                      data={pieOneData}
                      onPieClick={handlePieClick}
                    />
                  </Col>
                  <Col xs={24} sm={24} md={12} lg={12}>
                    <Tooltip placement="bottomRight" title="当前时间30天内首次下单的客户为新客户，不含退款数据">
                      <Title level={4}>
                        新老客户占比
                        <QuestionCircleOutlined className="ml-2" />
                      </Title>
                    </Tooltip>
                    <PieChart
                      className="flex-1 flex justify-center items-center"
                      type="pie"
                      labelPosition="inside"
                      loading={false}
                      defaultData={[{ name: '新客户', value: 1, color: '#6eb088' }, { name: '老客户', value: 1, color: '#3875f7' }]}
                      data={pieThreeData}
                      onPieClick={handleOldOrNewClick}
                    />
                  </Col>
                </Row>
              </Card>
            </Col>
          </Row>
          <Row gutter={[10, 16]} id="bottomRow" className={classNames(styles.row, 'flex-1 overflow-hidden')}>
            <Col xs={24} sm={24} md={12} lg={12}>

              <Card
                classNames={{
                  body: 'flex flex-col h-full',
                }}
                className={styles.leftCard}
                style={{ height: bottomRowHeight }}
              >
                <div className="flex flex-col h-full">
                  <Row gutter={[16, 16]}>
                    <Col xs={24} sm={24} md={16} lg={16}>
                      <Title level={4}>
                        <Tooltip placement="bottomRight" title="不含退货数据">
                          销售地区
                          <QuestionCircleOutlined className="ml-2" />
                        </Tooltip>
                      </Title>
                      <BarChart
                        height={isMobile ? 300 : (bottomRowHeight as number) / 2 - 50}
                        loading={isChatPending}
                        data={chartData}
                        config={{
                          gridLeft: isMobile ? '-10%' : '-3%',
                          colors: ['#1677ff', '#52c41a', '#faad14'],
                          negativeColors: ['#ff4d4f', '#f5222d', '#cf1322'],
                          onBarClick: handleBarClick,
                          isStack: false,
                          showLegend: false,
                          showGrid: false,
                          showYAxis: false,
                        }}
                      />
                    </Col>
                    <Col xs={24} sm={24} md={8} lg={8}>
                      <Tooltip placement="bottomLeft" title="不含退货数据">
                        <Title level={4}>
                          客户类别占比
                          <QuestionCircleOutlined className="ml-2" />
                        </Title>
                      </Tooltip>
                      <PieChart
                        height={isMobile ? 300 : (bottomRowHeight as number) / 2 - 50}
                        className="flex-1 flex justify-center items-center"
                        type="pie"
                        labelPosition="inside"
                        loading={false}
                        defaultData={[{ name: '暂无数据', value: 1, color: '#68aff9' }]}
                        data={pieTwoData}
                        onPieClick={handleSaleGroupRatioClick}
                      />
                    </Col>
                  </Row>
                  <div className="flex-col flex flex-1 overflow-hidden">
                    <Flex justify="space-between" align="center" style={{ marginTop: 0, marginBottom: 0 }}>
                      <Title level={4}>客户排行</Title>
                      <Flex justify="center" align="center">
                        <Checkbox
                          checked={checkboxStates.big}
                          onChange={e => handleCheckboxChange('big', e)}
                        >
                          大货
                        </Checkbox>
                        <Checkbox
                          checked={checkboxStates.plate}
                          onChange={e => handleCheckboxChange('plate', e)}
                        >
                          剪板
                        </Checkbox>
                      </Flex>
                    </Flex>

                    <div className="flex-1 min-h-0 overflow-hidden" ref={tableContiainerRef}>
                      <Table
                        columns={columns}
                        dataSource={customerData}
                        loading={loading}
                        scroll={{
                          x: 'max-content',
                          y: isMobile ? 300 : (tableContiainerRef.current?.clientHeight) - 40,
                        }}
                        sticky
                        rootClassName={styles.tableContainer}
                        pagination={false}
                        rowKey="customer_id"
                        size="small"
                        style={{ height: isMobile ? 330 : (tableContiainerRef.current?.clientHeight) }}
                        rowClassName={(_, index) => `${index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!'}`}
                        onRow={() => ({
                          onMouseEnter: () => {
                            // 当滚动到最后一行时加载更多数据
                            const scrollElement = document.querySelector('.ant-table-body')
                            if (!scrollElement)
                              return

                            const { scrollTop, scrollHeight, clientHeight } = scrollElement
                            if (scrollHeight - scrollTop <= clientHeight + 50 && !loading && hasMore) {
                              handleLoadMore()
                            }
                          },
                        })}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>

              <Card
                classNames={{
                  body: styles.rightCard,
                }}
                style={{ height: bottomRowHeight }}
              >
                <TrendBox
                  onChangeType={handleChangeType}
                  chartHeight={isMobile ? undefined : '100%'}
                  oleOrNew={currentOldOrNew}
                  saleGroupRatio={currentSaleGroupRatio}
                  saleArea={currentSelectArea}
                  customerId={currentCustomer}
                  dateRange={dateRange}
                  saleSystemId={saleSystem.value}
                  saleUserId={saleUser.value}
                />
              </Card>
            </Col>
          </Row>
        </div>
      </div>
    </ListFrame>
  )
}
