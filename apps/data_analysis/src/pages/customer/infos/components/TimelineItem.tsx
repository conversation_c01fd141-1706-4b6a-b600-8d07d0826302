import { formatUrl } from '@/utils/format'
import { formatDate } from '@ly/utils'
import { Col, Image, Row, Space, Tag, Typography } from 'antd'

interface Props {
  imageList: string[]
  item: Api.TeamList.QywxCustomerFollowRecordDetailData
}
const { Paragraph, Text } = Typography
export function TimelineItem(props: Props) {
  const { imageList, item } = props
  return (
    <>
      <Text>{formatDate(item.create_date)}</Text>
      {imageList.length > 0
        ? (
            <Row gutter={[10, 10]}>
              {
                imageList.map((item, index) => {
                  return (
                    <Col span={6} key={index}>
                      <Image src={formatUrl(item, '')} alt={item} />
                    </Col>
                  )
                })
              }
            </Row>
          )
        : null}
      <Paragraph className="!mb-1">
        {item.feedbacks?.[0]?.feedback_solution}
      </Paragraph>
      <Space size={[8, 8]}>
        {
          item.label_names?.map((label, index) => {
            return (
              <Tag key={index} color="processing">
                {label}
              </Tag>
            )
          })
        }
      </Space>
    </>
  )
}
