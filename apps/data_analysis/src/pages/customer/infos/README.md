# 这里是 客户分析 模块

## 客户分析 已接入企业微信外部客户聊天栏
只需要把以下链接复制黏贴到企业微信企业后台的相应的应用中的`配置到聊天工具栏`即可
> https://hcscmtest.zzfzyc.com/qywx_workspace/oauth2?appid=ww8f7fa2422fe8df3d&agentid=1000003&redirectUri=/customer/infos

根据客户分析模块README中的OAuth2链接，我来解释各个参数的作用：

## 链接结构分析
```
https://hcscmtest.zzfzyc.com/qywx_workspace/oauth2?appid=ww8f7fa2422fe8df3d&agentid=1000003&redirectUri=/customer/infos
```
## 参数详细说明
### 1. appid=ww8f7fa2422fe8df3d
- 作用 : 企业微信公司的corpid，你可以去企业微信公司后台`我的企业`中查看企业corpid
- 格式 : 以"ww"开头的字符串
- 用途 :
  - 标识具体的企业微信应用
  - 用于OAuth认证时验证应用身份
  - 确保授权请求来自合法的企业微信应用
### 2. agentid=1000003
- 作用 : 企业微信应用内的代理应用ID
- 用途 :
  - 标识企业微信内的具体应用代理
  - 用于权限控制和应用范围限定
  - 在OAuth流程中确定用户授权的具体应用范围
### 3. redirectUri=%2Fcustomer/infos
- 作用 : OAuth认证成功后的重定向URI
- 格式 : URL编码后的路径（%2F = /）
- 实际路径 : /customer/infos
- 用途 :
  - 指定OAuth认证完成后跳转到的页面
  - 确保用户认证后直接进入客户对账模块
  - 提供无缝的用户体验
## 工作流程
1. 用户点击链接 : 在企业微信聊天工具栏中点击配置的链接
2. OAuth认证 : 系统使用 appid 和 agentid 进行身份验证
3. 权限确认 : 验证用户是否有访问该应用的权限
4. 重定向 : 认证成功后跳转到 redirectUri 指定的客户分析页面
5. 功能使用 : 用户可以直接使用客户分析功能
## 配置说明
这个链接需要在企业微信企业后台的相应应用中配置到"聊天工具栏"，配置后用户就可以在聊天界面直接访问客户分析功能，实现了企业微信与业务系统的深度集成。
