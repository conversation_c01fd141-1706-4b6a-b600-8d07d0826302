import type { DateRange } from '@/components/dateComponents/RangeDate'
import type { CardData } from '@/components/swiperCard'
import type { RootState } from '@/store'
import type { ColumnsType } from 'antd/es/table'
import WeightSvg from '@/assets/I-2.svg?react'
import RollSvg from '@/assets/I-3.svg?react'
import PriceSvg from '@/assets/I-4.svg?react'
import AIAnalysis from '@/components/AIAnalysis'
import { RangeDate } from '@/components/dateComponents'
import { SmallLineChart } from '@/components/echartsComponents'
import HeaderBox from '@/components/headerBox'
import LabelBox from '@/components/LabelBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { Selects } from '@/components/selectComponents'
import SwiperCard from '@/components/swiperCard'
import { FetchCustomerList, FetchEmployeeList, FetchGetReceivableOrderList, FetchGetTopProductData, FetchSaleSystemList } from '@/service/api'
import { GetTeamList } from '@/service/api/customerVisit'
import { EmployeeType } from '@/service/request/type'
import { isWxWork, useMobileScreen } from '@/utils'
import { getWechatCustomerInfo } from '@/utils/wechatCustomerUtil'
import { ClockCircleOutlined } from '@ant-design/icons'
import { formatDate, formatHashTag, formatPriceDiv, formatRollDiv, formatUnitPriceDiv, formatWeightDiv, getFilterData } from '@ly/utils'
import { Checkbox, Col, Empty, Flex, message, Row, Table, Tag, Timeline, Typography } from 'antd'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { useEffectOnActive } from 'keepalive-for-react'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import { useNavigate, useSearchParams } from 'react-router-dom'
import TrendBox, { DATE_CONFIG, getAnalysisDateRange } from '../components/TrendBox'
import { FetchGetCustomerInfo } from '../index.config'
import { TimelineItem } from './components/TimelineItem'
import styles from './index.module.scss'

const { Title } = Typography

const cards: CardData[] = [
  {
    id: 1,
    title: '总匹数',
    key: 'total_roll',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><RollSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 2,
    title: '总数量',
    key: 'total_settle_weight',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><WeightSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
  {
    id: 3,
    title: '总销售金额',
    key: 'total_sale_amount',
    icon: <div className="flex items-center justify-center bg-[#e8f4ff]" style={{ borderRadius: '50px', width: '40px', height: '40px' }}><PriceSvg style={{ width: '30px', height: '30px' }} /></div>,
  },
] as const
interface CheckboxState {
  big: boolean
  plate: boolean
}
// 添加获取 sale_mode_iden 的工具函数
function getSaleModeIden(states: CheckboxState): number | undefined {
  // 使用对象映射简化逻辑
  const modeMap = {
    'true,false': 1, // 只勾选大货
    'false,true': 2, // 只勾选剪板
    'true,true': undefined, // 都勾选
    'false,false': undefined, // 都不勾选
  }

  const key = `${states.big},${states.plate}`
  return modeMap[key as keyof typeof modeMap]
}
export function Component() {
  const { corpId, agentId } = useSelector((state: RootState) => state.auth)

  const [searchParams] = useSearchParams()
  const [customer, setCustomer] = useState<{
    value?: number
    label?: string
  }>({
    value: searchParams.get('customer_id') ? Number(searchParams.get('customer_id')) : undefined,
    label: searchParams.get('customer_name') !== 'undefined' ? (searchParams.get('customer_name') || undefined) : undefined,
  })
  const [saleSystem, setSaleSystem] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_system_id')) || '',
    label: !['undefined', 'null'].includes(searchParams.get('sale_system_name')!) ? searchParams.get('sale_system_name') : '',
  }) // 部门
  const [saleUser, setSaleUser] = useState<{
    value: number
    label: string
  }>({
    value: Number(searchParams.get('sale_user_id')) || '',
    label: !['undefined', 'null'].includes(searchParams.get('sale_user_name')!) ? searchParams.get('sale_user_name') : '',
  }) // 销售员
  // const customerId = searchParams.get('customer_id') // 客户id
  const start_time = searchParams.get('start_time') // 上一个路由传入的开始时间
  const end_time = searchParams.get('end_time') // 上一个路由传入的结束时间
  const [messageApi, contextHolder] = message.useMessage() // 消息提示
  const navigate = useNavigate() // 路由跳转
  const [dateRange, setDateRange] = useState<DateRange>([
    new Date(start_time || new Date()),
    new Date(end_time || new Date()),
  ]) // 日期范围
  // 在企业微信聊天侧边栏获取客户信息
  const getCustomerData = async () => {
    try {
      const customerInfo = await getWechatCustomerInfo({
        corpId,
        agentId,
      })

      console.log('客户信息:', customerInfo)
      // customerInfo.customerId - 客户ID（单聊场景）
      // customerInfo.corpGroupChatId - 群聊ID（群聊场景）
      // customerInfo.entry - 进入场景

      // 根据场景处理业务逻辑
      if (customerInfo.customerId) {
        // 单聊场景的业务逻辑
        setCustomer({ value: customerInfo.customerId, label: customerInfo.customerName })
      }
    }
    catch (error) {
      console.error('获取客户信息失败:', error)
    }
  }
  useEffectOnActive(() => {
    if (isWxWork()) {
      getCustomerData()
    }
  }, [])
  const customBreadcrumbs = [ // 头标签
    {
      title: '工作台',
      onClick: () => navigate('/'),
    },
    {
      title: '客户分析',
      onClick: () => navigate(`/customer/analysis?start_time=${dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')}&end_time=${dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD')}`),
    },
    {
      title: '客户详情',
      onClick: () => {},
    },
  ]
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [loading, setLoading] = useState(false)
  const [productData, setProductData] = useState<Api.GetTopProductData.GetTopProductResForTop[]>([])
  const [orderList, setOrderList] = useState<Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem[]>([])
  const [orderListHasMore, setOrderListHasMore] = useState(false)
  const [orderListLoading, setOrderListLoading] = useState(false)
  const { mutateAsync: fetchTopProduct } = FetchGetTopProductData()
  const [checkboxStates, setCheckboxStates] = useState({
    big: true, // 大货
    plate: true, // 剪板
  })
  const states = checkboxStates
  const sale_mode_iden = getSaleModeIden(states)
  const [dateType, setDateType] = useState(DATE_CONFIG.DAILY_ANALYSIS.TYPE)
  const isMobile = useMobileScreen()
  // 获取趋势分析的日期范围
  const trendDateRange = useMemo(() => {
    if (!dateRange)
      return { trend_start_time: '', trend_end_time: '' }
    const [start, end] = getAnalysisDateRange(dateType, dateRange)
    return {
      trend_start_time: start,
      trend_end_time: end,
    }
  }, [dateRange, dateType])

  function handleChangeType(dateType: 1 | 2) {
    setDateType(dateType)
  }
  // 修改数据获取方法
  const fetchData = useCallback(async (currentPage: number, currentCheckboxStates?: typeof checkboxStates) => {
    if (!dateRange)
      return

    // 使用传入的 currentCheckboxStates 或当前的 checkboxStates
    const states = currentCheckboxStates || checkboxStates
    const sale_mode_iden = getSaleModeIden(states)
    setLoading(true)
    try {
      const res = await fetchTopProduct({
        page: currentPage,
        size: 10,
        start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
        end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
        customer_id: customer.value,
        sale_system_id: saleSystem.value,
        sale_user_id: saleUser.value,
        sale_mode_iden,
      })
      console.log('top_data_list', res.top_data_list)
      if (currentPage === 1) {
        setProductData(res.top_data_list || [])
      }
      else {
        setProductData(prev => [...prev, ...(res.top_data_list || [])])
      }

      setHasMore((res.top_data_list || []).length === 10)
    }
    catch (error) {
      console.error('获取产品排行数据失败:', error)
      messageApi.error('获取数据失败')
    }
    finally {
      setLoading(false)
    }
  }, [dateRange, customer.value, saleSystem.value, saleUser.value, fetchTopProduct, messageApi])

  const columns = useMemo<ColumnsType>(() => [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 180,
      fixed: 'left',
      render: (_: string, record: Api.GetTopProductData.GetTopProductResForTop) => {
        return formatHashTag(record.product_code, record.product_name)
      },
    },
    {
      title: '销售匹数',
      dataIndex: 'sale_roll',
      key: 'sale_roll',
      width: 80,
    },
    {
      title: '匹数占比',
      dataIndex: 'roll_ratio',
      key: 'roll_ratio',
      width: 80,
    },
    {
      title: '退货匹数',
      dataIndex: 'return_roll',
      key: 'return_roll',
      width: 80,
    },
    {
      title: '合计匹数',
      dataIndex: 'total_roll',
      key: 'total_roll',
      width: 80,
    },
    {
      title: '合计数量',
      dataIndex: 'total_settle_weight',
      key: 'total_settle_weight',
      width: 80,
    },
    {
      title: '合计金额',
      dataIndex: 'total_sale_amount',
      key: 'total_sale_amount',
      width: 80,
    },
    {
      title: '颜色数量',
      dataIndex: 'color_count',
      key: 'color_count',
      width: 80,
      render: (_: string, record: Api.GetTopProductData.GetTopProductResForTop) => {
        const params = {
          customer_id: customer.value,
          customer_name: encodeURIComponent(customer.label || ''),
          product_id: record.product_id,
          sale_system_id: saleSystem.value,
          sale_system_name: saleSystem.label || '',
          sale_user_id: saleUser.value,
          sale_user_name: saleUser.label || '',
          product_code: encodeURIComponent(record.product_code || ''),
          product_name: encodeURIComponent(record.product_name || ''),
          start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
          end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return (
          <Links to={`/customer/customer-product?${queryString}`}>
            {record.color_count}
          </Links>
        )
      },
    },
    {
      title: '近15天趋势',
      dataIndex: 'last_15_day_sale_amount',
      key: 'last_15_day_sale_amount',
      ...(isMobile ? {} : { fixed: 'right' }),
      width: 120,
      render: (data: number[]) => (
        <SmallLineChart data={data} width={120} height={30} />
      ),
    },
  ], [customer, dateRange, saleSystem, saleUser, isMobile])
  const { mutateAsync } = FetchGetReceivableOrderList()
  const orderListPage = useRef(1)
  async function getReceivableOrderList(page: number) {
    if (!dateRange)
      return
    setOrderListLoading(true)
    try {
      const res = await mutateAsync({
        customer_id: customer.value,
        page,
        size: 10,
        start_time: dateRange?.[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        end_time: dateRange?.[1] ? dayjs(dateRange[1]).format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
      })
      console.log('page', page)
      if (page === 1) {
        setOrderList((res.list || []).map((item, index) => ({ ...item, index: index + 1 })))
      }
      else {
        setOrderList((prev) => {
          const startIndex = prev.length + 1
          return [...prev, ...(res.list || []).map((item, index) => ({ ...item, index: startIndex + index }))]
        })
      }
      setOrderListHasMore((res.list || []).length === 10)
    }

    catch (error) {
      console.error('获取产品排行数据失败:', error)
      messageApi.error('获取数据失败')
    }
    finally {
      setOrderListLoading(false)
    }
  }
  const columns1: ColumnsType = [
    {
      title: '序号',
      dataIndex: 'index',
      key: 'index',
      width: 50,
      fixed: 'left',
      render: (_: string, _record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem, index) => {
        return index + 1
      },
    },
    {
      title: '成品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 140,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatHashTag(record.product_code, record.product_name)
      },
    },
    {
      title: '色号颜色',
      dataIndex: 'product_color_name',
      key: 'product_color_name',
      width: 100,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatHashTag(record.product_color_code, record.product_color_name)
      },
    },
    {
      title: '缸号',
      dataIndex: 'dyelot_number',
      key: 'dyelot_number',
      width: 80,
    },
    {
      title: '销售单价',
      dataIndex: 'sale_price',
      key: 'sale_price',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatUnitPriceDiv(record.sale_price || 0)
      },
    },
    {
      title: '优惠幅度',
      dataIndex: 'offset_sale_price',
      key: 'offset_sale_price',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatUnitPriceDiv(record.offset_sale_price || 0)
      },
    },

    {
      title: '匹数',
      dataIndex: 'roll',
      key: 'roll',
      width: 50,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatRollDiv(record.roll || 0)
      },
    },
    {
      title: '结算空差',
      dataIndex: 'settle_error_weight',
      key: 'settle_error_weight',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatWeightDiv(record.settle_error_weight || 0)
      },
    },
    {
      title: '其他金额',
      dataIndex: 'other_amount',
      key: 'other_amount',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatPriceDiv(record.other_amount || 0)
      },
    },
    {
      title: '销售金额',
      dataIndex: 'sale_amount',
      key: 'sale_amount',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatPriceDiv(record.sale_amount || 0)
      },
    },
    {
      title: '日期',
      dataIndex: 'order_time',
      key: 'order_time',
      width: 80,
      render: (_: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        return formatDate(record.order_time)
      },
    },
    {
      title: '销售单',
      dataIndex: 'sale_order_no',
      key: 'sale_order_no',
      width: 100,
    },
    {
      title: '应收状态',
      dataIndex: 'audit_status_name',
      fixed: 'right',
      key: 'audit_status_name',
      width: 80,
      render: (text: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        const getAuditStatusColor = () => {
          switch (record.audit_status) {
            case 2:
              return 'green'
            case 1:
              return 'orange'
            case 3:
              return 'red'
            default:
              return 'default'
          }
        }
        return (
          <Tag color={getAuditStatusColor()}>
            {text}
          </Tag>
        )
      },
    },
    {
      title: '收款状态',
      dataIndex: 'collect_status_name',
      fixed: 'right',
      key: 'collect_status_name',
      width: 80,
      render: (text: string, record: Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem) => {
        const getCollectStatusColor = () => {
          switch (record.collect_status) {
            case 3:
              return 'green'
            case 1:
              return 'red'
            case 2:
              return 'orange'
          }
        }
        return (
          <Tag color={getCollectStatusColor()}>
            {text}
          </Tag>
        )
      },
    },
  ]
  const tableContainer = useRef<HTMLDivElement | null>(null)
  const [width, setWidth] = useState(0)
  useEffect(() => {
    setTimeout(() => {
      setWidth(tableContainer.current?.offsetWidth || 0)
    }, 100)
  }, [isMobile])
  const { mutateAsync: fetchProductAnalysis, isPending } = FetchGetCustomerInfo() // 获取产品分析数据
  const [productAnalysisData, setProductAnalysisData] = useState<Api.CustomerAnalysis.CustomerAnalysisDataResponse>() // 产品分析数据
  const fetchData1 = useCallback(async (params: Api.CustomerAnalysis.CustomerAnalysisDataRequest) => {
    try {
      const res = await fetchProductAnalysis(params)
      setProductAnalysisData(res)
    }
    catch (error) {
      console.error('获取数据失败:', error)
      messageApi.error('发生意外，请稍后再试')
    }
  }, [fetchProductAnalysis, messageApi])

  // 添加处理函数
  const handleCheckboxChange = (type: 'big' | 'plate') => {
    const newState = {
      ...checkboxStates,
      [type]: !checkboxStates[type],
    }

    // 确保至少有一个选中
    if (newState.big || newState.plate) {
      setCheckboxStates(newState)
      setPage(1) // 重置页码
      fetchData(1, newState) // 传入新的状态
    }
  }

  const handleLoadMore = useCallback(() => {
    if (loading || !hasMore)
      return
    setPage(prev => prev + 1)
    fetchData(page + 1)
  }, [loading, hasMore, page, fetchData])

  const handleLoadOrderListMore = () => {
    if (orderListLoading || !orderListHasMore)
      return
    orderListPage.current = orderListPage.current + 1
    getReceivableOrderList(orderListPage.current)
  }
  const { mutateAsync: getTeamList, data } = GetTeamList({

    onError: (error) => {
      console.error('获取团队列表失败:', error)
      messageApi.error('获取数据失败')
    },
  })
  async function getCustomerVisit() {
    await getTeamList(getFilterData({
      customer_id: customer.value,
      start_time: dayjs(dateRange?.[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange?.[1]).format('YYYY-MM-DD'),
    }))
  }
  useEffect(() => {
    getCustomerVisit()
  }, [dateRange, customer.value])
  useEffect(() => {
    if (!dateRange)
      return
    fetchData1({
      start_time: dayjs(dateRange[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dateRange[1]).format('YYYY-MM-DD'),
      customer_id: customer.value,
      sale_system_id: saleSystem.value,
      sale_user_id: saleUser.value,
    })
    orderListPage.current = 1
    setPage(1)
    fetchData(1)
    getReceivableOrderList(1)
  }, [customer.value, dateRange, saleSystem.value, saleUser.value])
  return (
    <ListFrame
      header={(
        <HeaderBox
          showUserInfo
          breadcrumbs={customBreadcrumbs}
          rightSlot={(
            <AIAnalysis
              url="/h5/v1/ai/analysis/productColorDetailCusAnalysisData"
              fixed={false}
              outputParams={{
                type: 2,
                start_time: formatDate(dateRange![0].toDateString()),
                end_time: formatDate(dateRange![1].toDateString()),
                sale_mode_iden,
                customer_id: `${customer.value}`,
                sale_system_id: saleSystem.value, // 部门id
                sale_user_id: saleUser.value, // 销售员id
                trend_start_time: trendDateRange.trend_start_time,
                trend_end_time: trendDateRange.trend_end_time,
              } as Api.ProductDetailAi.Request}
              type="output"
              text="AI报表分析"
            />
          )}
        />
      )}
      className={classNames(styles.page)}
    >
      {contextHolder}
      <div className={classNames(styles.container, isMobile ? '' : 'overflow-hidden')}>
        <div className={styles.content}>
          <div className={styles.leftContent}>
            <div className={styles.overviewCard}>
              <Row gutter={[10, 16]} className={styles.headerBox}>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="客户" fullWidth>
                    <Selects
                      fetchApi={FetchCustomerList}
                      disabled={isPending}
                      placeholder="选择客户"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={customer}
                      onChange={(value: any, _option: any) => {
                        setCustomer({ value, label: _option?.name })
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="日&nbsp;&nbsp;&nbsp;&nbsp;期" fullWidth>
                    <RangeDate
                      value={dateRange}
                      onChange={setDateRange}
                      loading={false}
                      format="YYYY-MM-DD"
                      // minDate={new Date('2010-01-01')}
                      // maxDate={new Date()}
                      allowClear={false}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="部&nbsp;&nbsp;&nbsp;&nbsp;门" fullWidth>
                    <Selects
                      fetchApi={FetchSaleSystemList}
                      disabled={isPending}
                      placeholder="选择部门"
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleSystem}
                      onChange={(value: any, _option: any) => {
                        setSaleSystem({ value, label: _option?.name })
                      }}
                    />
                  </LabelBox>
                </Col>
                <Col xs={24} sm={24} md={12} lg={8} className={styles.headerBoxItem}>
                  <LabelBox label="销售员" fullWidth>
                    <Selects
                      fetchApi={FetchEmployeeList}
                      disabled={isPending}
                      placeholder="选择销售员"
                      query={{ duty: EmployeeType.salesman }}
                      pagination={{ defaultPage: 1, defaultSize: 50 }}
                      fieldNames={{ label: 'name', value: 'id' }}
                      isSearch
                      keywordFile="name"
                      value={saleUser}
                      onChange={(value: any, _option: any) => {
                        setSaleUser({ value, label: _option?.name })
                      }}
                    />
                  </LabelBox>
                </Col>
              </Row>
              <SwiperCard cards={cards} data={productAnalysisData} loading={isPending} />
            </div>
            <div className={styles.tableCard}>
              <Flex justify="space-between" align="center" style={{ marginBottom: 0 }}>
                <Title level={4}>产品排行</Title>
                <Flex>
                  <Checkbox
                    checked={checkboxStates.big}
                    onChange={() => handleCheckboxChange('big')}
                  >
                    大货
                  </Checkbox>
                  <Checkbox
                    checked={checkboxStates.plate}
                    onChange={() => handleCheckboxChange('plate')}
                  >
                    剪板
                  </Checkbox>
                </Flex>
              </Flex>
              {/* 表格内容 */}
              <div ref={tableContainer} className="flex-1">
                <Table
                  columns={columns}
                  dataSource={productData}
                  rootClassName={styles.tableContainer}
                  loading={loading}
                  scroll={{ x: width, y: isMobile ? 300 : '100%' }}
                  style={{ width, height: '100%' }}
                  pagination={false}
                  rowKey="product_id"
                  rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!')}
                  size="small"
                  onRow={() => ({
                    onMouseEnter: () => {
                    // 当滚动到最后一行时加载更多数据
                      const scrollElement = document.querySelector('.ant-table-body')
                      if (!scrollElement)
                        return

                      const { scrollTop, scrollHeight, clientHeight } = scrollElement
                      if (scrollHeight - scrollTop <= clientHeight + 50 && !loading && hasMore) {
                        handleLoadMore()
                      }
                    },
                  })}
                />
              </div>
            </div>
            <div className={classNames(styles.tableCard, 'overflow-hidden')}>
              <Title level={4}>
                交易记录
              </Title>
              {/* 表格内容 */}
              <div className="flex-1 overflow-hidden">
                <Table<Api.GetReceivableOrderList.DataAnalysisReceivableOrderItem>
                  columns={columns1}
                  dataSource={orderList}
                  rootClassName={styles.tableContainer}
                  loading={loading}
                  scroll={{ x: width, y: isMobile ? 300 : '90%' }}
                  style={{ width, height: '100%' }}
                  pagination={false}
                  rowKey={record => record.index ? `${record.index}` : (record.order_id ? `${record.order_id}_${record.order_no || ''}` : `${record.product_code}_${record.product_name}_${record.product_color_code}_${record.product_color_name}_${record.dyelot_number || ''}`)}
                  rowClassName={(_, index) => (index % 2 === 0 ? 'bg-white!' : 'bg-gray-50!')}
                  size="small"
                  onRow={() => ({
                    onMouseEnter: () => {
                    // 当滚动到最后一行时加载更多数据
                      const scrollElement = document.querySelector('.ant-table-body')
                      if (!scrollElement)
                        return

                      const { scrollTop, scrollHeight, clientHeight } = scrollElement
                      if (scrollHeight - scrollTop <= clientHeight + 50 && !orderListLoading && orderListHasMore) {
                        handleLoadOrderListMore()
                      }
                    },
                  })}
                />
              </div>
            </div>
          </div>
          <div className={classNames(styles.rightContent, isMobile ? '!min-h-[unset]' : '')}>
            <div className={classNames(styles.card)}>
              <TrendBox
                chartHeight={isMobile ? undefined : '100%'}
                onChangeType={handleChangeType}
                dateRange={dateRange}
                saleSystemId={saleSystem.value}
                saleUserId={saleUser.value}
                customerId={customer.value}
              />
            </div>
            <div className={classNames(styles.card, 'flex-1 flex flex-col overflow-hidden', isMobile ? 'max-h-[600px]' : '')}>
              <Title level={4}>拜访记录</Title>
              <div className="flex-1 p-2 overflow-auto">
                {data?.customer_follow_record_detail_data && data.customer_follow_record_detail_data.length > 0
                  ? (
                      <Timeline
                        items={data.customer_follow_record_detail_data.map((item) => {
                          return {
                            dot: <ClockCircleOutlined style={{ fontSize: '16px' }} />,
                            children: <TimelineItem item={item} imageList={item.attachment_url?.filter(item => item) || []}></TimelineItem>,
                          }
                        })}
                      />
                    )
                  : <Empty />}
              </div>
            </div>
          </div>
        </div>
      </div>
    </ListFrame>
  )
}
