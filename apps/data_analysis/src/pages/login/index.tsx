import type { AppDispatch, RootState } from '@/store'
import type { FormProps } from 'antd'
import { REG_PWD } from '@/constants/reg'
import { useRouterPush } from '@/hooks/routerPush'
import LayoutFooter from '@/layout/components/footer'
import { basePrefix } from '@/router'
import { FetchInformation } from '@/service/api'
import { FetchScanCodeLogin } from '@/service/api/wecom'
import { loginAsync, setToken, setUserInfo, setWecomInfo } from '@/store/slice/auth'
import { isBuild, isPhoneValid } from '@/utils'
import { useMessage } from '@/utils/message'

import { LoadingOutlined } from '@ant-design/icons'
import WecomLogin, { WWLoginType } from '@ly/wecom'
import { Button, Form, Input, Modal } from 'antd'
import classNames from 'classnames'
import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate, useSearchParams } from 'react-router-dom'
import styles from './index.module.scss'

// 免登录路径
// https://hcscmtest.zzfzyc.com/qywx_workspace/oauth2?appid=wpr_caCgAAD6gDyRB6UURymefIKEn0zg&agentid=1000030&redirectUri=dashboard
export function Component() {
  const { showMessage, contextHolder } = useMessage()
  const dispatch = useDispatch<AppDispatch>()
  const [searchParams] = useSearchParams({
    appid: '', // wpr_caCgAAgbJAAsmdDoRDL0htN6dzwQ
    agentid: '', // 1000009
    redirectUri: ``, // dashboard
  })
  const corp_id = searchParams.get('appid') || ''
  const agent_id = searchParams.get('agentid') || ''
  console.log('redirectUri', corp_id, agent_id, searchParams.get('redirectUri'))
  const { mutateAsync: asyncFetchInformation } = FetchInformation()
  const { mutate: scanCodeLogin } = FetchScanCodeLogin({
    onSuccess: async (data) => {
      dispatch(setToken(data.token))
      console.log('scanCodeLogin token', data.token)
      const userInfo = await asyncFetchInformation()
      dispatch(setUserInfo(userInfo))
      showMessage.success('登录成功')
      setTimeout(() => {
        const redirectPath = searchParams.get('redirectUri') || '/dashboard'
        if (redirectPath.includes(`${basePrefix}`)) {
          window.location.replace(`${window.location.origin}/${basePrefix}`)
          return
        }
        // 使用 window.location.replace 进行重定向
        window.location.replace(`${window.location.origin}/${basePrefix}${redirectPath}`)
        // navigate(`${searchParams.get('redirectUri') || `/dashboard`}`, {
        //   replace: true,
        // })
        // window.location.href = `${window.location.origin}/${basePrefix}/${searchParams.get('redirectUri') || `dashboard`}`
        // navigate('/dashboard')
      }, 500)
    },
    onError: (error) => {
      console.error(error)
      showMessage.error(`登录失败: ${error.message}`)
    },
  })
  const [isWecomLogin, setIsWecomLogin] = useState(false)
  const wecomLogin = useRef<WecomLogin | null>(null)
  const isPending = useSelector((state: RootState) => state.auth.isPending)
  // const { mutate: login, isPending } = FetchLogin({
  //   onSuccess: (data) => {
  //     showMessage.success('登录成功')
  //     setTimeout(() => {
  //       navigate('/dashboard')
  //     }, 500)
  //   },
  //   onError: (error) => {
  //     // 登录失败的处理
  //     console.error('登录失败:', error)
  //     showMessage.error('登录失败')
  //   }
  // })
  const handleLogin: FormProps<FieldType>['onFinish'] = async (values) => {
    try {
      await dispatch(loginAsync({
        phone: values.phone!,
        password: values.password!,
      })).unwrap()

      showMessage.success('登录成功')
      setTimeout(() => {
        const redirectPath = searchParams.get('redirectUri') || '/dashboard'
        if (redirectPath.includes(`${basePrefix}`)) {
          window.location.replace(`${window.location.origin}/${basePrefix}`)
          return
        }
        // 使用 window.location.replace 进行重定向
        window.location.replace(`${window.location.origin}/${basePrefix}${redirectPath}`)
        // navigate(`${searchParams.get('redirectUri') || `/dashboard`}`, {
        //   replace: true,
        // })

        // if(searchParams.get('redirectUri')?.includes(`/${basePrefix}`)){
        //   window.location.href = `${window.location.origin}${searchParams.get('redirectUri')}`
        //   return
        // }
        // window.location.href = `${window.location.origin}/${basePrefix}/${searchParams.get('redirectUri') || `dashboard`}`
        // navigate('/dashboard')
      }, 500)
    }
    catch (_error) {
      showMessage.error('登录失败')
    }
  }
  useEffect(() => {
    const code = searchParams.get('code')
    const corp_id = searchParams.get('appid') || ''
    const agent_id = searchParams.get('agentid') || ''
    dispatch(setWecomInfo({ corpId: corp_id, agentId: agent_id }))
    console.log('code', code)
    if (code) {
      scanCodeLogin({ code, corp_id, agent_id })
    }
  }, [searchParams])
  useEffect(() => {
    if (isWecomLogin) {
      let url = searchParams.get('redirectUri')
      if (searchParams.get('redirectUri')?.includes(`/${basePrefix}`)) {
        url = `${window.location.origin}${searchParams.get('redirectUri')}`
      }
      else {
        url = `${window.location.origin}/${basePrefix}/${searchParams.get('redirectUri') || `/dashboard`}`
      }
      wecomLogin.current = new WecomLogin({
        el: '#wecom-login',
        appid: corp_id,
        agentid: agent_id,
        redirectUri: url,
        loginType: WWLoginType.corpApp,
        onSuccess: (code) => {
          scanCodeLogin({ code, corp_id, agent_id })
        },
        onFail: (error) => {
          console.error('error', error)
          showMessage.error(`登录失败: ${error.message}`)
        },
        onCheck: (isWeComLogin) => {
          if (isWeComLogin) {
            setIsWecomLogin(true)
          }
        },
      })
      wecomLogin.current?.mount()
    }
    return () => {
      wecomLogin.current?.unmount()
    }
  }, [isWecomLogin])
  interface FieldType {
    phone?: string
    password?: string
  }
  return (
    <>
      {contextHolder}
      <div className={styles.wrapper}>
        <div className={styles.loginCard}>
          <h1 className={styles.title}>欢迎来到工作台</h1>
          <div className={styles.tabs}>
            <span
              className={classNames(styles.tab, !isWecomLogin && styles.active)}
              onClick={() => setIsWecomLogin(false)}
            >
              密码登录
            </span>
            <span
              className={classNames(styles.tab, isWecomLogin && styles.active)}
              onClick={() => setIsWecomLogin(true)}
            >
              企业微信登录
            </span>
          </div>

          <div className={styles.content}>
            {!isWecomLogin
              ? (
                  <Form className={styles.form} onFinish={handleLogin}>
                    <Form.Item<FieldType>
                      name="phone"
                      rules={[
                        { required: true, message: '请输入手机号!' },
                        {
                          validator: (_, value) =>
                            isPhoneValid(value)
                              ? Promise.resolve()
                              : Promise.reject(new Error('请输入正确的手机号!')),
                        },
                      ]}
                    >
                      <Input className={styles.input} placeholder="请输入手机号" />
                    </Form.Item>
                    <Form.Item<FieldType>
                      name="password"
                      rules={[
                        { required: true, message: '请输入密码!' },
                        {
                          validator: (_, value) =>
                            REG_PWD.test(value)
                              ? Promise.resolve()
                              : Promise.reject(new Error('请输入6-18位密码!')),
                        },
                      ]}
                    >
                      <Input.Password className={styles.input} placeholder="请输入密码" />
                    </Form.Item>
                    <Form.Item>
                      <Button
                        htmlType="submit"
                        className={styles.button}
                        disabled={isPending}
                      >
                        {isPending ? <LoadingOutlined spin /> : '登录'}
                      </Button>
                    </Form.Item>
                  </Form>
                )
              : (
                  <div id="wecom-login" className={styles.wecomLogin}></div>
                )}
          </div>

        </div>
      </div>
      {isBuild() ? <LayoutFooter /> : null}
    </>
  )
}
