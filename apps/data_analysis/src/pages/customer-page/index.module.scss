body{
  background: #f7f7f7;
}
.container{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.nav_container{
  background: #ffffff;
}

.searchBox {
  position: sticky;
  top: 0;
  width: 100%;
  height: 42px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  padding-left: 10px;
  .search {
    flex: 1 1 auto;
  }
  .addNew {
    flex-shrink: 0;
    margin-left: 12px;
    margin-right: 12px;
  }
}
  .goBack{
    width: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
  }


  .listBox {
    position: relative;
    overflow: scroll;
    flex: 1 1 auto;
    background: #f7f7f7;
  }

  .itemBox {
    margin-left: 12px;
    margin-right: 12px;
    padding: 0 23px;
    height: 50px;
    background: #ffffff;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 12px;
    box-sizing: border-box;

    .cussName {
      flex: 1;
      font-size: 16px;
      font-weight: 500;
      color: #000000;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .platform_source_name {
      flex: 1;
    }

    .phone {
      flex: 1;
      font-size: 16px;
      color: #000000;
    }

    .woker {
      flex: 1;
      font-size: 16px;
      color: #000000;
      text-align: right;
    }
  }

  .acticveitemBox {
    border: 1px solid #337fff;
  }
.platform_name {
  background: #ecf2ff;
  border-radius: 6px;
  margin-left: 10px;
  font-size: 12px;
  border: none !important;
  text-align: center;
  color: #007aff;
  padding: 2px 5px;
}
