import ListFrame from '@/components/listFrame'
import { FetchCustomerList } from '@/service/api/customer'
import { useMessage } from '@/utils/message'
import { getFilterData } from '@ly/utils'
import { useUnmount } from 'ahooks'
import { Button, InfiniteScroll, NavBar, PullToRefresh, SearchBar, Tag } from 'antd-mobile'
import classnames from 'classnames'
import { useEffectOnActive, useKeepAliveRef } from 'keepalive-for-react'
import React, { useCallback, useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

const defaultSize = 20

export function Component() {
  const navigate = useNavigate()
  const location = useLocation()
  const isFirst = useRef(true)
  const [height, setHeight] = useState(0)
  const aliveRef = useKeepAliveRef()
  const { showMessage, contextHolder } = useMessage()

  useEffect(() => {
    const listElement = document.getElementById('list')
    if (listElement) {
      setHeight(listElement.getBoundingClientRect().height)
    }
  }, [])

  const [search, setSearch] = useState({
    name: null,
    page: 1,
    size: defaultSize,
  })

  const [clientList, setClientList] = useState({
    list: [] as Api.Customer.Response[],
    total: 0,
  })

  const [hasMore, setHasMore] = useState(true)
  const { mutateAsync: fetchCustomerList, isPending } = FetchCustomerList({
    onError: (error) => {
      setClientList({ list: [], total: 0 })
      showMessage.error(`获取客户列表失败: ${error.message}`)
    },
  })

  const [clientObj, setClientObj] = useState({
    clientId: null,
    clientName: '',
  })

  const pageNum = useRef({ size: search.size, page: search.page })

  const getSearchData = useCallback((value) => {
    pageNum.current.page = 1
    setClientList({ list: [], total: 0 })
    setSearch(prev => ({ ...prev, name: value, size: defaultSize }))
  }, [])
  const loadMore = async () => {
    if (isPending || clientList.list.length >= clientList.total) {
      setHasMore(false)
      return
    }
    console.log('loadMore')
    pageNum.current.page++

    const payload = {
      name_or_shortname: search.name,
    }

    try {
      const res = await fetchCustomerList(getFilterData({
        ...search,
        page: pageNum.current.page,
        size: pageNum.current.size,
        ...payload,
      }))

      if (location.state?.clientId) {
        res.list = res.list.map(item => ({
          ...item,
          checked: item.id === location.state.clientId,
        }))
      }

      // 追加新数据
      setClientList(prev => ({
        ...prev,
        list: [...prev.list, ...(res?.list || [])],
        total: res?.total || 0,
      }))

      // 检查是否还有更多数据
      const hasMoreData = (res?.list?.length || 0) > 0
        && (clientList.list.length + (res?.list?.length || 0)) < (res?.total || 0)
      setHasMore(hasMoreData)
    }
    catch (e) {
      showMessage.error('获取列表失败')
      pageNum.current.page-- // 回退页码
    }
  }

  const onRefresh = async () => {
    pageNum.current.page = 1
    setSearch(prev => ({ ...prev, size: defaultSize }))
    setHasMore(true)
  }

  const getCuss = async () => {
    const payload = {
      name_or_shortname: search.name,
    }

    try {
      const res = await fetchCustomerList(getFilterData({
        ...search,
        ...payload,
      }))

      if (location.state?.clientId) {
        res.list = res.list.map(item => ({
          ...item,
          checked: item.id === location.state.clientId,
        }))
      }

      if (isFirst.current) {
        isFirst.current = false
      }

      // 覆盖数据（第一页或搜索）
      setClientList({
        list: res?.list || [],
        total: res?.total || 0,
      })

      // 检查是否还有更多数据
      const hasMoreData = (res?.list?.length || 0) > 0
        && (res?.list?.length || 0) < (res?.total || 0)
      setHasMore(hasMoreData)
    }
    catch (e) {
      showMessage.error('获取列表失败')
      setClientList({ list: [], total: 0 })
      setHasMore(false)
    }
  }

  useEffectOnActive(() => {
    if (!isFirst.current) {
      getCuss()
    }
  }, [])
  useUnmount(() => {
    aliveRef.current?.destroy()
  })

  useEffect(() => {
    if (search.name === '') {
      setSearch(prev => ({ ...prev, name: null }))
    }
    if (search.name !== '') {
      getCuss()
    }
  }, [search])

  const goBack = () => {
    navigate(-1)
    // history.back()
  }

  const selectClient = (item) => {
    setClientList(prev => ({
      ...prev,
      list: prev.list.map(it => ({
        ...it,
        checked: it.id === item.id,
      })),
    }))

    setClientObj(item)

    const backupData = {
      clientId: item.id,
      clientName: item.name,
      clientPhone: item.phone,
      ...item,
    }

    // 使用 sessionStorage 存储数据
    sessionStorage.setItem('customerVisitData', JSON.stringify(backupData))
    navigate(-1, {
      state: {
        data: backupData,
      },
    })
  }

  useEffect(() => {
    if (clientObj?.clientId !== null) {
      setClientObj(clientObj)
    }
  }, [clientObj])

  const handleAddCustomer = () => navigate('/pages/customerEditor?type=add')

  return (
    <ListFrame header={(
      <NavBar
        onBack={() => navigate(-1)}
        className={styles.nav_container}
      >
        选择客户
      </NavBar>
    )}
    customContainerClassName={styles.container}
    >
      {contextHolder}
      <div className={styles.searchBox}>
        <SearchBar
          className={styles.search}
          placeholder="请输入客户名称"
          value={search.name || ''}
          onChange={value => setSearch(prev => ({ ...prev, name: value }))}
          onClear={() => getSearchData('')}
        />
        <Button
          shape="default"
          color="primary"
          size="small"
          loading={isPending}
          className={styles.addNew}
          onClick={() => getSearchData(search.name)}
        >
          搜索
        </Button>
      </div>

      <div className={styles.listBox} id="list">
        <PullToRefresh onRefresh={onRefresh}>
          <div className={styles.clientList}>
            {clientList?.list?.map((item, index) => (
              <div
                key={index}
                className={classnames(styles.itemBox, {
                  [styles.acticveitemBox]: item.checked,
                })}
                onClick={() => selectClient(item)}
              >
                <div className={styles.cussName}>
                  {item.name}
                </div>
                <div className={styles.phone}>{item.phone}</div>
                <div className={styles.worker}>{item.seller_name}</div>
              </div>
            ))}
          </div>
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
        </PullToRefresh>
      </div>
    </ListFrame>
  )
}
