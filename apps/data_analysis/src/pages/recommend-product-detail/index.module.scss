@import '@/styles/index.scss';
.title {
  padding: 12px 16px;
  background-color: var(--adm-color-background);
  font-size: 14px;
  color: var(--adm-color-text);
  margin-top: 12px;
}

.children {
  display: inline-block;
}
.nav_container {
  background-color: var(--adm-color-primary);
  color: white;
  flex-shrink: 0;
}
.container {
  height: 300px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.search {
  margin: 12px 0px;
  margin-top: 0;
}

.customerInfo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: var(--adm-color-box);
  font-size: 14px;
  color: var(--adm-color-text);
}

.productList {
  margin: 8px 0;
  :global {
    .adm-list-item {
      padding: 0;
    }
    .adm-list-item-content-main{
      padding: 6px 0px;
      font-size: 12px;
    }
  }
  .tableRow{
    display: flex;
    width: 100%;
    >div{
      @include common_ellipsis();
    }
  }
}

.tooltipWrapper {
  padding: 8px 16px;
}
