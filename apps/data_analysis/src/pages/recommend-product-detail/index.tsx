import PieChart from '@/components/echartsComponents/PieChart'
import { GetTeamDevProductDetailDataList } from '@/service/api/customerVisit'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { Card, Divider, Empty, ErrorBlock, List, NavBar, Popover, SearchBar, Space, Toast } from 'antd-mobile'
import dayjs from 'dayjs'

import { useEffectOnActive } from 'keepalive-for-react'
import { cloneDeep } from 'lodash-es'
import React, { useEffect, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

interface RouterParams {
  isTeam?: string
  startTime?: string
  endTime?: string
}

interface ProductItem {
  key: number
  id?: string
  name?: string
  weight_density?: string
  remark?: string
}

export function Component() {
  const location = useLocation()
  const params = location.state as RouterParams || {}

  const { mutateAsync: getTeamDevProductDetailDataListApi, isPending } = GetTeamDevProductDetailDataList()

  const startTime = params.startTime ? params.startTime : ''
  const endTime = params.endTime ? params.endTime : ''

  const originList = useRef<any[]>([])
  const [list, setList] = useState<any[]>([])
  const [pieChartData, setPieChartData] = useState<Array<{ name: string, value: number }>>([])

  const getData = async () => {
    try {
      const res = await getTeamDevProductDetailDataListApi({
        start_time: dayjs(startTime).format('YYYY-MM-DD'),
        end_time: dayjs(endTime).format('YYYY-MM-DD'),
      })
      originList.current = res?.list || []
      setList(cloneDeep(res?.list || []))

      if (res.summary) {
        const chartData = res.summary
          .slice(0, 10)
          .map(item => ({
            name: item.dev_product_name,
            value: item.proportion,
          }))
        console.log('chartData', chartData)
        setPieChartData(chartData)
      }
    }
    catch (error) {
      console.error('获取数据失败:', error)
      Toast.show({
        content: '获取数据失败',
        position: 'center',
      })
    }
  }

  useEffectOnActive(() => {
    getData()
  }, [])

  const handleSearch = (value: string) => {
    if (!value.trim()) {
      setList([...originList.current])
      return
    }

    const tempList = cloneDeep(originList.current)
    const filteredList = tempList.filter((item) => {
      item.dev_product_detail_list = item.dev_product_detail_list.filter((it) => {
        return it.name.includes(value) || it.remark.includes(value)
      })
      return item.dev_product_detail_list.length > 0
    })
    setList(filteredList)
  }
  const navigate = useNavigate()

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f2f2f2' }}>
      <NavBar
        onBack={() => navigate(-1)}
        className={styles.nav_container}
      >
        建议产品详情
      </NavBar>
      <Card className="mx-2 mt-2">
        <div className={styles.tooltipWrapper}>
          <Space align="center">
            <Popover
              content="客户建议产品的饼图，产品建议次数越多，字体占比越大"
              trigger="click"
              placement="right"
              mode="dark"
            >
              <QuestionCircleOutlined color="var(--adm-color-weak)" />
            </Popover>
          </Space>
        </div>

        <div className={styles.container}>
          <PieChart
            style={{ width: 300, height: 300 }}
            data={pieChartData}
            loading={isPending}
            type="pie"
            labelPosition="outside"
          />
        </div>
      </Card>

      <Card className="mt-2 mx-2">
        <SearchBar
          placeholder="请输入关键词"
          className={styles.search}
          onChange={handleSearch}
        />

        {list?.map((item, index) => (
          <div key={index}>
            <div className={styles.customerInfo}>
              <span>{item.contacts}</span>
              <span>
                {item.biz_unit_name}
                {' '}
                {item.phone}
              </span>
              <span>{item.sale_user_name}</span>
            </div>

            <List className={styles.productList}>
              <List.Item>
                <div className={styles.tableRow}>
                  <div style={{ width: '10%', textAlign: 'center' }}>序号</div>
                  <div style={{ width: '30%', textAlign: 'center' }}>面料</div>
                  <div style={{ width: '30%', textAlign: 'center' }}>克重</div>
                  <div style={{ width: '30%', textAlign: 'center' }}>备注</div>
                </div>
              </List.Item>
              {item.dev_product_detail_list?.length > 0
                ? (
                    item.dev_product_detail_list.map((product: ProductItem, idx: number) => (
                      <List.Item key={idx}>
                        <div className={styles.tableRow}>
                          <div style={{ width: '10%', textAlign: 'center' }}>{idx + 1}</div>
                          <div style={{ width: '30%', textAlign: 'center' }}>
                            {product.name}
                          </div>
                          <div style={{ width: '30%', textAlign: 'center' }}>{product.weight_density}</div>
                          <div style={{ width: '30%', textAlign: 'center' }}>{product.remark}</div>
                        </div>
                      </List.Item>
                    ))
                  )
                : (
                    <ErrorBlock status="empty" />
                  )}
            </List>
            {index < list.length - 1 && <Divider />}
          </div>
        ))}
      </Card>
    </div>
  )
}
