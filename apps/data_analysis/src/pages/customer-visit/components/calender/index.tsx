import type { PickerDate } from 'antd-mobile/es/components/date-picker/util'
import type { Dayjs } from 'dayjs'
import type { AtCalendarProps, AtCalendarPropsWithDefaults, AtCalendarState, Calendar } from './types/calendar'
import { DownOutlined } from '@ant-design/icons'
import { getFilterData } from '@ly/utils'
import classnames from 'classnames'
import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'
import AtCalendarBody from './body/index'
import AtCalendarController from './controller/index'
import './index.scss'

interface extraType {
  customMark?: boolean
  isDrawer?: boolean
  isOpen?: boolean
}

function AtCalendar(props: AtCalendarProps & extraType) {
  const {
    validDates = [],
    marks = [],
    isSwiper = true,
    hideArrow = false,
    isVertical = false,
    minDate,
    maxDate,
    className,
    selectedDates = [],
    isMultiSelect = false,
    format = 'YYYY/MM/DD',
    currentDate = Date.now(),
    monthFormat = 'YYYY年MM月',
  } = props as AtCalendarPropsWithDefaults

  const [state, setState] = useState<AtCalendarState>({
    generateDate: Date.now(),
    selectedDate: {
      start: new Date(),
    },
  })
  const [isOpen, setOpen] = useState(props.isOpen)
  const getSelectedDate = (
    start: number,
    end?: number,
  ): Calendar.SelectedDate => {
    const stateValue: Calendar.SelectedDate = {
      start,
      end: start,
    }

    if (typeof end !== 'undefined') {
      stateValue.end = end
    }

    return stateValue
  }
  const triggerChangeDate = (value: Dayjs): void => {
    const { format } = props

    if (typeof props.onMonthChange !== 'function') {
      return
    }

    props.onMonthChange(value.format(format))
  }
  const getSingleSelectdState = (value: Dayjs): Partial<AtCalendarState> => {
    const { generateDate } = state

    const stateValue: Partial<AtCalendarState> = {
      selectedDate: getSelectedDate(value.valueOf()),
      generateDate,
    }
    const interval = isOpen ? 'month' : 'week'

    const dayjsGenerateDate: Dayjs = value.startOf(interval)
    const generateDateValue: number = dayjsGenerateDate.valueOf()
    if (generateDateValue !== generateDate) {
      triggerChangeDate(dayjsGenerateDate)
      stateValue.generateDate = generateDateValue
    }

    return stateValue
  }

  const getMultiSelectedState = (
    value: Dayjs,
  ): Pick<AtCalendarState, 'selectedDate'> => {
    const { selectedDate } = state
    const { end, start } = selectedDate

    const valueUnix: number = value.valueOf()
    const tempstate: Pick<AtCalendarState, 'selectedDate'> = {
      selectedDate,
    }
    if (end) {
      tempstate.selectedDate = getSelectedDate(valueUnix, 0)
    }
    else {
      tempstate.selectedDate.end = Math.max(valueUnix, +start)
      tempstate.selectedDate.start = Math.min(valueUnix, +start)
    }

    return tempstate
  }

  const getInitializeState = (
    currentDate: Calendar.DateArg | Calendar.SelectedDate,
    isMultiSelect?: boolean,
  ): AtCalendarState => {
    let end: number
    let start: number
    let generateDateValue: number
    const interval = isOpen ? 'month' : 'week'

    if (!currentDate) {
      const dayjsStart = dayjs()
      start = dayjsStart.startOf('day').valueOf()
      generateDateValue = dayjsStart.startOf(interval).valueOf()
      return {
        generateDate: generateDateValue,
        selectedDate: {
          start,
        },
      }
    }

    if (isMultiSelect) {
      const { start: cStart, end: cEnd } = currentDate as Calendar.SelectedDate

      const dayjsStart = dayjs(cStart)

      start = dayjsStart.startOf('day').valueOf()
      generateDateValue = dayjsStart.startOf(interval).valueOf()

      end = cEnd ? dayjs(cEnd).startOf('day').valueOf() : start
    }
    else {
      const dayjsStart = dayjs(currentDate as Calendar.DateArg)
      start = dayjsStart.startOf('day').valueOf()
      generateDateValue = dayjsStart.startOf(interval).valueOf()

      end = start
    }

    return {
      generateDate: generateDateValue,
      selectedDate: getSelectedDate(start, end),
    }
  }

  const setMonth = (vectorCount: number): void => {
    const { format } = props
    const { generateDate } = state
    // 间隔 月 周
    const interval = isOpen ? 'month' : 'week'
    const _generateDate: Dayjs = dayjs(generateDate).add(vectorCount, interval)
    setState(prev => ({
      ...prev,
      // selectedDate: getSelectedDate(_generateDate.valueOf()),
      generateDate: _generateDate.valueOf(),
    }))

    if (vectorCount && typeof props.onMonthChange === 'function') {
      props.onMonthChange(_generateDate.format(format))
    }
  }

  const handleClickPreMonth = (isMinMonth?: boolean): void => {
    if (isMinMonth === true) {
      return
    }

    setMonth(-1)

    if (typeof props.onClickPreMonth === 'function') {
      props.onClickPreMonth()
    }
  }

  const handleClickNextMonth = (isMaxMonth?: boolean): void => {
    if (isMaxMonth === true) {
      return
    }

    setMonth(1)

    if (typeof props.onClickNextMonth === 'function') {
      props.onClickNextMonth()
    }
  }

  // picker 选择时间改变时触发
  const handleSelectDate = (value: PickerDate): void => {
    const _generateDate: Dayjs = dayjs(value)
    const _generateDateValue: number = _generateDate.valueOf()

    if (state.generateDate === _generateDateValue) {
      return
    }

    triggerChangeDate(_generateDate)
    setState(e => ({
      ...e,
      generateDate: _generateDateValue,
    }))
  }
  const handleSelectedDate = ({ state: newStateValue, item }): void => {
    if (typeof props.onSelectDate === 'function') {
      const info: Calendar.SelectedDate = {
        start: dayjs(newStateValue.start).format(props.format),
      }

      if (newStateValue.end) {
        info.end = dayjs(newStateValue.end).format(props.format)
      }

      props.onSelectDate({
        value: info,
        ...getFilterData(item, ['value']),
      })
    }
  }
  const handleDayClick = (item: Calendar.Item): void => {
    const { isMultiSelect } = props
    const { isDisabled, value } = item

    if (isDisabled) {
      return
    }

    const dayjsDate: Dayjs = dayjs(value)

    let stateValue: Partial<AtCalendarState> = {}

    if (isMultiSelect) {
      stateValue = getMultiSelectedState(dayjsDate)
    }
    else {
      stateValue = getSingleSelectdState(dayjsDate)
    }
    const newState = { ...state, ...stateValue } as AtCalendarState
    setState(newState)
    Promise.resolve().then(() => {
      handleSelectedDate({ state: newState.selectedDate, item })
    })

    if (typeof props.onDayClick === 'function') {
      props.onDayClick({ value: item.value })
    }
  }

  const handleDayLongClick = (item: Calendar.Item): void => {
    if (typeof props.onDayLongClick === 'function') {
      props.onDayLongClick({ value: item.value })
    }
  }

  const handleTriggerShowMore = () => {
    // 收起时聚焦到当前选择的日期
    if (isOpen) {
      setState(e => ({
        ...e,
        generateDate: (e.selectedDate.start).valueOf() as number,
      }))
    }
    setOpen(e => !e)
  }
  useEffect(() => {
    setState(getInitializeState(currentDate, isMultiSelect))
  }, [])

  const prevProps = useRef(props)

  useEffect(() => {
    const { currentDate, isMultiSelect } = props
    if (!currentDate || currentDate === prevProps.current.currentDate) {
      return
    }

    if (isMultiSelect && prevProps.current.isMultiSelect) {
      const { start, end } = currentDate as Calendar.SelectedDate
      const { start: preStart, end: preEnd } = prevProps.current
        .currentDate as Calendar.SelectedDate

      if (start === preStart && preEnd === end) {
        return
      }
    }

    const stateValue: AtCalendarState = getInitializeState(
      currentDate,
      isMultiSelect,
    )

    setState(stateValue)
    prevProps.current = props
  }, [props.currentDate, props.isMultiSelect])
  return (
    <div className={classnames('at-calendar', className)}>
      <AtCalendarController
        minDate={minDate}
        maxDate={maxDate}
        hideArrow={hideArrow}
        monthFormat={monthFormat}
        generateDate={new Date(state.generateDate)}
        onPreMonth={handleClickPreMonth}
        onNextMonth={handleClickNextMonth}
        onSelectDate={handleSelectDate}
      />
      <AtCalendarBody
        validDates={validDates}
        marks={marks}
        format={format}
        minDate={minDate}
        maxDate={maxDate}
        isSwiper={isSwiper}
        isVertical={isVertical}
        isOpen={isOpen}
        selectedDate={state.selectedDate}
        selectedDates={selectedDates}
        generateDate={state.generateDate}
        onDayClick={handleDayClick}
        onSwipeMonth={setMonth}
        onLongClick={handleDayLongClick}
        customMark={props?.customMark}
      />
      {
        props?.isDrawer
        && (
          <div
            className={classnames('show_more', isOpen ? 'show_more-close' : 'show_more-open')}
            onClick={handleTriggerShowMore}
          >
            <DownOutlined style={{ fontSize: 12 }} />
          </div>
        )
      }
    </div>
  )
}
export default AtCalendar
