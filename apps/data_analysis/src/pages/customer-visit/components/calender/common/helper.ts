import type { Dayjs } from 'dayjs'
import type Calendar from '../types/calendar'
import dayjs from 'dayjs'
import { flow as _flow } from 'lodash-es'
import * as constant from './constant'
import plugins from './plugins'

const TOTAL = 7 * 6
const TOTAL_WEEK = 7

function getFullItem(
  item: Partial<Calendar.Item>,
  options: Calendar.GroupOptions,
  selectedDate: Calendar.SelectedDate,
  isShowStatus?: boolean,
): any {
  const target = options.marks.find(x => x.value === item.value)
  if (target) {
    (item.marks as Array<Calendar.Mark>) = [{
      value: item.value as string,
      markNode: target.markNode,
    }]
  }
  if (!isShowStatus) {
    return item
  }

  const bindedPlugins = plugins.map(fn =>
    fn.bind(null, {
      options,
      selectedDate,
    }),
  )
  return _flow(bindedPlugins)(item)
}

// 生成日历
export default function generateCalendarGroup(
  options: Calendar.GroupOptions,
): (
    generateDate: number,
    selectedDate: Calendar.SelectedDate,
    isShowStatus?: boolean,
    isOpen?: boolean,
  ) => Calendar.ListInfo<Calendar.Item> {
  return function (
    generateDate: number,
    selectedDate: Calendar.SelectedDate,
    isShowStatus?: boolean,
    isOpen?: boolean,
  ): Calendar.ListInfo<Calendar.Item> {
    const date = dayjs(generateDate)

    const { format } = options
    const interval = isOpen ? 'month' : 'week'
    const TOTAL_DAYS = isOpen ? TOTAL : TOTAL_WEEK
    // 获取生成日期的第一天 和 最后一天
    const firstDate = date.startOf(interval)
    const lastDate = date.endOf(interval)

    const preIntervalDate = date.subtract(1, interval)
    // const preMonthDate = date.subtract(1, 'month')

    const list: Calendar.List<Calendar.Item> = []

    const nowMonthDays: number = isOpen ? date.daysInMonth() : TOTAL_WEEK // 获取这个月有多少天

    const preIntervalLastDay = preIntervalDate.endOf(interval).day() // 获取上个间隔的最后一天是周几
    // const preMonthLastDay = preMonthDate.endOf('month').day() // 获取上个月最后一天是周几

    // 生成上个间隔的日期
    for (let i = 1; i <= preIntervalLastDay + 1; i++) {
      const thisDate = firstDate.subtract(i, 'day').startOf('day')

      let item = {
        marks: [],
        _value: thisDate,
        text: thisDate.date(),
        type: isOpen ? constant.TYPE_PRE_MONTH : constant.TYPE_WEEK,
        value: thisDate.format(format),
      }

      item = getFullItem(item, options, selectedDate, isShowStatus)

      list.push(item)
    }
    list.reverse()

    // 生成这个间隔的日期
    for (let i = 0; i < nowMonthDays; i++) {
      const thisDate = firstDate.add(i, 'day').startOf('day')
      let item = {
        marks: [],
        _value: thisDate,
        text: thisDate.date(),
        type: isOpen ? constant.TYPE_NOW_MONTH : constant.TYPE_WEEK,
        value: thisDate.format(format),
      }

      item = getFullItem(item, options, selectedDate, isShowStatus)

      list.push(item)
    }
    // 生成下个间隔的日期
    let i = 1
    while (list.length < TOTAL_DAYS) {
      const thisDate = lastDate.add(i++, 'day').startOf('day')
      let item = {
        marks: [],
        _value: thisDate,
        text: thisDate.date(),
        type: isOpen ? constant.TYPE_NEXT_MONTH : constant.TYPE_WEEK,
        value: thisDate.format(format),
      }

      item = getFullItem(item, options, selectedDate, isShowStatus)

      list.push(item)
    }

    return {
      list,
      value: generateDate,
    }
  }
}

export function getGenerateDate(date: Calendar.DateArg | undefined): Dayjs {
  return dayjs(date).startOf('month')
}
