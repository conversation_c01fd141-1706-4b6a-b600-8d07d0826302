/* Flex Item */
/* Calendar */
.at-calendar {
  overflow: hidden;
  /* elements */
}
.at-calendar__header .header__flex {
  display: flex;
  align-items: center;
  -webkit-box-align: center;
  height: 30px;
  color: #b8bfc6;
  text-align: center;
}
.at-calendar__header .header__flex-item {
  flex: 0 0 14.2857142857%;
  -webkit-box-flex: 0 0 14.2857142857%;
  font-size: 14px;
}
.at-calendar__list.flex {
  display: flex;
  align-items: stretch;
  -webkit-box-align: stretch;
  flex-wrap: wrap;
  color: #7c86a2;
}
.at-calendar__list.flex .flex__item {
  flex: 0 0 14.2857142857%;
  -webkit-box-flex: 0 0 14.2857142857%;
  font-size: 14px;
  text-align: center;
  position: relative;
  margin: 5px 0;
}
.at-calendar__list.flex .flex__item-container {
  align-items: center;
  -webkit-box-align: center;
  display: flex;
  height: 30px;
  margin-left: auto;
  margin-right: auto;
  border-radius: 50%;
}
.at-calendar__list.flex .flex__item-container .container-text {
  flex: 1;
  -webkit-box-flex: 1;
}
.at-calendar__list.flex .flex__item-extra {
  height: 10px;
}
.at-calendar__list.flex .flex__item-extra .extra-marks {
  line-height: 1;
}
.at-calendar__list.flex .flex__item-extra .extra-marks .mark {
  width: 6px;
  height: 6px;
  margin-right: 2px;
  display: inline-block;
  background-color: #337fff;
  border-radius: 50%;
  overflow: hidden;
}
.at-calendar__list.flex .flex__item-extra .extra-marks .mark:last-child {
  margin-right: 0;
}
.at-calendar__list.flex .flex__item-extra .extra-marks .day_label {
  margin-right: 4px;
  border-radius: 8px;
  height: 10px;
  line-height: 10px;
  display: inline-block;
  overflow: hidden;
  padding: 0 6px;
  font-size: 12px;
}
.at-calendar__list.flex .flex__item-extra .extra-marks .day_label:last-child {
  margin-right: 0;
}
.at-calendar__list.flex .flex__item--today {
  color: #337fff;
  font-weight: bolder;
}
.at-calendar__list.flex .flex__item--blur {
  color: #e1e4e7;
}
.at-calendar__list.flex .flex__item--selected {
  color: white;
  /* stylelint-disable-next-line */
}
.at-calendar__list.flex .flex__item--selected .flex__item-container {
  margin-right: 0;
  margin-left: 0;
  border-radius: unset;
  background-color: rgba(51, 127, 255, 0.7);
}
.at-calendar__list.flex .flex__item--selected-head .flex__item-container {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 50px;
  border-top-left-radius: 50px;
  margin-right: 0;
}
.at-calendar__list.flex .flex__item--selected-tail .flex__item-container {
  margin-right: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 50px;
  border-top-right-radius: 50px;
}
.at-calendar__list.flex .flex__item--selected .extra-marks .mark {
  background-color: white;
}
.at-calendar__list.flex .flex__item--selected-head.flex__item--selected-tail {
  background-color: transparent;
}
.at-calendar__list.flex .flex__item--selected-head.flex__item--selected-tail .flex__item-container {
  border-radius: 50px;
  width: 30px;
  margin: 0 auto;
  background-color: rgba(51, 127, 255, 0.7);
}
.at-calendar__controller {
  display: flex;
  align-items: center;
  -webkit-box-align: center;
  justify-content: center;
  -webkit-box-pack: center;
  margin-bottom: 10px;
}
.at-calendar__controller .controller__arrow {
  flex: 0 0 24px;
  -webkit-box-flex: 0 0 24px;
  height: 24px;
  border-radius: 12px;
  display: inline-block;
  background-size: 8px 12px;
  background-position: center;
  background-color: #f7f8fc;
  background-repeat: no-repeat;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAYCAYAAADzoH0MAAAAAXNSR0IArs4c6QAAAnFJREFUOBGVVF1rE0EUnXt3tzFtWmqjKYKfqIhVa1L8FQVRWtwnXwRhidXGDwQf81oCUQMioZRCHwNSgiD4lD9QSYVKsA8KbaW1jbamX8adnWsmMnESbYz7cs6ee8/ZnZm7y9h/Xk/Gs70TE9lOZQNFWsGx1IvDJoozxNDttNpmHOfyTssBj59PHxceP6keREDlYPvBGUMJzTD5LHuKhHtC70EEQe72atMAIoLu0MWzRPxInZnEdxZib2I37L2XEI/HsSvYd44AQrqZIW5b3J8fHR0sS/2ve5DJZIzFFexnSD262QAs+c1js45zyVU6KqIwnU5bS58x0mhGhusbaz153Sw9dW+QSr3yCdwJe4wCKlCigbAWiw7PAYDQdclrAclkxk8+iDBifr3JMq3lO86VQsVMuq549RQSU687mOcNANE+VfiFxuLd6NX3e5llD8qjskqb54E8n24mk5Yf3B6ab2auBsgGC8Q7QOJ1AS6ExrSZ12s6r57CyIi99cNgswywtkkIzDB2eSSdftmuGxp57RgfOfY38HlvRWVNqgmYsDb57sDkZK5hb1RHZQ9+U8bu37S/MtOc0zUg8G2U1yOV4WrTdcXrAqT4MDq0yokXVINEwb32pS9WOJfLmboueW0OGgtP05mj3IXTum6iuXHogDtr27an9D/eQBVijr2AiB/VvUQuePenNXZBfmhKrxEl6Hjv1vAHA2lJ1wRBcH9vf5+cH6k3DZANsei1eWCwIrm6uOf1Jsenq8v7Z4ActFJxrsBMo6gC0GAebPHq/Z6bqJoVyn/EQpGFK08MmF2B/Oj1wZKqtYzxeM5MJKY6dMNPQnnePR8FubkAAAAASUVORK5CYII=");
}
.at-calendar__controller .controller__arrow--right {
  transform: rotate(180deg);
}
.at-calendar__controller .controller__arrow--disabled {
  opacity: 0.5;
}
.at-calendar__controller .controller__info {
  flex: 0 0 auto;
  -webkit-box-flex: 0 0 auto;
  font-size: 14px;
  margin-left: 20px;
  margin-right: 20px;
}

.at-calendar-slider__main .main__body {
  display: flex;
  width: 100%;
}
.at-calendar-slider__main .main__body--animate {
  transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
}
.at-calendar-slider__main .main__body .body__slider {
  flex: 0 0 100%;
  -webkit-box-flex: 0 0 100%;
}
.at-calendar-slider__main .main__body {
  height: 300px;
  transition: height 0.3s ease-in-out;
}
.at-calendar-slider__main .main__body-close {
  height: 50px;
}

.show_more {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  height: 25px;
  font-size: 14px;
  transition: all 0.3s ease-in-out;
}
.show_more-open {
  transform: rotateZ(0deg);
}
.show_more-close {
  transform: rotateZ(-180deg);
}