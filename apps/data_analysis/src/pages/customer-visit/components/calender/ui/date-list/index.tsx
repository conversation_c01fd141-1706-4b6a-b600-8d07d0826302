import type { Calendar } from '../../types/calendar'
import classnames from 'classnames'
import { uniqueId as _uniqueId } from 'lodash-es'
import React, { useRef } from 'react'
import * as constant from '../../common/constant'

const MAP: Record<number, string> = {
  [constant.TYPE_PRE_MONTH]: 'pre',
  [constant.TYPE_NOW_MONTH]: 'now',
  [constant.TYPE_NEXT_MONTH]: 'next',
}

export interface Props {
  list: Calendar.List<Calendar.Item>

  customMark?: boolean // 自定义mark

  onClick?: (item: Calendar.Item) => void

  onLongClick?: (item: Calendar.Item) => void
}

function AtCalendarList(props: Props) {
  const handleLongPress = (item: Calendar.Item): void => {
    if (typeof props.onLongClick === 'function') {
      props.onLongClick(item)
    }
  }

  const pressTimer = useRef<NodeJS.Timeout | null>(null)
  const isLongPress = useRef(false)

  const handleMouseDown = (item: Calendar.Item) => {
    isLongPress.current = false
    pressTimer.current = setTimeout(() => {
      isLongPress.current = true
      handleLongPress(item)
    }, 500) // 500ms 长按阈值
  }

  const handleMouseUp = () => {
    if (pressTimer.current) {
      clearTimeout(pressTimer.current)
    }
  }
  const handleClick = (item: Calendar.Item): void => {
    if (typeof props.onClick === 'function') {
      props.onClick(item)
    }
  }

  const { list } = props
  if (!list || list.length === 0) {
    return null
  }

  return (
    <div className="at-calendar__list flex">
      {list.map((item: Calendar.Item, index) => {
        return (
          <div
            key={`list-item-${item.value}-${index}`}
            onClick={() => handleClick(item)}
            onMouseDown={() => handleMouseDown(item)}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            className={classnames(
              'flex__item',
              `flex__item--${MAP[item.type]}`,
              {
                'flex__item--today': item.isToday,
                'flex__item--active': item.isActive,
                'flex__item--selected': item.isSelected,
                'flex__item--selected-head': item.isSelectedHead,
                'flex__item--selected-tail': item.isSelectedTail,
                'flex__item--blur':
													item.isDisabled
													|| item.type === constant.TYPE_PRE_MONTH
													|| item.type === constant.TYPE_NEXT_MONTH,
              },
            )}
          >
            <div className="flex__item-container">
              <div className="container-text">{item.text}</div>
            </div>
            <div className="flex__item-extra extra">
              {item.marks && item.marks.length > 0
                ? (
                    <div className="extra-marks">
                      {item.marks.map((mark, key) => (
                        props?.customMark
                          ? mark.markNode
                          : (
                              <span key={key} className="mark">
                                {mark.value as React.ReactNode}
                              </span>
                            )
                      ))}
                    </div>
                  )
                : (
                    <div className="extra-marks">
                      <div className="day_label"></div>
                    </div>
                  )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
export default AtCalendarList
