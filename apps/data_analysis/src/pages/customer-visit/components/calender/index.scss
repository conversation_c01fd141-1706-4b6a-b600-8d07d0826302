@mixin display-flex {
  display: flex;
}

@mixin flex-wrap($value: nowrap) {
  flex-wrap: $value;
}

@mixin align-items($value: stretch) {
  align-items: $value;
  @if $value == flex-start {
    -webkit-box-align: start;
  } @else if $value == flex-end {
    -webkit-box-align: end;
  } @else {
    -webkit-box-align: $value;
  }
}

@mixin align-content($value: flex-start) {
  align-content: $value;
}

@mixin justify-content($value: flex-start) {
  justify-content: $value;
  @if $value == flex-start {
    -webkit-box-pack: start;
  } @else if $value == flex-end {
    -webkit-box-pack: end;
  } @else if $value == space-between {
    -webkit-box-pack: justify;
  } @else {
    -webkit-box-pack: $value;
  }
}

/* Flex Item */
@mixin flex($fg: 1, $fs: null, $fb: null) {
  flex: $fg $fs $fb;
  -webkit-box-flex: $fg;
}

@mixin flex-order($n) {
  order: $n;
  -webkit-box-ordinal-group: $n;
}

@mixin align-self($value: auto) {
  align-self: $value;
}

$color-brand: #337fff !default;
/* Calendar */
$at-calendar-day-size: 30px !default;
$at-calendar-mark-size: 6px !default;
$at-calendar-header-color: #b8bfc6 !default;
$at-calendar-main-color: $color-brand !default;
$at-calendar-day-color: #7c86a2 !default;
.at-calendar {
  overflow: hidden;

  /* elements */
  &__header {
    .header__flex {
      @include display-flex;
      @include align-items(center);

      height: 30px;
      color: $at-calendar-header-color;
      text-align: center;

      &-item {
        @include flex(0 0 calc(100% / 7));

        font-size: 14px;
      }
    }
  }

  &__list {
    &.flex {
      @include display-flex;
      @include align-items();
      @include flex-wrap(wrap);

      color: $at-calendar-day-color;

      .flex__item {
        @include flex(0 0 calc(100% / 7));

        font-size: 14px;
        text-align: center;
        position: relative;
        margin: 5px 0;

        &-container {
          @include align-items(center);
          @include display-flex;

          //width: $at-calendar-day-size;
          height: $at-calendar-day-size;
          margin-left: auto;
          margin-right: auto;
          border-radius: 50%;

          .container-text {
            @include flex;
          }
        }

        &-extra {
          height: 10px;
          .extra-marks {
            // position: absolute;
            // bottom: 5px;
            line-height: 1;
            // left: 50%;
            // transform: translateX(-50%);

            .mark {
              width: $at-calendar-mark-size;
              height: $at-calendar-mark-size;
              margin-right: 2px;
              display: inline-block;
              background-color: $at-calendar-main-color;
              border-radius: 50%;
              overflow: hidden;

              &:last-child {
                margin-right: 0;
              }
            }
          }
          .extra-marks .day_label {
            margin-right: 4px;
            border-radius: 8px;
            height: 10px;
            line-height: 10px;
            display: inline-block;
            overflow: hidden;
            padding: 0 6px;
            font-size: 12px;
            &:last-child {
              margin-right: 0;
            }
          }
        }

        &--today {
          color: $at-calendar-main-color;
          font-weight: bolder;
        }

        &--blur {
          color: #e1e4e7;
        }

        &--selected {
          color: white;
          .flex__item-container{
            margin-right: 0;
            margin-left: 0;
            border-radius: unset;
            background-color: rgba($color: $at-calendar-main-color, $alpha: 0.7);
          }

          &-head .flex__item-container {
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 50px;
            border-top-left-radius: 50px;
            margin-right: 0;
          }

          &-tail .flex__item-container{
            margin-right: 0;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
            border-bottom-right-radius: 50px;
            border-top-right-radius: 50px;
          }

          /* stylelint-disable-next-line */
          .extra-marks .mark {
            background-color: white;
          }

          &-head.flex__item--selected-tail {
            background-color: transparent;

            .flex__item-container {
              border-radius: 50px;
              width: $at-calendar-day-size;
              margin: 0 auto;
              background-color: rgba($color: $at-calendar-main-color, $alpha: 0.7);
            }
          }
        }
      }
    }
  }

  &__controller {
    @include display-flex;
    @include align-items(center);
    @include justify-content(center);

    margin-bottom: 10px;

    .controller__arrow {
      @include flex(0 0 24px);

      height: 24px;
      border-radius: 12px;
      display: inline-block;
      background-size: 8px 12px;
      background-position: center;
      background-color: #f7f8fc;
      background-repeat: no-repeat;
      background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAYCAYAAADzoH0MAAAAAXNSR0IArs4c6QAAAnFJREFUOBGVVF1rE0EUnXt3tzFtWmqjKYKfqIhVa1L8FQVRWtwnXwRhidXGDwQf81oCUQMioZRCHwNSgiD4lD9QSYVKsA8KbaW1jbamX8adnWsmMnESbYz7cs6ee8/ZnZm7y9h/Xk/Gs70TE9lOZQNFWsGx1IvDJoozxNDttNpmHOfyTssBj59PHxceP6keREDlYPvBGUMJzTD5LHuKhHtC70EEQe72atMAIoLu0MWzRPxInZnEdxZib2I37L2XEI/HsSvYd44AQrqZIW5b3J8fHR0sS/2ve5DJZIzFFexnSD262QAs+c1js45zyVU6KqIwnU5bS58x0mhGhusbaz153Sw9dW+QSr3yCdwJe4wCKlCigbAWiw7PAYDQdclrAclkxk8+iDBifr3JMq3lO86VQsVMuq549RQSU687mOcNANE+VfiFxuLd6NX3e5llD8qjskqb54E8n24mk5Yf3B6ab2auBsgGC8Q7QOJ1AS6ExrSZ12s6r57CyIi99cNgswywtkkIzDB2eSSdftmuGxp57RgfOfY38HlvRWVNqgmYsDb57sDkZK5hb1RHZQ9+U8bu37S/MtOc0zUg8G2U1yOV4WrTdcXrAqT4MDq0yokXVINEwb32pS9WOJfLmboueW0OGgtP05mj3IXTum6iuXHogDtr27an9D/eQBVijr2AiB/VvUQuePenNXZBfmhKrxEl6Hjv1vAHA2lJ1wRBcH9vf5+cH6k3DZANsei1eWCwIrm6uOf1Jsenq8v7Z4ActFJxrsBMo6gC0GAebPHq/Z6bqJoVyn/EQpGFK08MmF2B/Oj1wZKqtYzxeM5MJKY6dMNPQnnePR8FubkAAAAASUVORK5CYII=');

      &--right {
        transform: rotate(180deg);
      }

      &--disabled {
        opacity: 0.5;
      }
    }

    .controller__info {
      @include flex(0 0 auto);

      font-size: 14px;
      margin-left: 20px;
      margin-right: 20px;
    }
  }
}

.at-calendar-slider__main {
  .main__body {
    @include display-flex;

    width: 100%;

    &--animate {
      transition: transform 300ms cubic-bezier(0.36, 0.66, 0.04, 1);
    }

    .body__slider {
      @include flex(0 0 100%);
    }
  }

  .main__body {
    height: 300px;
    transition: height 0.3s ease-in-out;
    &-close {
      height: 50px;
    }
  }
}

.show_more {
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
  height: 25px;
  font-size: 14px;
  transition: all 0.3s ease-in-out;
  &-open {
    transform: rotateZ(0deg);
  }
  &-close {
    transform: rotateZ(-180deg);
  }
}
