import type { PickerDate } from 'antd-mobile/es/components/date-picker/util'
import type { Dayjs } from 'dayjs'
import type { AtCalendarControllerProps } from '../types/calendar'
import { DatePicker, Picker } from 'antd-mobile'
import classnames from 'classnames'
import dayjs from 'dayjs'
import { useState } from 'react'

function AtCalendarController(props: AtCalendarControllerProps) {
  const {
    generateDate,
    minDate,
    maxDate,
    monthFormat,
    hideArrow,
  } = props

  const dayjsDate: Dayjs = dayjs(generateDate)
  const dayjsMinDate: Date | boolean = !!minDate && dayjs(minDate).toDate()
  const dayjsMaxDate: Date | boolean = !!maxDate && dayjs(maxDate).toDate()

  const isMinMonth: boolean
      = dayjsMinDate && dayjs(dayjsMinDate).startOf('month').isSame(dayjsDate)

  const isMaxMonth: boolean
      = dayjsMaxDate && dayjs(dayjsMaxDate).startOf('month').isSame(dayjsDate)

  const minDateValue: PickerDate | undefined = dayjsMinDate || undefined
  const maxDateValue: PickerDate | undefined = dayjsMaxDate || undefined
  const [visible, setVisible] = useState(false)
  return (
    <div className="at-calendar__controller controller">
      {hideArrow
        ? null
        : (
            <div
              className={classnames('controller__arrow controller__arrow--left', {
                'controller__arrow--disabled': isMinMonth,
              })}
              onClick={() => props.onPreMonth(isMinMonth)}
            />
          )}
      <div onClick={() => setVisible(true)}>
        <DatePicker
          precision="month"
          visible={visible}
          max={maxDateValue}
          min={minDateValue}
          onClose={() => {
            setVisible(false)
          }}
          onConfirm={val => {
            props.onSelectDate(val)
          }}
          // onSelect={props.onSelectDate}
          value={new Date(dayjsDate.format('YYYY-MM'))}
        >
          {value => (
            <span className="controller__info">
              {dayjs(value).format(monthFormat)}
            </span>
          )}
        </DatePicker>
      </div>
      {hideArrow
        ? null
        : (
            <div
              className={classnames(
                'controller__arrow controller__arrow--right',
                {
                  'controller__arrow--disabled': isMaxMonth,
                },
              )}
              onClick={() => props.onNextMonth(isMaxMonth)}
            />
          )}
    </div>
  )
}
export default AtCalendarController
