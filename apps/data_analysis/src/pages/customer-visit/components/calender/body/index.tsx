import type { AtCalendarBodyListGroup, AtCalendarBodyProps, Calendar } from '../types/calendar'
import { Swiper } from 'antd-mobile'
import classnames from 'classnames'
import dayjs from 'dayjs'
import { useEffect, useRef, useState } from 'react'
import generateCalendarGroup from '../common/helper'
import AtCalendarDateList from '../ui/date-list/index'
import AtCalendarDayList from '../ui/day-list/index'
import '../index.scss'

function AtCalendarBody(props: AtCalendarBodyProps) {
  const {
    validDates,
    marks = [],
    format = 'YYYY/MM/DD',
    minDate,
    maxDate,
    generateDate = Date.now(),
    selectedDate = {
      end: Date.now(),
      start: Date.now(),
    },
    selectedDates,
    customMark = false,
    isOpen = false,
  } = props

  const currentSwiperIndex = useRef(1)
  const maxWidth = useRef(0)

  let generateFunc: (
    generateDate: number,
    selectedDate: Calendar.SelectedDate,
    isShowStatus?: boolean,
    isOpen?: boolean
  ) => Calendar.ListInfo<Calendar.Item>
  const getGroups = (
    generateDate: number,
    selectedDate: Calendar.SelectedDate,
  ): AtCalendarBodyListGroup => {
    const dayjsDate = dayjs(generateDate)
    const arr: AtCalendarBodyListGroup = []
    const interval = isOpen ? 'month' : 'week'

    const preList: Calendar.ListInfo<Calendar.Item> = generateFunc(
      dayjsDate.subtract(1, interval).valueOf(),
      selectedDate,
      false,
      isOpen,
    )

    const nowList: Calendar.ListInfo<Calendar.Item> = generateFunc(
      generateDate,
      selectedDate,
      true,
      isOpen,
    )

    const nextList: Calendar.ListInfo<Calendar.Item> = generateFunc(
      dayjsDate.add(1, interval).valueOf(),
      selectedDate,
      false,
      isOpen,
    )

    const preListIndex
				= currentSwiperIndex.current === 0 ? 2 : currentSwiperIndex.current - 1
    const nextListIndex
				= currentSwiperIndex.current === 2 ? 0 : currentSwiperIndex.current + 1

    arr[preListIndex] = preList
    arr[nextListIndex] = nextList
    arr[currentSwiperIndex.current] = nowList
    return arr
  }

  const [state, setState] = useState({
    listGroup: [] as AtCalendarBodyListGroup,
  })

  useEffect(() => {
    generateFunc = generateCalendarGroup({
      validDates,
      format,
      minDate,
      maxDate,
      marks,
      selectedDates,
    })
    setState(e => ({
      ...e,
      listGroup: getGroups(generateDate, selectedDate),
    }))
  }, [])

  useEffect(() => {
    const element = document.querySelector('.at-calendar-slider__main')
    if (element) {
      maxWidth.current = element.clientWidth
    }
  }, [])

  useEffect(() => {
    const {
      validDates,
      marks,
      format,
      minDate,
      maxDate,
      generateDate,
      selectedDate,
      selectedDates,
    } = props

    generateFunc = generateCalendarGroup({
      validDates,
      format,
      minDate,
      maxDate,
      marks,
      selectedDates,
    })
    const listGroup = getGroups(generateDate, selectedDate)
    setState(e => ({
      ...e,
      listGroup,
    }))
  }, [props])

  const handleChange = (
    index: number,
  ): void => {
    const oldIndex = currentSwiperIndex.current
    currentSwiperIndex.current = index

    // 计算真实的滑动方向
    let direction = 0
    if (oldIndex === 0 && index === 2) {
      direction = -1
    }
    else if (oldIndex === 2 && index === 0) {
      direction = 1
    }
    else {
      direction = index > oldIndex ? 1 : -1
    }

    // 直接调用 onSwipeMonth，不再累加 changeCount
    props.onSwipeMonth(direction)
  }

  // const handleAnimateFinish = (): void => {
  //   console.log('changeCount', changeCount.current, isPreMonth.current)
  //   // isPreMonth.current false 为下个月 true 为上个月
  //   if (changeCount.current > 0) {
  //     props.onSwipeMonth(
  //       isPreMonth.current ? -changeCount.current : changeCount.current,
  //     )
  //     changeCount.current = 0
  //   }
  // }

  // const handleSwipeTouchStart = (
  //   e: TouchEvent,
  // ): void => {
  //   console.log('handleSwipeTouchStart')
  //   const touch = e.touches[0]
  //   swipeStartPoint.current = props.isVertical ? touch.clientY : touch.clientX
  // }

  // const handleSwipeTouchEnd = (
  //   e: TouchEvent,
  // ): void => {
  //   const touch = e.changedTouches[0]
  //   isPreMonth.current = props.isVertical
  //     ? touch.clientY - swipeStartPoint.current > 0
  //     : touch.clientX - swipeStartPoint.current > 0
  // }

  const { isSwiper } = props
  const { listGroup } = state

  if (!isSwiper) {
    return (
      <div
        className={classnames(
          'main',
          'at-calendar-slider__main',
        )}
      >
        <AtCalendarDayList />
        <div className="main__body body">
          <div className="body__slider body__slider--now">
            <AtCalendarDateList
              list={listGroup[1].list}
              onClick={props.onDayClick}
              onLongClick={props.onLongClick}
              customMark={customMark}
            />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className={classnames(
        'main',
        'at-calendar-slider__main',
      )}
    >
      <AtCalendarDayList />
      <Swiper
        loop
        defaultIndex={1}
        indicator={() => null}
        className={classnames('main__body', props.isOpen ? '' : 'main__body-close')}
        onIndexChange={handleChange}
        direction={props.isVertical ? 'vertical' : 'horizontal'}
        // onAnimationEnd={handleAnimateFinish}
        // onTouchEnd={handleSwipeTouchEnd}
        // onTouchStart={handleSwipeTouchStart}
      >
        {listGroup.map((item, key) => (
          <Swiper.Item key={key}>
            <AtCalendarDateList
              list={item.list}
              onClick={props.onDayClick}
              onLongClick={props.onLongClick}
              customMark={customMark}
            />
          </Swiper.Item>
        ))}
      </Swiper>

    </div>
  )
}
export default AtCalendarBody
