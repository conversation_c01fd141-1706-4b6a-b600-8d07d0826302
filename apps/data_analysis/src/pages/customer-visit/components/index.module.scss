@import '@/styles/index.scss';
.visit_record_item {
  padding: 0 !important;
  padding-bottom: 12px;

  .container {
    padding-bottom: 0;
  }

  .item_info {
    display: flex;
    flex-flow: row nowrap;
    margin-bottom: 10px;

    .user_profile {
      flex-shrink: 0;
      display: flex;
      flex-flow: row nowrap;
      justify-content: center;
      align-items: center;
      color: white;
      width: 42px;
      height: 42px;
      border-radius: 50%;
      border: 1px solid #4581ff;
      margin-right: 12px;
      background: linear-gradient(337deg, #7bb7ff 0%, #4581ff 100%);
    }

    .info {
      flex: 1 1 auto;

      .info_top {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        margin-bottom: 10px;
        justify-content: space-between;
        font-size: 12px;

        .delete {
          flex-shrink: 0;
          margin-left: 12px;
          color: #5192ff;
          @include active();
        }
      }

      .item_tag_list {
        display: flex;
        flex-flow: row wrap;
        align-items: center;
      }

      .item_tag {
        padding: 4px 4px;
        margin: 0 4px 4px 0;
        background-color: #ecf2ff;
        color: #528aff;
        border-color: transparent;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .name {
      display: flex;
      // font-size: 16px;
      color: #444444;
      font-weight: 500;
    }

    .time {
      font-size: 16px;
      font-weight: 400;
      color: #373737;
      line-height: 12px;
    }
  }

  .product {
    padding: 10px 10px;
    background-color: #f6f7fb;
    border-radius: 16px;

    .product_item {
      margin-bottom: 10px;

      &:last-child {
        margin-bottom: 0;
      }

      .product_title {
        font-size: 16px;
        font-weight: 500;
        color: #313131;
        margin-bottom: 10px;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
      }

      .product_feedback {
        font-size: 16px;
        font-weight: 400;
        color: #727374;
        line-height: 1.4;
      }

      .new_product_feedback {
        margin-top: 10px;
        overflow: hidden;
        font-size: 14px;
        font-weight: 500;
        line-height: 1;
        color: #f56730;
        white-space: pre-wrap;
      }
      .remark{
        color: #ccc;
      }
      .new {
        float: left;
        background-color: #f56730;
        font-size: 10px;
        padding: 3px 6px;
        border-radius: 10px;
        color: white;
        margin-right: 10px;
      }

      .match{
        @extend .new;
        background-color: #c280ff;
      }
    }
  }
}

.imageList{
  margin-top: 10px;
}
.footer {
  // display: flex;
  // flex-flow: row nowrap;
  // justify-content: flex-start;
  font-size: 12px;
  margin-top: 18px;
  font-weight: 400;
  color: #afafaf;
  line-height: 30px;
  width: 100%;
  line-height: 1.5;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex: 1 1 auto;

  .time {
    float: right;
    margin-right: 12px;
  }

  .addressBody {
    // flex: 1;
  }
}

.icon {
  display: inline-block;
  float: left;
}

.flex {
  display: flex;
}
