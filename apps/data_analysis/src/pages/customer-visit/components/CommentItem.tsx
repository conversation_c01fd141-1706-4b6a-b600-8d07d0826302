import dayjs from 'dayjs'
import styles from './CommentItem.module.scss'

interface CommentItemProps {
  name?: string
  reply?: string
  createTime?: string
}

function CommentItem(props: CommentItemProps) {
  const { name, reply } = props

  console.log('CommentItem createTime', props.createTime)

  return (
    <div className={styles.commentItem}>
      <div className={styles.container}>
        <div className={styles.name}>
          {name}
          :
          {' '}
        </div>

        <div className={styles.reply}>

          <span>
            {reply}
          </span>
          <span className={styles.time}>{dayjs(props.createTime).fromNow()}</span>

        </div>
      </div>
    </div>
  )
}

export default CommentItem
