import type { ImageUploadItem } from 'antd-mobile'
import { isBuild } from '@/utils'
import { formatUrl } from '@/utils/format'
import uploadCDNImg, { UploadWay } from '@/utils/uploadImage'
import { CloseCircleOutlined } from '@ant-design/icons'
import { message } from 'antd'
import { Image, ImageUploader, ImageViewer, Space } from 'antd-mobile'
import { memo, useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

// 图片列表
interface ImageParam {
  onChange?: (val: string[]) => void
  defaultList?: string[]
  onlyRead?: boolean
  limit?: number
  onProgress?: (progress: number) => void
  onProgressEnd?: () => void
}
function PictureItem({ onChange, defaultList, onlyRead = false, limit = 20, onProgress, onProgressEnd }: ImageParam) {
  // const { getWxPhoto, getChatPhoto } = useUploadCDNImg()
  const [loading, setLoading] = useState(false)
  const [imageList, setImageLise] = useState<string[]>([])
  const [uploadProgress, setUploadProgress] = useState(0)
  // 筛选内容展示
  // const [showPopup, setshowPopup] = useState(false)
  // const [List, setList] = useState<any[]>([{ id: 1, name: '手机相册', check: false }, { id: 2, name: '从微信对话选择', check: false }])
  useEffect(() => {
    setImageLise(defaultList || [])
  }, [defaultList])

  // 上传图片
  const uploadImage = async () => {
    console.log('imageList', imageList)
    if (imageList.length >= limit) {
      Taro.showToast({
        title: `最多上传${limit}张图片`,
        icon: 'none',
      })
      return
    }
    const remain = limit - imageList.length
    // setshowPopup(true)
    // Taro.showActionSheet({
    //   alertText: '选择方式',
    //   itemList: ['拍摄', '从相册选择', '从微信对话选择'],
    //   success: async (e) => {
    //     setLoading(true)
    //     setUploadProgress(0)
    //     try {
    //       console.log('success', e)
    //       // 处理上传进度
    //       const handleProgress = (progress: number) => {
    //         console.log('progress', progress)
    //         setUploadProgress(progress)
    //         onProgress?.(progress)
    //       }
    //       let list: any
    //       if (e.tapIndex === 0) {
    //         list = await getWxPhoto('after-sale', remain, ['camera'], handleProgress)
    //       }
    //       else if (e.tapIndex === 1) {
    //         list = await getWxPhoto('after-sale', remain, ['album'], handleProgress)
    //       }
    //       else {
    //         list = await getChatPhoto('after-sale', remain, handleProgress)
    //       }
    //       if (!Array.isArray(list)) {
    //         list = [list]
    //       }
    //       console.log('success list', list)
    //       const images: any[] = []
    //       list?.forEach((item) => {
    //         images.push(item.url)
    //       })
    //       setImageLise([...imageList, ...images])
    //       onChange?.([...imageList, ...images])
    //     }
    //     catch (error) {
    //       console.error('上传失败:', error)
    //       Taro.showToast({
    //         title: '上传失败',
    //         icon: 'error',
    //       })
    //     }
    //     finally {
    //       setLoading(false)
    //       setUploadProgress(0)
    //       onProgressEnd?.()
    //     }
    //   },
    //   fail: (error) => {
    //     console.log('error', error)
    //     setLoading(false)
    //     setUploadProgress(0)
    //     onProgressEnd?.()
    //   },
    // })
  }

  // 上传图片逻辑
  // const handPic = async(id) => {
  //   let list: any
  //   // let resArr = List.filter(item => {
  //   //   return item.check
  //   // })
  //   if (id == 1) {
  //     list = await getWxPhoto('after-sale', 9)
  //   }
  //   else {
  //     list = await getChatPhoto('after-sale', 9)
  //   }
  //   const images: any[] = []
  //   list?.forEach((item) => {
  //     images.push(item.url)
  //   })
  //   setImageLise([...imageList, ...images])
  //   onChange?.([...imageList, ...images])
  //   // setshowPopup(false)
  // }

  // 删除图片
  const delImage = (index) => {
    imageList.splice(index, 1)
    setImageLise(() => [...imageList])
    onChange?.([...imageList])
  }
  // // 监听上传的图片变化
  // useEffect(() => {
  //   if (!isFirst.current) {
  //     onChange?.(imageList)
  //   }
  //   isFirst.current = false
  // }, [imageList])
  const [visible, setVisible] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  // 预览图片
  const showImage = (index: number) => {
    // const list = imageList?.map((item) => {
    //   return formatUrl(item, '!w800')
    // })
    setCurrentIndex(index)
    setVisible(true)
    // Taro.previewImage({
    //   current: list[index], // 当前显示
    //   urls: list, // 需要预览的图片http链接列表
    // })
  }

  // 点击选择的方式
  // const handItem = (item) => {
  //   List.map((it) => {
  //     if (item.id == it.id) {
  //       it.check = true
  //     }
  //     else {
  //       it.check = false
  //     }
  //     return it
  //   })
  //   setList([...List])
  //   handPic(item.id)
  // }
  const [fileList, setFileList] = useState<ImageUploadItem[]>([])

  useEffect(() => {
    if (defaultList) {
      setFileList(defaultList.map(url => ({ url: formatUrl(url, '') })))
    }
  }, [defaultList])

  const handleUpload = async (file: File) => {
    console.log('file', file)
    // const defaultUploadWay = isBuild() ? UploadWay.PROD : UploadWay.TEST
    // TODO: 现在写死是测试环境，因为现在也是在测试环境测试
    const defaultUploadWay = UploadWay.TEST
    try {
      // 这里需要实现你的文件上传逻辑
      // 返回上传后的图片 URL
      const res = await uploadCDNImg(file, 'h5', file.name, defaultUploadWay)
      console.log('res', res)
      return { url: res.url } as ImageUploadItem
    }
    catch (error) {
      console.error('上传失败:', error)
      return { url: '' } as ImageUploadItem
    }
  }

  const handleChange = (items: ImageUploadItem[]) => {
    setFileList(items)
    const urls = items.map(item => item.url)
    onChange?.(urls)
  }
  return (
    <>
      {onlyRead
        ? (
            <Space wrap className={styles.image_main}>
              {imageList?.map((item, index) => (
                <Image
                  key={index}
                  fit="cover"
                  style={{ borderRadius: 4 }}
                  width={80}
                  height={80}
                  src={formatUrl(item)}
                  onClick={() => showImage(index)}
                >
                </Image>
              ))}
            </Space>
          )
        : null}
      {!onlyRead && (
        <ImageUploader
          value={fileList}
          className={styles.imageUploader}
          onChange={handleChange}
          upload={handleUpload}
          multiple
          maxCount={limit}
          showUpload={!onlyRead && fileList.length < limit}
          onCountExceed={(exceed) => {
            message.info(`最多选择 ${limit} 张图片，你多选了 ${exceed} 张`)
          }}
          style={{ '--cell-size': '80px' }}
        />
      )}
      <ImageViewer.Multi
        images={imageList?.map(item => formatUrl(item, ''))}
        visible={visible}
        defaultIndex={currentIndex}
        onClose={() => {
          setVisible(false)
        }}
      />
    </>
  )
}

export default memo<ImageParam>(PictureItem)
