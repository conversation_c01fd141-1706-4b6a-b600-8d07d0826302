import type { RootState } from '@/store'
import { AddComment } from '@/service/api/customerVisit'
import { formatUrl } from '@/utils/format'
import { useMessage } from '@/utils/message'
import { WechatOutlined } from '@ant-design/icons'
import { formatDateTime } from '@ly/utils'
import { Divider, Image, Space, Tag } from 'antd-mobile'
import { FaLocationDot } from 'react-icons/fa6'
import { TiMessages } from 'react-icons/ti'
import { useSelector } from 'react-redux'
import Comment from './Comment'
import styles from './index.module.scss'
import UploadImage from './uploadImage'

interface Props {
  item: Api.GetTeamPurchaserFollowRecordData.QywxCustomerFollowRecordDetailData
  onDelete?: (id: number) => void
  onEdit?: (id: number) => void
  rightSlot?: React.ReactNode
}

function RecordItem(props: Props) {
  const userInfo = useSelector((state: RootState) => state.auth.userInfo)
  const { showMessage, contextHolder } = useMessage()
  const { item } = props
  // const userInfo = JSON.parse(Taro.getStorageSync('userInfo') || '{}')
  const handleDelete = (id: number) => {
    props?.onDelete?.(id)
  }
  // 补充客户信息
  const handleEdit = (id: number) => {
    props?.onEdit?.(id)
  }

  const handleOpenLocation = () => {
    // 使用腾讯地图逆地址标注 地图调起
    // https://apis.map.qq.com/uri/v1/geocoder?coord=40.040452,116.273486;title:腾讯北京总部大楼;addr:北京市海淀区西北旺东路10号&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77
    const url = `https://apis.map.qq.com/uri/v1/geocoder?coord=${item.latitude},${item.longitude}&referer=OB4BZ-D4W3U-B7VVO-4PJWW-6TKDJ-WPB77`
    window.location.href = url
  }
  const { mutateAsync } = AddComment({
    onSuccess: () => {
      showMessage.success('添加成功')
    },
    onError: (error) => {
      showMessage.error(error.message)
    },
  })

  const handleCommentConfirm = async (value: string) => {
    try {
      await mutateAsync({
        comment_text: value,
        customer_follow_record_id: item.id,
      })
      const newComment = {
        comment_text: value,
        comment_time: formatDateTime(new Date(), 'YYYY-MM-DD HH:mm:ss'),
        comment_user_name: userInfo?.user_name,
        comment_user_id: userInfo?.user_id,
      }
      item.comments = !item.comments ? [newComment] : [newComment, ...item.comments]
    }
    catch (err) {
      console.error('添加失败')
    }
  }
  const has_purchaser = item.biz_unit_name || item.external_user_name

  return (
    <div className={styles.visit_record_item}>
      {contextHolder}
      <div className={styles.container}>

        <div className={styles.item_info}>
          <div className={styles.user_profile}>{item.submit_user_name?.split('')[0]}</div>
          <div className={styles.info}>
            <div className={styles.info_top}>
              <div className={styles.name}>
                <span>{item.submit_user_name}</span>
                {
                  has_purchaser
                    ? item.biz_unit_name
                      ? <span style={{ color: '#686868', marginLeft: '10px' }}>{item.biz_unit_name}</span>
                      : (
                          <div className={styles.flex}>
                            <span
                              style={{ color: '#686868', margin: '0 10px', marginRight: '4px' }}
                            >
                              {item.external_user_name}
                            </span>
                            <WechatOutlined style={{ color: '#1aad19' }} />
                            {/* <IconFont name="icon-weixindenglu" size={30} /> */}
                          </div>
                        )
                    : (
                        <span
                          style={{ color: 'red', margin: '0 10px' }}
                        >
                          无客户
                        </span>
                      )
                }
              </div>
              {
                props.rightSlot
                  ? props.rightSlot
                  : userInfo?.user_id === item.submit_user_id
                    && (
                      <div className="flex flex-row items-center">
                        {/* {
                          !has_purchaser ? <div className={styles.delete} onClick={() => handleEdit(item.id!)}>修改</div> : null
                        } */}
                        <div className={styles.delete} onClick={() => handleDelete(item.id!)}>删除</div>
                      </div>
                    )
              }
            </div>
            <div className={styles.item_tag_list}>
              {
                item.label_names?.map((label: string, labelIndex: number) => {
                  return (
                    <Tag
                      key={labelIndex}
                      className={styles.item_tag}
                      color="primary"
                    >
                      {label}
                    </Tag>
                  )
                })
              }
            </div>
          </div>
        </div>
        {/* 未添加到企业微信的原因 */}
        {
          item.not_add_corp_wechat_remark ? <div style={{ color: 'red', fontSize: '24rpx', margin: '4px 0' }}>{item.not_add_corp_wechat_remark}</div> : null
        }
        <div className={styles.product}>

          {
            item.feedbacks?.map((feedback: any, feedbackIndex: number) => {
              return (
                <div className={styles.product_item} key={feedbackIndex}>
                  {
                    feedback.feedback_title
                      ? (
                          <div className={styles.product_title}>
                            <div>
                              <TiMessages className={styles.icon} style={{ marginRight: '10px' }} />
                              {feedback.feedback_title}
                            </div>
                          </div>
                        )
                      : null
                  }
                  {/* 拜访反馈 */}
                  {
                    feedback.feedback_solution
                      ? (
                          <div className={styles.product_feedback}>
                            <TiMessages className={styles.icon} style={{ marginRight: '10px' }} />
                            {feedback.feedback_solution}
                          </div>
                        )
                      : null
                  }
                  {item.matchable_product_names && item.matchable_product_names.length > 0
                    ? (
                        <div className={styles.new_product_feedback}>
                          <div className={styles.match}>匹配</div>
                          {item.matchable_product_names?.map((it, i) => {
                            return <span key={i} style={{ float: 'left', margin: '0 4px', color: '#c280ff' }}>{it}</span>
                          })}
                        </div>
                      )
                    : null}
                  {
                    item.dev_products?.map((it, i) => {
                      return (
                        <div className={styles.new_product_feedback} key={i}>
                          <div className={styles.new}>开发</div>
                          <span style={{ float: 'left', margin: '0 4px' }}>{it.name}</span>
                          <span style={{ float: 'left', margin: '0 4px' }}>
                            {it.weight_density}
                            G
                          </span>
                          <span className={styles.remark} style={{ float: 'left', margin: '0 4px' }}>{it.remark}</span>
                        </div>
                      )
                    })
                  }

                </div>
              )
            })
          }
        </div>

        <div className={styles.imageList}>
          <UploadImage defaultList={item.attachment_url?.filter(url => url)} onlyRead />
        </div>
        <div
          className={styles.footer}
          onClick={handleOpenLocation}
        >
          <div className={styles.addressBody}>
            <FaLocationDot size={16} style={{ float: 'left', marginRight: '6px' }} />
            {item.location_address}
            <span className={styles.time}>{formatDateTime(item.create_date as string, 'YYYY-MM-DD HH:mm')}</span>
          </div>
        </div>
        <Divider direction="horizontal" style={{ margin: '10px 0' }}></Divider>
      </div>
      <Comment onConfirm={handleCommentConfirm} replyList={item.comments} />
    </div>
  )
}
export default RecordItem
