import { Divider, Input } from 'antd-mobile'
import { uniqueId as _uniqueId } from 'lodash-es'
import { useRef, useState } from 'react'
import styles from './Comment.module.scss'
import CommentItem from './CommentItem'

interface CommentProps {
  onConfirm?: (value: string) => void
  replyList?: Api.GetTeamPurchaserFollowRecordData.QywxComment[]
}

function Comment(props: CommentProps) {
  const { onConfirm, replyList } = props
  const uniqueId = _uniqueId('textBar_')

  const [value, setValue] = useState<string>('')

  const handleConfirm = () => {
    onConfirm?.(value)
    setValue('')
  }

  return (
    <>
      <div className={styles.text_bar} id={uniqueId}>

        <Input
          value={value}
          type="text"
          className={styles.input}
          placeholder="来说点什么..."
          onChange={value => setValue(value)}
          onEnterPress={handleConfirm}
        >
        </Input>
      </div>
      <div className={styles.replyList}>
        {replyList?.map((replyItem, index) => {
          return (
            <div key={index}>
              <CommentItem
                name={replyItem.comment_user_name}
                reply={replyItem.comment_text}
                createTime={replyItem.comment_time}
                key={index}
              >
              </CommentItem>
              {
                index !== replyList.length - 1
                && <Divider direction="horizontal" style={{ margin: '6px 0' }}></Divider>
              }
            </div>
          )
        })}
      </div>
    </>
  )
}
export default Comment
