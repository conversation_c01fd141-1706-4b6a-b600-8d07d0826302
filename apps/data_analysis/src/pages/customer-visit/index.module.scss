@import '@/styles/index.scss';
.bubble{
  :global{
    .adm-floating-bubble-button{
      overflow: unset !important;
      background-color: white!important;
    }
  }
}
.container{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.nav_container {
  position: sticky;
  top: 0;
  background-color: #4581ff;
  color: white;
  flex-shrink: 0;
}
.main {
  --tag-list-padding-top: 16px;
  --trigger-height: 10px;
  --swiper-layout-top: 22px;
  background: linear-gradient(to bottom, #4581ff 25%, #f8f8f8 42%);
  padding-bottom: 0;
  box-sizing: border-box;
  overflow-y: auto !important;
  height: 100%;
  

  .layout {
    margin-top: var(--swiper-layout-top);
    padding: 10px;
    margin-left: 10px;
    margin-right: 10px;
    background-color: white;
    border-radius: 10px;
    box-sizing: border-box;
  }
  .calendar_layout {
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 12px;
    background-color: white;
    padding: 10px;
    border-radius: 10px;
    box-sizing: border-box;
    .day_label {
      margin-right: 4px;
      background: #c4c4c4;
      border-radius: 8px;
      line-height: 12px;
      height: 12px;
      display: inline-block;
      overflow: hidden;
      padding: 0 6px;
      font-size: 10px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}
.swiper {
  height: 100%;
  .swiper_item {
    border-radius: 10px;
    overflow: hidden;
  }
}
.swiper_top {
  display: flex;
  flex-flow: row nowrap;
  background-color: white;
  position: relative;
  .left {
    position: relative;
    z-index: 1;
    .swiper_title {
      display: flex;
      font-size: 16px;
      font-weight: 500;
      color: #414141;
      align-items: center;
      & > text {
        margin-right: 10px;
      }
      .refresh {
        font-size: 12px;
        display: flex;
        align-items: center;
      }
    }
    .swiper_content {
      margin-top: 19px;
      font-size: 16px;
      color: #666666;
    }
  }
  .right {
    width: 213px;
    height: 125px;
    overflow: hidden;
    position: absolute;
    right: -18px;
    top: -30px;
    .swiper_image {
      width: 100%;
      height: 100%;
    }
  }
}
.tag_list {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  box-sizing: border-box;
  transition: all 0.3s ease-in-out;
  grid-gap: 16px;
  overflow: hidden;
  .tag {
    padding: 0;
    height: 56px;
    font-weight: 500;
    border-color: #f6f6f6;
    background-color: #f6f6f6;
    color: #434343;
  }

  .selected {
    border-color: #3d85ff;
    background-color: #f6faff;
    color: #4d8fff;
  }
}
.trigger_bar {
  width: 100%;
  height: --trigger-height;
  background-color: white;
  display: flex;
  flex-flow: row nowrap;
  justify-content: center;
  align-items: center;
}
.calendar_top {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  margin-bottom: 12px;
  .left {
    color: #414141;
    white-space: nowrap;
    flex-shrink: 0;
    .swiper_title {
      display: flex;
      font-size: 16px;
      font-weight: 500;
      color: #414141;
      align-items: flex-end;
    }
    .backToday {
      font-size: 12px;
      color: #4581ff;
      margin-left: 10px;
    }
  }

  .right {
    display: flex;
    flex: 1 1 auto;
    margin-left: 10px;
    flex-flow: row nowrap;
    align-items: center;
    font-size: 16px;
    color: #414141;
    justify-content: flex-end;
    .selectedPerson {
      font-size: 16px;
      @include common_ellipsis(1);
    }
  }
}

.moveBtn {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 2px solid #cde5ff;
  box-shadow: 0px 0px 10px 0px rgba(104, 180, 255, 0.7);
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  image {
    width: 100%;
    height: 100%;
  }
}
.no_bg_moveBtn {
  box-shadow: none;
  background-color: transparent;
  border: none;
  width: 130px;
  height: 130px;
}

.emptyTips {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  line-height: 40px;
  color: #c2c2c2;
  font-size: 12px;
}
.emptyIcon {
  height: unset;
}
.box{
  font-size: 12px;
  border: 1px solid #f2f2f2;
  border-radius: 8px;
  padding: 10px;
  margin-top: 15px;
  text{
    color: #4581ff;
    margin-right: 8px;
  }
  &_title{
    line-height: 2;
    gap: 8px;
  }
  &_container{
    display: flex;
    justify-content: space-between;
    align-items: center;

    &_left{
      flex: 1;
      height: 20px;
      display: flex;
      flex-flow: column nowrap;
      justify-content: center;
      align-items: center;
      border-right: 1px solid #f2f2f2;
      @include active;
    }
    &_right{
      flex: 1;
      height: 20px;
      display: flex;
      flex-flow: column nowrap;
      justify-content: center;
      align-items: center;
      @include active;

    }
    &__title{
      line-height: 2;
      width: 100%;
      text-align: center;
    }
    &__content{
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &__left{
        line-height: 2;
        flex: 1;
        text-align: center;
      }
      &__right{
        line-height: 2;
        flex: 1;
        text-align: center;
      }
    }
  }
}
