import ListFrame from '@/components/listFrame'
import { useRouterPush } from '@/hooks/routerPush'
import { DeletePurchaserFollowRecord, GetTeamList, GetTeamMonthRecordNumsList, GetTeamPurchaserFollowRecordData } from '@/service/api/customerVisit'
import { formatUrl } from '@/utils/format'
import { useMessage } from '@/utils/message'
import { PlusCircleFilled, RedoOutlined, RightOutlined } from '@ant-design/icons'
import { formatDateTime, getFilterData } from '@ly/utils'
import { Dialog, FloatingBubble, Image, InfiniteScroll, List, NavBar } from 'antd-mobile'
import classNames from 'classnames'
import dayjs from 'dayjs'
import { useEffectOnActive } from 'keepalive-for-react'
import { useEffect, useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import Calendar from './components/calender'
import RecordItem from './components/RecordItem'
import styles from './index.module.scss'

export function Component() {
  const navigate = useNavigate()
  const { showMessage, contextHolder } = useMessage()
  const [offset, setOffset] = useState({ x: -24, y: -140 })
  const tempList = useRef<any[]>([])
  const [list, setList] = useState<{
    list: Api.GetTeamPurchaserFollowRecordData.QywxCustomerFollowRecordDetailData[]
    total: number
  }>({ list: [], total: 0 })
  // 最大分页数
  const maxLimitSize = 10
  const pageNum = useRef({
    size: maxLimitSize,
    page: 1,
  })
  const [hasMore, setHasMore] = useState(true)
  const { mutateAsync: getTeamListApi, data, isPending } = GetTeamList({
    onSuccess: (data) => {
      const newList = data?.customer_follow_record_detail_data || []
      const currentPage = pageNum.current.page

      if (currentPage === 1) {
      // 首次加载或刷新
        tempList.current = newList
        setList({ list: newList, total: data?.total || 0 })
      }
      else {
      // 加载更多
        const updatedList = [...tempList.current, ...newList]
        tempList.current = updatedList
        console.log('updatedList', updatedList)
        setList({ list: updatedList, total: data?.total || 0 })
      }

      // 修改 hasMore 计算逻辑
      const totalLoaded = tempList.current.length
      const totalCount = data?.total || 0
      const shouldHaveMore = totalLoaded < totalCount && newList.length === pageNum.current.size
      setHasMore(shouldHaveMore)
    },
    onError: (error) => {
      showMessage.error(`获取团队列表失败: ${error.message}`)
      setHasMore(false)
    },
  })
  const [selectedPersonText, setSelectedPersonText] = useState('')
  const { mutateAsync: getTeamRecordDataApi, data: personRecordDataState } = GetTeamPurchaserFollowRecordData({
    onError: (error) => {
      showMessage.error(error.message)
    },
  })

  const [start, setStart] = useState(formatDateTime(dayjs(new Date()).toDate(), 'YYYY-MM-DD'))
  const prevStart = useRef(start)
  const [formData, setFormData] = useState({
    submit_user_ids: [] as { id: number, name: string }[],
  })
  const [end, setEnd] = useState(formatDateTime(dayjs(new Date()).toDate(), 'YYYY-MM-DD'))
  const prevEnd = useRef(end)

  const getTeamList = async () => {
    await getTeamListApi(getFilterData({
      start_time: start as string,
      end_time: end as string,
      submit_user_ids: formData.submit_user_ids.map(item => item.id).join(','),
      page: pageNum.current.page,
      size: pageNum.current.size,
    }))
  }
  const getTeamRecordData = async () => {
    await getTeamRecordDataApi(getFilterData({
      start_time: start as string,
      submit_user_ids: formData.submit_user_ids.map(item => item.id).join(','),
      end_time: end as string,
    }))
  }
  const shouldUpdate = useRef<boolean>(false)
  const prevMonth = useRef<string>('')
  const isFirst = useRef(true)
  const getData = async () => {
    if (!shouldUpdate.current) {
      return
    }
    // 重置分页状态
    pageNum.current.page = 1
    setHasMore(true)
    await getTeamList()
    getTeamRecordData()
    if (isFirst.current) {
      isFirst.current = false
    }
  }
  const { mutateAsync: getTeamMonthRecordNumsApi } = GetTeamMonthRecordNumsList({
    onError: (error) => {
      showMessage.error(error.message)
    },
  })
  const mockMarks = { code: 0, msg: 'success', version: 'pre 6bc0412e 2025-05-10 16:51:21', data: { list: [{ date: '2025-05-08', total_record_nums: 1, effective_record_nums: 1 }], total: 0 } }
  const [monthMarks, setMonthMarks] = useState<{
    value: string
    markNode: React.ReactNode
  }[]>(mockMarks.data.list?.map((item) => {
    return {
      value: item.date,
      markNode: (
        <div
          className={styles.day_label}
        >
          {item?.effective_record_nums}
          /
          {item?.total_record_nums}
        </div>
      ),
    }
  }))
  const getTeamMonthRecordNums = async (selectDate: string | number | Date | dayjs.Dayjs) => {
    const res = await getTeamMonthRecordNumsApi({
      submit_user_ids: formData.submit_user_ids.map(item => item.id).join(','),
      date: `${dayjs(selectDate).format('YYYY-MM-01')}`,
    })
    setMonthMarks(res.list?.map((item) => {
      return {
        value: item.date,
        markNode: (
          <div
            className={styles.day_label}
          >
            {item?.effective_record_nums}
            /
            {item?.total_record_nums}
          </div>
        ),
      }
    }))
  }
  useEffect(() => {
    // 选择不同月份
    if (prevMonth.current !== dayjs(start).format('YYYY-MM-01')) {
      prevMonth.current = dayjs(start).format('YYYY-MM-01')
      getTeamMonthRecordNums(start)

      shouldUpdate.current = true
    }
    if (prevStart.current !== start) {
      shouldUpdate.current = true
    }
    if (prevEnd.current !== end) {
      shouldUpdate.current = true
    }
    console.log('shouldUpdate', shouldUpdate.current)
    getData()
  }, [start, end, formData.submit_user_ids])
  useEffectOnActive(() => {
    if (isFirst.current) {
      return
    }
    getTeamMonthRecordNums(start)
    getData()
  }, [])
  useEffectOnActive(() => {
    setTimeout(() => {
      const employeeDataString = sessionStorage.getItem('employeeListData')
      if (employeeDataString) {
        try {
          const employeeData: Api.Employee.Response[] = JSON.parse(employeeDataString)
          const textArr = employeeData.map(item => item.name)
          let text = ''
          if (textArr.length > 0) {
            text = textArr.length >= 2 ? `${textArr[0]}、${textArr[1]}等${employeeData.length}人...` : textArr.join('、')
          }
          else {
            text = '' // 或者其他默认文本
          }

          setFormData((prev: any) => ({
            ...prev,
            submit_user_ids: employeeData,
          }))
          setSelectedPersonText(text)

          // 清理 sessionStorage 中的数据，避免下次进入页面时重复处理
          sessionStorage.removeItem('employeeListData')
        }
        catch (error) {
          console.error('Failed to parse employeeListData from sessionStorage:', error)
          // 清理可能已损坏的数据
          sessionStorage.removeItem('employeeListData')
        }
      }
    }, 0)
  }, []) // 依赖 location，当路由变化（例如返回）时触发
  const handleRefresh = () => {
    pageNum.current.page = 1
    setHasMore(true)

    getTeamList()
    tempList.current = [] // 清空临时列表
    getTeamRecordData()
  }
  const { routerPushByKey } = useRouterPush()
  const handleClickMatchProduct = () => {
    routerPushByKey(`/match-product-detail`, {
      state: {
        startTime: start,
        endTime: end,
      },
    })
  }
  const handleClickRecmProduct = () => {
    routerPushByKey(`/recommend-product-detail`, {
      state: {
        startTime: start,
        endTime: end,
      },
    })
  }
  const formatStart = formatDateTime(start, 'YYYY-MM-DD')
  const formatEnd = formatDateTime(end, 'YYYY-MM-DD')
  const handleSelectPerson = (e) => {
    e.stopPropagation()
    const selectedIdsToPass = formData.submit_user_ids
    routerPushByKey(`/employee-list`, {
      state: {
        selectedIds: encodeURIComponent(JSON.stringify(selectedIdsToPass)),
        type: 'checkbox', // 或者 'radio'，根据您的需求
      },
    })
  }
  const { mutateAsync: deleteApi } = DeletePurchaserFollowRecord({
    onSuccess: () => {
      showMessage.success('删除成功')
      shouldUpdate.current = true
      getData()
    },
    onError: (error) => {
      showMessage.error(error.message)
    },
  })
  const handleDelete = async (id: number) => {
    const result = await Dialog.confirm({
      content: '请确定删除此条拜访记录？',
    })
    if (result) {
      await deleteApi({
        customer_follow_record_id: id,
      })
    }
  }

  const handleBackToday = (e) => {
    e.stopPropagation()
    prevStart.current = start
    prevEnd.current = end
    shouldUpdate.current = true
    setStart(formatDateTime(Date.now(), 'YYYY-MM-DD'))
    setEnd(formatDateTime(Date.now(), 'YYYY-MM-DD'))
  }
  async function loadMore() {
    console.log('loadMore', hasMore)
    if (!hasMore || isPending)
      return
    try {
      pageNum.current.page += 1
      await getTeamList() // 传入true表示是加载更多
    }
    catch (e) {
      throw new Error(e.message)
    }
  }

  // 选择时间
  const onSelectDate = (event) => {
    prevStart.current = start
    prevEnd.current = end
    if (!event.value.end) {
      return
    }
    const { start: startValue, end: endValue } = event.value
    // 选择不同月份
    if (prevMonth.current !== dayjs(startValue).format('YYYY-MM-01')) {
      prevMonth.current = dayjs(startValue).format('YYYY-MM-01')

      getTeamMonthRecordNums(startValue)
    }

    if (event.marks.length) {
      shouldUpdate.current = true
    }
    else {
      shouldUpdate.current = false
      setList({ list: [], total: 0 })
    }
    setStart(formatDateTime(dayjs(startValue).toDate(), 'YYYY-MM-DD'))
    setEnd(formatDateTime(dayjs(endValue).toDate(), 'YYYY-MM-DD'))
  }

  const handleMonthChange = (e) => {
    if (prevMonth.current !== dayjs(e).format('YYYY-MM-01')) {
      prevMonth.current = dayjs(e).format('YYYY-MM-01')

      getTeamMonthRecordNums(e)
      shouldUpdate.current = true
    }
  }
  function handleAddNewVisit() {
    routerPushByKey(`/customer-visit-add`)
  }
  const handleEdit = (id: number) => {
    routerPushByKey(`/customer-visit-add`, {
      state: {
        type: 'edit',
        id,
      },
    })
  }
  return (
    <ListFrame
      header={(
        <NavBar
          onBack={() => navigate(-1)}
          className={styles.nav_container}
        >
          客户拜访
        </NavBar>
      )}
      customContainerClassName={styles.container}
      className={styles.main}
    >
      {contextHolder}
      <div
        className={styles.layout}
      >
        <div className={styles.swiper_top}>
          <div className={styles.left}>
            <div className={styles.swiper_title}>
              <span>拜访记录</span>
              <div className={styles.refresh} onClick={handleRefresh}>
                <RedoOutlined />
                刷新
              </div>
            </div>
            <div
              className={styles.swiper_content}
            >
              有效
              {data?.effective_record_nums || 0}
              {' '}
              |
              总共
              {data?.total_record_nums || 0}
            </div>
          </div>
          <div className={styles.right}>
            <Image
              className={styles.swiper_image}
              src="https://cdn.zzfzyc.com/mp/team.png"
            />
          </div>
        </div>
        <div className={classNames('flex flex-row items-center', styles.box)}>
          <div className={styles.box_container_left} onClick={handleClickMatchProduct}>
            匹配产品：
            {personRecordDataState?.matchable_product_nums || 0}
          </div>
          <div className={styles.box_container_right} onClick={handleClickRecmProduct}>
            建议产品：
            {personRecordDataState?.recommendation_product_nums || 0}
          </div>
        </div>
      </div>
      <div className={styles.calendar_layout}>
        <div className={styles.calendar_top}>
          <div className={styles.left}>
            <div
              className={styles.swiper_title}
            >
              {formatStart ? formatStart === formatEnd ? formatStart : `${formatStart} 至 ${formatEnd}` : '选择日期'}
            </div>
            <div className={styles.backToday} onClick={handleBackToday}>回到今天</div>
          </div>
          <div className={styles.right} onClick={handleSelectPerson}>
            <span
              style={selectedPersonText ? { color: '#4581ff' } : {}}
              className={styles.selectedPerson}
            >
              {selectedPersonText || '选择人员'}
            </span>
            <RightOutlined style={{ color: selectedPersonText ? '#4581ff' : '#3d3d3d' }} />
          </div>

        </div>

        <Calendar
          isDrawer
          maxDate={dayjs().toDate()}
          isOpen={false}
          format="YYYY-MM-DD"
          currentDate={{ start, end }}
          onSelectDate={onSelectDate}
          customMark
          isMultiSelect
          onMonthChange={handleMonthChange}
          marks={monthMarks}
        />

      </div>
      <List className="mt-2">
        {list?.list?.map((item, index) => (
          <List.Item key={index}>
            <RecordItem key={index} item={item} onDelete={handleDelete} onEdit={handleEdit} />
          </List.Item>
        ))}
      </List>
      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
      <FloatingBubble
        axis="xy"
        className={styles.bubble}
        style={{
          '--initial-position-bottom': '0',
          '--initial-position-right': '0',
          'overflow': 'unset !important',
          'backgroundColor': 'white',
        }}
        onClick={handleAddNewVisit}
        onOffsetChange={(offset) => {
          setOffset(offset)
        }}
        offset={offset}
      >
        <PlusCircleFilled style={{ fontSize: '30px', color: '#4581ff' }} />
        {/* <Image src={formatUrl('https://test.cdn.zzfzyc.com/mall/xinzengshouxin.png', '')} width={100} height={50} /> */}
      </FloatingBubble>
    </ListFrame>
  )
}
