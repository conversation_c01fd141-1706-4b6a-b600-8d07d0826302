import type { RootState } from '@/store'
import DateRangePicker from '@/components/dateRangePicker'
import HeaderBox from '@/components/headerBox'
import IconText from '@/components/iconText'
import PrintBtn from '@/components/printButton'
import { CollectType } from '@/enums'
import { useRouterPush } from '@/hooks/routerPush'
import { FetchQYWechatSignature, GetIDByQywxUserId } from '@/service/api/customer.ts'
import { FetchCustomerReconciliationList } from '@/service/api/customerBill'
import { PrintDataType, PrintType } from '@/service/request/type'
import { isWxWork, randomString, useMobileScreen } from '@/utils'
import { processDataOut } from '@/utils/handBinary'
import { useMessage } from '@/utils/message'
import { formatHashTag, formatPriceDiv, getFilterData } from '@ly/utils'
import { Flex, FloatButton, Skeleton } from 'antd'
import { <PERSON>lapse, Divider, Grid } from 'antd-mobile'
import classnames from 'classnames'
import dayjs from 'dayjs'
import { useCallback, useEffect, useRef, useState } from 'react'
import { CiUser } from 'react-icons/ci'
import InfiniteScroll from 'react-infinite-scroll-component'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

type IDataItem = Api.ShouldCollectOrder.CustomerReconciliationResponse
type IDataItemList = Api.ShouldCollectOrder.ShouldCollectOrderOrderDetail

function CollapseTitle({ item, currentOrderId }: { item: IDataItem, currentOrderId: number | null }) {
  return (
    <div className={classnames(styles['collapse-title'], currentOrderId === item.order_id ? styles['collapse-title-active'] : '')}>
      <Flex justify="space-between">
        <span>{item.order_no}</span>
        <span>{item.collect_type_name}</span>
      </Flex>
      <Flex justify="space-between">
        <span>{item.order_time}</span>
        <span>
          ￥
          {item.total_price?.toLocaleString()}
        </span>
      </Flex>
    </div>
  )
}
function CollapseContent({ itemList }: { itemList: IDataItemList[] }) {
  return (
    <div className={styles.collapse}>
      {itemList.map((item, index) => (
        <div key={index}>
          <div className={styles['collapse-content']}>
            <Flex justify="space-between">
              <span>{formatHashTag(item.code, item.name)}</span>
              <span>
                ¥
                {' '}
                {item.total_price?.toLocaleString()}
              </span>
            </Flex>
            <Flex justify="space-between">
              <span>{formatHashTag(item.color_code, item.color_name)}</span>
              <span>
                {item.weight}
                {' '}
                {item.measurement_unit_name}
                （
                {item.roll}
                匹）
              </span>
            </Flex>
          </div>
          <Divider />
        </div>
      ))}
    </div>
  )
}
function CollapseFooter({ item, currentOrderId }: { item: IDataItem, currentOrderId: number | null }) {
  return (
    <div className={classnames(styles.collapse, styles['collapse-footer'], currentOrderId === item.order_id ? styles['collapse-footer-active'] : '')}>
      <Flex justify="space-between">
        <span>
          折扣：￥
          {item.discount_price?.toLocaleString()}
        </span>
        <span>
          优惠：¥
          {item.remove_money?.toLocaleString()}
        </span>
        <span>
          扣款：¥
          {item.chargeback_money?.toLocaleString()}
        </span>
      </Flex>
      <Flex justify="space-between">
        <span>
          实收：￥
          {item.actually_collect_price?.toLocaleString()}
        </span>
        <span>
          核销：¥
          {item.write_off_price?.toLocaleString()}
        </span>
      </Flex>
    </div>
  )
}
const PAGE = 1
const INITIAL_SIZE = 20
const SIZE_INCREMENT = 20
let TEST_ENTRY = ''
let TEST_CUSTOMER_ID = 0
let TEST_CUSTOMER_NAME = ''
let TEST_CORP_GROUP_CHAT_ID = ''

const single = ['single_chat_tools', 'contact_profile']
const group = ['group_chat_tools']

console.log('NODE_ENV', process.env.NODE_ENV)
// 测试用
if (process.env.NODE_ENV === 'development') {
  TEST_ENTRY = 'group_chat_tools'
  TEST_CUSTOMER_ID = 1776291434889216
  TEST_CUSTOMER_NAME = '佛山市科技有限公司'
  TEST_CORP_GROUP_CHAT_ID = 'wrMI-NQAAA_iS-6XMcc2Dzj5r49fQ-uw'
}
// 客户对账单
// 企业微信移动端通过 /qywx_workspace/customer-debit?corpId=ww36730df336e4c722&agentId=1000008&appId=ww36730df336e4c722 获取客户信息
// 添加到企业微信客户群聊 聊天栏中
export function CustomerReconciliationContainer() {
  if (process.env.NODE_ENV !== 'production' && isWxWork()) {
    import('vconsole').then((VConsole) => {
      // eslint-disable-next-line no-new
      new VConsole.default({ theme: 'dark' })
    })
  }
  const { showMessage, contextHolder } = useMessage()

  const entryRef = useRef(TEST_ENTRY)
  const [customerInfo, setCustomerInfo] = useState<{
    id?: number
    name?: string
  }>({
    id: TEST_CUSTOMER_ID,
    name: TEST_CUSTOMER_NAME,
  })
  const { routerPushByKey } = useRouterPush()
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<IDataItem[]>([])
  const [size, setSize] = useState(INITIAL_SIZE)
  const [dates, setDates] = useState<[Date, Date]>([
    dayjs().startOf('month').toDate(),
    dayjs().endOf('month').toDate(),
  ])
  const [hasMore, setHasMore] = useState(true)

  // 使用 fetchCustomerReconciliationList hook
  const { mutate: fetchList, data: originData } = FetchCustomerReconciliationList({
    onSuccess: (res) => {
      // 处理列表数据和汇总数据
      const processedData = processDataOut(res)

      setData(processedData.list)
      // 根据返回数据判断是否还有更多
      setHasMore(res.total > res.list.length)
      setLoading(false)
    },
    onError: (error) => {
      console.error('获取数据失败:', error)
      setLoading(false)
      setHasMore(false)
    },
  })

  const { mutateAsync: fetchSignature } = FetchQYWechatSignature({

    onError: (error) => {
      console.error('获取数据失败:', error)
      setLoading(false)
    },
  })
  const location = useLocation()
  const { corpId: CORP_ID, agentId: AGENT_ID } = useSelector((state: RootState) => state.auth)
  // const searchParams = new URLSearchParams(location.search)
  // const CORP_ID = searchParams.get('corpId') || ''
  // const AGENT_ID = searchParams.get('agentId') || ''
  const APP_ID = CORP_ID

  // const CORP_ID = 'ww36730df336e4c722'
  // const AGENT_ID = '1000008'
  // const APP_ID = 'ww36730df336e4c722'
  const jsApiList = ['getContext', 'getCurExternalContact', 'getCurCorpGroupContact', 'getCurExternalChat', 'launchMiniprogram']
  const _initConfig = ({ timestamp, nonceStr, agentSignature, signature }: {
    timestamp: number
    nonceStr: string
    agentSignature: string
    signature: string
  }) => {
    return new Promise<void>((resolve, reject) => {
      wx.config({
        beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId: APP_ID, // 必填，企业微信的corpID，必须是本企业的corpID，不允许跨企业使用
        timestamp, // 必填，生成签名的时间戳
        nonceStr, // 必填，生成签名的随机串
        signature, // 必填，签名，见 附录-JS-SDK使用权限签名算法
        jsApiList, // 必填，需要使用的JS接口列表，凡是要调用的接口都需要传进来
      })
      wx.ready(() => {
        // config信息验证后会执行ready方法，所有接口调用都必须在config接口获得结果之后，config是一个客户端的异步操作，所以如果需要在页面加载时就调用相关接口，则须把相关接口放在ready函数中调用来确保正确执行。对于用户触发时才调用的接口，则可以直接调用，不需要放在ready函数中。
        wx.agentConfig({
          corpid: CORP_ID, // 必填，企业微信的corpid，必须与当前登录的企业一致
          agentid: AGENT_ID, // 必填，企业微信的应用id （e.g. 1000247）
          timestamp, // 必填，生成签名的时间戳
          nonceStr, // 必填，生成签名的随机串
          signature: agentSignature, // 必填，签名，见附录-JS-SDK使用权限签名算法
          jsApiList, // 必填，传入需要使用的接口名称
          success(res) {
            console.log('agentConfig success', res)
            // 回调
            resolve()
            getContext()
          },
          fail(res) {
            if (res.errMsg.includes('function not exist')) {
              showMessage.error('版本过低请升级')
            }
            // reject(res)
          },
        })
      })

      wx.error((res: any) => {
        // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
        console.error('wx config信息验证', res)
        // reject(res)
      })
    })
  }

  function getContext() {
    wx.invoke('getContext', {}, async (res: { entry: string, err_msg: string }) => {
      if (res.err_msg === 'getContext:ok') {
        // eslint-disable-next-line no-console
        console.log('getContext', res)
        entryRef.current = res.entry
        try {
          let customer: Api.getID.Response = {}
          if (single.includes(res.entry)) {
            customer = await getCurrentExternalId()
          }
          if (group.includes(res.entry)) {
            customer = await getCurExternalChatId()
          }
          console.log('customer', customer)
          getData(customer.customer_id)
        }
        catch (e: any) {
          showMessage.error(e)
        }
        // setShareTicket(res.shareTicket) // 可用于调用getShareInfo接口
      }
      else {
        // 错误处理
        console.error('错误: getContext', res)
        showMessage.error('错误: getContext')
      }
    })
  }
  const { mutateAsync: fetchGetID } = GetIDByQywxUserId({

    onError: (error) => {
      console.error('获取数据失败:', error)
      setLoading(false)
    },
  })
  function getCurrentExternalId() {
    return new Promise<Api.getID.Response>((resolve, reject) => {
      wx.invoke('getCurExternalContact', {}, async (res: { userId: string, err_msg: string }) => {
        if (res.err_msg === 'getCurExternalContact:ok') {
          // eslint-disable-next-line no-console
          console.log('userId', res)
          try {
            const result = await fetchGetID({
              qywx_customer_id: res.userId,
            })

            setCustomerInfo({
              id: result.customer_id,
              name: result.customer_name,
            })
            resolve(result)
          }
          catch (e) {
            showMessage.error(`获取客户ID失败：${e.message}`)
            reject(new Error('请求失败：获取客户ID失败'))
          }
        }
        else {
          // 错误处理
          const error = '错误: getCurExternalContact'
          showMessage.error(error)
          console.error(error, res)
          reject(new Error(error))
        }
      })
    })
  }

  function getCurExternalChatId() {
    return new Promise<Api.getID.Response>((resolve, reject) => {
      wx.invoke('getCurExternalChat', {}, async (res: { chatId?: string, err_msg?: string }) => {
        if (res.err_msg === 'getCurExternalChat:ok') {
          // eslint-disable-next-line no-console
          console.log('group chatId', res) // 返回当前外部群的群聊ID
          const result = await fetchGetID({
            qywx_group_chat_id: res.chatId,
          })
          setCustomerInfo({
            id: result.customer_id,
            name: result.customer_name,
          })
          resolve(result)
        }
        else {
          // 错误处理
          const error = '错误: getCurExternalChat'
          showMessage.error(error)
          console.error(error, res)
          reject(new Error(error))
        }
      })
    })
  }
  const getSignature = async () => {
    const timestamp = Number((+new Date() / 1000).toFixed(0))
    const noncestr = randomString(6)
    const res = await fetchSignature({
      nonceStr: noncestr,
      timestamp,
      url: window.location.href,
    })
    if (!res.app_signature || !res.corp_signature) {
      showMessage.error('企业接口认证过期')
      return
    }
    _initConfig({
      timestamp,
      nonceStr: noncestr,
      agentSignature: res.app_signature!,
      signature: res.corp_signature!,
    })
  }
  async function getData(customerId?: number) {
    // 初始化数据加载
    fetchList({
      customer_id: customerId || customerInfo.id!,
      page: PAGE,
      size: INITIAL_SIZE,
      start_time: dayjs(dates[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dates[1]).format('YYYY-MM-DD'),
    })
  }
  // 加载更多
  const loadMoreData = async () => {
    if (loading || !customerInfo.id)
      return

    setLoading(true)
    fetchList({
      customer_id: customerInfo.id,
      page: PAGE,
      size,
      start_time: dayjs(dates[0]).format('YYYY-MM-DD'),
      end_time: dayjs(dates[1]).format('YYYY-MM-DD'),
    })
  }

  // 处理日期变更
  function handleDateChange(newDates: [Date, Date]) {
    if (!customerInfo.id) {
      return showMessage.error('请先选择客户')
    }
    setDates(newDates)
    setSize(INITIAL_SIZE)
    setData([])
    fetchList({
      customer_id: customerInfo.id,
      page: PAGE,
      size: INITIAL_SIZE,
      start_time: dayjs(newDates[0]).format('YYYY-MM-DD'),
      end_time: dayjs(newDates[1]).format('YYYY-MM-DD'),
    })
  }
  const isMobile = useMobileScreen()
  // 触发自定义事件
  const handleCustomerChange = useCallback((event: CustomEvent<{ customer_id: number, customer_name: string, start_time: string, end_time: string }>) => {
    const { customer_id, customer_name, start_time, end_time } = event.detail
    setCustomerInfo({ id: customer_id, name: customer_name })

    if (start_time && end_time) {
      setDates([dayjs(start_time).toDate(), dayjs(end_time).toDate()])
    }
    else {
      setDates([
        dayjs().startOf('month').toDate(),
        dayjs().endOf('month').toDate(),
      ])
    }

    setSize(INITIAL_SIZE)
    setData([])
    fetchList({
      customer_id,
      page: PAGE,
      size: INITIAL_SIZE,
      start_time,
      end_time,
    })
  }, [])
  // 初始化数据加载
  useEffect(() => {
    if (isMobile && isWxWork()) {
      // h5 调试时使用
      if (process.env.NODE_ENV === 'development') {
        getData()
      }
      else {
        // 企业微信 授权
        getSignature()
      }
    }
  }, [isMobile])

  // 监听客户变更事件
  useEffect(() => {
    const { customer_id, customer_name, start_time, end_time } = location.state || {}
    if (customer_id && customer_name) {
      setCustomerInfo({
        id: Number.parseInt(customer_id),
        name: decodeURIComponent(customer_name),
      })
      setDates([
        dayjs(start_time || dates[0]).toDate(),
        dayjs(end_time || dates[1]).toDate(),
      ])
      // 初始化数据加载
      fetchList({
        customer_id: Number.parseInt(customer_id),
        page: PAGE,
        size: INITIAL_SIZE,
        start_time: dayjs(start_time || dates[0]).format('YYYY-MM-DD'),
        end_time: dayjs(end_time || dates[1]).format('YYYY-MM-DD'),
      })
    }
    else if (isMobile && !isWxWork()) {
      // 非企业微信移动端，跳转回客户欠款页面 因为企业微信移动端不通过location获取客户信息，通过客户群聊获取
      showMessage.error('客户信息不完整')
      routerPushByKey('/customer-debit', { replace: true })
    }
  }, [location])

  // 如果是pc端，计算scrollDiv高度
  const [scrollDivHeight, setScrollDivHeight] = useState('auto')
  // 监听客户变更，如果客户变更并且scrollDiv高度为auto，计算scrollDiv高度
  useEffect(() => {
    if (customerInfo.id && scrollDivHeight === 'auto') {
      getHeight()
    }
  }, [customerInfo.id, scrollDivHeight])
  function getHeight() {
    if (!customerInfo.id) {
      return
    }
    const scrollableDiv2Dom = document.querySelector(`#scrollableDiv2`) as HTMLElement
    if (scrollableDiv2Dom) {
      // 计算可用高度 = 视窗高度 - 导航栏高度 - 客户信息高度 - 网格高度 - 内边距
      const availableHeight = window.innerHeight
        - scrollableDiv2Dom.offsetTop - 100
      setScrollDivHeight(`${availableHeight}px`)
    }
  }
  const navigate = useNavigate()
  const onWindowResize = () => {
    // 如果是pc端，跳转回客户欠款页面
    if (!isMobile) {
      navigate('/customer-debit', { replace: true })
    }
    getHeight()
  }

  useEffect(() => {
    // 监听窗口大小变化
    window.addEventListener('resize', onWindowResize)
    window.addEventListener('customerChanged', handleCustomerChange as EventListener)
    return () => {
      window.removeEventListener('customerChanged', handleCustomerChange as EventListener)
      window.removeEventListener('resize', onWindowResize)
    }
  }, [isMobile])

  useEffect(() => {
    if (customerInfo.id) {
      loadMoreData()
    }
  }, [size]) // 监听 size 变化而不是 page

  // 打印数据
  const printOptions = {
    dataType: PrintDataType.Product,
    type: PrintType.PrintTemplateTypePurchaserReconciliation,
  }
  const printQueryUrl = encodeURIComponent('/mp/v1/should_collect_order/report_forms/getCustomerReconciliationListForPrint')
  const printQueryData = encodeURIComponent(JSON.stringify({ ...getFilterData({
    customer_id: customerInfo.id,
    start_time: dayjs(dates[0]).format('YYYY-MM-DD'),
    end_time: dayjs(dates[1]).format('YYYY-MM-DD'),
  }) }))
  const [currentOrderId, setCurrentOrderId] = useState<number | null>(null)
  const handleClickPanel = (item: IDataItem) => {
    if (currentOrderId === item.order_id) {
      setCurrentOrderId(null)
    }
    else {
      setCurrentOrderId(item.order_id || null)
    }
  }
  return (
    <>
      {contextHolder}
      <Flex justify="space-between" className={classnames(styles['customer-info'])}>
        <IconText
          className="text-black"
          icon={CiUser}
          text={customerInfo.name || '请选择客户'}
        />
        <div>
          <DateRangePicker value={dates} allowClear={false} onChange={handleDateChange} />
        </div>
      </Flex>
      {/* 修改条件判断，适应移动端 */}
      {(!isMobile && customerInfo.id) || isMobile
        ? (
            <>
              <Grid className={styles['customer-info-grid']} columns={2} gap={8}>
                <Grid.Item>
                  <Grid columns={2} gap={8} className="flex justify-start items-center gap-2">
                    <Grid.Item className="whitespace-nowrap">上期结余</Grid.Item>
                    <Grid.Item>
                      ￥
                      {formatPriceDiv(originData?.summary?.last_balance_price || 0).toLocaleString()}
                    </Grid.Item>
                  </Grid>
                </Grid.Item>
                <Grid.Item className="flex justify-end w-full">
                  <Grid columns={2} gap={8} className="flex justify-start items-center gap-2 w-[200px]">
                    <Grid.Item span={1} className="whitespace-nowrap">本期应收</Grid.Item>
                    <Grid.Item span={1}>
                      ￥
                      {formatPriceDiv(originData?.summary?.should_collect_money || 0).toLocaleString()}
                    </Grid.Item>
                  </Grid>
                </Grid.Item>
                <Grid.Item>
                  <Grid columns={2} gap={8} className="flex justify-start items-center gap-2 ">
                    <Grid.Item span={1} className="whitespace-nowrap">本期已收</Grid.Item>
                    <Grid.Item span={2}>
                      ￥
                      {formatPriceDiv(originData?.summary?.collected_money || 0).toLocaleString()}
                    </Grid.Item>
                  </Grid>
                </Grid.Item>
                <Grid.Item className="flex justify-end w-full">
                  <Grid columns={2} gap={8} className="flex justify-start items-center gap-2 w-[200px]">
                    <Grid.Item span={1} className="whitespace-nowrap">本期结余</Grid.Item>
                    <Grid.Item span={1}>
                      ￥
                      {formatPriceDiv(originData?.summary?.end_period || 0).toLocaleString()}
                    </Grid.Item>
                  </Grid>
                </Grid.Item>
              </Grid>
              <div
                id="scrollableDiv2"
                style={{
                  height: isMobile ? `calc(100vh - var(--content-padding) * 2)` : scrollDivHeight,
                  overflow: 'auto',
                }}
              >
                <InfiniteScroll
                  dataLength={data.length}
                  next={() => setSize(prev => prev + SIZE_INCREMENT)}
                  hasMore={hasMore}
                  loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
                  endMessage={<Divider>暂无更多</Divider>}
                  scrollableTarget="scrollableDiv2"
                >
                  <Collapse accordion>
                    {data.map(item => (
                      <Collapse.Panel
                        arrowIcon={[CollectType.CollectTypeActual, CollectType.CollectTypeAdvance].includes(item.collect_type as CollectType) ? null : undefined}
                        disabled={[CollectType.CollectTypeActual, CollectType.CollectTypeAdvance].includes(item.collect_type as CollectType)}
                        onClick={() => handleClickPanel(item)}
                        key={item.order_id?.toString() || ''}
                        title={<CollapseTitle item={item} currentOrderId={currentOrderId} />}
                      >
                        <CollapseContent itemList={item.item_list || []} />
                        <CollapseFooter currentOrderId={currentOrderId} item={item} />
                      </Collapse.Panel>
                    ))}
                  </Collapse>
                </InfiniteScroll>
              </div>
            </>
          )
        : (
            <div className="flex justify-center items-center h-full">
              请在左侧选择客户
            </div>
          )}
      {
        customerInfo.id && data.length
          ? (
              <PrintBtn
                print={printOptions}
                queryUrl={printQueryUrl}
                queryData={printQueryData}
                data={customerInfo}
                customStyles={{ width: '100%', margin: '0 auto' }}
                printBtnSlot={(
                  <FloatButton
                    type="primary"
                    description="预览账单"
                    shape="square"
                    style={{ width: '100px', zIndex: 1, position: 'relative', left: '50%', bottom: '10px', transform: 'translateX(-50%)' }}
                  />
                )}
              >
              </PrintBtn>
            )
          : null
      }

    </>
  )
}

export function Component() {
  return (
    <Flex vertical className="h-screen">
      <HeaderBox />
      <CustomerReconciliationContainer />
    </Flex>
  )
}
