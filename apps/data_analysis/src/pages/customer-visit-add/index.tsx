import type { RootState } from '@/store'
import type { FormInstance } from 'antd-mobile/es/components/form'
import type { RadioValue } from 'antd-mobile/es/components/radio'
import type { Label } from '../customer-visit-label-info'
import Img2 from '@/assets/IMG-2.svg?react'
import Img3 from '@/assets/IMG-3.svg?react'
import Img0 from '@/assets/IMG.svg?react'
import Wechat from '@/assets/wechat.svg?react'
import Wechatcom from '@/assets/wechatcom.svg?react'
import ListFrame from '@/components/listFrame'
import { useRouterPush } from '@/hooks/routerPush'
import { AddPurchaserVisit, EditPurchaserVisit, GetCorpTagList, GetUserLocation, GetVisitTagList } from '@/service/api/customerVisit'
import { getUuid } from '@/utils/format'
import { DeleteOutlined, LoadingOutlined, MinusSquareOutlined, PlusSquareOutlined, RightOutlined } from '@ant-design/icons'
import { formatHashTag, getFilterData } from '@ly/utils'
import { useMount, useUnmount } from 'ahooks'
import { Button, Card, Divider, Form, Input, NavBar, Radio, Space, Stepper, Tag, TextArea, Toast } from 'antd-mobile'
import { message } from 'antd/lib'
import classnames from 'classnames'
import classNames from 'classnames'
import { useEffectOnActive, useKeepAliveRef } from 'keepalive-for-react'
import { cloneDeep, uniqueId } from 'lodash-es'
import { useRef, useState } from 'react'
import { FaLocationDot } from 'react-icons/fa6'
import { useSelector } from 'react-redux'
import { useLocation, useNavigate } from 'react-router-dom'
import UploadImage from '../customer-visit/components/uploadImage'
import Skeleton from '@/components/skeleton'
import styles from './index.module.scss'

interface SelectPurchaserUserProps {
  value?: { purchaser_id: number, purchase_name: string }
  onChange?: (value: { purchaser_id: number, purchase_name: string }) => void
}
function SelectPurchaserUser(props: SelectPurchaserUserProps) {
  const { value, onChange } = props
  console.log('SelectPurchaserUser', value)
  const { routerPushByKey } = useRouterPush()
  const onClickNav = () => {
    routerPushByKey('/customer-page', {
      state: {
        clientId: value?.purchaser_id,
      },
    })
  }
  const [hasBind, sethasBind] = useState(true)
  useEffectOnActive(() => {
    setTimeout(() => {
    // 检查localStorage中是否有数据
      const storedData = sessionStorage.getItem('customerVisitData')
      if (storedData) {
        try {
          const data = JSON.parse(storedData)
          sethasBind(data.clientId && data.qywx_customers)
          onChange?.({ ...data, purchaser_id: data.clientId, purchase_name: data.clientName })
          // 使用后清除数据
          sessionStorage.removeItem('customerVisitData')
        }
        catch (e) {
          console.error('解析存储的客户数据失败', e)
        }
      }
    }, 0)
  }, [])
  return (
    <div className="flex flex-row justify-end items-center" onClick={onClickNav}>
      <div>
        {
          !value?.purchase_name
            ? '请选择客户'
            : value?.purchase_name
        }
        {!hasBind
          ? (
              <div className="text-xs text-red-600">
                <Wechat style={{ width: '16px', height: '16px' }} className="mr-2"></Wechat>
                未绑定微信
              </div>
            )
          : null}
      </div>

      <div className={styles.desc}>
        <RightOutlined className={classnames(styles.icon)} />
      </div>
    </div>
  )
}
interface SelectWechatProps {
  value?: { external_type: number, external_user_id: string, external_user_name: string, follow_user_id: string }
  onChange?: (value: { external_type: number, external_user_id: string, external_user_name: string }) => void
}
function SelectWechat(props: SelectWechatProps) {
  const { value, onChange } = props
  const { routerPushByKey } = useRouterPush()
  const onClickWechatNav = () => {
    routerPushByKey('/wechat-page', {
      state: {
        type: 'radio',
        ...(value && value.external_user_id ? { selectedIds: JSON.stringify([{ external_user_id: value.external_user_id, name: value.external_user_name, external_type: value.external_type }]) } : {}),
      },
    })
  }
  const subname = useRef({
    external_type: 0,
    corp_full_name: '',
  })
  // const [formData, setValue] = useState({
  //   external_user_id: value?.external_user_id,
  //   external_user_name: value?.external_user_name,
  // })
  useEffectOnActive(() => {
    setTimeout(() => {
    // 检查wechat数据
      const wechatData = sessionStorage.getItem('wechatFriendsData')
      if (wechatData) {
        try {
          const data = JSON.parse(wechatData)
          console.log('data', data)
          // setValue({
          //   external_user_id: data.external_user_id,
          //   external_user_name: data.external_user_name,
          // })
          subname.current = {
            external_type: data.external_type,
            corp_full_name: data.corp_full_name,
          }
          onChange?.({
            external_user_id: data.external_user_id,
            external_user_name: data.name,
            external_type: data.external_type,
            // follow_user_id: data.follow_user_id,
          })
          // 使用后清除数据
          sessionStorage.removeItem('wechatFriendsData')
        }
        catch (e) {
          console.error('解析微信好友数据失败', e)
        }
      }
    }, 0)
  }, [])
  return (
    <div className="flex flex-row justify-end items-center" onClick={onClickWechatNav}>
      {
        !value?.external_user_name
          ? '请选择客户'
          : (
              <span className="break-all">
                {subname.current.external_type === 1 ? <Wechat style={{ width: '16px', height: '16px' }} className="mr-2"></Wechat> : <Wechatcom className="mr-2" style={{ width: '16px', height: '16px' }}></Wechatcom>}
                {value?.external_user_name}
                <span className={classNames(subname.current.external_type === 2 ? 'text-[#e8be91]' : 'text-[#07c160]', 'ml-2 inline')}>
                  @
                  {subname.current.external_type === 1 ? '微信' : subname.current.corp_full_name}
                </span>
              </span>
            )
      }
      <div className={styles.desc}>
        <RightOutlined className={classnames(styles.icon)} />
      </div>
    </div>
  )
}
interface SelectTagListProps {
  required?: boolean
  purchaser_id?: number
  external_user_id?: string
  follow_user_id?: string
  value?: Label[]
  loading?: boolean
  onChange: (value: { id: number, name: string }) => void
}
function SelectTagList(props: SelectTagListProps) {
  const { value = [], onChange, loading = false } = props
  const [messageApi, contextHolder] = message.useMessage()

  const { routerPushByKey } = useRouterPush()
  const handleClickLabelInfo = () => {
    // 如果正在加载，禁用点击
    if (loading) {
      return
    }
    if (!props.external_user_id && !props.purchaser_id) {
      messageApi.info('请先选择成交客户或未成交客户')
      return
    }
    routerPushByKey('/customer-visit-label-info', {
      state: {
        labelList: JSON.stringify(value),
        purchaser_id: props.purchaser_id,
        external_user_id: props.external_user_id,
        // follow_user_id: props.follow_user_id,
        requireValidate: '1',
      },
    })
  }
  useEffectOnActive(() => {
    setTimeout(() => {
    // 检查标签数据
      const labelData = sessionStorage.getItem('labelInfoData')
      if (labelData) {
        try {
          const data = JSON.parse(labelData)
          // setAllAccount(data)
          onChange?.(data)
          // 使用后清除数据
          sessionStorage.removeItem('labelInfoData')
        }
        catch (e) {
          console.error('解析标签数据失败', e)
        }
      }
    }, 0)
  }, [])
  return (
    <>
      {contextHolder}
      <div className="relative w-full">
        {loading ? (
          // Loading 状态：只显示骨架屏
          <div className="pointer-events-none">
            <div className="w-full flex justify-between items-center mb-2">
              <Skeleton width={80} height={20} />
              <Skeleton width={20} height={20} />
            </div>
            {value.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {value.slice(0, 3).map((_, index) => (
                  <Skeleton key={index} width={60} height={24} round />
                ))}
              </div>
            )}
          </div>
        ) : (
          // 正常状态
          <>
            <div
              className="w-full flex justify-between font-bold text-[15px] cursor-pointer"
              onClick={handleClickLabelInfo}
            >
              <div className={props.required ? 'required' : ''}>
                标签信息
              </div>
              <RightOutlined className={classnames(styles.icon)} />
            </div>
            {value.length !== 0 && (
              <div className={classnames(styles.tag_list, 'mt-2')}>
                {value.map((item) => (
                  <Tag
                    key={item.id}
                    className={styles.switchBar}
                    color="primary"
                    round
                  >
                    {item.name}
                  </Tag>
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </>
  )
}
interface SelectProductListProps {
  value?: { id: number, finish_product_code: string, finish_product_name: string }[]
  onChange?: (value: { id: number, finish_product_code: string, finish_product_name: string }[]) => void
}
function SelectProductList(props: SelectProductListProps) {
  const { value, onChange } = props
  const { routerPushByKey } = useRouterPush()
  // 跳转
  const handleAddNewProduct = () => {
    routerPushByKey('/product-list-selector', {
      state: {
        selectedIds: value || [],
      },
    })
  }
  const handleDeleteProductTag = (row: any) => {
    onChange?.(value!.filter(it => it.id !== row.id))
  }
  useEffectOnActive(() => {
    setTimeout(() => {
      const productData = sessionStorage.getItem('productSelectorData')
      if (productData) {
        try {
          const selectedProducts = JSON.parse(productData)
          // 更新表单数据
          onChange?.(selectedProducts)
        }
        catch (e) {
          console.error('解析产品数据失败:', e)
        }
        finally {
        // 清除数据，避免重复处理
          sessionStorage.removeItem('productSelectorData')
        }
      }
    }, 0)
  }, [])
  return (
    <>
      <div className="flex flex-row items-center" style={{ fontSize: '16px', marginBottom: '10px' }}>
        可匹配产品
        <div onClick={() => handleAddNewProduct()} style={{ marginLeft: '8px' }}>
          <PlusSquareOutlined style={{ color: '#337fff' }} />
        </div>
      </div>
      {
        value
          ? (
              <Space wrap>
                {
                  value?.map((it) => {
                    return (
                      <Tag round key={it.id} color="#1677ff">
                        {formatHashTag(it.finish_product_code, it.finish_product_name)}
                        <DeleteOutlined style={{ color: 'red', marginLeft: '4px', fontSize: '12px' }} onClick={() => handleDeleteProductTag(it)} />
                      </Tag>
                    )
                  })
                }
              </Space>
            )
          : null
      }
    </>
  )
}
interface FormValue {
  purchaser_id: number
  purchase_name: string
  external_type: number
  purchase_wechat: string
  attachment_url: string[]
  qywx_customers?: Api.Customer.QywxCustomers[]
  selected_qywx_customer_id?: string
  selected_qywx_customer_name?: string
  latitude: number
  longitude: number
  location_address: string
  location_name: string
  feedbacks: {
    remote_id?: number
    id: string
    feedback_title: string
    new_product_feedback: string
    feedback_solution: string
    product_list?: {
      id: number
      finish_product_code: string
      finish_product_name: string
    }[]
  }
  labels: LabelList
  assists?: {
    remote_id?: number
    assist_content: string
    department_id: number
  }[]
  recommendation: {
    remote_id?: number
    id: string
    product_name: string
    weight_density: number
    remark: string
  }[]
  visiting_situations: {
    parentId: number
    id?: number
    other_tag_remark: string
    visit_tag_id: number
    visit_tag_name: string
  }
  textarea_reason: string
  external_user_id: string
  external_user_name: string
  follow_user_id: string
}
type LabelList = {
  label_id: number
}[]
const TEXTAREA_LIMIT = 4
const UNIQUE_ID_RECOMMEND = 'recommend'
export function Component() {
  const [customerType, setCustomerType] = useState('existing')
  const formRef = useRef<FormInstance>(null)
  const [showTextarea, setShowTextarea] = useState(false)
  const userInfo = useSelector((state: RootState) => state.auth.userInfo)
  const location = useLocation()

  // 获取传递的参数
  const type = location.state?.type || 'add' // 默认为新增
  const visitId = location.state?.id // 编辑时的ID
  // 根据type确定页面标题
  const pageTitle = type === 'edit' ? '编辑客户拜访' : '新增客户拜访'
  const [messageApi, contextHolder] = message.useMessage()
  const formDataInitialValues = {
    purchase_wechat: '',
    purchaser: {
      purchaser_id: 0,
      purchase_name: '',
    },
    selected_qywx_customer: 0,
    attachment_url: [] as string[],
    location_address: '',
    location_name: '',
    labels: [] as LabelList,
    feedback_solution: '',
    matchable_products: [] as {
      id: number
      finish_product_code: string
      finish_product_name: string
    }[],
    recommendation: [
      {
        id: uniqueId(UNIQUE_ID_RECOMMEND),
        product_name: '',
        remark: '',
        weight_density: '',
      },
    ],
    textarea_reason: '',
    external_user: {
      external_user_id: '',
      external_user_name: '',
      follow_user_id: '',
    },
  }
  const deaultFormData = {
    purchase_name: '',
    purchase_wechat: '',
    purchaser_id: 0,
    qywx_customers: [],
    selected_qywx_customer_id: '',
    selected_qywx_customer_name: '',
    attachment_url: [] as string[],
    location_address: '',
    location_name: '',
    latitude: '',
    longitude: '',
    labels: [] as LabelList,
    visiting_situations: {
      parentId: 0,
      id: 0,
      other_tag_remark: '',
      visit_tag_id: 0,
      visit_tag_name: '',
    },
    feedbacks: {
      id: getUuid(),
      feedback_title: '',
      new_product_feedback: '',
      feedback_solution: '',
      product_list: [],
    },
    recommendation: [
      {
        id: uniqueId(UNIQUE_ID_RECOMMEND),
        product_name: '',
        remark: '',
        weight_density: '',
      },
    ],
    textarea_reason: '',
    external_user_id: '',
    external_user_name: '',
    // follow_user_id: '',
  }
  interface TagListItem1 { id: number, name: string, checked: boolean, enum: number, list: TagListItem2[] }
  // 拜访模式
  interface TagListItem2 { id: number, name: string, sort: number, checked: boolean }
  const [tagList1, setTagList1] = useState<TagListItem1[]>([])

  // 拜访进度
  const [tagList2, setTagList2] = useState<TagListItem2[]>([])
  const [formData, setFormData] = useState<FormValue>(deaultFormData)
  const defaultTagList = useRef<any[]>([])

  function handleCustomerTypeChange(type: 'existing' | 'potential' | 'noWechat') {
    setCustomerType(type)
    setShowTextarea(false)
    if (type !== 'noWechat') {
      setFormData(prev => ({
        ...prev,
        textarea_reason: '',
      }))
      // 拜访模式
      setTagList1(defaultTagList.current)
      // 拜访进度
      setTagList2(defaultTagList.current[0].mp_visit_tag_detail_data_list.map((it) => {
        it.checked = false
        return it
      }).sort((a, b) => a.sort - b.sort))
    }
  }
  const extId = ['existing'].includes(customerType) ? formData.selected_qywx_customer_id : formRef.current?.getFieldValue('external_user').external_user_id
  console.log('extId', extId)
  // 未添加到企业微信 没写明原因 字数不够
  const textarea_error = formData.textarea_reason?.length < TEXTAREA_LIMIT

  const { mutateAsync: getTagListApi } = GetVisitTagList()

  const getTagList = async () => {
    try {
      const res = await getTagListApi()
      // const formData = getFormDataFromStorage()
      const tagList = res.list.map((item, index) => {
        const checked = false
        return {
          id: index + 1,
          enum: item.visiting_mode,
          name: item.visiting_mode_name,
          list: item.mp_visit_tag_detail_data_list || [],
          checked,
        }
      })
      // 拜访模式
      defaultTagList.current = cloneDeep(tagList)
      setTagList1(tagList)
      if (!res.list[0].mp_visit_tag_detail_data_list) {
        return
      }
      // 拜访进度
      setTagList2(res.list[0].mp_visit_tag_detail_data_list?.map((it) => {
        it.checked = false
        return it
      })?.sort((a, b) => a.sort - b.sort) || [])
    }
    catch (e) {
      console.error(e)
      messageApi.error(e.message)
    }
  }

  const handleShowTextarea = () => {
    if (formData.purchaser_id || formData.external_user_id) {
      setFormData(prev => ({
        ...prev,
        purchaser_id: 0,
        purchase_name: '',
        // follow_user_id: '',
        external_user_id: '',
        external_user_name: '',
        purchase_wechat: '',
      }))
    }

    setShowTextarea((prev) => {
      setTagList1(!prev ? defaultTagList.current.filter(item => [1, 4].includes(item.enum)) : defaultTagList.current)
      return !prev
    })
  }
  const textarea_reason_required = !(showTextarea && !!formData.textarea_reason)
  useMount(() => {
    getTagList()
  })

  // const [allAccount, setAllAccount] = useState<Label[]>([])
  const { mutateAsync: getLastTagListApi, isPending: isTagListLoading } = GetCorpTagList()
  const getLastTagList = async (externalUserId?: string) => {
    try {
      const res = await getLastTagListApi(getFilterData({
        follow_user_id: userInfo?.follow_user_id,
        external_user_id: externalUserId || extId,
      }))
      const labels = res.list?.reduce((acc: Label[], group) => {
        const tags = group.corp_tags?.filter(tag => tag.is_select).map(tag => ({
          label_id: tag.tag_id,
          id: tag.id,
          pid: group.id,
          checked: tag.is_select,
          name: tag.tag_name,
        })) || []
        return [...acc, ...tags]
      }, []) as Label[]
      formRef.current?.setFieldValue('labels', labels)
    }
    catch (e) {
      messageApi.error(e.message)
    }
  }
  const handleAddNewRecommend = () => {
    setFormData((prev) => {
      return {
        ...prev,
        recommendation: [...prev.recommendation, {
          id: uniqueId(UNIQUE_ID_RECOMMEND),
          product_name: '',
          remark: '',
          weight_density: '',
        }],
      }
    })
  }
  const handleBlur = (value: string, type: 'remark' | 'product_name', index: number) => {
    const rowIndex = index
    if (type === 'remark') {
      setFormData((prev) => {
        return {
          ...prev,
          recommendation: prev.recommendation.map((item, index) => {
            if (index === rowIndex) {
              item.remark = value
            }
            return item
          }),
        }
      })
    }
    else if (type === 'product_name') {
      setFormData((prev) => {
        return {
          ...prev,
          recommendation: prev.recommendation.map((item, index) => {
            if (index === rowIndex) {
              item.product_name = value
            }
            return item
          }),
        }
      })
    }
  }
  // 删除
  const handleDeleteRecommendation = (i: number) => {
    setFormData((prev) => {
      return {
        ...prev,
        recommendation: prev.recommendation.filter((_, index) => index !== i),
      }
    })
  }
  const handleChangeWeight = (value: number, index: number) => {
    setFormData((prev) => {
      prev.recommendation[index].weight_density = value
      return {
        ...prev,
        recommendation: [...prev.recommendation],
      }
    })
  }
  const changePic = (list) => {
    setFormData(prev => ({ ...prev, attachment_url: list }))
  }
  const handleInputTextarea = (value: string) => {
    setFormData(prev => ({ ...prev, textarea_reason: value }))
    return value
  }
  function onChangeValue(value: string) {
    setFormData(prev => ({ ...prev, visiting_situations: {
      ...prev.visiting_situations,
      other_tag_remark: value,
    } }))
  }

  function handleSelectVisitMode(value: RadioValue) {
    const currentTagList1 = tagList1.find(item => item.id === value)?.list.map((item) => {
      item.checked = false
      return item
    }).sort((a, b) => a.sort - b.sort) || []
    formRef.current?.setFieldValue('visitSituations', '')
    setTagList2(currentTagList1)
    setTagList1(prev => prev.map((item) => {
      if (item.id === value) {
        item.checked = true
      }
      else {
        item.checked = false
      }
      return item
    }))
    // setFormData(prev => ({
    //   ...prev,
    //   visiting_situations: {
    //     ...prev.visiting_situations,
    //     parentId: value as number,
    //   },
    // }))
  }
  function handleSelectVisitProcess(value: RadioValue) {
    setTagList2(prev => prev.map((item) => {
      if (item.id === value) {
        item.checked = true
      }
      else {
        item.checked = false
      }
      return item
    }))
    // setFormData(prev => ({
    //   ...prev,
    //   visiting_situations: {
    //     parentId: tagList1.find(item => item.checked)?.enum || 0,
    //     visit_tag_name: tagList2.find(item => item.id === value)?.name || '',
    //     other_tag_remark: otherInputValue,
    //     visit_tag_id: value as number,
    //   },
    // }))
  }
  const navigate = useNavigate()
  const { mutateAsync: submit } = AddPurchaserVisit({
    onSuccess: () => {
      messageApi.success('成功')
      setFormData(deaultFormData)
      formRef.current?.resetFields()
      // 重置
      setTagList1(defaultTagList.current)
      setTagList2(defaultTagList.current[0].list)
      setShowTextarea(false)
      setTimeout(() => {
        navigate('/customer-visit', {
          replace: true,
        })
      }, 1000)
    },
    onError: (error) => {
      messageApi.error(error.message)
    },
  })
  const { mutateAsync: editSubmit } = EditPurchaserVisit({
    onSuccess: () => {
      messageApi.success('修改成功')
      setFormData(deaultFormData)
      formRef.current?.resetFields()
      // 重置
      setTagList1(defaultTagList.current)
      setTagList2(defaultTagList.current[0].list)
      setShowTextarea(false)
      setTimeout(() => {
        navigate('/customer-visit', {
          replace: true,
        })
      }, 1000)
    },
    onError: (error) => {
      messageApi.error(error.message)
    },
  })
  async function handleSubmit() {
    if (customerType === 'noWechat') {
      if (!formData.textarea_reason) {
        messageApi.error('请填写未添加微信的原因')
        return
      }
    }
    formRef.current?.validateFields().then(async (values) => {
      console.log('values', values)
      const params = getFilterData({
        attachment_url: formData.attachment_url,
        biz_unit_id: formRef.current?.getFieldValue('purchaser').purchaser_id,
        coordinate_system: 'gcj02',
        dev_products: formData.recommendation.filter(item => item.product_name).map((item) => {
          return {
            // id: item.id,
            name: item.product_name,
            remark: item.remark,
            weight_density: String(item.weight_density),
          }
        }),
        external_user_id: ['existing'].includes(customerType) ? formData.selected_qywx_customer_id : formRef.current?.getFieldValue('external_user').external_user_id,
        feedbacks: [
          {
            feedback_solution: formRef.current?.getFieldValue('feedback_solution'),
          },
        ],
        not_add_corp_wechat_remark: formRef.current?.getFieldValue('textarea_reason'),
        labels: formRef.current?.getFieldValue('labels').map(item => ({
          corp_tag_id: item.id,
        })),
        latitude: String(formData.latitude),
        longitude: String(formData.longitude),
        location_name: formData.location_name,
        location_address: formData.location_address,
        matchable_products: formRef.current?.getFieldValue('matchable_products').map(item => item.id),
        visiting_situations: [
          {
            other_tag_remark: formRef.current?.getFieldValue('otherInputValue'),
            visit_tag_id: formRef.current?.getFieldValue('visitSituations'),
            visit_tag_name: tagList2.find(item => item.checked)?.name || '',
          },
        ],
      })
      if (type === 'edit') {
        await editSubmit({
          ...params,
          id: visitId,
        })
        return
      }
      await submit(params)
    }).catch((e) => {
      console.error('error', e)
    })
  }
  function handleChangePurchaser(data: Api.Customer.Response) {
    setShowTextarea(false)
    setFormData(pre => ({
      ...pre,
      purchaser_id: data.purchaser_id,
      purchase_name: data.purchase_name,
      purchase_wechat: data.external_user_name,
      external_user_id: '',
      external_user_name: '',
      qywx_customers: data.qywx_customers,
      selected_qywx_customer_id: data.qywx_customers?.[0]?.id,
      selected_qywx_customer_name: data.qywx_customers?.[0]?.name,
      // follow_user_id: data.follow_user_id,
    }))
    // 默认选择第一个
    if (data.qywx_customers) {
      formRef.current?.setFieldValue('selected_qywx_customer', data.qywx_customers[0].id)
    }
    getLastTagList(data.qywx_customers?.[0]?.id)
  }
  function handleChangeWechat(data) {
    setShowTextarea(false)
    setFormData(pre => ({
      ...pre,
      external_user_id: data.external_user_id,
      external_user_name: data.name,
      external_type: data.external_type,
      // follow_user_id: data.follow_user_id,
      purchase_name: '',
      purchaser_id: 0,
      purchase_wechat: '',
    }))
    getLastTagList(data.external_user_id)
  }
  function handleChangeTagList(data) {
    formRef.current?.setFieldValue('labels', data || [])
    // setAllAccount(data)
    // setFormData(pre => ({
    //   ...pre,
    //   labels: data,
    // }))
  }
  function handleSelectProductList(selectedProducts) {
    // 更新表单数据
    setFormData((prev) => {
      return {
        ...prev,
        feedbacks: {
          ...prev.feedbacks,
          product_list: selectedProducts,
        },
      }
    })
  }
  const { mutateAsync } = GetUserLocation({
    onSuccess: (res) => {
      // 获取最近的POI信息
      const nearestPoi = res.pois?.[0]
      const poiInfo = nearestPoi ? `${nearestPoi.title}(${nearestPoi._distance}米)` : ''
      const locationAddress = [
        res.formatted_addresses?.standard_address || '',
        poiInfo ? `附近：${poiInfo}` : '',
      ].filter(Boolean).join('，')
      setFormData(prev => ({
        ...prev,
        latitude: res.ad_info?.location?.lat || 0,
        longitude: res.ad_info?.location?.lng || 0,
        location_name: res.formatted_addresses?.recommend || '',
        location_address: locationAddress || '',
      }))
      formRef.current?.setFieldsValue({
        latitude: res.ad_info?.location?.lat || 0,
        longitude: res.ad_info?.location?.lng || 0,
        location_name: res.formatted_addresses?.recommend || '',
        location_address: res.formatted_addresses?.standard_address || '',
      })
    },
    onError: (error) => {
      console.error('error', error)
    },
  })
  const getLocationAddress = async () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        // 成功回调
        async (position) => {
          const latitude = position.coords.latitude // 纬度
          const longitude = position.coords.longitude // 经度
          await mutateAsync({
            lat: latitude,
            lng: longitude,
            location_type: 'wgs84',
          })
        },
        // 错误回调
        (error) => {
          switch (error.code) {
            case error.PERMISSION_DENIED:
              Toast.show({
                content: '获取位置失败：您拒绝了位置请求，请在浏览器设置中允许获取位置信息',
                position: 'center',
              })
              break
            case error.POSITION_UNAVAILABLE:
              Toast.show({
                content: '获取位置失败：位置信息不可用',
                position: 'center',
              })
              break
            case error.TIMEOUT:
              Toast.show({
                content: '获取位置失败：请求超时，请检查网络后重试',
                position: 'center',
              })
              break
          }
        },
        // 配置选项
        {
          enableHighAccuracy: true, // 高精度
          timeout: 5000, // 超时时间
          maximumAge: 0, // 缓存时间
        },
      )
    }
    else {
      Toast.show({
        content: '您的浏览器不支持获取位置信息，请使用其他浏览器或更新当前浏览器版本',
        position: 'center',
      })
    }
  }
  const handleCheckLocation = () => {
    getLocationAddress()
  }
  // 获取经纬度
  useMount(() => {
    getLocationAddress()
  })
  const aliveRef = useKeepAliveRef()
  useUnmount(() => {
    aliveRef.current?.destroy()
  })
  return (
    <>
      <ListFrame
        header={(
          <NavBar
            // back={<LeftOutlined />}
            onBack={() => navigate(-1)}
            className={styles.nav_container}
          >
            {pageTitle}
          </NavBar>
        )}
        footer={
          <Button block className={styles.button} onClick={handleSubmit} color="primary" type="submit" size="large">提交</Button>
        }
        customContainerClassName={styles.container}
      >
        {contextHolder}
        <Form
          ref={formRef}
          initialValues={formDataInitialValues}
          layout="horizontal"
          className={classnames('mx-2', styles.form)}
        >
          <Card
            headerClassName={styles.card_header}
            title={(
              <div className="flex w-full justify-between">
                <span className="font-bold">客户类型</span>
                <span className="required text-red-600 font-normal">必选</span>
              </div>
            )}
            className="mt-2"
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
            headerStyle={{ paddingBottom: '0px' }}
          >
            <div className={styles.customerType}>
              <div
                className={classnames(styles.customerTypeItem, customerType === 'existing' && styles.active)}
                onClick={() => handleCustomerTypeChange('existing')}
              >
                <Img2 />
                <span>现有客户</span>
              </div>
              <div
                className={classnames(styles.customerTypeItem, customerType === 'potential' && styles.active)}
                onClick={() => handleCustomerTypeChange('potential')}
              >
                <Img0 />
                <span>潜在客户</span>
              </div>
              <div
                className={classnames(styles.customerTypeItem, customerType === 'noWechat' && styles.active)}
                onClick={() => handleCustomerTypeChange('noWechat')}
              >
                <Img3 />
                <span>未加微信</span>
              </div>
            </div>
            <Form.Item
              name="purchaser"
              required={false}
              className={classNames('p-0 text-base', customerType === 'existing' ? '' : '!hidden')}
              label="客户名称"
              rules={[{ required: customerType === 'existing' }]}
            >
              <SelectPurchaserUser onChange={handleChangePurchaser} />
            </Form.Item>
            {customerType === 'existing' && formData.qywx_customers && formData.qywx_customers.length > 0 && (
              <Form.Item
                layout="vertical"
                name="selected_qywx_customer"
                label="客户微信"
                className="p-0 text-base"
              >
                <Radio.Group
                  onChange={(value: RadioValue) => {
                    // 处理选择的企业微信客户
                    const selectedCustomer = formData.qywx_customers?.find(customer => customer.id === value)
                    if (selectedCustomer) {
                      setFormData(prev => ({
                        ...prev,
                        selected_qywx_customer_id: selectedCustomer.id,
                        selected_qywx_customer_name: selectedCustomer.name,
                      }))
                      getLastTagList(selectedCustomer.id)
                    }
                  }}
                >
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {formData.qywx_customers.map(customer => (
                      <Radio
                        key={customer.id}
                        value={customer.id}
                        className={styles.radio}
                        style={{
                          width: '100%',
                          padding: '8px 0',
                          borderBottom: '1px solid #f0f0f0',
                        }}
                      >
                        <div className="flex flex-col">
                          <span className="font-medium text-right">
                            {customer.type === '企业微信' ? <Wechatcom className="mr-2" style={{ width: '16px', height: '16px' }}></Wechatcom> : <Wechat style={{ width: '16px', height: '16px' }} className="mr-2"></Wechat>}

                            {customer.name}
                            <span className={classNames(
                              customer.type === '企业微信' ? 'text-[#e8be91]' : 'text-[#07c160]',
                              'ml-1 inline',
                            )}
                            >
                              @
                              {customer.type}
                            </span>
                          </span>
                        </div>
                      </Radio>
                    ))}
                  </Space>
                </Radio.Group>
              </Form.Item>
            )}
            <Form.Item
              name="external_user"
              required={false}
              rules={[{ required: customerType === 'potential' }]}
              className={classNames('p-0 text-base', customerType === 'potential' ? '' : '!hidden')}
              label="微信"
            >
              <SelectWechat onChange={handleChangeWechat} />
            </Form.Item>
            <Form.Item
              name=""
              className={classNames('p-0 text-base', customerType === 'noWechat' ? '' : '!hidden')}
              label="未添加微信"
            >
              <div onClick={handleShowTextarea} className="flex flex-row justify-end items-center">
                <div className="truncate max-w-[150px]">
                  {
                    !formData.textarea_reason
                      ? '输入原因'
                      : formData.textarea_reason
                  }
                </div>
                <div className={styles.desc}>
                  <RightOutlined className={classnames(styles.icon, showTextarea && styles.show)} />
                </div>
              </div>
            </Form.Item>
            {
              showTextarea && (
                <Form.Item
                  name="textarea_reason"
                  required={false}
                  rules={[{ required: true }]}
                  className="p-0 text-base"
                >
                  <TextArea
                    className={classnames(styles.textarea, textarea_error ? styles.textarea__require : '')}
                    onChange={handleInputTextarea}
                    maxLength={300}
                    showCount
                    autoFocus
                    value={formData.textarea_reason}
                    placeholder="请写明原因"
                  >
                  </TextArea>
                </Form.Item>
              )
            }
          </Card>
          <Card
            title={(
              <div className="required">
                拜访情况
              </div>
            )}
            className="mt-2"
            headerStyle={{ paddingBottom: '0px' }}
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
          >
            <Form.Item
              label="拜访模式"
              rules={[{ required: true }]}
              required={false}
              name="visitMode"
              layout="vertical"
              className={styles.checkboxItem}
            >
              {/* {!formError.error_tagList1 ? null : <span className="require">{formError.error_tagList1}</span>} */}
              <Radio.Group value={formData.visiting_situations.parentId} onChange={handleSelectVisitMode}>
                {
                  tagList1.map((item) => {
                    return (
                      <Radio checked={item.checked} key={item.id} value={item.id}>{item.name}</Radio>
                    )
                  })
                }
              </Radio.Group>
            </Form.Item>

            <Form.Item
              label="拜访进度"
              rules={[{ required: true }]}
              required={false}
              name="visitSituations"
              layout="vertical"
              className={styles.checkboxItem}
            >
              {/* {!formError.error_tagList2 ? null : <span className="require">{formError.error_tagList2}</span>} */}
              <Radio.Group value={formData.visiting_situations.visit_tag_id} onChange={handleSelectVisitProcess}>
                {
                  tagList2.length > 0
                    ? tagList2.map((item) => {
                        return (
                          <div key={item.id} className="items-center" style={item.name === '其他' && item.checked ? { display: 'flex' } : { display: 'inline-flex' }}>
                            <Radio checked={item.checked} key={item.id} value={item.id}>
                              {item.name}
                            </Radio>

                            {
                            // 其他
                              item.name === '其他' && item.checked
                                ? (
                                    <Form.Item
                                      label=""
                                      rules={[{ required: true, message: '请输入文字' }]}
                                      required={false}
                                      name="otherInputValue"
                                      className={styles.checkboxItem}
                                    >
                                      <Input className={styles.otherInput} onChange={value => onChangeValue(value)} clearable placeholder="请输入文字" />
                                      {/* {formError.error_otherInput ? <div className="require" style={{ fontSize: '12px' }}>{formError.error_otherInput}</div> : null} */}
                                    </Form.Item>
                                  )
                                : null
                            }
                          </div>

                        )
                      })
                    : <div className={styles.empty}>暂无拜访进度</div>
                }
              </Radio.Group>

            </Form.Item>

          </Card>
          {
            ['existing', 'potential'].includes(customerType)
              ? (
                  <Card
                    className="mt-2"
                    style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
                  >
                    <Form.Item
                      label=""
                      rules={[{ required: textarea_reason_required, message: '请选择标签' }]}
                      required={false}
                      name="labels"
                      layout="vertical"
                      className={styles.checkboxItem}
                    >
                      <SelectTagList
                        required={textarea_reason_required}
                        purchaser_id={formData.purchaser_id}
                        external_user_id={extId}
                        follow_user_id={userInfo?.follow_user_id}
                        loading={isTagListLoading}
                        onChange={handleChangeTagList}
                      />
                    </Form.Item>
                  </Card>
                )
              : null
          }

          <Card
            title={(
              <div className="required">
                拜访反馈
              </div>
            )}
            headerStyle={{ paddingBottom: '0px' }}
            className="mt-2"
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
          >
            <div className={styles.feedbackContainer}>
              <Form.Item
                className="w-full rounded-md"
                rules={[{ required: true, message: '请输入拜访反馈' }]}
                required={false}
                name="feedback_solution"
              >
                <TextArea
                  className={styles.textareaSolution}
                  rows={5}
                  showCount
                  placeholder="描述拜访内容(产品、幅宽、克重) 、解决方案等"
                >
                </TextArea>
              </Form.Item>
              <Divider direction="horizontal" style={{ margin: '12rpx 0' }}></Divider>
              <Form.Item
                name="matchable_products"
                required={false}
              >
                <SelectProductList onChange={handleSelectProductList} />
              </Form.Item>
            </div>
          </Card>
          <Card
            className="mt-2"
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
          >
            <div className="w-full flex justify-between font-bold text-[15px] mb-2">
              <div>
                建议开发产品（选填）
              </div>
              <div style={{ marginRight: '16rpx' }} className={styles.desc} onClick={handleAddNewRecommend}>
                <PlusSquareOutlined style={{ color: '#337fff' }} />
              </div>
            </div>
            <div className={classnames(styles.tack_list)}>
              {
                formData.recommendation?.map((item, index) => {
                  return (
                    <div key={item.id} className={styles.tack_item} style={{ paddingTop: '10px', paddingBottom: '10px' }}>
                      <div className={styles.tack_context}>
                        <div className="flex flex-row items-center">
                          <div className="flex flex-row items-center " style={{ marginRight: '10px' }}>
                            <span className={styles.subTitle}>名称</span>
                            <Input value={item.product_name} onChange={value => handleBlur(value, 'product_name', index)} placeholder="填写名称" />
                          </div>
                          <div className="flex flex-row items-center">
                            <span className={styles.subTitle}>克重</span>
                            <Stepper
                              style={{ '--input-width': '60px' }}
                              defaultValue={0}
                              formatter={value => `${value}g`}
                              parser={text => Number.parseFloat(text.replace('g', ''))}
                              onChange={value => handleChangeWeight(value, index)}
                            />
                          </div>
                        </div>
                        <div className="flex flex-row items-center">
                          <span className={styles.subTitle}>备注</span>
                          <Input value={item.remark} placeholder="填写备注" onChange={value => handleBlur(value, 'remark', index)} data-index={index} style={{ width: '100%' }} />
                        </div>
                      </div>
                      {
                        index !== 0
                          ? (
                              <div onClick={() => handleDeleteRecommendation(index)}>
                                <MinusSquareOutlined style={{ color: '#e64340' }} />
                              </div>
                            )
                          : null
                      }

                    </div>
                  )
                })
              }
            </div>
          </Card>
          <Card
            title="附件凭证"
            headerStyle={{ paddingBottom: '0px' }}
            className="mt-2"
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
          >
            <UploadImage defaultList={formData.attachment_url} onChange={changePic} />
          </Card>
          <Card
            className="mt-2"
            style={{ 'borderRadius': '16px', '--adm-card-header-border-width': 0 }}
          >
            <Form.Item
              label="拜访位置"
              rules={[{ required: false }]}
              name="location_address"
              required
            >
              <div className={classnames(styles.address_bar)} onClick={handleCheckLocation}>
                <div className={styles.address}>{formData.location_address ?? '点击获取位置信息'}</div>
                <FaLocationDot size={30} />
              </div>
            </Form.Item>
          </Card>
        </Form>
      </ListFrame>
    </>
  )
}
