@import '@/styles/index.scss';
.form{
  :global {
    .adm-list-body{
      background-color: unset;
    }
    .adm-list-item-content{
      border: unset;
    }
  }
}
.container{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.card_header{
  :global{
    .adm-card-header-title{
      width: 100%;
    }
  }
}
.customerType{
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  .customerTypeItem{
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    background-color: white;
    color: #333333;
    font-size: 12px;
    padding: 10px 20px;
    border-radius: 10px;
    border: 1px solid #f2f2f2;
    gap: 10px;
    &.active{
      border: 2px solid #caddfd;
      background-color: rgba(#4193f7, 0.2);
    }
  }
}
.nav_container {
  position: sticky;
  top: 0;
  background-color: var(--adm-color-primary);
  color: white;
  flex-shrink: 0;
}
.radio{
  :global {
    .adm-radio-content{
      width: 100%;
    }
  }
}
.checkboxItem{
  padding: 0;
  :global {
    .adm-list-item-content-main{
      padding: 0;
    }
    .adm-radio {
      position: relative;
    }
    .adm-radio .adm-radio-icon > svg{
      display: none;
    }
    .adm-radio-icon {
      display: none;
    }
    .adm-radio-checked{
      .adm-radio-content{
        color: white;
        
      }
      &.adm-radio{
        background-color: #4581ff;
        border: 1px solid #4581ff;
      }
    }
    .adm-radio{
      border: 1px solid #f2f2f2;
      border-radius: 6px;
      padding: 4px 10px;
      margin-bottom: 10px;
      margin-right: 10px;
    }
    .adm-radio-content {
      font-size: 14px;
      color: #333;
      white-space: nowrap;
      padding: unset;
    }
  }
}
.buttonText {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
.desc{
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  justify-content: flex-end;
}
.icon{
  transition: transform 0.2s ease;
}
.show{
  transform: rotate(90deg);
}
.button {
  background-color: #337fff;
  border-radius: 50px;
  color: white;
  font-size: 16px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.subTitle{
  padding: 10px 0;
  font-size: 12px;
}
.address_bar {
  display: flex;
  flex-flow: row nowrap;
  justify-content: space-between;
  align-items: center;
  width: 100%;

  .address {
    flex: 0 1 auto;
    font-size: 14px;
    color: #333333;
    // @include common_ellipsis(2);
  }
}

.feedbackContainer {
  padding: 12px 12px;
  background-color: #f6f7fb;
  border-radius: 10px;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
  .textareaSolution{
    width: 100%;
    height: 150px;
    color: #898a8c;
    font-size: 12px;
    border-radius: 10px;
    box-sizing: border-box;
  }
}

.input {
  flex: 1 1 auto;
  font-size: 14px;
  height: 92px;
  color: #898a8c;
  @include common_ellipsis(2);
  margin-right: 20px;
}
.otherInput{
  width: 100%;
  font-size: 12px;
  height: 28px;
  padding: 2px 10px;
  box-sizing: border-box;
  border: 1px solid #f0f0f0;
}
.textarea{
  width: 100%;
  background: #f6f7fb;
  height: 150px;
  color: #898a8c;
  font-size: 16px;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 5px;
  position: relative;
  border: 1px solid transparent;
}
.textarea__require{
  border: 1px solid #e64340;
}
.placeholder{
  font-size: 16px;
  font-weight: 400;
}
.tag_list {
  display: flex;
  width: 100%;
  flex-flow: row wrap;

  .switchBar {
    @include common_ellipsis(1);
    white-space: nowrap;
    max-width: 150px;
    box-sizing: border-box;
    display: flex;
    flex-flow: row nowrap;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    margin-bottom: 10px;
    padding-top: 4px;
    padding-bottom: 4px;
    background: #e9eefb;
    color: #4581ff;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid transparent;
    overflow: hidden;
  }

}

.tack_list {
  .tack_item {
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-flow: row nowrap;
    justify-content: space-between;
    align-items: center;
    background-color: #f6f7fb;
    padding: 0px 8px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .tack_context {
      color: #343435;
      font-size: 16px;

      .subTitle{
        white-space: nowrap;
        margin-right: 10px;
      }
      input{
        background-color: #f2f2f2;
        font-size: 12px;
        padding: 2px 8px;
      }
    }
  }
}
.disabled {
  opacity: 0.5;
}

.empty{
  font-size: 12px;
  color: #ccc;
  text-align: center;
}
