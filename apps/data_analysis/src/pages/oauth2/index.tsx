import { redirectToOAuth2 } from '@/utils/auth'
import React, { useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'

export function Component() {
  const [searchParams] = useSearchParams({
    appid: '', // wpr_caCgAAgbJAAsmdDoRDL0htN6dzwQ
    agentid: '', // 1000009
    redirectUri: ``,
  })
  useEffect(() => {
    const corp_id = searchParams.get('appid') || ''
    const agent_id = searchParams.get('agentid') || ''
    const redirectUri = searchParams.get('redirectUri') || ''
    // 如果需要自动跳转到企业微信登录
    if (corp_id && agent_id) {
      redirectToOAuth2(corp_id, agent_id, redirectUri)
    }
  }, [searchParams])
  return <div>跳转中。。。</div>
}
