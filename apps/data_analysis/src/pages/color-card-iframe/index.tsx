import { GenerateAccountSetId } from '@/service/api'
import { useEffect, useRef, useState } from 'react'

export function Component() {
  const { mutateAsync } = GenerateAccountSetId()
  const [url, setUrl] = useState('')
  async function getId() {
    const res = await mutateAsync()
    setUrl(`https://hcscmtest.zzfzyc.com/qywx_back/colorCardShopIndex?id=${res.encrypted_account_set_id}`)
  }
  useEffect(() => {
    getId()
  }, [])
  return (
    <div className="h-full w-full">
      <iframe
        src={url}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
        }}
        title="电子色卡"
      />
    </div>
  )
}
