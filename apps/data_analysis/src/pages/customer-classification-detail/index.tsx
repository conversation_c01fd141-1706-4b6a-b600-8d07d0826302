import HeaderBox from '@/components/headerBox'
import Links from '@/components/Links'
import ListFrame from '@/components/listFrame'
import { useRouterPush } from '@/hooks/routerPush'
import router from '@/router'
import { FetchGetCustomerMatrixDetail } from '@/service/api/customer'
import { useMobileScreen } from '@/utils'
import { RightOutlined, SearchOutlined } from '@ant-design/icons'
import { formatPriceDiv } from '@ly/utils'
import { Button, Dropdown, Input, message, Select, Table, Tag } from 'antd'
import { Card, Divider, NavBar, SearchBar, Selector } from 'antd-mobile'
import classNames from 'classnames'
import dayjs from 'dayjs'
import React, { useCallback, useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

interface CustomerData {
  id: string
  name: string
  type: '核心客户' | '潜力客户' | '活跃低价值' | '普通客户'
  amount: number
  frequency: number
  lastTransaction: string
}

// 客户类型映射
const CUSTOMER_TYPE_MAP = {
  1: '核心客户',
  2: '潜力客户',
  3: '活跃低价值',
  4: '普通客户',
} as const

// 客户类型配置
const CUSTOMER_TYPE_CONFIG = {
  核心客户: {
    color: '#ef4444',
    description: '购买频率≥xx，金额≥xx',
  },
  潜力客户: {
    color: '#f97316',
    description: '购买频率＜xx，金额≥xx',
  },
  活跃低价值: {
    color: '#14b8a6',
    description: '购买频率＜xx，金额＜xx',
  },
  普通客户: {
    color: '#6b7280',
    description: '购买频率≥xx，金额＜xx',
  },
} as const
// 类型定义
type CustomerType = keyof typeof CUSTOMER_TYPE_CONFIG
const typeColorMap = Object.fromEntries(
  Object.entries(CUSTOMER_TYPE_CONFIG).map(([key, value]) => [key, value.color]),
) as Record<CustomerType, string>
// 客户类型标签组件
function CustomerTypeTag({ type }: { type: keyof typeof typeColorMap }) {
  return (
    <Tag color={typeColorMap[type]} style={{ borderRadius: '4px' }}>
      {type}
    </Tag>
  )
}
// 客户类型说明组件
function CustomerTypeExplanation({ isMobile, averageData }: {
  isMobile?: boolean
  averageData: { averageOrderNum: number, averageSalePrice: number }
}) {
  const containerClass = isMobile ? styles.mobileCustomerTypeExplain : styles.customerTypeExplain

  // 动态生成客户类型说明
  const dynamicConfig = {
    核心客户: {
      color: '#ef4444',
      description: `购买频率≥${averageData.averageOrderNum.toFixed(0)}，金额≥¥${formatPriceDiv(averageData.averageSalePrice).toLocaleString()}`,
    },
    潜力客户: {
      color: '#f97316',
      description: `购买频率＜${averageData.averageOrderNum.toFixed(0)}，金额≥¥${formatPriceDiv(averageData.averageSalePrice).toLocaleString()}`,
    },
    活跃低价值: {
      color: '#14b8a6',
      description: `购买频率≥${averageData.averageOrderNum.toFixed(0)}，金额＜¥${formatPriceDiv(averageData.averageSalePrice).toLocaleString()}`,
    },
    普通客户: {
      color: '#6b7280',
      description: `购买频率＜${averageData.averageOrderNum.toFixed(0)}，金额＜¥${formatPriceDiv(averageData.averageSalePrice).toLocaleString()}`,
    },
  }

  return (
    <div className={containerClass}>
      <h3>客户类型说明</h3>
      <div>
        {Object.entries(dynamicConfig).map(([type, config]) => (
          <div key={type}>
            <span className={styles.dot} style={{ backgroundColor: config.color }}></span>
            {' '}
            {type}
            :
            {config.description}
          </div>
        ))}
      </div>
    </div>
  )
}
export function Component() {
  const isMobile = useMobileScreen()
  const location = useLocation()
  // 计算最近30天的日期范围
  const endDate = dayjs().format('YYYY-MM-DD')
  const startDate = dayjs().subtract(29, 'day').format('YYYY-MM-DD')
  const [dateRange, setDateRange] = useState<string>('最近30天')
  const [customerType, setCustomerType] = useState<string[]>([])
  const [searchValue, setSearchValue] = useState<string>('')
  const [customerData, setCustomerData] = useState<CustomerData[]>([])
  const [filteredData, setFilteredData] = useState<CustomerData[]>([])
  const [loading, setLoading] = useState(false)
  const [averageData, setAverageData] = useState<{
    averageOrderNum: number
    averageSalePrice: number
  }>({ averageOrderNum: 0, averageSalePrice: 0 })

  // 防抖搜索函数
  const debouncedSearch = useCallback(
    debounce((customerName: string) => {
      const params = new URLSearchParams(location.search)
      const startDate = params.get('startDate')
      const endDate = params.get('endDate')
      loadCustomerData(startDate || undefined, endDate || undefined, customerName || undefined)
    }, 500),
    [location.search],
  )

  // 防抖函数实现
  function debounce<T extends (...args: any[]) => void>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout
    return ((...args: any[]) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }) as T
  }

  // 获取客户矩阵详情数据
  const { mutate: fetchCustomerMatrixDetail } = FetchGetCustomerMatrixDetail({
    onSuccess: (data) => {
      if (data) {
        // 转换API数据为组件所需格式
        const allCustomers: CustomerData[] = []

        // 处理核心客户
        if (data.core_list) {
          data.core_list.forEach((customer) => {
            allCustomers.push({
              id: customer.customer_id?.toString() || '',
              name: customer.customer_name || '',
              type: '核心客户',
              amount: customer.total_sale_price || 0,
              frequency: customer.order_num || 0,
              lastTransaction: customer.order_time || '',
            })
          })
        }

        // 处理潜力客户
        if (data.potential_list) {
          data.potential_list.forEach((customer) => {
            allCustomers.push({
              id: customer.customer_id?.toString() || '',
              name: customer.customer_name || '',
              type: '潜力客户',
              amount: customer.total_sale_price || 0,
              frequency: customer.order_num || 0,
              lastTransaction: customer.order_time || '',
            })
          })
        }

        // 处理活跃低价值客户
        if (data.ordinary_list) {
          data.ordinary_list.forEach((customer) => {
            allCustomers.push({
              id: customer.customer_id?.toString() || '',
              name: customer.customer_name || '',
              type: '活跃低价值',
              amount: customer.total_sale_price || 0,
              frequency: customer.order_num || 0,
              lastTransaction: customer.order_time || '',
            })
          })
        }

        // 处理普通客户
        if (data.un_active_list) {
          data.un_active_list.forEach((customer) => {
            allCustomers.push({
              id: customer.customer_id?.toString() || '',
              name: customer.customer_name || '',
              type: '普通客户',
              amount: customer.total_sale_price || 0,
              frequency: customer.order_num || 0,
              lastTransaction: customer.order_time || '',
            })
          })
        }

        setCustomerData(allCustomers)
        setAverageData({
          averageOrderNum: data.average_order_num || 0,
          averageSalePrice: data.average_sale_price || 0,
        })
      }
      setLoading(false)
    },
    onError: (error) => {
      console.error('获取客户数据失败:', error)
      message.error('获取客户数据失败')
      setLoading(false)
    },
  })

  // 客户类型选项
  const customerTypeOptions = [
    { label: '核心客户', value: '核心客户' },
    { label: '潜力客户', value: '潜力客户' },
    { label: '活跃低价值', value: '活跃低价值' },
    { label: '普通客户', value: '普通客户' },
  ]

  // 统计数据
  const statistics = {
    核心客户: customerData.filter(item => item.type === '核心客户').length,
    潜力客户: customerData.filter(item => item.type === '潜力客户').length,
    活跃低价值: customerData.filter(item => item.type === '活跃低价值').length,
    普通客户: customerData.filter(item => item.type === '普通客户').length,
  }

  // 处理搜索和筛选
  useEffect(() => {
    let result = [...customerData]

    // 按客户类型筛选
    if (customerType.length > 0) {
      result = result.filter(item => customerType.includes(item.type))
    }

    // 按搜索关键词筛选
    if (searchValue) {
      result = result.filter(item =>
        item.name.toLowerCase().includes(searchValue.toLowerCase()),
      )
    }

    setFilteredData(result)
  }, [customerType, searchValue, customerData])
  // 加载客户数据的函数
  const loadCustomerData = async (startTime?: string, endTime?: string, customerName?: string) => {
    setLoading(true)
    fetchCustomerMatrixDetail({
      start_time: startTime,
      end_time: endTime,
      customer_name: customerName,
    })
  }
  // 从URL获取参数并加载数据
  useEffect(() => {
    const params = new URLSearchParams(location.search)
    // const startDate = params.get('startDate')
    const endDate = params.get('endDate')

    if (startDate && endDate) {
      const timeRange = startDate === endDate ? endDate : `${startDate}至${endDate}`
      setDateRange(timeRange)
      // 加载对应时间范围的客户数据
      loadCustomerData(startDate, endDate)
    }
    else {
      // 如果没有时间参数，加载默认数据
      loadCustomerData()
    }
  }, [location.search])

  // PC端表格列定义
  const columns = [
    {
      title: '客户名称',
      dataIndex: 'name',
      key: 'name',
      render: (_: keyof typeof typeColorMap, record: Api.GetCustomerMatrixDetail.ShouldCollectOrderCustomerMatrixDetail) => {
        const params = {
          customer_id: record.id,
          customer_name: encodeURIComponent(record.name || ''),
          start_time: startDate,
          end_time: endDate,
        }

        const queryString = Object.entries(params)
          .filter(([_, value]) => value !== undefined)
          .map(([key, value]) => `${key}=${value}`)
          .join('&')

        return <Links to={`/customer/infos?${queryString}`}>{record.name}</Links>
      },
    },
    {
      title: '客户类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: keyof typeof typeColorMap) => <CustomerTypeTag type={type} />,
    },
    {
      title: '采购金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => `¥${amount.toLocaleString()}`,
      sorter: (a: CustomerData, b: CustomerData) => a.amount - b.amount,
    },
    {
      title: '购买频率',
      dataIndex: 'frequency',
      key: 'frequency',
      render: (frequency: number) => (
        <span>
          {frequency}
          {' '}
          {frequency > 10 ? '↑' : frequency < 7 ? '↓' : ''}
        </span>
      ),
      sorter: (a: CustomerData, b: CustomerData) => a.frequency - b.frequency,
    },
    {
      title: '最近交易',
      dataIndex: 'lastTransaction',
      key: 'lastTransaction',
      sorter: (a: CustomerData, b: CustomerData) =>
        new Date(a.lastTransaction).getTime() - new Date(b.lastTransaction).getTime(),
    },
  ]

  // 渲染PC端界面
  const renderPCView = () => (
    <div className={styles.pcContainer}>
      <div className={styles.header}>
        <h1>
          {dateRange}
          客户分类明细
        </h1>
        <div className={styles.filters}>
          <Select
            mode="multiple"
            allowClear
            style={{ width: '200px' }}
            placeholder="客户类型筛选"
            value={customerType}
            onChange={setCustomerType}
            options={customerTypeOptions}
            maxTagCount="responsive"
            popupMatchSelectWidth={false}
          />
          <Input
            placeholder="搜索客户名称"
            prefix={<SearchOutlined />}
            onChange={(e) => {
              const value = e.target.value
              setSearchValue(value)
              // 使用防抖搜索
              debouncedSearch(value)
            }}
            style={{ width: 200 }}
          />
        </div>
      </div>

      <CustomerTypeExplanation averageData={averageData} />

      <div className={styles.statistics}>
        <div className={classNames(styles.statItem, 'text-[#2e6aec]')} style={{ backgroundColor: 'rgb(239, 246, 255)' }}>
          <h3>核心客户数量</h3>
          <p>{statistics['核心客户']}</p>
        </div>
        <div className={classNames(styles.statItem, 'text-[#ec6a26]')} style={{ backgroundColor: 'rgb(255, 247, 237)' }}>
          <h3>潜力客户数量</h3>
          <p>{statistics['潜力客户']}</p>
        </div>
        <div className={classNames(styles.statItem, 'text-[#209d92]')} style={{ backgroundColor: 'rgb(240, 253, 250)' }}>
          <h3>活跃低价值数量</h3>
          <p>{statistics['活跃低价值']}</p>
        </div>
        <div className={classNames(styles.statItem, 'text-[#535d6a]')} style={{ backgroundColor: 'rgb(249, 250, 251)' }}>
          <h3>普通客户数量</h3>
          <p>{statistics['普通客户']}</p>
        </div>
      </div>
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 flex flex-col overflow-auto">
          <Table
            rootClassName={styles['table-container']}
            dataSource={filteredData}
            columns={columns}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 100,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: total => `共 ${total} 条记录`,
            }}
            scroll={{ y: true }}
            sticky
          />
        </div>
      </div>
    </div>
  )
  const { routerPushByKey } = useRouterPush()
  function handleClickCustomer(customer: CustomerData) {
    const params = {
      customer_id: customer.id,
      customer_name: encodeURIComponent(customer.name || ''),
      start_time: startDate,
      end_time: endDate,
    }

    const queryString = Object.entries(params)
      .filter(([_, value]) => value !== undefined)
      .map(([key, value]) => `${key}=${value}`)
      .join('&')
    routerPushByKey(`/customer/infos?${queryString}`)
  }
  // 渲染移动端界面
  const renderMobileView = () => (
    <div className={styles.mobileContainer}>

      <CustomerTypeExplanation isMobile averageData={averageData} />

      <div className={styles.mobileFilters}>
        {/* <Selector
          options={customerTypeOptions}
          value={customerType}
          multiple
          onChange={v => setCustomerType(v as string[])}
          style={{ '--border-radius': '4px' }}
        />
        <SearchBar
          placeholder="搜索客户名称"
          onChange={v => setSearchValue(v)}
          style={{ '--border-radius': '4px', 'marginTop': '8px' }}
        /> */}
        <Select
          mode="multiple"
          allowClear
          style={{ width: '200px' }}
          placeholder="客户类型筛选"
          value={customerType}
          onChange={setCustomerType}
          options={customerTypeOptions}
          maxTagCount="responsive"
          popupMatchSelectWidth={false}
        />
        <Input
          placeholder="搜索客户名称"
          prefix={<SearchOutlined />}
          allowClear
          onChange={(e) => {
            const value = e.target.value
            setSearchValue(value)
            // 使用防抖搜索
            debouncedSearch(value)
          }}
          style={{ flex: 1 }}
        />
      </div>

      <div className={styles.mobileStatistics}>
        <div className={styles.mobileStatRow}>
          <div className={styles.mobileStatItem} style={{ backgroundColor: 'rgba(255, 182, 185, 0.2)' }}>
            <div className={styles.mobileStatLabel}>核心客户数量</div>
            <div className={styles.mobileStatValue}>{statistics['核心客户']}</div>
          </div>
          <div className={styles.mobileStatItem} style={{ backgroundColor: 'rgba(255, 238, 173, 0.2)' }}>
            <div className={styles.mobileStatLabel}>潜力客户数量</div>
            <div className={styles.mobileStatValue}>{statistics['潜力客户']}</div>
          </div>
        </div>
        <div className={styles.mobileStatRow}>
          <div className={styles.mobileStatItem} style={{ backgroundColor: 'rgba(187, 222, 214, 0.2)' }}>
            <div className={styles.mobileStatLabel}>活跃低价值数量</div>
            <div className={styles.mobileStatValue}>{statistics['活跃低价值']}</div>
          </div>
          <div className={styles.mobileStatItem} style={{ backgroundColor: 'rgba(226, 226, 226, 0.5)' }}>
            <div className={styles.mobileStatLabel}>普通客户数量</div>
            <div className={styles.mobileStatValue}>{statistics['普通客户']}</div>
          </div>
        </div>
      </div>

      <div className={styles.mobileCustomerList}>
        {filteredData.map(customer => (
          <Card key={customer.id} className={styles.customerCard}>
            <div className={styles.customerHeader}>
              <div className={styles.customerName} onClick={() => handleClickCustomer(customer)}>
                {customer.name}
                <RightOutlined />
              </div>
              <div className={styles.customerType}>
                <CustomerTypeTag type={customer.type} />
              </div>
            </div>
            <Divider style={{ margin: '8px 0' }} />
            <div className={styles.customerDetails}>
              <div className={styles.detailItem}>
                <div className={styles.detailLabel}>采购金额</div>
                <div className={styles.detailValue}>
                  ¥
                  {customer.amount.toLocaleString()}
                </div>
              </div>
              <div className={styles.detailItem}>
                <div className={styles.detailLabel}>购买频率</div>
                <div className={styles.detailValue}>
                  {customer.frequency}
                  {' '}
                  {customer.frequency > 10 ? '↑' : customer.frequency < 7 ? '↓' : ''}
                </div>
              </div>
              <div className={styles.detailItem}>
                <div className={styles.detailLabel}>最近交易</div>
                <div className={styles.detailValue}>{customer.lastTransaction}</div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
  const navigate = useNavigate()
  return (
    <ListFrame
      header={
        isMobile
          ? (
              <NavBar
                onBack={() => navigate(-1)}
                className="bg-white"
              >
                {dateRange}
                客户分类明细
              </NavBar>
            )
          : <HeaderBox showUserInfo />
      }
      className={styles.customerClassificationDetail}
    >

      {isMobile ? renderMobileView() : renderPCView()}
    </ListFrame>
  )
}
