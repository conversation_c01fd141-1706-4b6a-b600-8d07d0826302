.customerClassificationDetail {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f3f4f6;
}

// PC端样式
.pcContainer {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  // max-width: 1200px;
  // margin: 0 auto;
}
.table-container {
  flex: 1;
  overflow: auto;
  
  :global {
    .ant-table-wrapper, .ant-spin-nested-loading, .ant-spin-container {
      height: 100%;
      overflow: hidden;
    }
    .ant-spin-container{
      display: flex;
      flex-direction: column;

    }
    .ant-table {
      height: 100%;
      flex: 1;
      overflow-y: scroll;
    }
  }
}
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h1 {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
  }
}

.filters {
  display: flex;
  gap: 12px;
}

.customerTypeExplain {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  h3 {
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 12px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }

  li {
    display: flex;
    align-items: center;
    font-size: 14px;
  }

  .dot {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
  }
}

.statistics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;

  .statItem {
    background-color: white;
    padding: 8px;
    border-radius: 8px;
    text-align: left;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    h3 {
      font-size: 14px;
      margin: 0 0 4px 0;
    }

    p {
      font-size: 24px;
      font-weight: bold;
      margin: 0;
    }
  }
}

// 移动端样式
.mobileContainer {
  padding: 0;
}

.mobileHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;

  h2 {
    font-size: 16px;
    margin: 0;
    font-weight: bold;
  }

  .backButton, .moreButton {
    font-size: 18px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.mobileCustomerTypeExplain {
  background-color: white;
  padding: 16px;
  margin: 12px;
  border-radius: 8px;

  h3 {
    font-size: 14px;
    margin-top: 0;
    margin-bottom: 12px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    display: flex;
    align-items: center;
    font-size: 12px;
    margin-bottom: 8px;
  }

  .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }
}

.mobileFilters {
  display: flex;
  gap: 8px;
  padding: 0 12px;
  margin-bottom: 12px;
}

.mobileStatistics {
  padding: 0 12px;
  margin-bottom: 12px;

  .mobileStatRow {
    display: flex;
    gap: 12px;
    margin-bottom: 12px;
  }

  .mobileStatItem {
    flex: 1;
    background-color: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

    .mobileStatLabel {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .mobileStatValue {
      font-size: 18px;
      font-weight: bold;
    }
  }
}

.mobileCustomerList {
  padding: 0 12px;

  .customerCard {
    margin-bottom: 12px;
    border-radius: 8px;
    overflow: hidden;
  }

  .customerHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .customerName {
      font-size: 16px;
      font-weight: bold;
      color: #1890ff;
      cursor: pointer;
      // text-decoration: underline;
      transition: color 0.3s ease;
      
      &:hover {
        color: #40a9ff;
        text-decoration: underline;
      }
      
      &:active {
        color: #096dd9;
      }
    }
  }

  .customerDetails {
    display: flex;
    flex-wrap: wrap;

    .detailItem {
      width: 33.33%;
      padding: 4px 0;

      .detailLabel {
        font-size: 12px;
        color: #666;
        margin-bottom: 2px;
      }

      .detailValue {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}
