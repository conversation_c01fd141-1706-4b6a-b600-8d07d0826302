.customerClassificationDetail {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f3f4f6;
}

.pcContainer {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-container {
  flex: 1;
  overflow: auto;
}
.table-container :global .ant-table-wrapper, .table-container :global .ant-spin-nested-loading, .table-container :global .ant-spin-container {
  height: 100%;
  overflow: hidden;
}
.table-container :global .ant-spin-container {
  display: flex;
  flex-direction: column;
}
.table-container :global .ant-table {
  height: 100%;
  flex: 1;
  overflow-y: scroll;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.header h1 {
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.filters {
  display: flex;
  gap: 12px;
}

.customerTypeExplain {
  background-color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.customerTypeExplain h3 {
  font-size: 16px;
  margin-top: 0;
  margin-bottom: 12px;
}
.customerTypeExplain ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.customerTypeExplain li {
  display: flex;
  align-items: center;
  font-size: 14px;
}
.customerTypeExplain .dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 8px;
}

.statistics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 20px;
}
.statistics .statItem {
  background-color: white;
  padding: 8px;
  border-radius: 8px;
  text-align: left;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.statistics .statItem h3 {
  font-size: 14px;
  margin: 0 0 4px 0;
}
.statistics .statItem p {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.mobileContainer {
  padding: 0;
}

.mobileHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 10;
}
.mobileHeader h2 {
  font-size: 16px;
  margin: 0;
  font-weight: bold;
}
.mobileHeader .backButton, .mobileHeader .moreButton {
  font-size: 18px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobileCustomerTypeExplain {
  background-color: white;
  padding: 16px;
  margin: 12px;
  border-radius: 8px;
}
.mobileCustomerTypeExplain h3 {
  font-size: 14px;
  margin-top: 0;
  margin-bottom: 12px;
}
.mobileCustomerTypeExplain ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.mobileCustomerTypeExplain li {
  display: flex;
  align-items: center;
  font-size: 12px;
  margin-bottom: 8px;
}
.mobileCustomerTypeExplain .dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.mobileFilters {
  display: flex;
  gap: 8px;
  padding: 0 12px;
  margin-bottom: 12px;
}

.mobileStatistics {
  padding: 0 12px;
  margin-bottom: 12px;
}
.mobileStatistics .mobileStatRow {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}
.mobileStatistics .mobileStatItem {
  flex: 1;
  background-color: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.mobileStatistics .mobileStatItem .mobileStatLabel {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}
.mobileStatistics .mobileStatItem .mobileStatValue {
  font-size: 18px;
  font-weight: bold;
}

.mobileCustomerList {
  padding: 0 12px;
}
.mobileCustomerList .customerCard {
  margin-bottom: 12px;
  border-radius: 8px;
  overflow: hidden;
}
.mobileCustomerList .customerHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.mobileCustomerList .customerHeader .customerName {
  font-size: 16px;
  font-weight: bold;
}
.mobileCustomerList .customerDetails {
  display: flex;
  flex-wrap: wrap;
}
.mobileCustomerList .customerDetails .detailItem {
  width: 33.33%;
  padding: 4px 0;
}
.mobileCustomerList .customerDetails .detailItem .detailLabel {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}
.mobileCustomerList .customerDetails .detailItem .detailValue {
  font-size: 14px;
  font-weight: 500;
}