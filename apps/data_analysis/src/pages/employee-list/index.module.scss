.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f7f7f7;
}
.container1{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.nav_container {
  background-color: var(--adm-color-primary);
  color: white;
  flex-shrink: 0;
}

.search {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  flex-shrink: 0;
  gap: 8px;
  .search_input{
    flex: 1;
  }
  .search_button{

  }
}

.department_list {
  background: #fff;
  :global {
    .adm-capsule-tabs-header{
      padding: 6px 8px;
    }
    .adm-capsule-tabs {
      --active-color: var(--adm-color-primary);
      --background-color: #f5f5f5;
    }
  }
}

.list_container {
  flex: 1;
  overflow: hidden;
  
  :global {
    .adm-index-bar {
      height: 100%;
    }
    
    .adm-list-item-content-main {
      padding: 12px 0;
    }
  }
}

.employee_item {
  display: flex;
  align-items: center;
  gap: 6px;
  .employee_avatar{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    color: white;
    border: 2px solid #337fff;
    background: linear-gradient(337deg, #7bb7ff 0%, #4581ff 100%);
  }
  .employee_content{
    
  }
  .name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
  }
  
  .phone {
    font-size: 14px;
    color: #666;
  }
}
.bottom_bar {
  display: flex;
  gap: 12px;
  background: #fff;
  border-top: 1px solid #e5e5e5;
  flex-shrink: 0;
  padding-bottom: calc(12px + env(safe-area-inset-bottom));
  
  :global(.adm-button) {
    flex: 1;
  }
}
