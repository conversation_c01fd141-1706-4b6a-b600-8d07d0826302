import ListFrame from '@/components/listFrame'
import { FetchSaleSystemList } from '@/service/api'
import { FetchEmployeeList } from '@/service/api/employee'
import { FetchSaleSystemList1 } from '@/service/api/marketingSysytem'
import { EmployeeType } from '@/service/request/type'
import { Button, CapsuleTabs, Checkbox, IndexBar, List, NavBar, Radio, SearchBar } from 'antd-mobile'
import { useKeepAliveRef } from 'keepalive-for-react'
import { pinyin } from 'pinyin-pro'
import { useEffect, useMemo, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

interface Department {
  id: number
  name: string
  code?: string
}
export function Component() {
  const [search, setSearch] = useState<{ sale_system_id: number | null, name: string }>({
    sale_system_id: null,
    name: '', // 修改为与接口匹配的参数名
  })
  const aliveRef = useKeepAliveRef()

  const [searchTerm, setSearchTerm] = useState('') // 用于暂存SearchBar的输入值
  const location = useLocation()
  const navigate = useNavigate()
  const [employeeList, setEmployeeList] = useState<Api.Employee.Response[]>([])
  const [total, setTotal] = useState(0)
  const { mutateAsync: fetchEmployees } = FetchEmployeeList()
  const [multipleSelections, setMultipleSelections] = useState<Api.Employee.Response[]>([])
  const [statusList, setStatusList] = useState<Department[]>([
    { id: -1, name: '全部人员' },
  ])

  const type = (location.state?.type || 'checkbox') as 'radio' | 'checkbox'
  const letterList = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ#'

  const getSearchData = (value: string) => {
    setSearchTerm(value)
  }
  function handleSearch() {
    setSearch(prev => ({ ...prev, name: searchTerm })) // 点击搜索按钮时，用 searchTerm 更新 search.name
  }

  async function getEmployees() {
    try {
      const res = await fetchEmployees({
        ...search,
        duty: EmployeeType.salesman, // 根据实际业务需求设置
        ...(location.state?.query ? JSON.parse(decodeURIComponent(location.state.query)) : {}),
      })

      // 保留从路由状态传递过来的已选ID，并结合当前multipleSelections中的ID
      let initialSelectedIds: number[] = []
      console.log('selectedIds', location.state.selectedIds)
      if (location.state?.selectedIds) {
        try {
          initialSelectedIds = JSON.parse(decodeURIComponent(location.state.selectedIds))
          console.log('initialSelectedIds:', initialSelectedIds) // 添加这一行以检查解析后的值
        }
        catch (error) {
          console.error('Failed to parse selectedIds from location state:', error)
          initialSelectedIds = []
        }
      }
      // 将 multipleSelections 中已有的选项也加入到初始选中判断中
      const currentSelectedIds = multipleSelections.map(item => item.id || 0)
      const allSelectedIds = Array.from(new Set([...initialSelectedIds, ...currentSelectedIds]))
      console.log('allSelectedIds', allSelectedIds)
      const employees = res?.list?.map((item) => {
        const isChecked = allSelectedIds.includes(item.id!) || false // 检查 item.id 是否在 initialSelectedIds 中，或者在 multipleSelections 中
        if (isChecked && !multipleSelections.some(selected => selected.id === item.id)) {
          setMultipleSelections(prev => [...prev, item])
        }
        return {
          ...item,
          checked: isChecked,
        }
      }) || []

      setEmployeeList(employees)
      setTotal(res?.total || 0)
    }
    catch (e) {
      console.error('获取员工列表失败:', e)
    }
  }

  const handleSelect = (employee: Api.Employee.Response) => {
    if (type === 'checkbox') {
      setMultipleSelections(prev => [...prev, employee])
    }
    else {
      setMultipleSelections([employee])
    }
  }

  const handleUnselect = (employee: Api.Employee.Response) => {
    if (type === 'checkbox') {
      setMultipleSelections(prev => prev.filter(item => item.id !== employee.id))
    }
    else {
      setMultipleSelections([])
    }
  }

  const handleConfirm = () => {
    sessionStorage.setItem('employeeListData', JSON.stringify(multipleSelections))
    navigate(-1)
  }

  const handleReset = () => {
    setMultipleSelections([])
    setSearch({
      sale_system_id: null,
      name: '',
    })
    // 清空 employeeList 中的 checked 状态
    setEmployeeList(prevList => prevList.map(emp => ({ ...emp, checked: false })))
    sessionStorage.setItem('employeeListData', JSON.stringify([]))
    navigate(-1)
  }

  const { mutateAsync: departmentApi } = FetchSaleSystemList()
  const getDepartments = async () => {
    try {
      const res = await departmentApi({
        status: 1, // 获取启用状态的营销体系
      })
      const departments: Department[] = res.list.map(item => ({
        id: item.id || 0,
        name: item.name || '',
        code: item.code,
      }))
      setStatusList([{ id: -1, name: '全部人员' }, ...departments])
    }
    catch (e) {
      console.error('获取部门列表失败:', e)
    }
  }

  const handleDepartmentChange = (department: Department) => {
    if (department.name === '全部人员') {
      setSearch(prev => ({ ...prev, sale_system_id: null }))
    }
    else {
      setSearch(prev => ({ ...prev, sale_system_id: department.id }))
    }
  }
  useEffect(() => {
    setEmployeeList(prevList =>
      prevList.map(emp => ({
        ...emp,
        checked: multipleSelections.some(sel => sel.id === emp.id),
      })),
    )
  }, [multipleSelections])
  useEffect(() => {
    getDepartments()
    return () => {
      aliveRef.current?.destroy()
    }
  }, [])

  useEffect(() => {
    getEmployees()
  }, [search])

  // useEffect(() => {
  //   if (location.state?.selectedIds) {
  //     try {
  //       const initialSelections = JSON.parse(decodeURIComponent(location.state.selectedIds))
  //       setMultipleSelections(initialSelections)
  //     }
  //     catch (error) {
  //       console.error('Failed to parse selectedIds from location state for initial selections:', error)
  //       setMultipleSelections([])
  //     }
  //   }
  // }, [])

  // 按拼音首字母分组
  const groupedEmployees = useMemo(() => {
    const groups: Record<string, Api.Employee.Response[]> = {}
    employeeList.forEach((employee) => {
      if (employee.name) {
        const initials = pinyin(employee.name, { pattern: 'first', type: 'array', v: true })
        const initial = letterList.includes(initials?.[0].toUpperCase() || '#')
          ? initials[0].toUpperCase()
          : '#'
        if (!groups[initial]) {
          groups[initial] = []
        }
        groups[initial].push(employee)
      }
    })
    return groups
  }, [employeeList])

  return (
    <ListFrame
      className={styles.container}
      customContainerClassName={styles.container1}
      header={(
        <NavBar
        // back={<LeftOutlined />}
          onBack={() => navigate(-1)}
          className={styles.nav_container}
        >
          选择人员
        </NavBar>
      )}
      footer={(
        <div className={styles.bottom_bar}>
          <Button
            fill="outline"
            className={styles.reset_button}
            onClick={handleReset}
          >
            重置
          </Button>
          <Button
            color="primary"
            disabled={multipleSelections.length === 0}
            onClick={handleConfirm}
          >
            确定(
            {multipleSelections.length}
            )
          </Button>
        </div>
      )}
    >

      <div className={styles.search}>
        <SearchBar
          placeholder="请输入员工姓名"
          value={searchTerm}
          onChange={getSearchData}
          onSearch={handleSearch} // 用户按下回车键时也触发搜索
          className={styles.search_input}
          onClear={() => {
            setSearchTerm('')
            setSearch(prev => ({ ...prev, name: '' })) // 清除搜索框内容后立即触发搜索
          }} // 清除搜索框内容
        />
        <Button
          color="primary"
          onClick={handleSearch}
          className={styles.search_button}
          size="small" // 可以调整按钮大小
        >
          搜索
        </Button>
      </div>

      <div className={styles.department_list}>
        <CapsuleTabs
          className={styles.department_tabs}
          onChange={(key) => {
            const department = statusList.find(item => item.id === Number(key))
            if (department) {
              handleDepartmentChange(department)
            }
          }}
          activeKey={`${search.sale_system_id || -1}`}
        >
          {statusList.map(department => (
            <CapsuleTabs.Tab
              title={department.name}
              key={department.id}
            />
          ))}
        </CapsuleTabs>
      </div>

      <div className={styles.list_container}>
        <IndexBar>
          {Object.entries(groupedEmployees).map(([initial, employees]) => (
            <IndexBar.Panel
              index={initial}
              title={initial}
              key={initial}
            >
              <List>
                {employees.map(employee => (
                  <List.Item
                    // eslint-disable-next-line style/multiline-ternary
                    prefix={type === 'checkbox' ? (
                      <Checkbox
                        checked={employee.checked}
                        onChange={(checked) => {
                          if (checked) {
                            handleSelect(employee)
                          }
                          else {
                            handleUnselect(employee)
                          }
                        }}
                      />
                    ) : (
                      <Radio
                        checked={employee.checked}
                        onChange={(checked) => {
                          if (checked) {
                            handleSelect(employee)
                          }
                          else {
                            // Radio 通常不需要取消选择的逻辑，除非是Radio.Group
                            // 这里保持和Checkbox一致，但实际Radio点击已选中的不会触发onChange(false)
                            handleUnselect(employee)
                          }
                        }}
                      />
                    )}
                    key={employee.id}
                    arrowIcon={false}
                    onClick={() => {
                      if (employee.checked) {
                        handleUnselect(employee)
                      }
                      else {
                        handleSelect(employee)
                      }
                    }}
                    // extra={employee.checked ? '已选' : ''}
                  >
                    <div className={styles.employee_item}>
                      <div className={styles.employee_avatar}>{employee.name?.slice(0, 1)}</div>
                      <div className={styles.employee_content}>
                        <div className={styles.name}>{employee.name}</div>
                        <div className={styles.phone}>{employee.phone}</div>
                      </div>
                      {/* {employee.department_name && (
                        <span className={styles.department}>{employee.department_name}</span>
                      )} */}
                    </div>
                  </List.Item>
                ))}
              </List>
            </IndexBar.Panel>
          ))}
        </IndexBar>
      </div>
    </ListFrame>
  )
}
