import <PERSON><PERSON>hart from '@/components/echartsComponents/PieChart'
import { GetTeamMatchableProductDetailData, GetTeamMatchableProductPurchaserRankDetailList } from '@/service/api/customerVisit'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { formatHashTag, formatPriceMul, formatTimeCustomized } from '@ly/utils'
import { useMount } from 'ahooks'
import { Card, ErrorBlock, List, Loading, NavBar, Popover, ProgressBar, Tabs, Toast } from 'antd-mobile'
import dayjs from 'dayjs'
import { useEffectOnActive } from 'keepalive-for-react'
import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom' // Assuming react-router-dom for routing
import styles from './index.module.scss'
// IconFont and other custom components like LayoutBlock would need to be standard React components
// For LayoutBlock, we'll use Card. For IconFont, we'll use a placeholder or antd-mobile icon.

interface RouterParams {
  isTeam?: string
  startTime?: string
  endTime?: string
}

// Simplified TablePropsType for List rendering
interface ProductItemType {
  key: string | number
  product_id: string
  product_code: string
  product_name: string
  [key: string]: any
}

export function Component() {
  const location = useLocation()
  const params = (location.state as RouterParams) || {} // Assuming params are passed via location.state
  const startTime = params.startTime ? decodeURIComponent(params.startTime) : ''
  const endTime = params.endTime ? decodeURIComponent(params.endTime) : ''

  const [pieChartData, setPieChartData] = useState<Array<{ name: string, value: number }>>([])
  const { mutateAsync: getTeamRankApi, isPending, data } = GetTeamMatchableProductDetailData({
    onSuccess: (res) => {
      const list = res.list?.slice(0, 10) || []
      const chartData = list?.map(item => ({ name: formatHashTag(item.product_code, item.product_name), value: item.roll }))
      setPieChartData(chartData)
    },
    onError: (error) => {
      Toast.show({ content: error.message })
    },
  })
  const { mutateAsync: getTeamPurchaserApi, isPending: rankLoading, data: rankData } = GetTeamMatchableProductPurchaserRankDetailList({

    onError: (error) => {
      Toast.show({ content: error.message })
    },
  })

  const [activeTab, setActiveTab] = useState('0')
  const getData = async () => {
    try {
      await getTeamRankApi({
        start_time: dayjs(startTime).format('YYYY-MM-DD'),
        end_time: dayjs(endTime).format('YYYY-MM-DD'),
      })
    }
    catch (error) {
      Toast.show({ content: 'Error fetching rank data.' })
      console.error('Error fetching rank data:', error)
    }
  }
  const navigate = useNavigate()

  const getPurchaserData = async () => {
    try {
      await getTeamPurchaserApi({
        start_time: dayjs(startTime).format('YYYY-MM-DD'),
        end_time: dayjs(endTime).format('YYYY-MM-DD'),
      })
    }
    catch (error) {
      Toast.show({ content: 'Error fetching purchaser data.' })
      console.error('Error fetching purchaser data:', error)
    }
  }

  useEffectOnActive(() => {
    getData()
  }, []) // Dependencies for fetching initial data

  const handleTabChange = (key: string) => {
    setActiveTab(key)
    if (key === '1') {
      // Check if data already exists to avoid redundant calls
      const currentPurchaserData = rankData?.list
      if (!currentPurchaserData || currentPurchaserData.length === 0) {
        getPurchaserData()
      }
    }
  }

  const renderProductRanking = () => {
    const isLoading = isPending
    const dataList = data?.list?.slice(0, 10) || []

    if (isLoading) {
      return <div className={styles.loadingContainer}><Loading /></div>
    }
    if (dataList.length === 0) {
      return <div className={styles.emptyContainer}><ErrorBlock status="empty" /></div>
    }

    return (
      <div className={styles.ranking_list}>
        {dataList.map((rankingItem: any, index: number) => (
          <div className={styles.ranking_bar} key={index}>
            <span className={styles.ranking_text}>
              {index === 0 ? '🥇 ' : index === 1 ? '🥈 ' : index === 2 ? '🥉 ' : '    '}
              {formatHashTag(rankingItem.product_code, rankingItem.product_name)}
            </span>
            <div className={styles.progress_container}>
              <ProgressBar percent={formatPriceMul(rankingItem.proportion)} style={{ '--fill-color': '#4581ff' }} />
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderCustomerList = () => {
    const isLoading = rankLoading
    const customerList = rankData?.list || []

    if (isLoading) {
      return <div className={styles.loadingContainer}><Loading /></div>
    }
    if (customerList.length === 0) {
      return <div className={styles.emptyContainer}><ErrorBlock status="empty" /></div>
    }

    return customerList.map((item: any, index: number) => (
      <Card
        key={index}
        headerStyle={{ borderBottom: 'none', paddingBottom: 0 }}
        title={(
          <div className={styles.customerItemTitle}>
            <span>{item.contacts}</span>
            <span>
              {item.biz_unit_name}
              {' '}
              {item.phone}
            </span>
            <span>{item.sale_user_name}</span>
          </div>
        )}
      >
        <List className={styles.customerProductList}>
          {(item.matchable_product_list && item.matchable_product_list.length > 0)
            ? item.matchable_product_list.map((product: ProductItemType, i: number) => (
                <List.Item key={product.product_id || i}>
                  <div className={styles.productItem}>
                    <div className={styles.productKey}>{i + 1}</div>
                    <div className={styles.productInfo}>{formatHashTag(product.product_code, product.product_name)}</div>
                  </div>
                </List.Item>
              ))
            : <List.Item><ErrorBlock status="empty" /></List.Item>}
        </List>
      </Card>
    ))
  }

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column', background: '#f2f2f2' }}>
      <NavBar
        // back={<LeftOutlined />}
        onBack={() => navigate(-1)}
        className={styles.nav_container}
      >
        匹配产品详情
      </NavBar>
      <Tabs activeKey={activeTab} onChange={handleTabChange} className={styles.tabsContainer}>
        <Tabs.Tab title="产品排行" key="0" className={styles.scrollViewItem}>
          <Card className={styles.layoutBlock}>
            <Popover
              content="客户匹配产品的饼图，产品匹配次数越多，字体占比越大"
              placement="right"
              mode="dark"
              trigger="click"
            >
              <QuestionCircleOutlined className={styles.tooltipIcon} />
            </Popover>
            <div className={styles.container}>
              {isPending
                ? <div className="flex-row items-center justify-center"><Loading /></div>
                : pieChartData.length > 0
                  ? <PieChart data={pieChartData} loading={false} style={{ width: 300, height: 300 }} />

                  : <ErrorBlock status="empty" />}
            </div>
          </Card>
          <Card title="匹配产品排行" className={styles.layoutBlock}>
            {renderProductRanking()}
          </Card>
        </Tabs.Tab>
        <Tabs.Tab title="客户" key="1" className={styles.scrollViewItem}>
          {renderCustomerList()}
        </Tabs.Tab>
      </Tabs>
    </div>
  )
}
