.scrollViewItem {
  display: block; /* Changed from inline-block for Tabs.Tab content */
  width: 100%;
  white-space: initial;
  vertical-align: top;
  height: 100%; /* Ensure parent has height or use flex */
  overflow-y: auto;
}

.tabsContainer :global .adm-tabs-header {
  background-color: white;
}

.nav_container {
  background-color: var(--adm-color-primary);
  color: white;
  flex-shrink: 0;
}

.container {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  min-height: 300px; /* Ensure space for chart */
}

.ranking_title {
  display: flex;
  font-size: 1.1rem;
  color: #333;
  margin-bottom: 8px;
  padding: 0 8px; /* Consistent padding with Card body */
  font-weight: 500;
}
.ranking_bar {
  width: 100%;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}
.ranking_bar .ranking_text {
  font-size: 0.875rem;
  width: 120px; /* Fixed width or flex-basis */
  color: #555;
  margin-right: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.ranking_bar .progress_container {
  flex: 1 1 auto;
}

.tabsBody {
  /* Add styles if Tabs component requires specific body styling */
}

.customerItemTitle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #333;
}

.customerProductList :global .adm-list-item {
  padding-left: 0;
}
.customerProductList .productItem {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #555;
}
.customerProductList .productItem .productKey {
  width: 15%;
  text-align: center;
  color: #777;
  border-right: 1px solid #ddd;
}
.customerProductList .productItem .productInfo {
  width: 85%;
  text-align: left; /* Usually product info is left aligned */
  padding-left: 10px;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  min-height: 100px;
}

.emptyContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100px; /* Ensure empty state has some height */
}

.tooltipIcon {
  margin-right: 8px; /* Space between icon and text if needed */
  color: #707070;
  font-size: 1.2rem; /* Adjust size as needed */
}