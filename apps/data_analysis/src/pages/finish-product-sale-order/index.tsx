import type { RootState } from '@/store'
import HeaderBox from '@/components/headerBox'
import { IFrame } from '@/components/IFrame'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'

export function Component() {
  const token = useSelector((state: RootState) => state.auth.token)
  const [iframeHeight, setIframeHeight] = useState(0)
  useEffect(() => {
    setIframeHeight(document.body.clientHeight - 44)
  }, [])
  const iframeRef = useRef<HTMLIFrameElement>(null)
  return (
    <>
      <HeaderBox
        isMobile
        style={{
          margin: '0 auto',
          maxWidth: '500px',
        }}
      />
      <IFrame
        ref={iframeRef}
        innerStringStyle={`
        .taro_page {
          overflow-y: unset !important;
        }
      `}
        style={{
          maxWidth: '500px',
          width: '100%',
          height: `${iframeHeight}px`,
          overflow: 'hidden',
          border: 'none',
        }}
        src={`${window.location.origin}/qywx_back/finishProductSaleOrderIndex?token=${token}`}
        // src={`http://192.168.1.82:10086/qywx_back/finishProductSaleOrderAdd?token=${token}`}
        // src={`https://hcscmtest.zzfzyc.com/qywx_back/finishProductSaleOrderIndex?token=${token}`}
      />
    </>
  )
}
