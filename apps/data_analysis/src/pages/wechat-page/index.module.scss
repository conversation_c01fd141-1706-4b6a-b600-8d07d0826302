.page {
  display: flex;
  flex-direction: column;
  // height: 100vh;
  background: #f5f5f5;
}
.nav_container {
  background-color: #4581ff;
  color: white;
  flex-shrink: 0;
}
.pageContainer{
  @media (min-width: 1024px) {
    max-width: 440px;
    margin: 0 auto;
  }
}
.searchBox {
  // position: sticky;
  // top: 0;
  // z-index: 100;
  width: 100%;
  padding: 10px;
  background: #ffffff;
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
  .search {
    flex: 1 1 auto;
  }
}

.listBox {
  flex: 1;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.itemBox {
  padding: 8px;
  background: #ffffff;
  border-bottom: 1px solid #f0f0f0;

  :global{
    .adm-checkbox {
      width: 100%;
    }
    .adm-checkbox-content{
      width: 100%;
    }
  }
}

.container {
  display: flex;
  align-items: center;
  width: 100%;

  .pic {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    margin-right: 12px;
  }

  &_main {
    flex: 1;
    margin-right: 16px;
  }

  &_item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    
    + .container_item {
      margin-top: 8px;
    }
  }

  &_bottom_item {
    margin-left: 12px;
  }

  &_bottom_count {
    color: #d9001b;
  }

  .cussName {
    font-size: 12px;
    font-weight: 500;
    color: #000000;
    margin-right: 4px;
  }

  .wechat {
    color: #07c160;
  }

  .company {
    color: #e8be91;
  }

  .icon {
    color: #666;
    font-size: 12px;
  }
}

.footer {
  padding: 8px;
  background: #fff;
  border-top: 1px solid #f0f0f0;
}
.goBack{
    width: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
