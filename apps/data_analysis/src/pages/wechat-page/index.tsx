import type { SearchBarRef } from 'antd-mobile'
import ListFrame from '@/components/listFrame'
import { GetWechatFriends } from '@/service/api/customerVisit'
import { LeftOutlined, RightOutlined } from '@ant-design/icons'
import { getFilterData } from '@ly/utils'
import { useMount, useUnmount } from 'ahooks'
import { message } from 'antd'
import { Button, Checkbox, Divider, DotLoading, Image, InfiniteScroll, NavBar, PullToRefresh, SearchBar } from 'antd-mobile'
import dayjs from 'dayjs'
import { useEffectOnActive, useKeepAliveRef } from 'keepalive-for-react'
import { useContext, useEffect, useMemo, useRef, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

type Type = 'radio' | 'checkbox'

interface ListProps {
  type?: Type
  clentList: any
  selectedItems: any[]
  onSelect: (items: any[]) => void
}

function List(props: ListProps) {
  const { clentList, selectedItems, onSelect, type } = props
  console.log('object :>> ', dayjs().format())

  const handleSelect = (item: any, checked: boolean) => {
    if (type === 'checkbox') {
      if (checked) {
        onSelect([...selectedItems, item])
      }
      else {
        onSelect(selectedItems.filter(it => it.external_user_id !== item.external_user_id))
      }
    }
    else {
      onSelect(checked ? [item] : [])
    }
  }

  // 跳转到客户关系
  const navigate = useNavigate()
  function toDetail(e: React.MouseEvent, item: any) {
    e.stopPropagation()
    localStorage.setItem('customerRelation', JSON.stringify(item))
    navigate('/customer-relation')
  }

  return clentList?.list?.map((item, index) => {
    const isSelected = selectedItems.some(it => it.external_user_id === item.external_user_id)
    return (
      <div key={index} className="w-full overflow-hidden">
        <div className={styles.itemBox}>
          <Checkbox
            checked={isSelected || item.is_bind}
            disabled={item.is_bind}
            onChange={checked => handleSelect(item, checked)}
          >
            <div className={styles.container}>
              <Image src={item.avatar} lazy className={styles.pic} fit="cover" />
              <div className={styles.container_main}>
                <div className={styles.container_item}>
                  <div className={styles.cussName}>
                    {item.name}
                  </div>
                  <div className={styles.platform_source_name}>
                    {
                      item.external_type === 1
                        ? (
                            <span className={styles.wechat}>
                              @
                              {item.external_type_name}
                            </span>
                          )
                        : (
                            <span className={styles.company}>
                              @
                              {item.corp_full_name}
                            </span>
                          )
                    }
                  </div>
                </div>
                <div className={styles.container_item}>
                  <div>
                    企微好友：
                    <span className={styles.container_bottom_count}>{item.corp_wechat_friend_count}</span>
                    人
                  </div>
                  <div className={styles.container_bottom_item}>
                    所在群聊：
                    <span className={styles.container_bottom_count}>{item.corp_group_chat_count}</span>
                    个
                  </div>
                </div>
              </div>
              {/* <RightOutlined className={styles.icon} /> */}
            </div>
          </Checkbox>
        </div>
        <Divider style={{ margin: '0 24px' }} />
      </div>
    )
  })
}

const pageLimit = 30

export function Component() {
  const [messageApi, contextHolder] = message.useMessage()
  const isFirst = useRef(true)
  const location = useLocation()
  const [multiSelection, setMultiSelection] = useState<any[]>([])
  console.log('location', location)
  const { type = 'checkbox', selectedIds } = location.state || {}
  const aliveRef = useKeepAliveRef()

  const [search, setSearch] = useState({
    name: '',
    page: 1,
  })
  const [clentList, setClientlist] = useState<{ list: Api.GetWechatFriends.Response[], total: number }>({ list: [], total: 0 })
  const [hasMore, setHasMore] = useState(true)

  const { mutateAsync: clitentFetch, isPending } = GetWechatFriends()

  const loadMore = async () => {
    if (isPending || clentList.list.length >= clentList.total) {
      setHasMore(false)
      return
    }

    await getCuss(search.page + 1)
  }

  // 下拉刷新
  const onRefresh = async () => {
    setHasMore(true)
    getCuss(1)
  }
  async function getCuss(page?: number, name?: string) {
    try {
      const params = {
        name: name || search.name,
        page: page || search.page,
        size: pageLimit,
      }
      const res = await clitentFetch(getFilterData(params))

      if (isFirst.current) {
        isFirst.current = false
      }
      setClientlist(prev => ({
        total: res?.total || 0,
        list: params.page === 1 ? res?.list : [...prev.list, ...(res?.list || [])],
      }))
      setHasMore((res?.list?.length || 0) > 0 && clentList.list.length < res.total)
      setSearch(prev => ({ ...prev, name: params.name, page: params.page }))
    }
    catch (e) {
      messageApi.error('获取微信好友列表失败')
      setHasMore(true)
    }
  }

  useEffectOnActive(() => {
    getCuss()
    if (selectedIds) {
      setMultiSelection(JSON.parse(decodeURIComponent(selectedIds)))
    }
  }, [])
  useUnmount(() => {
    aliveRef.current?.destroy()
  })
  const navigate = useNavigate()

  const goBack = () => {
    navigate(-1)
  }
  const handleApply = () => {
    if (multiSelection.length === 0) {
      messageApi.info('请选择微信好友')
      return
    }
    console.log('multiSelection', multiSelection)
    // 存储选择的数据
    const selectedData = type === 'radio' ? multiSelection[0] : multiSelection
    sessionStorage.setItem('wechatFriendsData', JSON.stringify(selectedData))
    goBack()
  }
  const inputRef = useRef<SearchBarRef>(null)
  const handleSearch = (value: string) => {
    const trimmedValue = value.trim()
    if (trimmedValue === '') {
      setClientlist({ list: [], total: 0 })
      getCuss(1, '')
      return
    }

    if (trimmedValue.length === 0) {
      messageApi.info('请输入关键词')
      return
    }

    setClientlist({ list: [], total: 0 })
    getCuss(1, trimmedValue)
  }
  const SearchBox = useMemo(() => {
    return (
      <div className={styles.searchBox}>
        <SearchBar
          className={styles.search}
          ref={inputRef}
          placeholder="请输入用户名称"
          onSearch={handleSearch}
          onClear={() => handleSearch('')}
        />
        <Button color="primary" size="small" fill="solid" onClick={() => handleSearch(inputRef.current?.nativeElement?.value || '')}>搜索</Button>
      </div>
    )
  }, [])
  // 添加自定义加载状态组件
  const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
    return (
      <div className="p-3 text-center text-gray-500">
        {hasMore
          ? (
              <>
                <span>加载中</span>
                <DotLoading />
              </>
            )
          : (
              <span>--- 没有更多了 ---</span>
            )}
      </div>
    )
  }
  return (
    <ListFrame
      header={(
        <NavBar
          onBack={() => navigate(-1)}
          className={styles.nav_container}
        >
          微信好友
        </NavBar>
      )}
      footer={(
        <Button
          block
          shape="default"
          color="primary"
          size="large"
          onClick={handleApply}
        >
          确定(
          {multiSelection.length}
          )
        </Button>
      )}
      className={styles.page}
      customContainerClassName={styles.pageContainer}
    >
      {contextHolder}
      {SearchBox}
      <div className={styles.listBox}>
        <PullToRefresh onRefresh={onRefresh}>
          <List
            type={type}
            clentList={clentList}
            selectedItems={multiSelection}
            onSelect={setMultiSelection}
          />
          <InfiniteScroll loadMore={loadMore} hasMore={hasMore}>
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        </PullToRefresh>
      </div>
    </ListFrame>
  )
}
