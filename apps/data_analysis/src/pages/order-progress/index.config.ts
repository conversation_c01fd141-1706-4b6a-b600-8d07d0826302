export interface IData {
  code: number
  data: Array<IDataItem>
  msg: string
}
export interface IDataItem {
  email: string
  gender: string
  name: {
    first: string
    last: string
    title: string
  }
  nat: string
  picture: {
    large: string
    medium: string
    thumbnail: string
  }
}
export function getData() {
  return new Promise<Array<IDataItem>>((resolve, reject) => {
    fetch('https://randomuser.me/api/?results=10&inc=name,gender,email,nat,picture&noinfo')
      .then(res => res.json())
      .then((body) => {
        resolve(body.results)
      })
      .catch(() => {
        reject()
      })
  })
}
