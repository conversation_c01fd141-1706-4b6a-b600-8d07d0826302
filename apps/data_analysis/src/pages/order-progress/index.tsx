import type { IDataItem } from '@/pages/order-progress/index.config.ts'
import DateRangePicker from '@/components/dateRangePicker'
import HeaderBox from '@/components/headerBox'
import IconText from '@/components/iconText'
import { getData } from '@/pages/order-progress/index.config.ts'
import { Avatar, Divider, Flex, List, Skeleton } from 'antd'
import { SearchBar, Tabs } from 'antd-mobile'
import React, { useEffect, useState } from 'react'
import { AiFillLike } from 'react-icons/ai'
import { FaRegMessage } from 'react-icons/fa6'
import { IoIosStarOutline } from 'react-icons/io'
import InfiniteScroll from 'react-infinite-scroll-component'
import styles from './index.module.scss'

interface Props {
  height: string
}
function OrderStatusList({ height: scrollDivHeight }: Props) {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<IDataItem[]>([])
  // 加载更多
  const loadMoreData = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    const res = await getData()
    setData([...data, ...res])
    setLoading(false)
  }
  useEffect(() => {
    loadMoreData()
  }, [])
  return (
    <div
      id="scrollableDiv1"
      style={{
        height: `calc(${scrollDivHeight} - var(--content-padding) * 2)`, // 计算高度 减去 tabs上下自带的padding
        overflow: 'auto',
      }}
    >
      <InfiniteScroll
        dataLength={data.length}
        next={loadMoreData}
        hasMore={data.length < 50}
        loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
        endMessage={<Divider plain>暂无更多</Divider>}
        scrollableTarget="scrollableDiv1"
      >
        <List
          itemLayout="vertical"
          size="large"
          dataSource={data}
          renderItem={item => (
            <List.Item
              key={item.email}
              actions={
                !loading
                  ? [
                      <IconText icon={IoIosStarOutline} text="156" key="list-vertical-star-o" />,
                      <IconText icon={AiFillLike} text="156" key="list-vertical-like-o" />,
                      <IconText icon={FaRegMessage} text="2" key="list-vertical-message" />,
                    ]
                  : undefined
              }
              extra="Content"
            >
              <List.Item.Meta
                avatar={<Avatar src={item.picture.large} />}
                title={<a href="https://ant.design">{item.name.last}</a>}
                description={item.email}
              />
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  )
}
// 客户维度
function CustomerList({ height: scrollDivHeight }: Props) {
  const [loading, setLoading] = useState(false)
  const [data, setData] = useState<IDataItem[]>([])
  // 加载更多
  const loadMoreData = async () => {
    if (loading) {
      return
    }
    setLoading(true)
    const res = await getData()
    setData([...data, ...res])
    setLoading(false)
  }
  useEffect(() => {
    loadMoreData()
  }, [])
  return (
    <div
      id="scrollableDiv2"
      style={{
        height: `calc(${scrollDivHeight} - var(--content-padding) * 2)`,
        overflow: 'auto',
      }}
    >
      <InfiniteScroll
        dataLength={data.length}
        next={loadMoreData}
        hasMore={data.length < 50}
        loader={<Skeleton avatar paragraph={{ rows: 1 }} active />}
        endMessage={<Divider plain>暂无更多</Divider>}
        scrollableTarget="scrollableDiv2"
      >
        <List
          itemLayout="vertical"
          size="large"
          dataSource={data}
          renderItem={item => (
            <List.Item
              key={item.email}
              actions={
                !loading
                  ? [
                      <IconText icon={IoIosStarOutline} text="156" key="list-vertical-star-o" />,
                      <IconText icon={AiFillLike} text="156" key="list-vertical-like-o" />,
                      <IconText icon={FaRegMessage} text="2" key="list-vertical-message" />,
                    ]
                  : undefined
              }
              extra="Content"
            >
              <List.Item.Meta
                avatar={<Avatar src={item.picture.large} />}
                title={<a href="https://ant.design">{item.name.last}</a>}
                description={item.email}
              />
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  )
}
// 订单进度
export function Component() {
  const [dates, setDates] = useState<[Date, Date]>([new Date(), new Date()])
  // 设置tabs高度
  const [scrollDivHeight, setHeight] = useState('100%')
  const getHeight = () => {
    const tabsHeaderDom = document.querySelector('.adm-tabs-header') as HTMLElement
    const searchBarDom = document.querySelector(`.${styles['search-bar']}`) as HTMLElement
    if (tabsHeaderDom && searchBarDom) {
      // 计算可用高度 = 视窗高度 - 搜索栏高度 - Tabs头部高度
      const availableHeight = window.innerHeight - searchBarDom.offsetHeight - tabsHeaderDom.offsetHeight
      setHeight(`${availableHeight}px`)
    }
  }
  useEffect(() => {
    getHeight()
    // 监听窗口大小变化
    window.addEventListener('resize', getHeight)
    return () => {
      window.removeEventListener('resize', getHeight)
    }
  }, [])

  useEffect(() => {
    console.log('dates', dates)
  }, [dates])

  // 处理日期变更
  const handleDateChange = (newDates: [Date, Date]) => {
    setDates(newDates)
  }
  return (
    <Flex vertical className="h-screen relative">
      <HeaderBox />
      <div className={styles['search-bar']}>
        <SearchBar
          placeholder="请输入客户名称、销售员"
          style={{
            '--border-radius': '100px',
            '--background': '#ffffff',
            '--height': '32px',
            '--padding-left': '12px',
            'marginBottom': '10px',
          }}
        />
        {/* <CalendarMobile /> */}
        <div>
          <DateRangePicker value={dates} border={false} onChange={handleDateChange} />
        </div>
      </div>
      <Tabs className="h-full">
        <Tabs.Tab title="订单状态" key="1">
          <OrderStatusList height={scrollDivHeight}></OrderStatusList>
        </Tabs.Tab>
        <Tabs.Tab title="客户维度" key="2">
          <CustomerList height={scrollDivHeight}></CustomerList>
        </Tabs.Tab>
      </Tabs>

    </Flex>
  )
}
