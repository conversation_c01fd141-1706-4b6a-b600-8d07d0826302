import type { PayloadAction } from '@reduxjs/toolkit'
import { FetchInformationWithRequest, FetchLoginWithRequest, FetchLogoutWithRequest } from '@/service/api'
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'

interface AuthState {
  token: string | null
  userInfo: Api.Auth.UserInfo | null
  isLoggedIn: boolean
  isPending: boolean // 登录的pending状态
  corpId: string
  agentId: string
}

const initialState: AuthState = {
  token: '',
  userInfo: null,
  isLoggedIn: false,
  isPending: false,
  corpId: '',
  agentId: '',
}

// 单独定义异步 action
export const loginAsync = createAsyncThunk(
  'auth/login',
  async ({ phone, password }: { phone: string, password: string }, { dispatch }) => {
    // 先进行登录
    const loginResponse = await FetchLoginWithRequest({ phone, password })
    // eslint-disable-next-line ts/no-use-before-define
    dispatch(setToken(loginResponse.token))

    // 登录成功后获取用户信息
    const userInfo = await FetchInformationWithRequest()
    // eslint-disable-next-line ts/no-use-before-define
    dispatch(setUserInfo(userInfo))

    return { success: true }
  },
)

export const logoutAsync = createAsyncThunk(
  'auth/logoutAsync',
  async (_, { dispatch }) => {
    await FetchLogoutWithRequest()
    // eslint-disable-next-line ts/no-use-before-define
    dispatch(logout())
    return { success: true }
  },
)

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload
      state.isLoggedIn = true
    },
    setUserInfo: (state, action: PayloadAction<Api.Auth.UserInfo>) => {
      state.userInfo = action.payload
    },
    setWecomInfo: (state, action: PayloadAction<{ corpId: string; agentId: string }>) => {
      state.corpId = action.payload.corpId
      state.agentId = action.payload.agentId
    },
    logout: (state) => {
      state.token = ''
      state.userInfo = null
      state.isLoggedIn = false
      state.isPending = false
    },
  },
  // 可以添加异步 action 的处理
  extraReducers: (builder) => {
    builder
      .addCase(loginAsync.pending, (state) => {
        state.isPending = true
      })
      .addCase(loginAsync.fulfilled, (state) => {
        state.isPending = false
      })
      .addCase(loginAsync.rejected, (state) => {
        state.isPending = false
      })
      .addCase(logoutAsync.pending, (state) => {
        state.isPending = true
      })
      .addCase(logoutAsync.fulfilled, (state) => {
        state.isPending = false
      })
      .addCase(logoutAsync.rejected, (state) => {
        state.isPending = false
      })
  },
})

export const { setToken, setUserInfo, logout, setWecomInfo } = authSlice.actions
export default authSlice.reducer
