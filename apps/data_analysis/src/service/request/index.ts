import type {
  UseMutationOptions,
  UseQueryOptions,
} from '@tanstack/react-query'
import type { BaseResponse, PaginationParams, PaginationResponse, RequestConfig } from './type'
import { basePrefix } from '@/router'
import { store } from '@/store'
import { logout } from '@/store/slice/auth'
import { clearToken } from '@/utils/auth'
import {
  QueryClient,
  useMutation,
  useQuery,
} from '@tanstack/react-query'
import { PAGE, paramsSerializer, SIZE } from './share'

// 定义业务错误
class BusinessError extends Error {
  constructor(public code: number, message: string) {
    super(message)
    this.name = 'BusinessError'
  }
}

// 创建 QueryClient 实例
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 1000 * 60 * 5, // 5分钟
    },
  },
})

function getBaseUrl() {
  const customUrl = localStorage.getItem('custom_api_url')
  return customUrl || import.meta.env.VITE_API_BASE_URL
}

// 修改原有的 BASE_URL 常量为函数调用
const BASE_URL = getBaseUrl()
console.log('BASE_URL', BASE_URL)
// // 从环境变量获取基础 URL
// const BASE_URL = import.meta.env.VITE_API_BASE_URL

// 验证 BASE_URL 是否存在
if (!BASE_URL) {
  console.warn('警告: API 基础 URL 未配置，请检查环境变量 VITE_API_BASE_URL')
}

// 创建一个处理登出的函数
function handleLogout() {
  console.log('location', location)
  // 清除 localStorage 中的 token
  clearToken()
  // 清除 store 中的数据
  store.dispatch(logout())
  if (location.pathname.includes('login')) {
    return
  }
  // 重定向到登录页
  window.location.href = `/${basePrefix}/login?redirectUri=${window.location.pathname}`
}
// 基础请求封装
export async function request<T = any>(config: RequestConfig): Promise<T> {
  try {
    const token = store.getState().auth.token

    // 处理 URL，如果以 @ 开头则添加 BASE_URL
    let fullUrl = config.url.startsWith('@')
      ? `${BASE_URL}${config.url.slice(1)}`
      : config.url
    // 如果是 GET 请求且有 data，将其转换为 URL 参数
    if (config.method === 'GET' && config.data) {
      if (!config.data.page && !config.data.size) {
        if ((config.pagination && typeof config.pagination === 'boolean') || typeof config.pagination === 'object') {
          if (typeof config.pagination === 'boolean') {
            config.data.page = PAGE
            config.data.size = SIZE
          }
          else {
            config.data.page = config.pagination?.page || PAGE
            config.data.size = config.pagination?.size || SIZE
          }
        }
      }
      // 处理分页参数
      const finalData = {
        ...(config.data || {}),
      }
      const queryString = paramsSerializer(finalData)
      if (queryString) {
        fullUrl += `${fullUrl.includes('?') ? '&' : '?'}${queryString}`
      }
    }

    // 这里假设使用 fetch，你也可以换成 axios
    const response = await fetch(fullUrl, {
      method: config.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Platform': '6', // 添加固定的 Platform 头部 h5
        'Authorization': token || '', // 从 localStorage 获取 token
        ...config.headers,
      },
      // 只在非 GET 请求时添加 body
      ...(config.method !== 'GET' && config.data
        ? {
            body: JSON.stringify(config.data),
          }
        : {}),
    })

    // 处理 401 错误
    if (response.status === 401) {
      handleLogout()
      throw new Error('登录已过期，请重新登录')
    }

    if (!response.ok) {
      throw new Error(`网络请求失败: ${response.status}`)
    }

    const result: BaseResponse<T> = await response.json()

    // 处理业务错误
    if (result.code === 401) {
      handleLogout()
      throw new BusinessError(result.code, '登录已过期，请重新登录')
    }

    if (result.code !== 0) {
      throw new BusinessError(result.code, result.msg)
    }

    return result.data
  }
  catch (error) {
    // 如果是业务错误且是 401，确保已经处理了登出
    if (error instanceof BusinessError && error.code === 401) {
      handleLogout()
    }
    throw error
  }
}
export type UseCustomQueryOptions<T = any> = Omit<UseQueryOptions<T, Error>, 'queryKey' | 'queryFn'> & { pagination?: RequestConfig['pagination'] }
// 封装 useQuery hook
export function useCustomQuery<T = any>(key: string | readonly unknown[], config: RequestConfig, options?: UseCustomQueryOptions<T>) {
  return useQuery({
    queryKey: Array.isArray(key) ? key : [key],
    queryFn: () => request<T>({ ...config, pagination: options?.pagination || false }),
    ...options,
  })
}

// 封装 useMutation hook
export function useCustomMutation<TData = unknown, TVariables = unknown>(config: RequestConfig, options?: MutationOptions<TData, TVariables>) {
  return useMutation({
    mutationFn: (variables?: TVariables) =>
      request<TData>({
        ...config,
        pagination: options?.pagination || false,
        data: variables === undefined ? undefined : variables, // 只有在有参数时才传递
      }),
    ...options,
  })
}
// 创建一个工具类型来简化 options 的类型定义
export type MutationOptions<TData, TVariables = void> = Omit<
  UseMutationOptions<TData, Error, TVariables>,
  'mutationFn'
> & { pagination?: RequestConfig['pagination'] }

export type PaginatedQueryOptions<T = any> = Omit<UseQueryOptions<PaginationResponse<T>, Error>, 'queryKey' | 'queryFn'> & { pagination?: RequestConfig['pagination'] }

// 封装分页查询 hook
export function useCustomPaginatedQuery<T = any>(key: string | readonly unknown[], params: PaginationParams, config: Omit<RequestConfig, 'data'>, options?: PaginatedQueryOptions<T>) {
  return useQuery({
    queryKey: Array.isArray(key) ? [...key, params] : [key, params],
    queryFn: () => request<PaginationResponse<T>>({
      ...config,
      method: 'GET',
      url: `${config.url}${buildQueryString(params)}`,
      pagination: options?.pagination || false,
    }),
    ...options,
  })
}

// 构建查询字符串
function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      searchParams.append(key, String(value))
    }
  })
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// 分页数据处理的工具函数
export const paginationUtils = {
  // 计算总页数
  getTotalPages: (total: number, size: number) => Math.ceil(total / size),

  // 检查是否有下一页
  hasNextPage: (currentPage: number, total: number, size: number) =>
    currentPage < Math.ceil(total / size),

  // 检查是否有上一页
  hasPrevPage: (currentPage: number) => currentPage > 1,

  // 获取默认分页参数
  getDefaultParams: (page = 1, size = 10): PaginationParams => ({
    page,
    size,
  }),
}
