import type { MutationOptions } from '../request'
import { request, useCustomMutation } from '../request'
// 获取cdn 签名/密钥
export function GetSignApi(options?: MutationOptions<Api.Upload.Response, Api.Upload.Request>) {
  return useCustomMutation<Api.Upload.Response, Api.Upload.Request>(
    {
      url: '@/h5/v1/cdn/token',
      method: 'GET',
    },
    { ...options },
  )
}
export function GetSignApiWithRequest(params?: Api.Upload.Request) {
  return request<Api.Upload.Response>({
    url: '@/h5/v1/cdn/token',
    method: 'GET',
    data: params,
  },
  )
}
