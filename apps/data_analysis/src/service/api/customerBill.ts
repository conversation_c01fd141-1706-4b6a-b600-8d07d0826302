import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取客户欠款单列表 hook
export function FetchCustomerBillList(options?: MutationOptions<PaginationResponse<Api.ShouldCollectOrder.CustomerBill>, Api.ShouldCollectOrder.FetchCustomerBillListParams>) {
  return useCustomMutation<PaginationResponse<Api.ShouldCollectOrder.CustomerBill>, Api.ShouldCollectOrder.FetchCustomerBillListParams>(
    {
      url: '@/h5/v1/should_collect_order/report_forms/getCustomerOweMoneyList',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 获取客户对账单列表 hook
export function FetchCustomerReconciliationList(options?: MutationOptions<PaginationResponse<Api.ShouldCollectOrder.CustomerReconciliationResponse, Api.ShouldCollectOrder.CustomerReconciliationResponse>, Api.ShouldCollectOrder.FetchCustomerReconciliationListParams>) {
  return useCustomMutation<PaginationResponse<Api.ShouldCollectOrder.CustomerReconciliationResponse, Api.ShouldCollectOrder.CustomerReconciliationResponse>, Api.ShouldCollectOrder.FetchCustomerReconciliationListParams>(
    {
      url: '@/h5/v1/should_collect_order/report_forms/getCustomerReconciliationListV2',
      method: 'GET',
    },
    options,
  )
}
