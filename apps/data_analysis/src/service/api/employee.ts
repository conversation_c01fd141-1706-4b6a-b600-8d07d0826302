import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取员工分类列表 hook
export function FetchEmployeeList(options?: MutationOptions<PaginationResponse<Api.Employee.Response>, Api.Employee.FetchEmployeeListParams>) {
  return useCustomMutation<PaginationResponse<Api.Employee.Response>, Api.Employee.FetchEmployeeListParams>(
    {
      url: '@/h5/v1/employee/list_enum',
      method: 'GET',
    },
    { ...options },
  )
}
