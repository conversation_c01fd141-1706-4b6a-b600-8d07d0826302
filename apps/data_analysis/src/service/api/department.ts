import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取部门详情(树形) hook
export function FetchDepartmentList(options?: MutationOptions<PaginationResponse<Api.Department.Response>, Api.Department.FetchDepartmentListParams>) {
  return useCustomMutation<PaginationResponse<Api.Department.Response>, Api.Department.FetchDepartmentListParams>(
    {
      url: '@/h5/v1/department',
      method: 'GET',
    },
    { ...options },
  )
}
