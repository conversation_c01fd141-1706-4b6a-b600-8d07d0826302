import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

export interface FetchSaleSystemListParams {
  address?: string
  phone?: string
  status?: number
  email?: string
  remark?: string
  name?: string
  code?: string
}

// 获取销售体系列表 hook
export function FetchSaleSystemList(options?: MutationOptions<PaginationResponse<Api.SaleSystem.Response>, FetchSaleSystemListParams>) {
  return useCustomMutation<PaginationResponse<Api.SaleSystem.Response>, FetchSaleSystemListParams>(
    {
      url: '@/h5/v1/saleSystem/getSaleSystemDropdownList',
      method: 'GET',
    },
    { ...options },
  )
}
