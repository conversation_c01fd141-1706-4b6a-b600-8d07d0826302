import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取团队月份跟进数
export function GetTeamMonthRecordNumsList(options?: MutationOptions<Api.TeamMonthRecordNumsList.Response, Api.TeamMonthRecordNumsList.Request>) {
  return useCustomMutation<Api.TeamMonthRecordNumsList.Response, Api.TeamMonthRecordNumsList.Request>(
    {
      url: '@/h5/v1/customer_follow_record/month_team_record',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取团队客户拜访记录
export function GetTeamList(options?: MutationOptions<Api.TeamList.Response, Api.TeamList.Request>) {
  return useCustomMutation<Api.TeamList.Response, Api.TeamList.Request>(
    {
      url: '@/h5/v1/customer_follow_record/teamList',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}
// 评论
export function AddComment(options?: MutationOptions<Api.addComment.Response, Api.addComment.Request>) {
  return useCustomMutation<Api.addComment.Response, Api.addComment.Request>(
    {
      url: '@/h5/v1/customer_follow_record/add_comment',
      method: 'POST',
    },
    { ...options },
  )
}
// 获取团队拜访记录数据
export function GetTeamPurchaserFollowRecordData(options?: MutationOptions<Api.GetTeamPurchaserFollowRecordData.Response, Api.GetTeamPurchaserFollowRecordData.Request>) {
  return useCustomMutation<Api.GetTeamPurchaserFollowRecordData.Response, Api.GetTeamPurchaserFollowRecordData.Request>(
    {
      url: '@/h5/v1/customer_follow_record/team_record',
      method: 'GET',
    },
    { ...options },
  )
}

// 删除
export function DeletePurchaserFollowRecord(options?: MutationOptions<Api.DeletePurchaserFollowRecord.Response, Api.DeletePurchaserFollowRecord.Request>) {
  return useCustomMutation<Api.DeletePurchaserFollowRecord.Response, Api.DeletePurchaserFollowRecord.Request>(
    {
      url: '@/h5/v1/customer_follow_record/delete',
      method: 'DELETE',
    },
    { ...options },
  )
}

// 获取客户拜访标签列表
export function GetVisitTagList(options?: MutationOptions<PaginationResponse<Api.GetVisitTagList.Response>, Api.GetVisitTagList.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetVisitTagList.Response>, Api.GetVisitTagList.Request>(
    {
      url: '@/h5/v1/visit_tag/enum_list',
      method: 'GET',
    },
    { ...options },
  )
}

// 获取获取上一次的标签id
export function GetLastTagData(options?: MutationOptions<Api.GetLastTagData.Response, Api.GetLastTagData.Request>) {
  return useCustomMutation<Api.GetLastTagData.Response, Api.GetLastTagData.Request>(
    {
      url: '@/v1/mp/purchaserFollowRecord/getLastTagData',
      method: 'GET',
    },
    { ...options },
  )
}
// 新增客户跟进记录入参
export function AddPurchaserVisit(options?: MutationOptions<Api.AddPurchaserVisit.Response, Api.AddPurchaserVisit.Request>) {
  return useCustomMutation<Api.AddPurchaserVisit.Response, Api.AddPurchaserVisit.Request>(
    {
      url: '@/h5/v1/customer_follow_record/add',
      method: 'POST',
    },
    { ...options },
  )
}
// 更新客户跟进记录
export function EditPurchaserVisit(options?: MutationOptions<Api.EditPurchaserVisit.Response, Api.EditPurchaserVisit.Request>) {
  return useCustomMutation<Api.EditPurchaserVisit.Response, Api.EditPurchaserVisit.Request>(
    {
      url: '@/h5/v1/customer_follow_record/update',
      method: 'POST',
    },
    { ...options },
  )
}

//
export function GetWechatFriends(options?: MutationOptions<PaginationResponse<Api.GetWechatFriends.Response>, Api.GetWechatFriends.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetWechatFriends.Response>, Api.GetWechatFriends.Request>(
    {
      url: '@/h5/v1/customer_follow_record/wechatFriends',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取企业微信标签列表
export function GetCorpTagList(options?: MutationOptions<PaginationResponse<Api.GetCorpTagList.Response>, Api.GetCorpTagList.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetCorpTagList.Response>, Api.GetCorpTagList.Request>(
    {
      url: '@/h5/v1/qywx/tobe_developed_app/corp_tag_list',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取团队拜访记录产品排行数据
export function GetTeamMatchableProductDetailData(options?: MutationOptions<PaginationResponse<Api.GetTeamMatchableProductDetailData.Response>, Api.GetTeamMatchableProductDetailData.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetTeamMatchableProductDetailData.Response>, Api.GetTeamMatchableProductDetailData.Request>(
    {
      url: '@/h5/v1/customer_follow_record/team_matchable_product_rank',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取团队拜访记录客户产品排行数据
export function GetTeamMatchableProductPurchaserRankDetailList(options?: MutationOptions<PaginationResponse<Api.GetTeamMatchableProductPurchaserRankDetailList.Response>, Api.GetTeamMatchableProductPurchaserRankDetailList.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetTeamMatchableProductPurchaserRankDetailList.Response>, Api.GetTeamMatchableProductPurchaserRankDetailList.Request>(
    {
      url: '@/h5/v1/customer_follow_record/team_matchable_product_customer_rank',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取团队拜访记录客户建议产品数据
export function GetTeamDevProductDetailDataList(options?: MutationOptions<PaginationResponse<Api.GetTeamDevProductDetailDataList.Response>, Api.GetTeamDevProductDetailDataList.Request>) {
  return useCustomMutation<PaginationResponse<Api.GetTeamDevProductDetailDataList.Response>, Api.GetTeamDevProductDetailDataList.Request>(
    {
      url: '@/h5/v1/customer_follow_record/dev_product_customer_rank',
      method: 'GET',
    },
    { ...options },
  )
}
// 获取用户定位
export function GetUserLocation(options?: MutationOptions<Api.GetUserLocation.Response, Api.GetUserLocation.Request>) {
  return useCustomMutation<Api.GetUserLocation.Response, Api.GetUserLocation.Request>(
    {
      url: '@/h5/v1/user/user_location',
      method: 'GET',
    },
    { ...options },
  )
}
