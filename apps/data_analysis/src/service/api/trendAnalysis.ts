// 趋势分析的公共请求方法
import type { MutationOptions } from '@/service/request'
// import type { PaginationResponse } from '@/service/request/type' // 分页需要
import { useCustomMutation } from '@/service/request'

// 获取趋势分析数据 http://192.168.1.28:50001/hcscm/admin/v1/analysis/productAnalysis/trend
export function FetchGetTrendAnalysisData(options?: MutationOptions<Api.TrendAnalysis.ProductAnalysisDataResponse, Api.TrendAnalysis.TrendAnalysisDataRequest>) {
  return useCustomMutation<Api.TrendAnalysis.ProductAnalysisDataResponse, Api.TrendAnalysis.TrendAnalysisDataRequest>(
    {
      url: '@/h5/v1/analysis/productAnalysis/trend',
      method: 'GET',
    },
    options,
  )
}
