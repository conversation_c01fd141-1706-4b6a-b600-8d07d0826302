import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { request, useCustomMutation } from '../request'

// 获取客户分类列表 hook
export function FetchCustomerList(options?: MutationOptions<PaginationResponse<Api.Customer.Response>, Api.Customer.Request>) {
  return useCustomMutation<PaginationResponse<Api.Customer.Response>, Api.Customer.Request>(
    {
      url: '@/h5/v1/business_unit/customer/enum_list',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 客户画像基本信息
export function FetchPurchaserProfile(options?: MutationOptions<Api.CustomerProfile.Response, Api.CustomerProfile.Request>) {
  return useCustomMutation<Api.CustomerProfile.Response, Api.CustomerProfile.Request>(
    {
      url: '@/h5/v1/purchaser/getPurchaserProfile',
      method: 'GET',
    },
    options,
  )
}
/**
 * 获取Signature
 */
export function FetchQYWechatSignature(options?: MutationOptions<Api.WxSignature.Response, Api.WxSignature.Request>) {
  return useCustomMutation<Api.WxSignature.Response, Api.WxSignature.Request>(
    {
      url: '@/h5/v1/qywx/tobe_developed_app/get_qywx_signature',
      method: 'GET',
    },
    options,
  )
}
/**
 * 获取Signature
 */
export function FetchQYWechatSignatureWithRequest(params: Api.WxSignature.Request) {
  return request<Api.WxSignature.Response>({
    url: '@/h5/v1/qywx/tobe_developed_app/get_qywx_signature',
    method: 'GET',
    data: params,
  })
}
/**
 * 根据企业微信UserId获取客户ID
 */
export function GetIDByQywxUserId(options?: MutationOptions<Api.getID.Response, Api.getID.Request>) {
  return useCustomMutation<Api.getID.Response, Api.getID.Request>(
    {
      url: '@/h5/v1/qywx/tobe_developed_app/get_qywx_bind_rel',
      method: 'GET',
    },
    options,
  )
}
/**
 * 根据企业微信UserId获取客户ID
 */
export function GetIDByQywxUserIdWithRequest(params: Api.getID.Request) {
  return request<Api.getID.Response>({
    url: '@/h5/v1/qywx/tobe_developed_app/get_qywx_bind_rel',
    method: 'GET',
    data: params,
  })
}
// 获取前十产品销售排名数据
export function FetchGetTopCustomerData(options?: MutationOptions<Api.GetTopCustomerData.Response, Api.GetTopCustomerData.Request>) {
  return useCustomMutation<Api.GetTopCustomerData.Response, Api.GetTopCustomerData.Request>(
    {
      url: '@/h5/v1/analysis/productAnalysis/topCustomer',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}
// 获取前十产品销售排名数据
export function FetchGetCustomerMatrixDetail(options?: MutationOptions<Api.GetCustomerMatrixDetail.Response, Api.GetCustomerMatrixDetail.Request>) {
  return useCustomMutation<Api.GetCustomerMatrixDetail.Response, Api.GetCustomerMatrixDetail.Request>(
    {
      url: '@/h5/v1/should_collect_order/matrix/getCustomerMatrixDetail',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}
