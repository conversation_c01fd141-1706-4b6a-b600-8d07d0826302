import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取员工分类列表 hook
export function FetchSaleSystemList1(options?: MutationOptions<PaginationResponse<Api.SaleSystem.Response>, Api.SaleSystem.Request>) {
  return useCustomMutation<PaginationResponse<Api.SaleSystem.Response>, Api.SaleSystem.Request>(
    {
      url: '@/h5/v1/saleSystem/getSaleSystemList',
      method: 'GET',
    },
    { ...options },
  )
}
