import type { MutationOptions } from '../request'
import { useCustomMutation } from '../request'
// 企业微信扫码登录请求 hook
export function FetchScanCodeLogin(options?: MutationOptions<Api.Auth.LoginResponse, Api.Auth.ScanCodeLoginReqest>) {
  return useCustomMutation<Api.Auth.LoginResponse, Api.Auth.ScanCodeLoginReqest>(
    {
      url: '@/h5/v1/scanCodeLogin',
      method: 'POST',
    },
    options,
  )
}
