import type { MutationOptions } from '../request'
import { request, useCustomMutation } from '../request'

// 登录请求参数类型
interface LoginParams {
  phone: string
  password: string
}
// 登录请求 hook
export function FetchLogin(options?: MutationOptions<Api.Auth.LoginResponse, LoginParams>) {
  return useCustomMutation<Api.Auth.LoginResponse, LoginParams>(
    {
      url: '@/h5/v1/login',
      method: 'POST',
    },
    options,
  )
}

// 登录请求 hook
export function FetchLoginWithRequest(params?: LoginParams) {
  return request<Api.Auth.LoginResponse>({
    url: '@/h5/v1/login',
    method: 'POST',
    data: params,
  },
  )
}

// 登出请求 hook
export function FetchLogout(options?: MutationOptions<Api.Auth.LogoutResponse, void>) {
  return useCustomMutation<Api.Auth.LogoutResponse, void>(
    {
      url: '@/h5/v1/logout',
      method: 'POST',
    },
    options,
  )
}
// 登出请求 hook
export function FetchLogoutWithRequest() {
  return request<Api.Auth.LogoutResponse>({
    url: '@/h5/v1/logout',
    method: 'POST',
  })
}
// 获取用户信息请求 hook
export function FetchInformation(options?: MutationOptions<Api.Auth.UserInfo, void>) {
  return useCustomMutation<Api.Auth.UserInfo, void>(
    {
      url: '@/h5/v1/information',
      method: 'GET',
    },
    options,
  )
}
// 获取用户信息请求 hook
export function FetchInformationWithRequest() {
  return request<Api.Auth.UserInfo>({
    url: '@/h5/v1/information',
    method: 'GET',
  })
}
// 换token
export function FetchSwitchAdminToken(options?: MutationOptions<{ token: string }, { token: string }>) {
  return useCustomMutation<{ token: string }, { token: string }>({
    url: '@/h5/v1/switchAdminToken',
    method: 'GET',
  }, options)
}
/**
 * color_card.GenerateAccountSetIdData
 */
export interface GenerateAccountSetIdDataResponse {
  /**
   * 响应码
   */
  code?: number
  data?: any
  /**
   * 加密后的账套ID
   */
  encrypted_account_set_id?: string
  /**
   * 响应信息
   */
  msg?: string
  /**
   * 版本号
   */
  version?: string
  [property: string]: any
}
// 生成加密的账套id
export function GenerateAccountSetId(options?: MutationOptions<GenerateAccountSetIdDataResponse, void>) {
  return useCustomMutation<GenerateAccountSetIdDataResponse, void>({
    url: '@/h5/v1/generateAccountSetId',
    method: 'GET',
  }, options)
}
