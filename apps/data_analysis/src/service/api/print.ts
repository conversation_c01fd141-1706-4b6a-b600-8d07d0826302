import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

export interface FetchPrintTemplateTemporaryTokenParams {
  /**
   * type
   */
  type?: number
  [property: string]: any
}

// 获取打印模板临时token hook
export function FetchPrintTemplateTemporaryToken(options?: MutationOptions<Api.PrintTemplate.Response, FetchPrintTemplateTemporaryTokenParams>) {
  return useCustomMutation<Api.PrintTemplate.Response, FetchPrintTemplateTemporaryTokenParams>(
    {
      url: '@/h5/v1/printTemplate/getMPPrintTemplateTemporaryToken',
      method: 'POST',
    },
    { ...options },
  )
}

export interface FetchPrintTemplateListParams {
  /**
   * download
   */
  download?: number
  /**
   * limit
   */
  limit?: number
  /**
   * offset
   */
  offset?: number
  /**
   * page
   */
  page?: number
  /**
   * size
   */
  size?: number
  [property: string]: any
}
// 获取打印模板列表 hook
export function FetchPrintTemplateList(options?: MutationOptions<PaginationResponse<Api.PrintTemplate.GetPrintTemplateListResponse>, FetchPrintTemplateListParams>) {
  return useCustomMutation<PaginationResponse<Api.PrintTemplate.GetPrintTemplateListResponse>, FetchPrintTemplateListParams>(
    {
      url: '@/h5/v1/printTemplate/getPrintTemplateList',
      method: 'GET',
    },
    { ...options },
  )
}
