import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取团队月份跟进数
export function GetMatrixData(options?: MutationOptions<Api.GetMatrixData.Response, Api.GetMatrixData.Request>) {
  return useCustomMutation<Api.GetMatrixData.Response, Api.GetMatrixData.Request>(
    {
      url: '@/h5/v1/should_collect_order/matrix/getMatrixData',
      method: 'GET',
    },
    { ...options },
  )
}
