import type { MutationOptions } from '../request'
import type { PaginationResponse } from '../request/type'
import { useCustomMutation } from '../request'

// 获取产品资料列表 https://hcscmtest.zzfzyc.com/hcscm/admin/v1/product/finishProduct/getFinishProductDropdownList
export function FetchProductList(options?: MutationOptions<PaginationResponse<Api.Product.Response>, Api.Product.Request>) {
  return useCustomMutation<PaginationResponse<Api.Product.Response>, Api.Product.Request>(
    {
      url: '@/h5/v1/product/finishProduct/getFinishProductDropdownList',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 获取产品颜色列表 /admin/v1/product/finishProductColor/searchSomeFinishProductColorField
export function FetchProductColorList(options?: MutationOptions<PaginationResponse<Api.Customer.Response>, Api.Customer.Request>) {
  return useCustomMutation<PaginationResponse<Api.Customer.Response>, Api.Customer.Request>(
    {
      url: '@/h5/v1/product/finishProductColor/getFinishProductColorDropdownList',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 获取前十产品销售排名数据
export function FetchGetTopProductData(options?: MutationOptions<Api.GetTopProductData.Response, Api.GetTopProductData.Request>) {
  return useCustomMutation<Api.GetTopProductData.Response, Api.GetTopProductData.Request>(
    {
      url: '@/h5/v1/analysis/productAnalysis/topProduct',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 获取前十产品销售排名数据
export function FetchGetTopColorData(options?: MutationOptions<Api.GetTopColorData.Response, Api.GetTopColorData.Request>) {
  return useCustomMutation<Api.GetTopColorData.Response, Api.GetTopColorData.Request>(
    {
      url: '@/h5/v1/analysis/productAnalysis/topColor',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}

// 获取指定客户的指定时间段的应收单记录
export function FetchGetReceivableOrderList(options?: MutationOptions<Api.GetReceivableOrderList.Response, Api.GetReceivableOrderList.Request>) {
  return useCustomMutation<Api.GetReceivableOrderList.Response, Api.GetReceivableOrderList.Request>(
    {
      url: '@/h5/v1/analysis/customerAnalysis/getReceivableOrderList',
      method: 'GET',
    },
    { ...options, pagination: true },
  )
}
