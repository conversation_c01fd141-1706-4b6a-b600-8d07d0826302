declare namespace Api.ProductColorDetailAi {
  /**
   * data_analysis.GetAnalysisHomeReq
   */
  export interface Request {
    abstract_sort_key?: string
    /**
     * 客户id
     */
    customer_id?: string
    /**
     * 是否导出报表
     */
    download?: number
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    limit?: number
    /**
     * 城市
     */
    location?: string
    /**
     * 偏移量 从0开始（优先使用）
     */
    offset?: number
    /**
     * 页码 从1开始
     */
    page?: number
    /**
     * 产品颜色id
     */
    product_color_id?: string
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    size?: number
    sort_key?: string
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 趋势分析结束时间
     */
    trend_end_time?: string
    /**
     * 趋势分析开始时间
     */
    trend_start_time?: string
    /**
     * 分析类型 1:产品分析 2:客户分析
     */
    type?: number
    /**
     * 布种类型id
     */
    type_fabric_id?: string
    [property: string]: any
  }
}
