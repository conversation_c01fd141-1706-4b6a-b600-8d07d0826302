declare namespace Api.Employee {
  export interface FetchEmployeeListParams {
    duty: EmployeeType
    status?: number
    department_id?: number
    name?: string
    code?: string
  }
  /**
   * system.GetEmployeeListEnumItem
   */
  export interface Response {
    /**
     * 编号
     */
    code?: string
    /**
     * 部门id
     */
    department_id?: number
    /**
     * 部门名称
     */
    department_name?: string
    /**
     * 职责列表
     */
    duty?: number[]
    /**
     * 职责列表
     */
    duty_name?: string[]
    /**
     * id
     */
    id?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 手机号码
     */
    phone?: string
    /**
     * 营销体系id列表
     */
    sale_system_list?: number[]
    /**
     * 营销体系列表
     */
    sale_system_name_list?: string[]
    [property: string]: any
  }
}
