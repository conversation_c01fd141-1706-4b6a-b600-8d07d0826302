// 控制台的模块
declare namespace Api.Dashboard {

  interface WorkbenchDataRequest { // 请求参数类型
    start_time: string // 开始时间
    end_time: string // 结束时间
    last_start_time: string // 上期开始时间
    last_end_time: string // 上期结束时间
  }
  interface WorkbenchDataResponse { // 响应数据类型
    [key?: string]: number | string | undefined // 指标key
    sale_amount: number | undefined // 销售金额
    sale_amount_last_rate: number | undefined // 销售金额较上期
    sale_amount_daily_avg: number | undefined // 销售金额日均
    roll: number | undefined // 匹数 100
    roll_last_rate: number | undefined // 匹数较上期
    roll_daily_avg: number | undefined // 匹数日均
    big_customer_count: number | undefined // 大货客户数
    big_customer_last_rate: number | undefined // 大货客户数较上期
    big_customer_daily_avg: number | undefined // 大货客户数日均
    big_order_weight: string | undefined // 大货数量(带单位)
    big_order_last_rate: number | undefined // 大货数量较上期
    big_order_daily_avg: string | undefined // 大货数量日均
    plate_order_weight: string | undefined // 剪板数量(带单位)
    plate_order_last_rate: number | undefined // 剪板数量较上期
    plate_order_daily_avg: string | undefined // 剪板数量日均
    small_customer_count: number | undefined // 小样客户数
    small_customer_last_rate: number | undefined // 小样客户数较上期
    small_customer_daily_avg: number | undefined // 小样客户数日均
    total_count: number | undefined // 总客户数
    total_count_last_rate: number | undefined // 总客户数较上期
    total_count_daily_avg: number | undefined // 总客户数日均
    total_debt: number | undefined // 总欠款
    advance_collect_amount: number | undefined // 预收金额
    actually_collect_amount: number | undefined // 实收金额
  }
}
