declare namespace Api.PrintTemplate {
  export interface Response {
    temporary_token: string
  }
  /**
   * system.GetPrintTemplateListData
   */
  export interface GetPrintTemplateListResponse {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 数据类型
     */
    data_type?: number
    /**
     * 数据类型名称
     */
    data_type_name?: string
    /**
     * 前端处理方式,用于前端判断处理模板
     */
    front_handler?: number
    /**
     * 前端处理方式名称,用于前端判断处理模板
     */
    front_handler_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 单据名称
     */
    order_name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 状态 1可用 2禁用
     */
    status?: number
    /**
     * 状态 1可用 2禁用
     */
    status_name?: string
    /**
     * Default    bool                           `json:"default"`     // 是否是默认模板
     */
    type?: number
    /**
     * 打印类型名称
     */
    type_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
}
