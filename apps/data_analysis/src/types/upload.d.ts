declare namespace Api.Upload {
  /**
   * system.GetCDNTokenQuery
   */
  export interface Request {
    method?: string
    save_key?: string
    scene?: number
    /**
     * 1测试 2正式
     */
    status?: number
    [property: string]: any
  }
  export interface Response {
    authorization: string
    bucket: string
    expiration: number
    method: string
    policy: string
  }
}
declare namespace Api.UploadQNY {
  export interface Response {
    'code': number
    'file_size': number
    'image-frames': number
    'image-height': number
    'image-type': string
    'image-width': number
    'message': string
    'mimetype': string
    'time': number
    'url': string
  }
}
