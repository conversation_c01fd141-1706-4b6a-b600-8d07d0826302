declare namespace Api.TrendAnalysis{
  /**
   * data_analysis.GetAnalysisHomeReq
   */
  export interface TrendAnalysisDataRequest {
    /**
     * 客户id
     */
    customer_id?: string
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    /**
     * 城市
     */
    location?: string
    /**
     * 产品颜色id
     */
    product_color_id?: string
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 分析类型 1:产品分析 2:客户分析
     */
    type?: number
    /**
     * 布种类型id
     */
    type_fabric_id?: string
    [property: string]: any
  }
  interface ProductAnalysisDataResponse {
    may_predict_data?: {
      predict_data: number
      range: number
    }
    /**
     * 三年的匹数统计(今年、去年、前年)
     */
    three_year_roll?: ThreeYearRoll
    /**
     * 三年的销售金额统计(今年、去年、前年)
     */
    three_year_sale_amount?: ThreeYearSaleAmount
    /**
     * 三年的数量统计(今年、去年、前年)
     */
    three_year_settle_weight?: ThreeYearSettleWeight
    [property: string]: any
  }

  /**
   * 三年的匹数统计(今年、去年、前年)
   */
  export interface ThreeYearRoll {
    /**
     * 前年每月/日匹数
     */
    before_year?: number[]
    /**
     * 去年每月/日匹数
     */
    last_year?: number[]
    /**
     * 当天预估Roll
     */
    this_day_estimate_roll?: number
    /**
     * 当月预估Roll
     */
    this_month_estimate_roll?: number
    /**
     * 今年每月/日匹数
     */
    this_year?: number[]
    [property: string]: any
  }

  /**
   * 三年的销售金额统计(今年、去年、前年)
   */
  export interface ThreeYearSaleAmount {
    /**
     * 前年每月/日销售金额
     */
    before_year?: any
    /**
     * 去年每月/日销售金额
     */
    last_year?: any
    /**
     * 当天预估销售金额
     */
    this_day_estimate_sale_amount?: number
    /**
     * 当月预估销售金额
     */
    this_month_estimate_sale_amount?: number
    /**
     * 今年每月/日销售金额
     */
    this_year?: any
    [property: string]: any
  }

  /**
   * 三年的数量统计(今年、去年、前年)
   */
  export interface ThreeYearSettleWeight {
    /**
     * 前年每月/日数量
     */
    before_year?: any
    /**
     * 去年每月/日数量
     */
    last_year?: any
    /**
     * 当天预估数量
     */
    this_day_estimate_settle_weight?: number
    /**
     * 当月预估数量
     */
    this_month_estimate_settle_weight?: number
    /**
     * 今年每月/日数量
     */
    this_year?: any
    [property: string]: any
  }
}
