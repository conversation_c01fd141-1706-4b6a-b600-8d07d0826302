// 产品分析的模块
declare namespace Api.ProductAnalysis {
  // 总体维度
  /**
   * data_analysis.GetAnalysisHomeReq
   */
  export interface ProductAnalysisDataRequest {
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    /**
     * 城市
     */
    location?: string
    /**
     * 产品颜色id
     */
    product_color_id?: string
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 分析类型 1:产品分析 2:客户分析
     */
    type?: number
    /**
     * 布种类型id
     */
    type_fabric_id?: string
    [property: string]: any
  }
  interface ProductAnalysisDataResponse { // 响应数据类型
    total_sale_amount: number // 产品总销售额
    total_roll: number // 产品总匹数
    total_settle_weight: string // 产品总数量（按单位分组）
    top_10_product_ids: number[] // 前十产品id
    top_10_products: Top10ProductDetail[] // 前十产品详细信息 (产品排行)
    top_10_customers: Top10CustomerDetail[] // 前十客户详细信息 (客户排行)
    total_customer_count: number // 客户数
    top_10_product_category_ratio: ProductCategoryRatio[] // 前十产品类别占比
    top_10_sale_group_ratio: SaleGroupRatio[] // 前十销售群体占比
    new_customer_ratio: string // 新客户占比
    old_customer_ratio: string // 老客户占比
    sales_area_stat: SalesAreaStat[] // 销售地区统计
    color_ratio: ColorRatio[] // 色号占比
    product_name?: string // 产品名称
    product_id?: number // 产品id

    // 改：
    top_10_products: Top10ProductDetail[] //  产品排行 (不分大货和剪板)
    top_10_big_products: Top10ProductDetail[] // 产品排行 大货
    top_10_plate_products: Top10ProductDetail[] // 产品排行 剪板

    color_sale_ranks: ColorSaleRanks[] // 颜色排行 (不分大货和剪板)
    color_sale_big_ranks: ColorSaleRanks[] // 产品排行 大货
    color_sale_plate_ranks: ColorSaleRanks[] // 产品排行 剪板

    customer_sale_ranks: CustomerSaleRanks[] // 客户排行 (不分大货和剪板)
    customer_sale_big_ranks: CustomerSaleRanks[] // 大货客户排行
    customer_sale_plate_ranks: CustomerSaleRanks[] // 剪板客户排行
  }
  // 产品详情维度
  // 颜色详情维度
  // 产品详情+颜色维度
  interface Top10ProductDetail { // 前十产品详细信息
    product_id: number // 产品ID
    product_code: string // 产品编号
    product_name: string // 产品名称
    sale_roll: number // 销售匹数
    return_roll: number // 退货匹数
    total_weight: number // 总数量
    total_roll: number // 总匹数
    sale_amount: number // 销售金额
    color_count: number // 颜色数量
    customer_count: number // 客户数量
    roll_ratio: string // 匹数占比，也就是占所有匹数的占比
    last_15_day_roll: number[] // 最近15客户下单该产品天每天的匹数
  }
  interface Top10CustomerDetail { // 前十客户详细信息
    customer_id: number // 客户ID
    customer_name: string // 客户名称
    total_roll: number // 总匹数
    sale_amount: number // 销售金额
    product_count: number // 面料种类数量
    color_count: number // 颜色数量
    roll_ratio: string // 匹数占比
    last_15_day_roll: number[] // 最近15天每天的匹数
  }
  interface ProductCategoryRatio { // 前十产品类别占比（客户类别占比）
    type_id: number // 布种类型ID
    type_name: string // 布种类型名称
    sale_ratio: string // 销售占比
  }
  interface SaleGroupRatio { // 前十销售群体占比（客户销售占比）
    sale_group_id: number // 销售群体ID
    sale_group_name: string // 销售群体名称
    sale_ratio: string // 销售占比
  }
  interface SalesAreaStat { // 销售地区统计
    value: number // 数量
    name: string // 名称
  }
  interface ColorSaleRanks { // 颜色排行
    color_code: string // 颜色编码
    color_id: number // 颜色ID
    color_name: string // 颜色名称
    customer_count: number // 客户数量
    last_15_day_roll: number[] // 最近15天每天的匹数
    roll: number // 匹数
    roll_ratio: string // 匹数占比
    sale_amount: number // 销售金额
    settle_weight: string // 数量
  }
  interface CustomerSaleRanks { // 客户排行
    customer_id: number // 客户ID
    customer_name: string // 客户名称
    roll: number // 匹数
    settle_weight: string // 数量
    sale_amount: number // 销售金额
    color_count: number // 颜色数量
    roll_ratio: string // 匹数占比
    last_15_day_roll: number[] // 最近15天每天的匹数
  }
  interface ColorRatio { // 产品详情+颜色详情
    color_code: string
    color_id: number
    color_name: string
    sale_ratio: string
  }
}
declare namespace Api.GetTopProductData {
  export interface GetTopProductResForTop {
    product_id: number // 产品ID
    product_name: string // 产品名称
    product_code: string // 产品编号
    color_count: number // 颜色数量
    product_color_ids: string // 产品颜色ID
    customer_count: number // 客户数量
    customer_ids: string // 客户ID
    total_sale_amount: number // 销售总额
    sale_roll: number // 销售匹数
    return_roll: number // 退货匹数
    total_roll: number // 总匹数
    total_settle_weight: string // 总数量（按单位分组）
    roll_ratio: string // 匹数占比
    last_15_day_sale_amount: number[] // 近15天销售额趋势
  }
  /**
   * data_analysis.GetTopDataRes
   */
  export interface Response {
    /**
     * 前十产品列表，可以是GetTopProductResForTop或GetTopColorResForTop的切片
     */
    top_data_list?: GetTopProductResForTop[]
    [property: string]: any
  }
  /**
   * data_analysis.GetTopProductReq
   */
  export interface Request {
    abstract_sort_key?: string
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 是否导出报表
     */
    download?: number
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    limit?: number
    location?: string
    /**
     * 偏移量 从0开始（优先使用）
     */
    offset?: number
    /**
     * 页码 从1开始
     */
    page?: number
    /**
     * 产品颜色id
     */
    product_color_id?: number
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    size?: number
    sort_key?: string
    /**
     * 开始时间
     */
    start_time?: string
    type_fabric_id?: string
    [property: string]: any
  }
}
declare namespace Api.GetTopColorData {
  export interface GetTopColorResForTop {
    product_id: number
    product_name: string
    product_color_id: number
    product_color_name: string // 产品颜色名称
    product_color_code: string // 产品颜色编号
    customer_count: number // 客户数量
    customer_ids: string // 客户ID
    total_sale_amount: number // 销售总额
    sale_roll: number // 销售匹数
    return_roll: number // 退货匹数
    total_roll: number // 总匹数
    total_settle_weight: string // 总数量（按单位分组）
    sale_amount_ratio: string // 销售金额占比
    last_15_day_sale_amount: number[] // 近15天销售额趋势
  }

  /**
   * data_analysis.GetTopDataRes
   */
  export interface Response {
    /**
     * 前十产品列表，可以是GetTopProductResForTop或GetTopColorResForTop的切片
     */
    top_data_list?: GetTopColorResForTop[]
    [property: string]: any
  }
  /**
   * data_analysis.GetTopProductReq
   */
  export interface Request {
    abstract_sort_key?: string
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 是否导出报表
     */
    download?: number
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    limit?: number
    location?: string
    /**
     * 偏移量 从0开始（优先使用）
     */
    offset?: number
    /**
     * 页码 从1开始
     */
    page?: number
    /**
     * 产品颜色id
     */
    product_color_id?: string
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    size?: number
    sort_key?: string
    /**
     * 开始时间
     */
    start_time?: string
    type_fabric_id?: number
    [property: string]: any
  }
}
declare namespace Api.GetReceivableOrderList {
  export interface Request {
    /**
     * 客户ID
     */
    customer_id: number
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * 页码
     */
    page?: number
    /**
     * 每页数量
     */
    size?: number
    /**
     * 开始时间
     */
    start_time?: string
    [property: string]: any
  }
  /**
   * data_analysis.GetReceivableOrderListRes
   */
  export interface Response {
    /**
     * 应收单列表
     */
    list?: DataAnalysisReceivableOrderItem[]
    /**
     * 总数量
     */
    total?: number
    [property: string]: any
  }

  /**
   * data_analysis.ReceivableOrderItem
   */
  export interface DataAnalysisReceivableOrderItem {
    /**
     * 审核状态 1:未审核 2:已审核
     */
    audit_status?: number
    /**
     * 收款状态 1:未收款 2:已收部分 3:已收全款
     */
    collect_status?: number
    /**
     * 状态信息
     */
    collect_type?: number
    /**
     * 已收金额
     */
    collected_amount?: number
    create_time?: string
    /**
     * 缸号
     */
    dyelot_number?: string
    /**
     * 优惠幅度
     */
    offset_sale_price?: number
    /**
     * 基础信息
     */
    order_id?: number
    /**
     * 应收单号
     */
    order_no?: string
    /**
     * 下单时间
     */
    order_time?: string
    /**
     * 其他金额
     */
    other_amount?: number
    product_code?: string
    /**
     * 色号
     */
    product_color_code?: string
    /**
     * 颜色
     */
    product_color_name?: string
    /**
     * 商品信息
     */
    product_name?: string
    /**
     * 数量信息
     */
    roll?: number
    /**
     * 价格信息
     */
    sale_amount?: number
    /**
     * 销售模式 1:大货 2:剪板 3:客订大货 4:客订剪板
     */
    sale_mode?: number
    sale_order_no?: string
    /**
     * 汇总金额
     */
    settle_amount?: number
    /**
     * 结算空差
     */
    settle_error_weight?: number
    /**
     * 结算数量
     */
    settle_weight?: number
    /**
     * 未收金额
     */
    uncollected_amount?: number
    [property: string]: any
  }
}
