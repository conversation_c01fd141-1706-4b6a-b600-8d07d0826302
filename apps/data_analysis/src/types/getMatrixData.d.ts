declare namespace Api.GetMatrixData{
  export interface Request {
    /**
     * end_time
     */
    end_time?: string
    /**
     * start_time
     */
    start_time?: string
    [property: string]: any
  }
  /**
   * should_collect_order.GetMatrixData
   */
  export interface Response {
    /**
     * 平均利润率
     */
    average_profit_margin?: string
    /**
     * 平均销售增长率
     */
    average_sale_growth_rate?: string
    /**
     * 客户周期
     */
    customer_cycle?: string
    customer_matrix?: HcscmStructureShouldCollectOrderGetCustomerMatrixData
    /**
     * 客户采购金额
     */
    customer_purchase_price?: string
    /**
     * 客户采购频率
     */
    customer_purchase_rate?: string
    /**
     * 最大利润率
     */
    max_profit_margin?: string
    /**
     * 最大销售增长率
     */
    max_sale_growth_rate?: string
    /**
     * 最小利润率
     */
    min_profit_margin?: string
    /**
     * 最小销售增长率
     */
    min_sale_growth_rate?: string
    /**
     * 产品周期
     */
    product_cycle?: string
    product_matrix?: HcscmStructureShouldCollectOrderGetProductMatrixData[]
    /**
     * 利润率
     */
    profit_margin?: string
    /**
     * 销售增长率
     */
    sale_growth_rate?: string
    [property: string]: any
  }

  /**
   * hcscm_structure_should_collect_order.GetCustomerMatrixData
   */
  export interface HcscmStructureShouldCollectOrderGetCustomerMatrixData {
    /**
     * 核心客户
     */
    core?: number
    /**
     * 核心客户增长数
     */
    core_growth?: number
    /**
     * 活跃低价值客户
     */
    ordinary?: number
    /**
     * 活跃低价值客户增长数
     */
    ordinary_growth?: number
    /**
     * 潜力客户
     */
    potential?: number
    /**
     * 潜力客户增长数
     */
    potential_growth?: number
    /**
     * 普通客户
     */
    un_active?: number
    /**
     * 普通客户增长数
     */
    un_active_growth?: number
    [property: string]: any
  }

  /**
   * hcscm_structure_should_collect_order.GetProductMatrixData
   */
  export interface HcscmStructureShouldCollectOrderGetProductMatrixData {
    /**
     * 订单详情数量
     */
    detail_num?: number
    /**
     * 面料
     */
    product?: string
    /**
     * 面料id
     */
    product_id?: number
    /**
     * 利润率
     */
    profit_margin?: string
    /**
     * 销售增长率
     */
    sale_growth_rate?: string
    [property: string]: any
  }
}
