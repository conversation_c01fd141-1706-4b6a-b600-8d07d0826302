// 产品分析的模块
declare namespace Api.CustomerAnalysis {
  /**
   * data_analysis.GetAnalysisHomeReq
   */
  export interface CustomerAnalysisDataRequest {
    /**
     * 客户id
     */
    customer_id?: string
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    /**
     * 城市
     */
    location?: string
    /**
     * 产品颜色id
     */
    product_color_id?: number
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 开始时间
     */
    start_time?: string
    /**
     * 分析类型 1:产品分析 2:客户分析
     */
    type?: number
    /**
     * 布种类型id
     */
    type_fabric_id?: number
    [property: string]: any
  }
  interface CustomerAnalysisDataResponse { // 响应数据类型
    customer_sale_ratio: CustomerSaleRatio[] // 客户销售占比
    total_roll: number // 总匹数
    total_settle_weight: string // 总数量
    total_customer_count: number // 客户数
    total_sale_amount: number // 总销售金额
    top_10_sale_group_ratio: SaleGroupRatio[] // 客户销售占比
    top_10_product_category_ratio: CustomerCategoryRatio[] // 客户类别占比
    new_customer_ratio: string // 近30天新客户占比
    old_customer_ratio: string // 近30天老客户占比
    sales_area_stat: SalesAreaStat[] // 销售地区
    top_10_product_ids: number[] | null // 前十产品id
    top_10_products: Top10ProductDetail[] | null // 前十产品详细信息 (产品排行)

    // 改
    top_10_customers: Top10CustomerDetail[] // 客户排行 (不分大货剪板)
    top_10_big_customers: Top10CustomerDetail[] | null // 客户排行 （大货）
    top_10_plate_customers: Top10CustomerDetail[] | null // 客户排行 （剪板）

    top_10_products: Top10ProductDetail[] | null // 产品排行 (不分大货剪板)
    top_10_big_products: Top10ProductDetail[] | null // 产品排行 （大货）
    top_10_plate_products: Top10ProductDetail[] | null // 产品排行 （剪板）
  }
  interface CustomerSaleRatio { // 客户销售占比
    customer_id: number // 销售群体ID
    customer_name: string // 销售群体名称
    sale_ratio: string // 销售占比
  }
  interface SaleGroupRatio { // 客户销售占比
    sale_group_id: number // 销售群体ID
    sale_group_name: string // 销售群体名称
    sale_ratio: string // 销售占比
  }
  interface CustomerCategoryRatio { // 客户类别占比
    type_id: number // 布种类型ID
    type_name: string // 布种类型名称
    sale_ratio: string // 销售占比
  }
  interface SalesAreaStat { // 销售地区
    name: string // 地区名称
    value: number // 销售金额
  }
  interface Top10CustomerDetail { // 客户排行
    customer_id: number // 客户ID
    customer_name: string // 客户名称
    total_roll: number // 匹数
    total_settle_weight: number // 数量
    sale_amount: number // 销售金额
    product_count: number // 面料种类数量
    color_count: number // 颜色数量
    roll_ratio: string // 匹数占比
    last_15_day_roll: number[] // 最近15天每天的匹数
  }
  interface Top10ProductDetail { // 前十产品详细信息
    product_id: number // 产品ID
    product_code: string // 产品编号
    product_name: string // 产品名称
    sale_roll: number // 销售匹数
    return_roll: number // 退货匹数
    total_weight: number // 总数量
    total_roll: number // 总匹数
    sale_amount: number // 销售金额
    color_count: number // 颜色数量
    customer_count: number // 客户数量
    roll_ratio: string // 匹数占比，也就是占所有匹数的占比
    last_15_day_roll: number[] // 最近15客户下单该产品天每天的匹数
  }
  // 客户详情 + 产品详情 的返回值
  interface CustomerProductDetailResponse {
    customer_id: number // 客户ID
    customer_name: string // 客户名称
    product_id: number // 点击进来对应的产品id
    product_code: string // 产品编号
    product_name: string // 产品名称
    total_sale_amount: number // 销售总额
    total_roll: number // 总匹数
    total_settle_weight: string // 总数量（按单位分组）
    color_sale_ranks: ProductColorSaleRank[] // 颜色销售排行
    color_sale_big_ranks: ProductColorSaleRank[] // 颜色销售排行 大货
    color_sale_plate_ranks: ProductColorSaleRank[] // 颜色销售排行 剪板
  }
  interface ProductColorSaleRank { // 颜色排行
    color_id: number // 颜色ID
    color_code: string // 颜色编号
    color_name: string // 颜色名称
    roll: number // 匹数
    settle_weight: number // 结算重量
    sale_amount: number // 销售金额
    customer_count: number // 客户数
    roll_ratio: string // 匹数占比
    last_15_day_roll: number[] // 最近15天每天的匹数
  }
}
