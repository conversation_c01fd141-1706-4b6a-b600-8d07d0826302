declare namespace Api.CustomerVisit {

}
declare namespace Api.TeamMonthRecordNumsList{
  export interface Request {
    /**
     * 日期日
     */
    date?: string
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    [property: string]: any
  }
  /**
   * structure.GetPurchaserFollowRecordNumsData
   */
  export interface Response {
    /**
     * 日期日
     */
    date?: string
    /**
     * 有效跟进记录数
     */
    effective_record_nums?: number
    /**
     * 总跟进记录数
     */
    total_record_nums?: number
    [property: string]: any
  }
}
declare namespace Api.TeamList {
  export interface Request {
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    /**
     * 起始时间
     */
    start_time?: string
    /**
     * 提交人IDs
     */
    submit_user_ids?: string
    [property: string]: any
  }
  /**
   * qywx.GetCustomerFollowRecordData
   */
  export interface Response {
    /**
     * 客户跟进记录详情数据
     */
    customer_follow_record_detail_data?: QywxCustomerFollowRecordDetailData[]
    /**
     * 有效跟进记录数
     */
    effective_record_nums?: number
    /**
     * 选择结束日期
     */
    select_end_date?: string
    /**
     * 选择开始日期
     */
    select_start_date?: string
    /**
     * 总数(用于分页)
     */
    total?: number
    /**
     * 总跟进记录数
     */
    total_record_nums?: number
    [property: string]: any
  }

  /**
   * qywx.CustomerFollowRecordDetailData
   */
  export interface QywxCustomerFollowRecordDetailData {
    /**
     * 附件URL
     */
    attachment_url?: string[]
    /**
     * 往来单位Id
     */
    biz_unit_id?: number
    /**
     * 往来单位名称
     */
    biz_unit_name?: string
    /**
     * 评论
     */
    comments?: QywxComment[]
    /**
     * 坐标系
     */
    coordinate_system?: string
    /**
     * 创建日期
     */
    create_date?: string
    /**
     * 开发产品
     */
    dev_products?: QywxDevProduct[]
    /**
     * Id
     */
    external_user_id?: string
    /**
     * 名称
     */
    external_user_name?: string
    /**
     * 反馈内容
     */
    feedbacks?: QywxFeedback[]
    /**
     * 跟进备注
     */
    follow_remark?: string
    /**
     * Id
     */
    id?: number
    /**
     * 标签名称
     */
    label_names?: string[]
    /**
     * 纬度
     */
    latitude?: string
    /**
     * 位置地址
     */
    location_address?: string
    /**
     * 位置名称
     */
    location_name?: string
    /**
     * 经度
     */
    longitude?: string
    matchable_product_ids?: number[]
    /**
     * 可匹配产品
     */
    matchable_product_names?: string[]
    /**
     * 未添加企业微信备注
     */
    not_add_corp_wechat_remark?: string
    /**
     * 提交人Id
     */
    submit_user_id?: number
    /**
     * 提交人名称
     */
    submit_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.Comment
   */
  export interface QywxComment {
    /**
     * 评论Id
     */
    comment_id?: number
    /**
     * 评论内容
     */
    comment_text?: string
    /**
     * 评论时间
     */
    comment_time?: string
    /**
     * 评论用户Id
     */
    comment_user_id?: number
    /**
     * 评论用户名称
     */
    comment_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.DevProduct
   */
  export interface QywxDevProduct {
    /**
     * Id
     */
    id?: number
    /**
     * 产品名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 克重
     */
    weight_density?: string
    [property: string]: any
  }

  /**
   * qywx.Feedback
   */
  export interface QywxFeedback {
    /**
     * 反馈解决方案
     */
    feedback_solution?: string
    /**
     * 反馈标题
     */
    feedback_title?: string
    /**
     * Id
     */
    id?: number
    /**
     * 新品反馈
     */
    new_product_feedback?: string
    [property: string]: any
  }
}
declare namespace Api.addComment {
  /**
   * qywx.CommentCustomerFollowRecordParam
   */
  export interface Request {
    /**
     * 评论内容
     */
    comment_text?: string
    /**
     * 客户跟进记录Id
     */
    customer_follow_record_id?: number
    [property: string]: any
  }
  export type Response = void
}
declare namespace Api.GetTeamPurchaserFollowRecordData {
  export interface Request {
    /**
     * 结束时间
     */
    end_time?: string
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * 客户标签Ids
     */
    purchaser_label_ids?: string
    /**
     * size
     */
    size?: number
    /**
     * 起始时间
     */
    start_time?: string
    /**
     * 提交人Ids
     */
    submit_user_ids?: string
    [property: string]: any
  }
  /**
   * qywx.GetCustomerFollowRecordData
   */
  export interface Response {
    /**
     * 客户跟进记录详情数据
     */
    customer_follow_record_detail_data?: QywxCustomerFollowRecordDetailData[]
    /**
     * 有效跟进记录数
     */
    effective_record_nums?: number
    /**
     * 选择结束日期
     */
    select_end_date?: string
    /**
     * 选择开始日期
     */
    select_start_date?: string
    /**
     * 总数(用于分页)
     */
    total?: number
    /**
     * 总跟进记录数
     */
    total_record_nums?: number
    [property: string]: any
  }

  /**
   * qywx.CustomerFollowRecordDetailData
   */
  export interface QywxCustomerFollowRecordDetailData {
    /**
     * 附件URL
     */
    attachment_url?: string[]
    /**
     * 往来单位Id
     */
    biz_unit_id?: number
    /**
     * 往来单位名称
     */
    biz_unit_name?: string
    /**
     * 评论
     */
    comments?: QywxComment[]
    /**
     * 坐标系
     */
    coordinate_system?: string
    /**
     * 创建日期
     */
    create_date?: string
    /**
     * 开发产品
     */
    dev_products?: QywxDevProduct[]
    /**
     * Id
     */
    external_user_id?: string
    /**
     * 名称
     */
    external_user_name?: string
    /**
     * 反馈内容
     */
    feedbacks?: QywxFeedback[]
    /**
     * 跟进备注
     */
    follow_remark?: string
    /**
     * Id
     */
    id?: number
    /**
     * 标签名称
     */
    label_names?: string[]
    /**
     * 纬度
     */
    latitude?: string
    /**
     * 位置地址
     */
    location_address?: string
    /**
     * 位置名称
     */
    location_name?: string
    /**
     * 经度
     */
    longitude?: string
    matchable_product_ids?: number[]
    /**
     * 可匹配产品
     */
    matchable_product_names?: string[]
    /**
     * 未添加企业微信备注
     */
    not_add_corp_wechat_remark?: string
    /**
     * 提交人Id
     */
    submit_user_id?: number
    /**
     * 提交人名称
     */
    submit_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.Comment
   */
  export interface QywxComment {
    /**
     * 评论Id
     */
    comment_id?: number
    /**
     * 评论内容
     */
    comment_text?: string
    /**
     * 评论时间
     */
    comment_time?: string
    /**
     * 评论用户Id
     */
    comment_user_id?: number
    /**
     * 评论用户名称
     */
    comment_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.DevProduct
   */
  export interface QywxDevProduct {
    /**
     * Id
     */
    id?: number
    /**
     * 产品名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 克重
     */
    weight_density?: string
    [property: string]: any
  }

  /**
   * qywx.Feedback
   */
  export interface QywxFeedback {
    /**
     * 反馈解决方案
     */
    feedback_solution?: string
    /**
     * 反馈标题
     */
    feedback_title?: string
    /**
     * Id
     */
    id?: number
    /**
     * 新品反馈
     */
    new_product_feedback?: string
    [property: string]: any
  }
}
declare namespace Api.DeletePurchaserFollowRecord {
  /**
   * qywx.DeleteCustomerFollowRecordParam
   */
  export interface Request {
    /**
     * 客户跟进记录Id
     */
    customer_follow_record_id?: number
    [property: string]: any
  }
  export type Response = void
}
declare namespace Api.GetVisitTagList {
  export type Request = void
  /**
   * structure.GetVisitTagData
   */
  export interface Response {
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    create_user_name?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 记录ID
     */
    id?: number
    /**
     * 拜访标签IDs
     */
    ids?: number[]
    /**
     * 最大拜访天数
     */
    max_range_day?: number
    /**
     * 最小拜访天数
     */
    min_range_day?: number
    /**
     * 拜访标签名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 排序
     */
    sort?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 状态名称
     */
    status_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 拜访模式名称
     */
    visiting_mode_names?: string[]
    /**
     * 拜访模式
     */
    visiting_modes?: number[]
    [property: string]: any
  }
}
declare namespace Api.GetLastTagData {
  export interface Request {
    purchaser_id?: number
    purchaser_clue_id?: number
  }
  /**
   * structure.GetLastTagData
   */
  export interface Response {
    /**
     * 标签信息
     */
    tag_infos?: StructureTagInfo[]
    [property: string]: any
  }

  /**
   * structure.TagInfo
   */
  export interface StructureTagInfo {
    /**
     * 标签ID
     */
    id?: number
    /**
     * 标签名称
     */
    name?: string
    /**
     * 标签ID
     */
    tag_id?: string
    [property: string]: any
  }
}
declare namespace Api.GetWechatFriends {
  export interface Request {
    name: string
  }
  /**
   * qywx.WechatFriendData
   */
  export interface Response {
    /**
     * 外部联系人头像，第三方不可获取
     */
    avatar?: string
    /**
     * 客户Id
     */
    biz_unit_id?: number
    /**
     * 客户名称
     */
    biz_unit_name?: string
    /**
     * 外部联系人所在企业的主体名称，仅当联系人类型是企业微信用户时有此字段
     */
    corp_full_name?: string
    /**
     * 群聊数量
     */
    corp_group_chat_count?: number
    /**
     * 群聊信息
     */
    corp_group_chat_info?: QywxCorpGroupChatInfo[]
    /**
     * 外部联系人所在企业的简称，仅当联系人类型是企业微信用户时有此字段
     */
    corp_name?: string
    /**
     * 企业微信好友数量
     */
    corp_wechat_friend_count?: number
    /**
     * 企业微信好友信息
     */
    corp_wechat_friend_info?: QywxCorpWeChatFriendInfo[]
    /**
     * 外部联系人类型，1-微信用户，2-企业微信用户
     */
    external_type?: number
    /**
     * 外部联系人类型名称
     */
    external_type_name?: string
    /**
     * Id
     */
    external_user_id?: string
    /**
     * 性别
     */
    gender?: string
    /**
     * 是否绑定
     */
    is_bind?: boolean
    /**
     * 外部联系人的姓名
     */
    name?: string
    /**
     * 客户线索Id
     */
    purchaser_clue_id?: number
    [property: string]: any
  }

  /**
   * qywx.CorpGroupChatInfo
   */
  export interface QywxCorpGroupChatInfo {
    /**
     * 群聊名称
     */
    group_chat_name?: string
    /**
     * 入群时间
     */
    join_time?: string
    [property: string]: any
  }

  /**
   * qywx.CorpWeChatFriendInfo
   */
  export interface QywxCorpWeChatFriendInfo {
    /**
     * 添加企业微信时间
     */
    add_create_time?: string
    /**
     * 名称
     */
    name?: string
    [property: string]: any
  }
}
declare namespace Api.GetCorpTagList {
  export interface Request {
    /**
     * 企业微信客户ID
     */
    external_user_id?: string
    /**
     * 企业微信员工ID
     */
    follow_user_id?: string
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    [property: string]: any
  }
  /**
   * qywx.GetCorpTagGroupData
   */
  export interface Response {
    /**
     * 标签组内的标签列表
     */
    corp_tags?: QywxTag[]
    /**
     * 标签组ID
     */
    group_id?: string
    /**
     * ID
     */
    id?: number
    /**
     * 名称
     */
    name?: string
    [property: string]: any
  }

  /**
   * qywx.Tag
   */
  export interface QywxTag {
    /**
     * ID
     */
    id?: number
    /**
     * 是否选中
     */
    is_select?: boolean
    /**
     * 标签id
     */
    tag_id?: string
    /**
     * 标签名称
     */
    tag_name?: string
    [property: string]: any
  }
}
declare namespace Api.GetTeamMatchableProductPurchaserRankDetailList {
  /**
   * qywx.GetMatchableProductPurchaserRankDetailData
   */
  export interface Response {
    /**
     * 客户Id
     */
    biz_unit_id?: number
    /**
     * 客户名称
     */
    biz_unit_name?: string
    /**
     * 联系人
     */
    contact_name?: string
    /**
     * 可匹配产品列表
     */
    matchable_product_list?: QywxMatchableProduct[]
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 销售员名称
     */
    sale_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.MatchableProduct
   */
  export interface QywxMatchableProduct {
    /**
     * 产品编码
     */
    product_code?: string
    /**
     * 产品Id
     */
    product_id?: number
    /**
     * 产品名称
     */
    product_name?: string
    [property: string]: any
  }

  export interface Request {
    start_time?: string
    end_time?: string
  }
}
declare namespace Api.GetTeamMatchableProductDetailData{
  /**
   * qywx.GetMatchableProductDetailData
   */
  export interface Response {
    /**
     * 编码
     */
    product_code?: string
    /**
     * 面料id
     */
    product_id?: number
    /**
     * 名称
     */
    product_name?: string
    /**
     * 比例
     */
    proportion?: number
    /**
     * 条数
     */
    roll?: number
    [property: string]: any
  }
  export interface Request {
    start_time?: string
    end_time?: string
  }
}
declare namespace Api.GetTeamDevProductDetailDataList {
  /**
   * qywx.GetDevProductDetailData
   */
  export interface Response {
    /**
     * 客户Id
     */
    biz_unit_id?: number
    /**
     * 客户名称
     */
    biz_unit_name?: string
    /**
     * 联系人
     */
    contact_name?: string
    /**
     * 开发产品列表
     */
    dev_product_detail_list?: QywxDevProductDetail[]
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 销售员id
     */
    sale_user_id?: number
    /**
     * 销售员名称
     */
    sale_user_name?: string
    [property: string]: any
  }

  /**
   * qywx.DevProductDetail
   */
  export interface QywxDevProductDetail {
    /**
     * 名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 克重
     */
    weight_density?: string
    [property: string]: any
  }

  export interface Request {
    start_time?: string
    end_time?: string
  }
}
declare namespace Api.AddPurchaserVisit {
  /**
   * qywx.CustomerFollowRecordParam
   */
  export interface Request {
    /**
     * 附件URL
     */
    attachment_url?: string[]
    /**
     * 客户Id
     */
    biz_unit_id?: number
    /**
     * 坐标系
     */
    coordinate_system?: string
    /**
     * 开发产品
     */
    dev_products?: QywxDevProduct[]
    /**
     * 外部联系人Id
     */
    external_user_id?: string
    /**
     * 反馈内容
     */
    feedbacks?: QywxFeedback[]
    /**
     * 跟进备注
     */
    follow_remark?: string
    /**
     * 标签
     */
    labels?: QywxCorpTag[]
    /**
     * 纬度
     */
    latitude?: string
    /**
     * 位置地址
     */
    location_address?: string
    /**
     * 位置名称
     */
    location_name?: string
    /**
     * 经度
     */
    longitude?: string
    /**
     * 可匹配产品Ids
     */
    matchable_products?: number[]
    /**
     * 未添加企业微信备注
     */
    not_add_corp_wechat_remark?: string
    /**
     * 拜访情况
     */
    visiting_situations?: QywxVisitingSituation[]
    [property: string]: any
  }

  /**
   * qywx.DevProduct
   */
  export interface QywxDevProduct {
    /**
     * Id
     */
    id?: number
    /**
     * 产品名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 克重
     */
    weight_density?: string
    [property: string]: any
  }

  /**
   * qywx.Feedback
   */
  export interface QywxFeedback {
    /**
     * 反馈解决方案
     */
    feedback_solution?: string
    /**
     * 反馈标题
     */
    feedback_title?: string
    /**
     * Id
     */
    id?: number
    /**
     * 新品反馈
     */
    new_product_feedback?: string
    [property: string]: any
  }

  /**
   * qywx.CorpTag
   */
  export interface QywxCorpTag {
    /**
     * 标签Id
     */
    corp_tag_id?: number
    [property: string]: any
  }

  /**
   * qywx.VisitingSituation
   */
  export interface QywxVisitingSituation {
    /**
     * Id
     */
    id?: number
    /**
     * 其他标签备注
     */
    other_tag_remark?: string
    /**
     * 拜访标签Id(id为0则为其他)
     */
    visit_tag_id?: number
    /**
     * 拜访标签名称
     */
    visit_tag_name?: string
    [property: string]: any
  }
  export interface Response {
    id?: number
    [property: string]: any
  }
}
declare namespace Api.GetUserLocation {
  /**
   * system.GetUserLocationResponse
   */
  export interface Response {
    /**
     * 行政区划信息
     */
    ad_info?: ResponseAdInfo
    /**
     * 以行政区划+道路+门牌号等信息组成的标准格式化地址
     */
    address?: string
    /**
     * 地址部件，address不满足需求时可自行拼接
     */
    address_component?: AddressComponent
    /**
     * 坐标相对位置参考
     */
    address_reference?: AddressReference
    /**
     * 结合知名地点形成的描述性地址，更具人性化特点
     */
    formatted_addresses?: FormattedAddresses
    /**
     * 查询的周边poi的总数，仅在传入参数get_poi=1时返回
     */
    poi_count?: number
    /**
     * 周边地点（POI/AOI）列表，数组中每个子项为一个POI/AOI对象
     */
    pois?: Pois[]
    [property: string]: any
  }

  /**
   * 行政区划信息
   */
  export interface ResponseAdInfo {
    /**
     * 行政区划代码
     */
    adcode?: string
    /**
     * 市
     */
    city?: string
    /**
     * 城市代码，由国家码+行政区划代码（提出城市级别）组合而来，总共为9位
     */
    city_code?: string
    /**
     * 区
     */
    district?: string
    /**
     * 坐标
     */
    location?: AdInfoLocation
    /**
     * 行政区划名称
     */
    name?: string
    /**
     * 国家
     */
    nation?: string
    /**
     * 国家代码（ISO3166标准3位数字码）
     */
    nation_code?: string
    /**
     * 城市电话区号
     */
    phone_area_code?: string
    /**
     * 省
     */
    province?: string
    [property: string]: any
  }

  /**
   * 坐标
   */
  export interface AdInfoLocation {
    /**
     * 纬度
     */
    lat?: number
    /**
     * 经度
     */
    lng?: number
    [property: string]: any
  }

  /**
   * 地址部件，address不满足需求时可自行拼接
   */
  export interface AddressComponent {
    /**
     * 市
     */
    city?: string
    /**
     * 区，可能为空字串
     */
    district?: string
    /**
     * 国家
     */
    nation?: string
    /**
     * 省
     */
    province?: string
    /**
     * 道路，可能为空字串
     */
    street?: string
    /**
     * 门牌，可能为空字串
     */
    street_number?: string
    [property: string]: any
  }

  /**
   * 坐标相对位置参考
   */
  export interface AddressReference {
    /**
     * 商圈
     */
    business_area?: BusinessArea
    /**
     * 知名区域，如商圈或人们普遍认为有较高知名度的区域
     */
    famous_area?: BusinessArea
    /**
     * 一级地标，可识别性较强、规模较大的地点、小区等
     */
    landmark_l1?: BusinessArea
    /**
     * 二级地标，较一级地标更为精确，规模更小
     */
    landmark_l2?: BusinessArea
    /**
     * 道路
     */
    street?: BusinessArea
    /**
     * 乡镇/街道（四级行政区划）
     */
    town?: BusinessArea
    [property: string]: any
  }

  /**
   * 商圈
   *
   * system.FamousAreaType
   *
   * 知名区域，如商圈或人们普遍认为有较高知名度的区域
   *
   * 一级地标，可识别性较强、规模较大的地点、小区等
   *
   * 二级地标，较一级地标更为精确，规模更小
   *
   * 道路
   *
   * 乡镇/街道（四级行政区划）
   */
  export interface BusinessArea {
    /**
     * 此参考位置到输入坐标的方位关系，如：北、南、内
     */
    _dir_desc?: string
    /**
     * 此参考位置到输入坐标的直线距离
     */
    _distance?: number
    /**
     * 乡镇/街道唯一标识（行政区划代码adcode）
     */
    id?: string
    /**
     * 坐标
     */
    location?: BusinessAreaLocation
    /**
     * 名称/标题
     */
    title?: string
    [property: string]: any
  }

  /**
   * 坐标
   */
  export interface BusinessAreaLocation {
    /**
     * 纬度
     */
    lat?: number
    /**
     * 经度
     */
    lng?: number
    [property: string]: any
  }

  /**
   * 结合知名地点形成的描述性地址，更具人性化特点
   */
  export interface FormattedAddresses {
    /**
     * 推荐使用的地址描述，描述精确性较高
     */
    recommend?: string
    /**
     * 粗略位置描述
     */
    rough?: string
    /**
     * 基于附近关键地点（POI）的精确地址
     */
    standard_address?: string
    [property: string]: any
  }

  export interface Pois {
    /**
     * 此参考位置到输入坐标的方位关系，如：北、南、内
     */
    _dir_desc?: string
    /**
     * 此参考位置到输入坐标的直线距离
     */
    _distance?: number
    /**
     * 行政区划信息
     */
    ad_info?: PoisAdInfo
    /**
     * 地址
     */
    address?: string
    /**
     * 地点分类信息
     */
    category?: string
    /**
     * 地点（POI）唯一标识
     */
    id?: string
    /**
     * 坐标
     */
    location?: PoisLocation
    /**
     * 名称
     */
    title?: string
    [property: string]: any
  }

  /**
   * 行政区划信息
   */
  export interface PoisAdInfo {
    /**
     * 行政区划代码
     */
    adcode?: string
    /**
     * 市
     */
    city?: string
    /**
     * 区
     */
    district?: string
    /**
     * 省
     */
    province?: string
    [property: string]: any
  }

  /**
   * 坐标
   */
  export interface PoisLocation {
    /**
     * 纬度
     */
    lat?: number
    /**
     * 经度
     */
    lng?: number
    [property: string]: any
  }
  export interface Request {
    /**
     * 坐标系类型 wgs84 gcj02
     */
    location_type?: string
    /**
     * 纬度
     */
    lat?: number
    /**
     * 经度
     */
    lng?: number
  }
}
declare namespace Api.EditPurchaserVisit {
  /**
   * qywx.UpdateCustomerFollowRecordParam
   */
  export interface Request {
    /**
     * 协助内容
     */
    assists?: Assist[]
    /**
     * 附件URL
     */
    attachment_url?: string[]
    /**
     * 客户Id
     */
    biz_unit_id?: number
    /**
     * 跟进记录Id
     */
    customer_follow_record_id?: number
    /**
     * 开发产品
     */
    dev_products?: DevProduct[]
    /**
     * 外部联系人Id
     */
    external_user_id?: string
    /**
     * 反馈内容
     */
    feedbacks?: Feedback[]
    /**
     * 跟进备注
     */
    follow_remark?: string
    /**
     * 标签
     */
    labels?: Label[]
    /**
     * 可匹配产品Ids
     */
    matchable_products?: number[]
    /**
     * 未添加企业微信备注
     */
    not_add_corp_wechat_remark?: string
    /**
     * 拜访情况
     */
    visiting_situations?: VisitingSituation[]
    [property: string]: any
  }

  export interface Assist {
    /**
     * 协助内容
     */
    assist_content?: string
    /**
     * 部门Id
     */
    department_id?: number
    /**
     * Id
     */
    id?: number
    [property: string]: any
  }

  export interface DevProduct {
    /**
     * Id
     */
    id?: number
    /**
     * 产品名称
     */
    name?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 克重
     */
    weight_density?: string
    [property: string]: any
  }

  export interface Feedback {
    /**
     * 反馈解决方案
     */
    feedback_solution?: string
    /**
     * 反馈标题
     */
    feedback_title?: string
    /**
     * Id
     */
    id?: number
    /**
     * 新品反馈
     */
    new_product_feedback?: string
    [property: string]: any
  }

  export interface Label {
    /**
     * 标签Id
     */
    corp_tag_id?: number
    [property: string]: any
  }

  export interface VisitingSituation {
    /**
     * Id
     */
    id?: number
    /**
     * 其他标签备注
     */
    other_tag_remark?: string
    /**
     * 拜访标签Id
     */
    visit_tag_id?: number
    /**
     * 拜访标签名称
     */
    visit_tag_name?: string
    [property: string]: any
  }
  /**
   * system.AddAndUpdateResponse
   */
  export interface Response {
    /**
     * Id
     */
    id?: number
    [property: string]: any
  }
}
