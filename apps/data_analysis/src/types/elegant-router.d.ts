/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router


declare module "@elegant-router/types" {
  type ElegantConstRoute = import('@ohh-889/react-auto-route').ElegantConstRoute;

  /**
   * route layout
   */
  export type RouteLayout = "base";

  /**
   * route map
   */
  export type RouteMap = {
    "root": "/";
    "not-found": "*";
    "401": "/401";
    "404": "/404";
    "abu-iframe": "/abu-iframe";
    "color-card-iframe": "/color-card-iframe";
    "customer": "/customer";
    "customer_analysis": "analysis";
    "customer_customer-product": "customer-product";
    "customer_infos": "infos";
    "customer-classification-detail": "/customer-classification-detail";
    "customer-debit": "/customer-debit";
    "customer-page": "/customer-page";
    "customer-reconciliation": "/customer-reconciliation";
    "customer-visit": "/customer-visit";
    "customer-visit-add": "/customer-visit-add";
    "customer-visit-label-info": "/customer-visit-label-info";
    "dashboard": "/dashboard";
    "employee-list": "/employee-list";
    "finish-product-sale-order": "/finish-product-sale-order";
    "finish-product-sale-order-add": "/finish-product-sale-order-add";
    "function-dashboard": "/function-dashboard";
    "login": "/login";
    "match-product-detail": "/match-product-detail";
    "oauth2": "/oauth2";
    "order-progress": "/order-progress";
    "product": "/product";
    "product_analysis": "analysis";
    "product_color-infos": "color-infos";
    "product_infos": "infos";
    "product-list-selector": "/product-list-selector";
    "recommend-product-detail": "/recommend-product-detail";
    "wechat-page": "/wechat-page";
  };

  /**
   * route key
   */
  export type RouteKey = keyof RouteMap;

  /**
   * route path
   */
  export type RoutePath = RouteMap[RouteKey];

  /**
   * custom route key
   */
  export type CustomRouteKey = Extract<
    RouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the generated route key
   */
  export type GeneratedRouteKey = Exclude<RouteKey, CustomRouteKey>;

  /**
   * the first level route key, which contain the layout of the route
   */
  export type FirstLevelRouteKey = Extract<
    RouteKey,
    | "401"
    | "404"
    | "abu-iframe"
    | "color-card-iframe"
    | "customer"
    | "customer-classification-detail"
    | "customer-debit"
    | "customer-page"
    | "customer-reconciliation"
    | "customer-visit"
    | "customer-visit-add"
    | "customer-visit-label-info"
    | "dashboard"
    | "employee-list"
    | "finish-product-sale-order"
    | "finish-product-sale-order-add"
    | "function-dashboard"
    | "login"
    | "match-product-detail"
    | "oauth2"
    | "order-progress"
    | "product"
    | "product-list-selector"
    | "recommend-product-detail"
    | "wechat-page"
  >;

  /**
   * the custom first level route key
   */
  export type CustomFirstLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the last level route key, which has the page file
   */
  export type LastLevelRouteKey = Extract<
    RouteKey,
    | "401"
    | "404"
    | "abu-iframe"
    | "color-card-iframe"
    | "customer-classification-detail"
    | "customer-debit"
    | "customer-page"
    | "customer-reconciliation"
    | "customer-visit-add"
    | "customer-visit-label-info"
    | "customer-visit"
    | "customer_analysis"
    | "customer_customer-product"
    | "customer_infos"
    | "dashboard"
    | "employee-list"
    | "finish-product-sale-order-add"
    | "finish-product-sale-order"
    | "function-dashboard"
    | "login"
    | "match-product-detail"
    | "oauth2"
    | "order-progress"
    | "product-list-selector"
    | "product_analysis"
    | "product_color-infos"
    | "product_infos"
    | "recommend-product-detail"
    | "wechat-page"
  >;

  /**
   * the custom last level route key
   */
  export type CustomLastLevelRouteKey = Extract<
    CustomRouteKey,
    | "root"
    | "not-found"
  >;

  /**
   * the single level route key
   */
  export type SingleLevelRouteKey = FirstLevelRouteKey & LastLevelRouteKey;

  /**
   * the custom single level route key
   */
  export type CustomSingleLevelRouteKey = CustomFirstLevelRouteKey & CustomLastLevelRouteKey;

  /**
   * the first level route key, but not the single level
  */
  export type FirstLevelRouteNotSingleKey = Exclude<FirstLevelRouteKey, SingleLevelRouteKey>;

  /**
   * the custom first level route key, but not the single level
   */
  export type CustomFirstLevelRouteNotSingleKey = Exclude<CustomFirstLevelRouteKey, CustomSingleLevelRouteKey>;

  /**
   * the center level route key
   */
  export type CenterLevelRouteKey = Exclude<GeneratedRouteKey, FirstLevelRouteKey | LastLevelRouteKey>;

  /**
   * the custom center level route key
   */
  export type CustomCenterLevelRouteKey = Exclude<CustomRouteKey, CustomFirstLevelRouteKey | CustomLastLevelRouteKey>;

  /**
   * the center level route key
   */
  type GetChildRouteKey<K extends RouteKey, T extends RouteKey = RouteKey> = T extends `${K}_${infer R}`
    ? R extends `${string}_${string}`
      ? never
      : T
    : never;

  /**
   * the single level route
   */
  type SingleLevelRoute<K extends SingleLevelRouteKey = SingleLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}$view.${K}`| `view.${LastLevelRouteKey}`|`layout.${RouteLayout}`;
        children?:ElegantConstRoute[] ;
        layout?:"base"
      }
    : never;

  /**
   * the last level route
   */
  type LastLevelRoute<K extends GeneratedRouteKey> = K extends LastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component: `view.${K}`;
      }
    : never;
  
  /**
   * the center level route
   */
  type CenterLevelRoute<K extends GeneratedRouteKey> = K extends CenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the multi level route
   */
  type MultiLevelRoute<K extends FirstLevelRouteNotSingleKey = FirstLevelRouteNotSingleKey> = K extends string
    ? ElegantConstRoute & {
        name: K;
        path: RouteMap[K];
        component: `layout.${RouteLayout}`| `view.${LastLevelRouteKey}`;
        children: (CenterLevelRoute<GetChildRouteKey<K>> | LastLevelRoute<GetChildRouteKey<K>>)[];
        layout?:"base"
      }
    : never;
  
  /**
   * the custom first level route
   */
  type CustomSingleLevelRoute<K extends CustomFirstLevelRouteKey = CustomFirstLevelRouteKey> = K extends string
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `layout.${RouteLayout}$view.${LastLevelRouteKey}`| `view.${LastLevelRouteKey}`| `$view.${LastLevelRouteKey}`;
        layout?:"base"
      }
    : never;

  /**
   * the custom last level route
   */
  type CustomLastLevelRoute<K extends CustomRouteKey> = K extends CustomLastLevelRouteKey
    ? Omit<ElegantConstRoute, 'children'> & {
        name: K;
        path: RouteMap[K];
        component?: `view.${LastLevelRouteKey}`;
        layout?:"base"
      }
    : never;

  /**
   * the custom center level route
   */
  type CustomCenterLevelRoute<K extends CustomRouteKey> = K extends CustomCenterLevelRouteKey
    ? Omit<ElegantConstRoute, 'component'> & {
        name: K;
        path: RouteMap[K];
        children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
      }
    : never;

  /**
   * the custom multi level route
   */
  type CustomMultiLevelRoute<K extends CustomFirstLevelRouteNotSingleKey = CustomFirstLevelRouteNotSingleKey> =
    K extends string
      ? ElegantConstRoute & {
          name: K;
          path: RouteMap[K];
          component: `layout.${RouteLayout}`| `view.${LastLevelRouteKey}`;
          children: (CustomCenterLevelRoute<GetChildRouteKey<K>> | CustomLastLevelRoute<GetChildRouteKey<K>>)[];
          layout?:"base"
        }
      : never;

  /**
   * the custom route
   */
  type CustomRoute = CustomSingleLevelRoute | CustomMultiLevelRoute;

  /**
   * the generated route
   */
  type GeneratedRoute = SingleLevelRoute | MultiLevelRoute;

  /**
   * the elegant route
   */
  type ElegantRoute = GeneratedRoute | CustomRoute;
}
