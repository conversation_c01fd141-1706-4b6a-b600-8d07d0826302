declare namespace Api.SaleSystem {
  /**
   * system.GetSaleSystemDropdownData
   */
  export interface Response {
    /**
     * 地址
     */
    address?: string
    /**
     * 编号
     */
    code?: string
    /**
     * 联系人
     */
    contacts?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 结算天数或月份
     */
    custom_cycle?: number
    /**
     * 默认客户
     */
    default_customer_id?: number
    /**
     * 默认客户名称
     */
    default_customer_name?: string
    /**
     * 默认仓库id
     */
    default_physical_warehouse?: number
    /**
     * 默认仓库名称
     */
    default_physical_warehouse_name?: string
    /**
     * 邮箱
     */
    email?: string
    /**
     * 传真号
     */
    fax_number?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 名称
     */
    name?: string
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 结算周期（settle_type为3时使用）
     */
    settle_cycle?: number
    /**
     * 结算周期名称
     */
    settle_cycle_name?: string
    /**
     * 默认结算类型
     */
    settle_type?: number
    /**
     * 默认结算类型名称
     */
    settle_type_name?: string
    /**
     * 状态
     */
    status?: number
    /**
     * 状态
     */
    status_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    [property: string]: any
  }
  export interface Request {
    address?: string
    phone?: string
    status?: number
    email?: string
    remark?: string
    name?: string
    code?: string
  }
}
