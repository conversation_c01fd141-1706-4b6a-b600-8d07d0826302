declare namespace Api.Customer {
  export interface Request {
    /**
     * 是否导出excel
     */
    download?: number
    /**
     * limit
     */
    limit?: number
    /**
     * 名称
     */
    name?: string
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * 营销系统ID
     */
    sale_system_id?: number
    name_or_shortname?: string
    /**
     * size
     */
    size?: number
    /**
     * 类型
     */
    unit_type_id?: string
    [property: string]: any
  }
  export interface QywxCustomers {
    id: string
    name: string
    type: string
  }
  /**
   * system.GetCustomerEnumListItem
   */
  export interface Response {
    /**
     * 地址
     */
    address?: string
    /**
     * 编号
     */
    code?: string
    /**
     * 联系人名称
     */
    contact_name?: string
    /**
     * 传真
     */
    fax_number?: string
    /**
     * 全称
     */
    full_name?: string
    id?: number
    /**
     * 名称
     */
    name?: string
    qywx_customers?: QywxCustomers[]
    /**
     * 跟单员id
     */
    order_follower_id?: number
    /**
     * 跟单员名称
     */
    order_follower_name?: string
    /**
     * 联系电话
     */
    phone?: string
    /**
     * 销售群体id
     */
    sale_group_id?: number
    /**
     * 销售群体名称
     */
    sale_group_name?: string
    /**
     * 销售员id
     */
    seller_id?: number
    /**
     * 销售员名称
     */
    seller_name?: string
    /**
     * 结算天数
     */
    settle_cycle?: number
    /**
     * 结算类型
     */
    settle_type?: number
    /**
     * 结算类型名称
     */
    settle_type_name?: string
    [property: string]: any
  }
}
declare namespace Api.CustomerProfile{
  export interface Request {

  }
  /**
   * structure.GetPurchaserProfileData
   */
  export interface Response {
    /**
     * 可用授信额度
     */
    credit_available_line?: number
    /**
     * 授信额度
     */
    credit_line?: number
    /**
     * 已用授信额度
     */
    credit_used_line?: number
    /**
     * 客户ID
     */
    id?: number
    /**
     * 是否是新客户
     */
    is_new_purchaser?: boolean
    /**
     * 是否为疑似流失客户
     */
    is_suspected_loss?: boolean
    /**
     * 最后访问时间
     */
    last_visit_time?: string
    /**
     * 严重逾期次数
     */
    overdue_serious_nums?: number
    /**
     * 轻微逾期次数
     */
    overdue_slight_nums?: number
    /**
     * 客户创建时间
     */
    purchaser_create_time?: string
    /**
     * 客户名称
     */
    purchaser_name?: string
    /**
     * 客户备注
     */
    purchaser_remark?: string
    /**
     * 销售人员名称
     */
    sale_user_name?: string
    [property: string]: any
  }
}
declare namespace Api.GetTopCustomerData{
  /**
   * data_analysis.GetTopProductReq
   */
  export interface Request {
    abstract_sort_key?: string
    /**
     * 客户id
     */
    customer_id?: string
    /**
     * 是否导出报表
     */
    download?: number
    /**
     * 结束时间
     */
    end_time?: string
    is_show_new_customer?: boolean
    is_show_old_customer?: boolean
    limit?: number
    location?: string
    /**
     * 偏移量 从0开始（优先使用）
     */
    offset?: number
    /**
     * 页码 从1开始
     */
    page?: number
    /**
     * 产品颜色id
     */
    product_color_id?: string
    /**
     * 产品id
     */
    product_id?: number
    sale_group_id?: string
    /**
     * 销售模式id
     */
    sale_mode_iden?: number
    /**
     * 营销体系id
     */
    sale_system_id?: number
    /**
     * 销售员id
     */
    sale_user_id?: number
    size?: number
    sort_key?: string
    /**
     * 开始时间
     */
    start_time?: string
    type_fabric_id?: number
    [property: string]: any
  }
  export interface GetTopCustomerResForTop {
    customer_id: number // 客户ID
    customer_name: string // 客户名称
    product_count: number // 产品数量
    product_color_count: number // 产品颜色数量
    total_sale_amount: number // 销售总额
    sale_roll: number // 销售匹数
    return_roll: number // 退货匹数
    total_roll: number // 总匹数
    total_settle_weight: string // 总数量（按单位分组）
    roll_ratio: string // 匹数占比
    last_15_day_sale_amount: number[] // 近15天销售额趋势
  }
  /**
   * data_analysis.GetTopDataRes
   */
  export interface Response {
    /**
     * 前十产品列表，可以是GetTopProductResForTop或GetTopColorResForTop的切片
     */
    top_data_list?: GetTopCustomerResForTop[]
    [property: string]: any
  }
}
declare namespace Api.GetCustomerMatrixDetail{
  export interface Request {
    /**
     * 客户id
     */
    customer_name?: string

    /**
     * end_time
     */
    end_time?: string
    /**
     * start_time
     */
    start_time?: string
    [property: string]: any
  }
  /**
   * should_collect_order.GetCustomerMatrixDetailData
   */
  export interface Response {
    /**
     * 平均订单数（购买频率）
     */
    average_order_num?: number
    /**
     * 平均销售金额
     */
    average_sale_price?: number
    /**
     * 核心客户
     */
    core_list?: ShouldCollectOrderCustomerMatrixDetail[]
    /**
     * 活跃低价值客户
     */
    ordinary_list?: ShouldCollectOrderCustomerMatrixDetail[]
    /**
     * 潜力客户
     */
    potential_list?: ShouldCollectOrderCustomerMatrixDetail[]
    /**
     * 普通客户
     */
    un_active_list?: ShouldCollectOrderCustomerMatrixDetail[]
    [property: string]: any
  }

  /**
   * should_collect_order.CustomerMatrixDetail
   */
  export interface ShouldCollectOrderCustomerMatrixDetail {
    /**
     * 客户id
     */
    customer_id?: number
    /**
     * 客户名称
     */
    customer_name?: string
    /**
     * 购买频率
     */
    order_num?: number
    /**
     * 最近交易日期
     */
    order_time?: string
    /**
     * 采购金额
     */
    total_sale_price?: number
    /**
     * 客户类型
     */
    type?: number
    [property: string]: any
  }
}
