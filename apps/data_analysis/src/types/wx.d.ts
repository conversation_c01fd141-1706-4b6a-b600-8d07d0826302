declare namespace Api.WxSignature{
  export interface Request {
    /**
     * id
     */
    nonceStr: string
    /**
     * id
     */
    timestamp: number
    /**
     * id
     */
    url: string
    [property: string]: any
  }
  /**
   * structure.QYWechatSignatureData
   */
  export interface Response {
    /**
     * 签名
     */
    app_signature?: string
    /**
     * 签名
     */
    corp_signature?: string
    [property: string]: any
  }

}
declare namespace Api.getID{
  export interface Request {
    /**
     * id
     */
    qywx_customer_id?: string
    qywx_group_chat_id?: string
    [property: string]: any
  }
  /**
   * qywx.QYWXCustomerBindRelResponse
   */
  export interface Response {
    customer_id?: number
    customer_name?: string
    [property: string]: any
  }

}
