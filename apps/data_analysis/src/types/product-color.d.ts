declare namespace Api.ProductColor {
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * 成品id
     */
    finish_product_id?: number
    /**
     * limit
     */
    limit?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * 颜色编号
     */
    product_color_code?: string
    /**
     * 颜色编号或名称
     */
    product_color_code_or_name?: string
    /**
     * 颜色id
     */
    product_color_id?: number
    /**
     * 颜色名称
     */
    product_color_name?: string
    /**
     * size
     */
    size?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 颜色类别id
     */
    type_finished_product_kind_id?: number
    /**
     * 布种类型id
     */
    type_grey_fabric_id?: number
    [property: string]: any
  }
  /**
   * product.GetFinishProductColorDropdownData
   */
  export interface Response {
    /**
     * 封面纹理图片URL（详情显示）
     */
    cover_texture_url?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 密度
     */
    density?: string
    /**
     * 成品编号
     */
    finish_product_code?: string
    /**
     * 成品全称
     */
    finish_product_full_name?: string
    /**
     * 成品克重(2)
     */
    finish_product_gram_weight?: string
    /**
     * 成品克重及单位名称
     */
    finish_product_gram_weight_and_unit_name?: string
    /**
     * 成品克重单位id(字典)
     */
    finish_product_gram_weight_unit_id?: number
    /**
     * 成品克重单位名称
     */
    finish_product_gram_weight_unit_name?: string
    /**
     * 成品id
     */
    finish_product_id?: number
    /**
     * 成品名称
     */
    finish_product_name?: string
    /**
     * 成品幅宽(2)
     */
    finish_product_width?: string
    /**
     * 成品幅宽及单位名称
     */
    finish_product_width_and_unit_name?: string
    /**
     * 成品幅宽单位id(字典)
     */
    finish_product_width_unit_id?: number
    /**
     * 成品幅宽单位名称
     */
    finish_product_width_unit_name?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布信息ID
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 长度转数量(公斤/米)
     */
    length_to_weight_rate?: number
    /**
     * 默认计量单位id(小程序使用)
     */
    measurement_unit_id?: number
    /**
     * 默认计量单位名称(小程序使用)
     */
    measurement_unit_name?: string
    /**
     * 拼接颜色编号和名称
     */
    merge_code_name?: string
    /**
     * 颜色编号
     */
    product_color_code?: string
    /**
     * 颜色名称
     */
    product_color_name?: string
    /**
     * 成品克重(1),优先使用
     */
    product_gram_weight?: string
    /**
     * 成品幅宽(1),优先使用
     */
    product_width?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 纹理图片URL(单张)
     */
    texture_url?: string[]
    /**
     * 颜色类别id
     */
    type_finished_product_kind_id?: number
    /**
     * 颜色类别名称
     */
    type_finished_product_kind_name?: string
    /**
     * 布种类型编号
     */
    type_grey_fabric_code?: string
    /**
     * 布种类型id
     */
    type_grey_fabric_id?: number
    /**
     * 布种类型名称
     */
    type_grey_fabric_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 纱支
     */
    yarn_count?: string
    [property: string]: any
  }
}
