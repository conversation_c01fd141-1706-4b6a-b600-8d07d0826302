declare namespace Api.Product {
  export interface Request {
    /**
     * download
     */
    download?: number
    /**
     * 染厂跟单用户ID
     */
    dye_factory_order_follower_id?: number
    /**
     * 染整工艺
     */
    dyeing_craft?: string
    /**
     * 成品编号
     */
    finish_product_code?: string
    /**
     * 成品编号或名称
     */
    finish_product_code_or_name?: string
    /**
     * 成品工艺
     */
    finish_product_craft?: string
    /**
     * 成品克重
     */
    finish_product_gram_weight?: string
    /**
     * 成品等级id
     */
    finish_product_level_id?: number
    /**
     * 成品名称或全称
     */
    finish_product_name?: string
    /**
     * 成品幅宽
     */
    finish_product_width?: string
    /**
     * 坯布信息编号
     */
    grey_fabric_code?: string
    /**
     * 坯布信息ID
     */
    grey_fabric_id?: number
    /**
     * 坯布信息名称
     */
    grey_fabric_name?: string
    /**
     * limit
     */
    limit?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * offset
     */
    offset?: number
    /**
     * page
     */
    page?: number
    /**
     * size
     */
    size?: number
    /**
     * 状态
     */
    status?: number
    /**
     * 布种类型id
     */
    type_grey_fabric_id?: number
    /**
     * 仓库id
     */
    warehouse_id?: number
    [property: string]: any
  }
  /**
   * product.GetFinishProductDropdownData
   */
  export interface Response {
    /**
     * 漂染性id（字典）
     */
    bleach_id?: number
    /**
     * 漂染性名称
     */
    bleach_name?: string
    /**
     * 经纬度
     */
    center?: string
    /**
     * 封面纹理图片URL（详情显示）
     */
    cover_texture_url?: string
    /**
     * 创建时间
     */
    create_time?: string
    /**
     * 创建人
     */
    creator_id?: number
    /**
     * 创建人
     */
    creator_name?: string
    /**
     * 密度
     */
    density?: string
    /**
     * 染厂跟单用户ID
     */
    dye_factory_order_follower_id?: number
    /**
     * 染厂跟单用户名称
     */
    dye_factory_order_follower_name?: string
    /**
     * 染整工艺
     */
    dyeing_craft?: string
    /**
     * 染损(两位小数)
     */
    dyeing_loss?: number
    /**
     * 成品编号
     */
    finish_product_code?: string
    /**
     * 成品工艺
     */
    finish_product_craft?: string
    /**
     * 成品全称
     */
    finish_product_full_name?: string
    /**
     * 成品克重(2)
     */
    finish_product_gram_weight?: string
    /**
     * 成品克重及单位名称
     */
    finish_product_gram_weight_and_unit_name?: string
    /**
     * 成品克重单位id(字典)
     */
    finish_product_gram_weight_unit_id?: number
    /**
     * 成品克重单位名称
     */
    finish_product_gram_weight_unit_name?: string
    /**
     * 成品成分
     */
    finish_product_ingredient?: string
    /**
     * 成品等级
     */
    finish_product_level_id?: number
    /**
     * 成品等级名称
     */
    finish_product_level_name?: string
    /**
     * 成品名称
     */
    finish_product_name?: string
    /**
     * 成品幅宽(2)
     */
    finish_product_width?: string
    /**
     * 成品幅宽及单位名称
     */
    finish_product_width_and_unit_name?: string
    /**
     * 成品幅宽单位id(字典)
     */
    finish_product_width_unit_id?: number
    /**
     * 成品幅宽单位名称
     */
    finish_product_width_unit_name?: string
    /**
     * 坯布编号
     */
    grey_fabric_code?: string
    /**
     * 坯布信息ID
     */
    grey_fabric_id?: number
    /**
     * 坯布名称
     */
    grey_fabric_name?: string
    /**
     * 记录ID
     */
    id?: number
    /**
     * 是否启用色卡编号
     */
    is_color_card?: boolean
    /**
     * 长度转数量(公斤/米)
     */
    length_to_weight_rate?: number
    /**
     * 计量单位id
     */
    measurement_unit_id?: number
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string
    /**
     * 纸筒数量(公斤)
     */
    paper_tube_weight?: number
    /**
     * 成品克重(1),优先使用
     */
    product_gram_weight?: string
    /**
     * 成品幅宽(1),优先使用
     */
    product_width?: string
    /**
     * 备注
     */
    remark?: string
    /**
     * 缩率率(经向)
     */
    shrinkage_warp?: string
    /**
     * 尺寸
     */
    size?: string
    /**
     * 标准数量(公斤)
     */
    standard_weight?: number
    /**
     * 存放区域
     */
    storage_area?: string
    /**
     * 成品色号总数
     */
    sum_colors?: number
    /**
     * 供应商计量单位id
     */
    supplier_measurement_unit_id?: number
    /**
     * 供应商计量单位名称
     */
    supplier_measurement_unit_name?: string
    /**
     * 纹理图片URL（详情显示）
     */
    texture_url?: string[]
    /**
     * 手感风格
     */
    touch_style?: string
    /**
     * 布种类型编号
     */
    type_grey_fabric_code?: string
    /**
     * 布种类型id
     */
    type_grey_fabric_id?: number
    /**
     * 布种类型名称
     */
    type_grey_fabric_name?: string
    /**
     * 修改时间
     */
    update_time?: string
    /**
     * 修改人
     */
    update_user_name?: string
    /**
     * 修改人
     */
    updater_id?: number
    /**
     * 仓库id
     */
    warehouse_id?: number
    /**
     * 仓库名称
     */
    warehouse_name?: string
    /**
     * 织造组织编号
     */
    weaving_organization_code?: string
    /**
     * 织造组织id
     */
    weaving_organization_id?: number
    /**
     * 织造组织名称
     */
    weaving_organization_name?: string
    /**
     * 空差数量(公斤)
     */
    weight_error?: number
    /**
     * 纱支
     */
    yarn_count?: string
    [property: string]: any
  }
}
