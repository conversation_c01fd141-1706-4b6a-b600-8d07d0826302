/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router


import type { LazyRouteFunction, RouteObject } from "react-router-dom";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";
type CustomRouteObject = Omit<RouteObject, 'Component'|'index'> & {
  Component?: React.ComponentType<any>|null;
};

export const layouts: Record<RouteLayout, LazyRouteFunction<CustomRouteObject>> = {
  base: () => import("@/layout/index"),
};

export const pages: Record<LastLevelRouteKey, LazyRouteFunction<CustomRouteObject>> = {
  401: () => import("@/pages/_builtin/401/index.tsx"),
  404: () => import("@/pages/_builtin/404/index.tsx"),
  "abu-iframe": () => import("@/pages/abu-iframe/index.tsx"),
  "color-card-iframe": () => import("@/pages/color-card-iframe/index.tsx"),
  "customer-classification-detail": () => import("@/pages/customer-classification-detail/index.tsx"),
  "customer-debit": () => import("@/pages/customer-debit/index.tsx"),
  "customer-page": () => import("@/pages/customer-page/index.tsx"),
  "customer-reconciliation": () => import("@/pages/customer-reconciliation/index.tsx"),
  "customer-visit-add": () => import("@/pages/customer-visit-add/index.tsx"),
  "customer-visit-label-info": () => import("@/pages/customer-visit-label-info/index.tsx"),
  "customer-visit": () => import("@/pages/customer-visit/index.tsx"),
  customer_analysis: () => import("@/pages/customer/analysis/index.tsx"),
  "customer_customer-product": () => import("@/pages/customer/customer-product/index.tsx"),
  customer_infos: () => import("@/pages/customer/infos/index.tsx"),
  dashboard: () => import("@/pages/dashboard/index.tsx"),
  "employee-list": () => import("@/pages/employee-list/index.tsx"),
  "finish-product-sale-order-add": () => import("@/pages/finish-product-sale-order-add/index.tsx"),
  "finish-product-sale-order": () => import("@/pages/finish-product-sale-order/index.tsx"),
  "function-dashboard": () => import("@/pages/function-dashboard/index.tsx"),
  login: () => import("@/pages/login/index.tsx"),
  "match-product-detail": () => import("@/pages/match-product-detail/index.tsx"),
  oauth2: () => import("@/pages/oauth2/index.tsx"),
  "order-progress": () => import("@/pages/order-progress/index.tsx"),
  "product-list-selector": () => import("@/pages/product-list-selector/index.tsx"),
  product_analysis: () => import("@/pages/product/analysis/index.tsx"),
  "product_color-infos": () => import("@/pages/product/color-infos/index.tsx"),
  product_infos: () => import("@/pages/product/infos/index.tsx"),
  "recommend-product-detail": () => import("@/pages/recommend-product-detail/index.tsx"),
  "wechat-page": () => import("@/pages/wechat-page/index.tsx"),
};
