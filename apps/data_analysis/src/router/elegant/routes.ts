/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/mufeng889/elegant-router
// Vue auto route: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '401',
    path: '/401',
    component: 'layout.base$view.401',
    meta: {
      title: '401'
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.base$view.404',
    meta: {
      title: '404'
    }
  },
  {
    name: 'abu-iframe',
    path: '/abu-iframe',
    component: 'layout.base$view.abu-iframe',
    meta: {
      title: 'abu-iframe'
    }
  },
  {
    name: 'color-card-iframe',
    path: '/color-card-iframe',
    component: 'layout.base$view.color-card-iframe',
    meta: {
      title: 'color-card-iframe'
    }
  },
  {
    name: 'customer',
    path: '/customer',
    component: 'layout.base',
    meta: {
      title: '客户分析'
    },
    children: [
      {
        name: 'customer_analysis',
        path: 'analysis',
        component: 'view.customer_analysis',
        meta: {
          title: '客户分析'
        }
      },
      {
        name: 'customer_customer-product',
        path: 'customer-product',
        component: 'view.customer_customer-product',
        meta: {
          title: '产品详情'
        }
      },
      {
        name: 'customer_infos',
        path: 'infos',
        component: 'view.customer_infos',
        meta: {
          title: '客户详情'
        }
      }
    ]
  },
  {
    name: 'customer-classification-detail',
    path: '/customer-classification-detail',
    component: 'layout.base$view.customer-classification-detail',
    meta: {
      title: '客户分类明细'
    }
  },
  {
    name: 'customer-debit',
    path: '/customer-debit',
    component: 'layout.base$view.customer-debit',
    meta: {
      title: '客户欠款单'
    }
  },
  {
    name: 'customer-page',
    path: '/customer-page',
    component: 'layout.base$view.customer-page',
    meta: {
      title: '客户列表'
    }
  },
  {
    name: 'customer-reconciliation',
    path: '/customer-reconciliation',
    component: 'layout.base$view.customer-reconciliation',
    meta: {
      title: '客户对账单'
    }
  },
  {
    name: 'customer-visit',
    path: '/customer-visit',
    component: 'layout.base$view.customer-visit',
    meta: {
      title: 'customer-visit'
    }
  },
  {
    name: 'customer-visit-add',
    path: '/customer-visit-add',
    component: 'layout.base$view.customer-visit-add',
    meta: {
      title: 'customer-visit-add'
    }
  },
  {
    name: 'customer-visit-label-info',
    path: '/customer-visit-label-info',
    component: 'layout.base$view.customer-visit-label-info',
    meta: {
      title: 'customer-visit-label-info'
    }
  },
  {
    name: 'dashboard',
    path: '/dashboard',
    component: 'layout.base$view.dashboard',
    meta: {
      title: '工作台'
    }
  },
  {
    name: 'employee-list',
    path: '/employee-list',
    component: 'layout.base$view.employee-list',
    meta: {
      title: 'employee-list'
    }
  },
  {
    name: 'finish-product-sale-order',
    path: '/finish-product-sale-order',
    component: 'layout.base$view.finish-product-sale-order',
    meta: {
      title: '成品销售单'
    }
  },
  {
    name: 'finish-product-sale-order-add',
    path: '/finish-product-sale-order-add',
    component: 'layout.base$view.finish-product-sale-order-add',
    meta: {
      title: '新增成品销售单'
    }
  },
  {
    name: 'function-dashboard',
    path: '/function-dashboard',
    component: 'layout.base$view.function-dashboard',
    meta: {
      title: 'function-dashboard'
    }
  },
  {
    name: 'login',
    path: '/login',
    component: 'layout.base$view.login',
    meta: {
      title: '登录'
    }
  },
  {
    name: 'match-product-detail',
    path: '/match-product-detail',
    component: 'layout.base$view.match-product-detail',
    meta: {
      title: 'match-product-detail'
    }
  },
  {
    name: 'oauth2',
    path: '/oauth2',
    component: 'layout.base$view.oauth2',
    meta: {
      title: 'oauth2'
    }
  },
  {
    name: 'order-progress',
    path: '/order-progress',
    component: 'layout.base$view.order-progress',
    meta: {
      title: '订单进度'
    }
  },
  {
    name: 'product',
    path: '/product',
    component: 'layout.base',
    meta: {
      title: '产品分析'
    },
    children: [
      {
        index: true,
        name: 'product_analysis',
        path: 'analysis',
        component: 'view.product_analysis',
        meta: {
          title: '产品分析'
        }
      },
      {
        name: 'product_color-infos',
        path: 'color-infos',
        component: 'view.product_color-infos',
        meta: {
          title: '颜色详情'
        }
      },
      {
        name: 'product_infos',
        path: 'infos',
        component: 'view.product_infos',
        meta: {
          title: '产品详情'
        }
      }
    ]
  },
  {
    name: 'product-list-selector',
    path: '/product-list-selector',
    component: 'layout.base$view.product-list-selector',
    meta: {
      title: 'product-list-selector'
    }
  },
  {
    name: 'recommend-product-detail',
    path: '/recommend-product-detail',
    component: 'layout.base$view.recommend-product-detail',
    meta: {
      title: 'recommend-product-detail'
    }
  },
  {
    name: 'wechat-page',
    path: '/wechat-page',
    component: 'layout.base$view.wechat-page',
    meta: {
      title: 'wechat-page'
    }
  }
];
