import type { CustomRoute, ElegantConstRoute } from '@elegant-router/types'

const ROOT_ROUTE: CustomRoute = {
  name: 'root',
  path: '/',
  component: 'layout.base$view.dashboard',
  meta: {
    title: '工作台',
  },
}

const NOT_FOUND_ROUTE: CustomRoute = {
  component: 'layout.base$view.404',
  meta: {
    constant: true,
    title: 'not-found',
  },
  name: 'not-found',
  path: '*',
}

export const builtinRoutes: ElegantConstRoute[] = [ROOT_ROUTE, NOT_FOUND_ROUTE]
