import type { RoutePath } from '@elegant-router/types'
import type { NavigateOptions } from 'react-router-dom'
import { basePrefix } from '@/router'
import { useNavigate } from 'react-router-dom'

export function useRouterPush() {
  const navigate = useNavigate()

  function routerPushByKey(key: RoutePath | string, options?: NavigateOptions) {
    if (key.includes(`/${basePrefix}`)) {
      window.location.href = `${window.location.origin}${key}`
      return
    }
    console.log('key', key, key.includes(`/${basePrefix}`))
    navigate(key, options)
  }
  function toHome(options?: NavigateOptions) {
    routerPushByKey('/', options)
  }
  function toLogin(options?: NavigateOptions) {
    routerPushByKey('/login', options)
  }
  function toOAuth2(options?: NavigateOptions) {
    routerPushByKey('/oauth2', options)
  }
  return {
    toHome,
    toLogin,
    toOAuth2,
    router<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  }
}
