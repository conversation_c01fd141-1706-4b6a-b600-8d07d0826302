import { useEffect, useState } from 'react'

interface WindowSize {
  width: number
  height: number
  isMobile: boolean // 是否是移动端
  isTablet: boolean // 是否是平板
  isDesktop: boolean // 是否是桌面端
}

function useWindowSize(mobileBreakpoint = 768, tabletBreakpoint = 1024): WindowSize {
  const [windowSize, setWindowSize] = useState<WindowSize>({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
    isMobile: false,
    isTablet: false,
    isDesktop: true,
  })

  useEffect(() => {
    // 处理窗口大小变化
    const handleResize = () => {
      const width = window.innerWidth
      setWindowSize({
        width,
        height: window.innerHeight,
        isMobile: width <= mobileBreakpoint,
        isTablet: width > mobileBreakpoint && width <= tabletBreakpoint,
        isDesktop: width > tabletBreakpoint,
      })
    }

    // 初始化
    handleResize()

    // 添加监听器
    window.addEventListener('resize', handleResize)

    // 清理监听器
    return () => window.removeEventListener('resize', handleResize)
  }, [mobileBreakpoint, tabletBreakpoint])

  return windowSize
}

export default useWindowSize
