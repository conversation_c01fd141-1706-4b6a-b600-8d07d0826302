@reference 'tailwindcss';
.errorPage {
  @apply min-h-screen flex items-center justify-center bg-gradient-to-b from-white to-gray-50;
  animation: fadeIn 0.5s ease-out;
  
  :global {
    .ant-result {
      @apply p-8 bg-white rounded-lg shadow-xs max-w-2xl w-full mx-4;
      animation: slideUp 0.5s ease-out;
      
      .ant-result-icon {
        @apply mb-6;
        animation: bounce 2s infinite;
        
        .anticon {
          @apply text-blue-500;
          transition: transform 0.3s ease;
          
          &:hover {
            transform: scale(1.1);
          }
        }
      }
      
      .ant-result-title {
        @apply text-6xl text-gray-800 font-light mb-6;
        background: linear-gradient(45deg, #2563eb, #3b82f6);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        animation: pulse 3s infinite;
      }
      
      .ant-result-subtitle {
        @apply mt-0;
      }
      
      .ant-result-extra {
        @apply mt-8 flex gap-3 justify-center;
        
        button {
          @apply px-6 h-10 flex items-center gap-2;
          transition: all 0.3s ease;
          
          &:hover {
            @apply transform -translate-y-0.5;
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
          }
          
          &:active {
            @apply transform translate-y-0;
          }
          
          .anticon {
            @apply text-base;
            transition: transform 0.3s ease;
          }
          
          &:hover .anticon {
            animation: wiggle 0.5s ease;
          }
        }
      }
    }
  }
}

.errorInfo {
  @apply text-center;
  opacity: 0;
  animation: fadeIn 0.5s ease-out 0.3s forwards;
  
  .mainText {
    @apply text-gray-600 text-lg mb-2;
  }
  
  .subText {
    @apply text-gray-400 text-sm;
  }
}

// 动画关键帧
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes wiggle {
  0%, 100% {
    transform: rotate(0);
  }
  25% {
    transform: rotate(-10deg);
  }
  75% {
    transform: rotate(10deg);
  }
}

// 移动端适配保持不变
@media (max-width: 640px) {
  .errorPage {
    @apply p-4;
    
    :global {
      .ant-result {
        @apply p-6 shadow-none;
        
        .ant-result-icon {
          @apply mb-4;
        }
        
        .ant-result-title {
          @apply text-4xl mb-4;
        }
        
        .ant-result-extra {
          @apply flex-col mt-6;
          
          button {
            @apply w-full justify-center;
          }
        }
      }
    }
  }
  
  .errorInfo {
    .mainText {
      @apply text-base;
    }
  }
}
