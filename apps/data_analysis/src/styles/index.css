@charset "UTF-8";
@layer tailwind-base, antd;

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer tailwind-base {
  @import 'tailwindcss';
}
:root {
  --primary-bg-color: #f2f9ff;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

* {
  box-sizing: border-box;
}

.noBackground {
  background-color: transparent !important;
  border-inline-end: 0px !important;
}

/* 修复 antd-mobile toast 图标不居中 */
.adm-toast-mask .adm-toast-main-icon .adm-toast-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.required:after {
  content: "*";
  color: #e64340;
}

:global .adm-error-block-image {
  display: flex;
  justify-content: center;
  align-items: center;
}