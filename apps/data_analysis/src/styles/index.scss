@import "tailwindcss";
@layer base {
  svg{
    display: inline !important;
  }
}

:root {
  --primary-bg-color: #f2f9ff;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
  
  &:hover {
    color: #535bf2;
  }
}

* {
  box-sizing: border-box;
}
@mixin active {
  transition: background-color 0.3s;

  &:active {
    background-color: #F0F0F0;
  }
}
//省略号
@mixin common_ellipsis($params: 1) {
  overflow: hidden;
  display: -webkit-box;
  white-space: normal;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: $params;
}

.noBackground {
  background-color: transparent !important;
  border-inline-end: 0px !important;
}
/* 修复 antd-mobile toast 图标不居中 */
.adm-toast-mask .adm-toast-main-icon .adm-toast-icon{
  display: flex;
  justify-content: center;
  align-items: center;
}
.required {
  // 是否显示红*
  &:after {
    content: "*";
    color: #e64340;
  }
}
