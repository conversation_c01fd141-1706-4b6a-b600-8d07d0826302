import type { ConfigProviderProps } from 'antd'
import type { RootState } from './store'
import router from '@/router'
import { px2remTransformer, StyleProvider } from '@ant-design/cssinjs'
import { destroy, init } from '@ly/abu-ai'
import { QueryClientProvider } from '@tanstack/react-query'
import { ConfigProvider } from 'antd'
import { ConfigProvider as AMConfigProvider } from 'antd-mobile'
import zhCNAM from 'antd-mobile/es/locales/zh-CN'
import zhCN from 'antd/locale/zh_CN'
import { useEffect, useMemo } from 'react'
import { useSelector } from 'react-redux'
import { RouterProvider, useLocation, useOutlet } from 'react-router-dom'
import { PersistGate } from 'redux-persist/integration/react'
import ApiPrefixEditor from './components/ApiPrefixEditor'
import Skeleton from './components/skeleton'
import { FetchSwitchAdminToken } from './service/api'
import { queryClient } from './service/request'
import { persistor } from './store'
import { useMobileScreen } from './utils'
import '@ly/abu-ai/abu-ai.css'

type SizeType = ConfigProviderProps['componentSize']
const componentSize: SizeType = 'small'
function Screen() {
  const isMobile = useMobileScreen()
  if (import.meta.env.MODE === 'development' && isMobile) {
    import('vconsole').then((VConsole) => {
      // eslint-disable-next-line no-new
      new VConsole.default({ theme: 'dark' })
    })
  }
  // const token = useSelector((state: RootState) => state.auth.token)
  // const isLoggedIn = useSelector((state: RootState) => state.auth.isLoggedIn)
  // const { mutateAsync: switchAdminToken } = FetchSwitchAdminToken({
  //   onSuccess: (res) => {
  //     init('abu-ai', {
  //       token: res.token,
  //     })
  //   },
  // })
  useEffect(() => {
    init('abu-ai', {
      token: '',
    })

    return () => {
      destroy('abu-ai')
    }
  }, [])

  return (
    <>
      {/* 只在非生产环境显示 API 编辑器 */}
      {import.meta.env.MODE !== 'production' && <ApiPrefixEditor />}
      <RouterProvider fallbackElement={<Skeleton />} future={{ v7_startTransition: true }} router={router} />
    </>
  )
}
const px2rem = px2remTransformer({
  rootValue: 16, // 32px = 1rem; @default 16
})
function App() {
  return (
    <PersistGate loading={null} persistor={persistor}>
      <StyleProvider transformers={[px2rem]}>

        <AMConfigProvider locale={zhCNAM}>
          <ConfigProvider locale={zhCN} componentSize={componentSize}>
            <QueryClientProvider client={queryClient}>
              <Screen />
            </QueryClientProvider>
          </ConfigProvider>
        </AMConfigProvider>
      </StyleProvider>
    </PersistGate>
  )
}

export default App
