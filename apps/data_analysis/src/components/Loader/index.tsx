import classNames from 'classnames'
import { useMemo } from 'react'
import styles from './index.module.scss'

interface LoadingProps {
  size?: number | string
  color?: string
  style?: React.CSSProperties
  className?: string
}

export default function Loader({
  size = 44,
  color = '#004dff',
  style,
  ...props
}: LoadingProps) {
  const diff = useMemo(() => {
    if (typeof size === 'number')
      return `${size / 2}px`

    const match = String(size).match(/^([\d.]+)([a-z%]*)$/i)
    if (match) {
      const [, value, unit] = match
      return `${Number(value) / 2}${unit}`
    }
    return '22px'
  }, [size])

  const minus = useMemo(() => `-${diff}`, [diff])

  const background = useMemo(() => {
    return `${color.replace(/^#/, 'rgba(').replace(/([0-9a-f]{6}|[0-9a-f]{3})/i, (...args) => {
      const hex = args[1]
      if (hex.length === 3) {
        const [r, g, b] = hex.split('')
        return `${Number.parseInt(r + r, 16)},${Number.parseInt(g + g, 16)},${Number.parseInt(b + b, 16)}`
      }
      return `${Number.parseInt(hex.slice(0, 2), 16)},${Number.parseInt(hex.slice(2, 4), 16)},${Number.parseInt(hex.slice(4, 6), 16)}`
    })},0.2)`
  }, [color])

  const containerStyle = {
    width: typeof size === 'number' ? `${size}px` : size,
    height: typeof size === 'number' ? `${size}px` : size,
    ...style,
  }

  const cubeStyle = {
    backgroundColor: background,
    border: `2px solid ${color}`,
  }

  return (
    <div className={classNames(styles.spinner, props.className)} style={containerStyle}>
      <div style={{ ...cubeStyle, transform: `translateZ(${minus}) rotateY(180deg)` }} />
      <div style={{ ...cubeStyle, transform: 'rotateY(-270deg) translateX(50%)', transformOrigin: 'top right' }} />
      <div style={{ ...cubeStyle, transform: 'rotateY(270deg) translateX(-50%)', transformOrigin: 'center left' }} />
      <div style={{ ...cubeStyle, transform: 'rotateX(90deg) translateY(-50%)', transformOrigin: 'top center' }} />
      <div style={{ ...cubeStyle, transform: 'rotateX(-90deg) translateY(50%)', transformOrigin: 'bottom center' }} />
      <div style={{ ...cubeStyle, transform: `translateZ(${diff})` }} />
    </div>
  )
}
