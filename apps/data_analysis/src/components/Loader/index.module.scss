.spinner {
  width: 44px;
  height: 44px;
  animation: spinner 2s infinite ease;
  transform-style: preserve-3d;

  > div {
    height: 100%;
    position: absolute;
    width: 100%;
  }
}

@keyframes spinner {
  0% {
    transform: rotate(45deg) rotateX(-25deg) rotateY(25deg);
  }

  50% {
    transform: rotate(45deg) rotateX(-385deg) rotateY(25deg);
  }

  100% {
    transform: rotate(45deg) rotateX(-385deg) rotateY(385deg);
  }
}
