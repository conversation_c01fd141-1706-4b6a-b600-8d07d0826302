import classNames from 'classnames'
import styles from './index.module.scss'

interface Props {
  header?: React.ReactNode
  children?: React.ReactNode
  footer?: React.ReactNode
  className?: string
  customStyles?: React.CSSProperties
  customBottomClassName?: string
  customContainerClassName?: string
}

function ListFrame(props: Props) {
  const { footer, header } = props

  return (
    <div className={classNames(styles.container, props.customContainerClassName)}>
      {
        header
      }
      <div className={classNames(styles.main, props.className)} style={props.customStyles}>
        {props.children}
      </div>
      {
        footer ? <div className={classNames(styles.bottom, props.customBottomClassName)}>{footer}</div> : null
      }
    </div>
  )
}
export default ListFrame
