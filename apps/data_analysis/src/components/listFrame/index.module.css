.container {
  display: flex;
  flex-flow: column nowrap;
  justify-content: space-between;
  height: 100dvh;
  background-color: #f2f2f2;
}
.container .main {
  flex: 1 1 auto;
  overflow: scroll;
  display: flex;
  flex-flow: column nowrap;
}
.container .bottom {
  position: relative;
  z-index: 1;
  flex-shrink: 0;
  box-shadow: -14px 0 4px 4px rgba(0, 0, 0, 0.05);
  background-color: white;
  padding: 0 12px;
  padding-top: 12px;
  padding-bottom: 12px;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}