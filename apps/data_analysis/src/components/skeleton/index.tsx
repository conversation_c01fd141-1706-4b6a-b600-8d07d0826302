import type { CSSProperties, FC } from 'react'
import classNames from 'classnames'
import styles from './index.module.scss'

interface SkeletonProps {
  width?: number | string
  height?: number | string
  animated?: boolean
  className?: string
  style?: CSSProperties
  round?: boolean
}

const Skeleton: FC<SkeletonProps> = ({
  width,
  height,
  animated = true,
  className,
  style,
  round = false,
}) => {
  return (
    <div
      className={classNames(
        styles.skeleton,
        {
          [styles.animated]: animated,
          [styles.round]: round,
        },
        className,
      )}
      style={{
        width,
        height,
        ...style,
      }}
    />
  )
}

export default Skeleton
