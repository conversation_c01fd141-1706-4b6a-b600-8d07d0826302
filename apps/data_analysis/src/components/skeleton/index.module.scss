@reference "tailwindcss";
.skeleton {
    border-radius: 4px;
    @apply inline-block bg-gray-200;
    
    &.animated {
      background: linear-gradient(
        90deg,
        rgba(190, 190, 190, 0.2) 25%,
        rgba(129, 129, 129, 0.24) 37%,
        rgba(190, 190, 190, 0.2) 63%
      );
      background-size: 400% 100%;
      animation: skeletonLoading 1.4s ease infinite;
    }
  
    &.round {
      @apply rounded-full;
    }
  }
  
  @keyframes skeletonLoading {
    0% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0 50%;
    }
  }
