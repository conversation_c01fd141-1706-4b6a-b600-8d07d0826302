import type { FC } from 'react'
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'

interface LineChartProps {
  data: number[]
  width: number
  height: number
}

const SmallLineChart: FC<LineChartProps> = ({ data, width, height }) => {
  const chartRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (!chartRef.current)
      return

    const chartInstance = echarts.init(chartRef.current)

    const safeData = data?.length > 0 ? data : [0]

    const option = {
      xAxis: {
        type: 'category',
        show: false,
        data: safeData.map((_, index) => index),
      },
      yAxis: {
        type: 'value',
        show: false,
      },
      series: [
        {
          data: safeData,
          type: 'line',
          smooth: false,
          lineStyle: {
            color: '#1677ff',
          },
          symbol: 'none',
        },
      ],
      grid: {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0,
      },
    }

    chartInstance.setOption(option)

    return () => {
      chartInstance.dispose()
    }
  }, [data])

  return <div ref={chartRef} style={{ width, height }} />
}

export default SmallLineChart
