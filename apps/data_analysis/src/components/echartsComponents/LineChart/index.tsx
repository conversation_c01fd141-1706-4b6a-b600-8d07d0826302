import Skeleton from '@/components/skeleton'
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'
import styles from './index.module.scss'

// 定义数据接口
interface LineChartData {
  name: string
  series: Array<{
    name: string
    value: number
  }>
}

interface LineChartProps {
  height?: number | string
  data: LineChartData[]
  loading?: boolean
  config?: {
    showYAxis?: boolean
    showGrid?: boolean
    showXAxisLine?: boolean
    colors?: string[]
    smooth?: boolean // 是否平滑曲线
    showSymbol?: boolean // 是否显示标记点
    showLegend?: boolean // 是否显示图例
  }
}

const defaultConfig = {
  showYAxis: true,
  showGrid: true,
  showXAxisLine: true,
  colors: ['#1677ff', '#52c41a', '#faad14'],
  smooth: true,
  showSymbol: true,
  showLegend: true,
}

const defaultData: LineChartData[] = []

function LineChart({
  height = 200,
  data = defaultData,
  loading = false,
  config = defaultConfig,
}: LineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()

  useEffect(() => {
    if (!chartRef.current || !data.length)
      return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    // 安全地获取系列名称
    const seriesNames = data[0]?.series?.map(item => item.name) || []
    if (!seriesNames.length)
      return

    const option = {
      tooltip: {
        trigger: 'axis',
        confine: true,
      },
      legend: {
        show: config.showLegend && seriesNames.length > 1,
        bottom: 0,
        data: seriesNames,
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '3%',
        bottom: config.showLegend && seriesNames.length > 1 ? '10%' : '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisTick: { show: false },
        axisLine: { show: config.showXAxisLine },
        axisLabel: {
          color: '#666',
          interval: 0,
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        show: config.showYAxis,
        splitLine: {
          show: config.showGrid,
          lineStyle: { color: '#f0f0f0' },
        },
      },
      series: seriesNames.map((seriesName, index) => ({
        name: seriesName,
        type: 'line',
        smooth: config.smooth,
        showSymbol: config.showSymbol,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: config?.colors?.[index % config.colors.length],
        },
        lineStyle: {
          width: 2,
        },
        data: data.map((item) => {
          const seriesItem = item.series?.find(s => s.name === seriesName)
          return seriesItem?.value ?? 0
        }),
      })),
    }

    chartInstance.current.setOption(option)

    const handleResize = () => chartInstance.current?.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data, config, height])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = undefined
      }
    }
  }, [])

  return (
    <div className={styles.lineChart} style={{ height, position: 'relative' }}>
      {loading ? <Skeleton width="100%" height={height} /> : null}
      <div ref={chartRef} style={{ height }} />
    </div>
  )
}

export default LineChart
