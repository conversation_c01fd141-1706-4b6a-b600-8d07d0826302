import Skeleton from '@/components/skeleton'
import * as echarts from 'echarts'
import { useEffect, useRef } from 'react'

interface BarCodeChartProps {
  height?: number
  data: Array<{
    name: string
    value: number
  }>
  loading?: boolean
  config?: {
    showYAxis?: boolean
    showGrid?: boolean
    showXAxisLine?: boolean
    barWidth?: string
    color?: string
    gridLeft?: string
  }
}

const defaultConfig = {
  showYAxis: false,
  showGrid: false,
  showXAxisLine: false,
  barWidth: '50%',
  color: '#1677ff',
  gridLeft: '-4%',
} as const

function BarCodeChart({
  height = 200,
  data = [],
  loading = false,
  config = defaultConfig,
}: BarCodeChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()

  // 初始化图表
  useEffect(() => {
    if (loading)
      return
    if (!chartRef.current)
      return

    // 确保只初始化一次
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }
    const option = {
      tooltip: {
        trigger: 'item',
      },
      grid: {
        left: config.gridLeft,
        right: '0%',
        top: '10px',
        bottom: '0px',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisTick: {
          show: false,
        },
        axisLine: {
          show: config.showXAxisLine,
        },
        axisLabel: {
          color: '#666',
          interval: 0,
          rotate: 45,
          margin: 8,
          align: 'right',
          verticalAlign: 'top', // 垂直对齐方式
          formatter: (value: string) => {
            // 如果文字太长可以截断
            return value.length > 6 ? `${value.substring(0, 6)}...` : value
          },
        },
        boundaryGap: ['0%', '0%'],
      },
      yAxis: {
        type: 'value',
        show: config.showYAxis,
        scale: true, // 设置成 scale 模式
        splitLine: {
          show: config.showGrid,
          lineStyle: {
            color: '#f0f0f0',
          },
        },
      },
      series: [
        {
          data: [1, 2, 3, 4, 6, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          type: 'bar',
          barWidth: '50%',
          barGap: '0%',
          barCategoryGap: '0%',
          itemStyle: {
            color: '#1677ff',
            borderRadius: [2, 2, 0, 0],
          },
          emphasis: {
            itemStyle: {
              color: '#1677ffbf',
            },
          },
        },
        {
          data: [1, 2, 3, 4, 6, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          type: 'bar',
          barGap: '0%',
          barCategoryGap: '0%',
          itemStyle: {
            color: '#1677ff',
            borderRadius: [2, 2, 0, 0],
          },
          emphasis: {
            itemStyle: {
              color: '#1677ffbf',
            },
          },
        },
        {
          data: [1, 2, 3, 4, 6, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15],
          type: 'bar',
          barWidth: '50%',
          barGap: '0%',
          barCategoryGap: '0%',
          itemStyle: {
            color: '#1677ff',
            borderRadius: [2, 2, 0, 0],
          },
          emphasis: {
            itemStyle: {
              color: '#1677ffbf',
            },
          },
        },
        // data.map((item,index) => ({
        //   data: item.series[item],
        //   name: item?.name,
        //   type: 'bar',
        //   barWidth: '50%',
        //   barGap: '0%',
        //   barCategoryGap: '0%',
        // })),
      ],
    }

    chartInstance.current.setOption(option)

    // 处理窗口大小变化
    const handleResize = () => chartInstance.current?.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [data, config, height])

  // 组件卸载时才销毁实例
  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = undefined
      }
    }
  }, [])

  return (
    <div style={{ height, position: 'relative' }}>
      {loading
        ? (
            <Skeleton width="100%" height={height} />
          )
        : null}
      <div ref={chartRef} style={{ height }} />
    </div>
  )
}

export default BarCodeChart
