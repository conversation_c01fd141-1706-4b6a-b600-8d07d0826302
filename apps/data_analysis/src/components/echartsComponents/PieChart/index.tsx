import Skeleton from '@/components/skeleton'
import classNames from 'classnames'
// import type { FC } from 'react'
import * as echarts from 'echarts'
import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react'
import styles from './index.module.scss'

interface PieChartProps {
  height?: number
  loading?: boolean
  defaultData?: Array<{
    name: string
    value: number
    color?: string
  }>
  title?: string
  data: Array<{
    name: string
    value: number
    color?: string
  }>
  className?: string
  style?: React.CSSProperties
  type?: 'ring' | 'pie'
  labelPosition?: 'inside' | 'outside' | 'none'
  itemGap?: number
  onPieClick?: (data: any) => void // 添加点击事件回调
}

export interface PieChartRef {
  refresh: () => void
}

const defaultDatas = [{ name: '暂无数据', value: 1, color: '#f4b352' }]

const PieChart = forwardRef<PieChartRef, PieChartProps>((props, ref) => {
  const {
    height = 200,
    loading = false,
    defaultData = defaultDatas,
    title,
    data,
    style,
    type = 'ring',
    labelPosition = 'none',
    itemGap = 0,
    onPieClick,
  } = props
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const [activeBar, setActiveBar] = useState<{ seriesName: string, dataIndex: number } | null>(null)
  function handleClick(params: any) {
    // 更新激活状态：如果点击的是当前激活的扇区，则取消激活
    setActiveBar(prevState =>
      prevState?.dataIndex === params.dataIndex
        ? null
        : {
            seriesName: params.seriesName,
            dataIndex: params.dataIndex,
          },
    )

    // 调用外部传入的点击回调
    onPieClick?.(params)
  }
  const initChart = useCallback(() => {
    if (!chartRef.current)
      return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    const option = {
      title: title
        ? {
            text: title,
            left: 'center',
            top: 4,
            textStyle: {
              color: '#1f1f1f',
              fontSize: 14,
              fontWeight: 'bolder',
            },
          }
        : undefined,
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}%',
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 10,
        left: 'center',
        itemWidth: 10,
        itemHeight: 10,
        textStyle: {
          color: '#666',
          fontSize: 12,
        },
      },
      series: [{
        type: 'pie',
        radius: type === 'ring' ? ['40%', '70%'] : '70%',
        center: ['50%', '50%'],
        top: '3%',
        bottom: '15%',
        avoidLabelOverlap: true,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: itemGap,
        },
        label: {
          show: true,
          formatter: '{b}\n{d}%',
          fontSize: 12,
          color: labelPosition === 'inside' ? 'white' : '#666',
          position: labelPosition === 'inside' ? 'inner' : 'outside',
        },
        labelLine: {
          show: true,
          showAbove: true,
          length: 10,
          length2: 15,
          smooth: 0.2,
        },
        data: data.sort((a, b) => b.value - a.value).map((item, index) => ({
          ...item,
          label: {
            show: Number.parseInt(`${item.value}`) >= 10, // 大于10%才显示
            fontSize: 12,
          },
          labelLine: {
            show: index < 3,
          },
          name: item.name,
          value: item.value,
          itemStyle: {
            color: activeBar === null || activeBar?.dataIndex === index ? item.color : '#E1E1E1',
          },
        })),
      }],
    }
    // 设置选项并强制刷新
    if (chartInstance.current) {
      chartInstance.current.setOption(option)
      // 添加点击事件监听
      chartInstance.current.on('click', handleClick)
      setTimeout(() => {
        chartInstance.current?.resize({ width: 'auto', height: 'auto' })
      }, 0)
    }
    const handleResize = () => chartInstance.current?.resize()
    window.addEventListener('resize', handleResize)
    chartInstance.current.setOption(option, true)
    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        chartInstance.current.off('click', handleClick)
      }
    }
  }, [title, data, height, type, labelPosition, itemGap, activeBar])

  useImperativeHandle(ref, () => ({
    refresh: () => {
      if (chartInstance.current) {
        initChart()
      }
    },
  }))

  useEffect(() => {
    initChart()

    const handleResize = () => {
      chartInstance.current?.resize()
    }
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = undefined
      }
    }
  }, [initChart])

  if (loading) {
    return <Skeleton width="100%" height="100%" />
  }
  if (!data) {
    return (
      <PieChart
        {...props}
        title={title}
        data={defaultData}
        itemGap={itemGap}
        type={type} // 使用实心饼图
      />
    )
  }
  return (
    <div className={classNames(styles.pieChart, props?.className)} style={style}>
      <div ref={chartRef} className={styles.chartContainer} style={{ height }} />
    </div>
  )
})

PieChart.displayName = 'PieChart'

export default PieChart
