import Skeleton from '@/components/skeleton'
import { Empty } from 'antd'
import * as echarts from 'echarts'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

// 定义数据接口
export interface BarChartData {
  name: string
  series: Array<{
    name: string
    value: number
  }>
}

interface BarChartProps {
  height?: number | string
  data: BarChartData[]
  loading?: boolean
  config?: {
    showYAxis?: boolean
    showGrid?: boolean
    showXAxisLine?: boolean
    barWidth?: string
    colors?: string[]
    gridLeft?: string
    isStack?: boolean // 是否堆叠
    showLegend?: boolean // 是否显示图例
    itemStyle?: any
    tooltip?: any
    defaultSeries?: string[]
    onLegendClick?: (name: string) => void
    onBarClick?: (data: { name: string, value: number, seriesName: string }) => void // 添加点击事件回调
  }
}

const defaultConfig = {
  showYAxis: true,
  showGrid: true,
  showXAxisLine: true,
  barWidth: '50%',
  colors: ['#1677ff', '#52c41a', '#faad14'],
  negativeColors: ['#ff4d4f', '#f5222d', '#cf1322'],
  gridLeft: '3%',
  isStack: false,
  showLegend: true,
}

const defaultData: BarChartData[] = []

function BarChart({
  height = 200,
  data = defaultData,
  loading = false,
  config = defaultConfig,
}: BarChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()
  const [chartReady, setChartReady] = useState(false)
  const [activeBar, setActiveBar] = useState<{ seriesName: string, dataIndex: number } | null>(null)
  // 初始化图表的 effect
  useEffect(() => {
    if (chartRef.current && !chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
      setChartReady(true)
    }
  }, [])
  function handleClick(params: any) {
    console.log('params', params)
    const clickedData = {
      name: params.name,
      value: params.value,
      seriesName: params.seriesName,
    }

    // 更新激活状态
    setActiveBar(prevState =>
      prevState?.seriesName === params.seriesName
      && prevState?.dataIndex === params.dataIndex
        ? null
        : {
            seriesName: params.seriesName,
            dataIndex: params.dataIndex,
          },
    )

    // 调用外部传入的点击回调
    config?.onBarClick?.(clickedData)
  }
  function handleLegendClick(params: any) {
    config?.onLegendClick?.(params.name)
  }
  useEffect(() => {
    // 确保 DOM 元素存在
    if (!chartRef.current)
      return

    // 初始化或重新初始化图表实例
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current, 'vintage', {
        width: 'auto',
        height: 'auto',
        renderer: 'canvas',
      })
      setChartReady(true)
    }

    // 处理数据更新
    if (!data?.length) {
      chartInstance.current.setOption({}, true)
      return
    }

    const seriesNames = data[0]?.series?.map((item, index) => ({
      name: item.name,
      itemStyle: {
        color: config.colors?.[index] || defaultConfig.colors[index],
      },
    })) || []
    if (!seriesNames.length) {
      chartInstance.current.setOption({}, true)
      return
    }
    // 处理图例选中状态
    const legendSelected: Record<string, boolean> = {}
    const allSeries = data?.[0]?.series?.map(item => item.name) || []
    allSeries.forEach((name: string) => {
      legendSelected[name] = config?.defaultSeries
        ? config.defaultSeries.includes(name)
        : true
    })

    const option = {
      tooltip: config.tooltip
        ? config.tooltip
        : {
            trigger: 'axis',
            axisPointer: { type: 'shadow' },
          },
      legend: {
        show: config.showLegend && seriesNames.length > 1, // 只有多系列时才显示图例
        bottom: 0,
        data: seriesNames,
        selected: legendSelected, // 设置图例默认选中状态
        // selectedMode: 'multiple',
      },
      grid: {
        left: config.gridLeft,
        right: '3%',
        top: '10%',
        bottom: config.showLegend && seriesNames.length > 1 ? '10%' : '0%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: data.map(item => item.name),
        axisTick: { show: false },
        axisLine: { show: config.showXAxisLine },
        axisLabel: {
          color: '#666',
          interval: 0,
          rotate: 45,
        },
      },
      yAxis: {
        type: 'value',
        show: config.showYAxis,
        scale: true, // 设置成 scale 模式
        splitLine: {
          show: config.showGrid,
          lineStyle: { color: '#f0f0f0' },
        },
      },
      label: {
        show: true,
        position: 'top',
        formatter: (params: any) => {
          return params.value === 0 ? '' : params.value
        },
        fontSize: 12,
        fontWeight: 'bolder',
        color: '#666',
      },
      series: seriesNames.map((series, index) => ({
        name: series.name,
        type: 'bar',
        stack: config.isStack ? 'total' : undefined,
        barWidth: config.barWidth,
        itemStyle: config.itemStyle
          ? config.itemStyle(series.name, index)
          : {
              color: (params: any) => {
                const value = params.data
                const colorIndex = index % (config.colors?.length || 1)
                // 根据是否是激活状态决定颜色
                if (activeBar
                  && (activeBar.seriesName !== series.name
                    || activeBar.dataIndex !== params.dataIndex)) {
                  return '#e0e0e0' // 非激活状态显示灰色
                }
                return value >= 0
                  ? config.colors?.[colorIndex]
                  : (config.negativeColors?.[colorIndex] || '#ff4d4f')
              },
              borderRadius: config.isStack
                ? index === seriesNames.length - 1
                  ? [2, 2, 0, 0]
                  : [0, 0, 0, 0]
                : [2, 2, 0, 0],
            },
        data: data.map((item) => {
          const seriesItem = item.series?.find(s => s.name === series.name)
          return seriesItem?.value ?? 0 // 使用空值合并运算符
        }),
      })),
    }
    // 设置选项并强制刷新
    if (chartInstance.current) {
      chartInstance.current.setOption(option)
      // 添加点击事件监听
      chartInstance.current.on('click', handleClick)
      // 图例点击事件监听
      chartInstance.current.on('legendselectchanged', handleLegendClick)
      setTimeout(() => {
        chartInstance.current?.resize({ width: 'auto', height: 'auto' })
      }, 0)
    }
    const handleResize = () => chartInstance.current?.resize()
    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      if (chartInstance.current) {
        chartInstance.current.off('click', handleClick)
        chartInstance.current.off('legendselectchanged', handleLegendClick)
      }
    }
  }, [data, config, height, activeBar])

  useEffect(() => {
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose()
        chartInstance.current = undefined
        setChartReady(false)
      }
    }
  }, [])

  return (
    <div className={styles.barChart} style={{ height, position: 'relative' }}>
      {loading
        ? <Skeleton width="100%" height={height} />
        : null }
      <div ref={chartRef} style={{ width: '100%', height, display: data && data.length ? 'block' : 'none' }} />
      {!data || !data.length ? <Empty description="暂无数据" style={{ height: '100%', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }} /> : null}
    </div>
  )
}

export default BarChart
