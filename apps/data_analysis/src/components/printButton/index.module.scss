.main {
  display: flex;
  align-items: center;
  justify-content: center;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 12px;
  cursor: pointer;
  
  &:hover {
    color: #666;
  }
}

.popover {
  :global(.ant-popover-inner) {
    padding: 0;
    background-color: #1f1f1f;
  }

  :global(.ant-popover-arrow) {
    &::before {
      background-color: #1f1f1f;
    }
  }
}

.templateList {
  padding: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.templateItem {
  padding: 8px 16px;
  cursor: pointer;
  white-space: nowrap;
  color: #fff;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}
