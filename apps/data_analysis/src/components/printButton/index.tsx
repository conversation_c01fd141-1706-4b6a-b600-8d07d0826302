import type { PrintDataType, PrintType } from '@/service/request/type'
import { FetchPrintTemplateList, FetchPrintTemplateTemporaryToken } from '@/service/api'
import { useMessage } from '@/utils/message'
import { Popover } from 'antd'
import dayjs from 'dayjs'
import { isEqual } from 'lodash-es'
import { useEffect, useRef, useState } from 'react'
import { AiTwotoneSnippets } from 'react-icons/ai'
import { useSearchParams } from 'react-router-dom'
import styles from './index.module.scss'

interface Props {
  data?: { id: number, name: string }
  print?: {
    type: PrintType
    dataType: PrintDataType
  }
  customStyles?: React.CSSProperties // 主样式`
  printBtnSlot?: React.ReactNode // 主按钮插槽
  queryUrl: string// 请求数据的url - 在父组件进行encodeURIComponent处理
  queryData: any// 请求数据的参数 - 在父组件进行encodeURIComponent(JSON.stringify())处理
}

function PrintBtn(props: Props) {
  const [searchParams] = useSearchParams()
  const { showMessage, contextHolder } = useMessage()
  const [visible, setVisible] = useState(false)
  const [loading, setLoading] = useState(false)
  const prevPrintRef = useRef<Props['print']>()

  const { mutate: getPrintTemplateList, data: templateListState } = FetchPrintTemplateList({
    onSuccess: (res) => {
      setLoading(false)
      if (!res.list?.length)
        showMessage.info('暂无可用的打印模板')
    },
    onError: (error) => {
      setLoading(false)
      showMessage.error(error.message)
    },
  })

  const getData = async () => {
    if (!props.print?.dataType || !props.print.type)
      return
    setLoading(true)
    getPrintTemplateList({
      type: props.print.type,
      data_type: props.print.dataType,
      status: 1,
    })
  }

  const { mutateAsync: getTemplateApi } = FetchPrintTemplateTemporaryToken({
    onError: (error) => {
      setLoading(false)
      showMessage.error(error.message)
    },
  })

  const openArrangeWeight = async (item: Api.PrintTemplate.GetPrintTemplateListResponse) => {
    try {
      setLoading(true)
      const res = await getTemplateApi({
        template_id: item.id,
      })
      let etitle = `${item.type_name}`
      if (props.data?.name) {
        etitle = props.data.name + etitle
      }
      console.log('printQueryData', props.queryData)
      if (props.queryData) {
        const encode = JSON.parse(decodeURIComponent(props.queryData))
        if (encode)
          etitle += `${dayjs(encode.start_time).format('YYMMDD')}-${dayjs(encode.end_time).format('YYMMDD')}`
      }
      window.location.href = `https://${window.location.hostname}/arrange-weight/arrange-weight?id=${searchParams.get('id')}&t=${res.temporary_token}&template_id=${item.id}&queryUrl=${props.queryUrl}&queryData=${props.queryData}&exportFileTitle=${etitle}`
    }
    catch (e: any) {
      setLoading(false)
      showMessage.error(e.message)
    }
  }

  const handleClickTemplate = async (item: Api.PrintTemplate.GetPrintTemplateListResponse) => {
    if (!item.id)
      return
    await openArrangeWeight(item)
  }

  const handleClickMaDan = async () => {
    if (loading)
      return
    if (templateListState?.list?.length === 1) {
      setVisible(false)
      await handleClickTemplate(templateListState.list[0])
      return
    }

    if (!templateListState?.list?.length)
      await getData()

    setVisible(true)
  }

  const content = (
    <div className={styles.templateList}>
      {templateListState?.list?.map(item => (
        <div
          key={item.id}
          className={styles.templateItem}
          onClick={() => handleClickTemplate(item)}
        >
          {item.order_name}
        </div>
      ))}
    </div>
  )

  useEffect(() => {
    if (!isEqual(prevPrintRef.current, props.print)) {
      getData()
      prevPrintRef.current = props.print
    }
  }, [props.print])

  return (
    <>
      {contextHolder}
      <div className={styles.main} style={props.customStyles}>
        {props.print && props.print.type && props.print.dataType && templateListState?.total && (
          <Popover
            open={visible}
            onOpenChange={setVisible}
            content={content}
            trigger="contextMenu"
            placement="top"
            classNames={{ root: styles.popover }}
          >
            <div onClick={handleClickMaDan}>
              {props.printBtnSlot || (
                <div className={styles.item}>
                  <div>
                    <AiTwotoneSnippets className={loading ? 'animate-spin' : ''} />
                  </div>
                  <div>码单</div>
                </div>
              )}
            </div>
          </Popover>
        )}
      </div>
    </>
  )
}

export default PrintBtn
