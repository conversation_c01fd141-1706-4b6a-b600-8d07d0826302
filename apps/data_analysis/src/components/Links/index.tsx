import { RightOutlined } from '@ant-design/icons'
import { <PERSON> } from 'react-router-dom'

interface LinksProps {
  to: string
  children: React.ReactNode
}

const Links: React.FC<LinksProps> = ({ to, children }) => {
  return (
    <Link
      to={to}
      className="inline-flex items-center gap-[6px] text-[#1677ff] text-sm"
    >
      {children}
      <RightOutlined className="text-xs" />
    </Link>
  )
}

export default Links
