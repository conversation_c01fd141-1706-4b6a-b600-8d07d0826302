import * as echarts from 'echarts'
import React, { useEffect, useRef } from 'react'

interface LineChartProps {
  data: Array<{ name: string, value: number }>
  lang: 'lineChart' | 'barChart' | 'pieChart' | 'radarChart'
}

function ChartComponent({ data, lang }: LineChartProps) {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts>()

  useEffect(() => {
    if (!chartRef.current)
      return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    let option: echarts.EChartsOption = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['数值'],
      },
    }

    switch (lang) {
      case 'lineChart':
        option = {
          ...option,
          xAxis: {
            type: 'category',
            data: data.map(item => item.name),
          },
          yAxis: {
            type: 'value',
          },
          series: [{
            name: '数值',
            data: data.map(item => item.value),
            type: 'line',
            smooth: true,
          }],
        }
        break

      case 'barChart':
        option = {
          ...option,
          xAxis: {
            type: 'category',
            data: data.map(item => item.name),
          },
          yAxis: {
            type: 'value',
          },
          series: [{
            name: '数值',
            data: data.map(item => item.value),
            type: 'bar',
          }],
        }
        break

      case 'pieChart':
        option = {
          tooltip: {
            trigger: 'item',
          },
          legend: {
            orient: 'vertical',
            left: 'left',
          },
          series: [{
            name: '数值',
            type: 'pie',
            radius: '50%',
            data: data.map(item => ({
              name: item.name,
              value: item.value,
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          }],
        }
        break

      case 'radarChart':
        option = {
          radar: {
            indicator: data.map(item => ({
              name: item.name,
              max: Math.max(...data.map(d => d.value)) * 1.2,
            })),
          },
          series: [{
            name: '数值',
            type: 'radar',
            data: [{
              value: data.map(item => item.value),
              name: '数据',
            }],
          }],
        }
        break
    }

    chartInstance.current.setOption(option)

    return () => {
      chartInstance.current?.dispose()
    }
  }, [data])

  return <div ref={chartRef} style={{ width: '100%', height: '400px' }} />
}

export default ChartComponent
