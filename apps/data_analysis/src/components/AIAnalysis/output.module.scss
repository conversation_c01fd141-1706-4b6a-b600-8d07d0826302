.outputContainer {
  position: relative;
  padding: 12px;
  min-height: 100px;
  max-height: 500px;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  background-color: white;
  color: #333;
  :global {
    .ant-timeline {
      margin: 16px 0;
      padding: 0 16px;
    }
    .chart-container {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin: 16px 0;
    }

    .markdown-body {
      font-size: 14px;
      line-height: 1.6;
      word-break: break-word;

      pre {
        background-color: #f6f8fa;
        border-radius: 6px;
        padding: 16px;
        overflow: auto;
      }

      code {
        background-color: rgba(175, 184, 193, 0.2);
        border-radius: 6px;
        padding: 0.2em 0.4em;
        font-size: 85%;
      }
    }
  }
  .content {
    :global {
      h1 {
        font-size: 20px;
        font-weight: bold;
      }
      h2 {
        font-size: 18px;
        font-weight: bold;
      }
      h3 {
        font-size: 16px;
        font-weight: bold;
      }
      p {
        margin: 8px 0;
      }
      pre {
        margin: 16px 0;
        padding: 16px;
        background-color: #f6f8fa;
        border-radius: 6px;
        overflow: auto;
      }
      code {
        font-family: Consolas, Monaco, 'Andale Mono', monospace;
        font-size: 14px;
        line-height: 1.5;
      }
      ul, ol {
        padding-left: 20px;
        margin: 8px 0;
      }
      table {
        border-collapse: collapse;
        margin: 16px 0;
        width: 100%;
      }
      th, td {
        border: 1px solid #dfe2e5;
        padding: 6px 13px;
      }
      blockquote {
        margin: 16px 0;
        padding: 0 16px;
        color: #6a737d;
        border-left: 4px solid #dfe2e5;
      }
    }
  }
}

.loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.errorMessage {
  color: #f56c6c;
  padding: 8px;
  text-align: center;
}


