import { CompressOutlined, FullscreenOutlined, SendOutlined } from '@ant-design/icons'
import { Input } from 'antd'
import React, { useState } from 'react'
import styles from './textarea.module.scss'

interface TextareaProps {
  placeholder?: string
  value?: string
  onChange?: (value: string) => void
  onSubmit?: (value: string) => void
}

export default function Textarea({
  placeholder = '输入或黏贴下单内容',
  value = '',
  onChange,
  onSubmit,
  ...props
}: TextareaProps) {
  const [showFullScreen, setShowFullScreen] = useState(false)
  const { TextArea } = Input

  const handleSubmit = () => {
    if (!value)
      return
    onSubmit?.(value)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className={styles.textareaContent}>
      <TextArea
        value={value}
        onChange={e => onChange?.(e.target.value)}
        className={`${styles.textarea} ${showFullScreen ? styles.fullScreen : ''}`}
        rows={3}
        placeholder={placeholder}
        onKeyDown={handleKeyDown}
        {...props}
      />
      <div className="flex justify-end items-center gap-x-1">
        <div
          className="cursor-pointer rounded-sm flex justify-center items-center hover:bg-[#f1f2f3] p-1"
          onClick={() => setShowFullScreen(!showFullScreen)}
        >
          {!showFullScreen
            ? (
                <FullscreenOutlined style={{ fontSize: '20px', color: '#3d3d43' }} />
              )
            : (
                <CompressOutlined style={{ fontSize: '20px', color: '#000' }} />
              )}
        </div>
        <div
          className="cursor-pointer rounded-sm flex justify-center items-center hover:bg-[#f1f2f3] p-1"
          onClick={handleSubmit}
        >
          <SendOutlined style={{ fontSize: '20px' }} />
        </div>
      </div>
    </div>
  )
}
