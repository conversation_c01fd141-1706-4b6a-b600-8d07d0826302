@font-face {
  font-family: "阿里妈妈数黑体 Bold";
  font-weight: 700;
  src: url("//at.alicdn.com/wf/webfont/0wfGzkRxGTr2/MzrM8WGQ8fVy.woff2") format("woff2"),
       url("//at.alicdn.com/wf/webfont/0wfGzkRxGTr2/8kj449JpI58L.woff") format("woff");
  font-display: swap;
}

.popoverContent {
  position: relative;
  padding-bottom: 32px;
}

.popoverHeader {
  width: 100%;
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  height: 32px;
  padding: 4px 8px;
  padding-bottom: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.headerActions {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.actionBtn {
  font-size: 14px;
  padding: 0;
  color: white;
}

.button {
  border-radius: 4px;
  padding: 0 10px;
  // width: 120px;
  height: 30px;
  display: flex;
  font-size: 14px;
  justify-content: center;
  align-items: center;
  font-family: "阿里妈妈数黑体 Bold";
}

.AITextareaCard {
  width: 240px;
  height: 254px;
  border-radius: 4px;
  padding: 5px;
  box-shadow: rgba(151, 65, 252, 0.2) 0 15px 30px -5px;
  background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB);
}

:global {
  .ant-popover-content{
    width: 500px!important;
    @media (max-width: 480px) {
      width: 100%!important;
    }
  }
  .ant-popover-inner {
    background-image: linear-gradient(144deg,#AF40FF, #5B42F3 50%,#00DDEB) !important;
  }
  .ant-popover-arrow::before {
    background-image: linear-gradient(144deg,#a43ffa, #a43ffa 50%,#a43ffa) !important;
    border: none !important;
    background-color: transparent !important;
  }
}
.draggableWrapper{
  position: fixed !important;
  z-index: 1000;
  min-width: unset !important;
  width: unset!important;
  :global(.handle) {
    cursor: move;
    user-select: none;
  }
}
