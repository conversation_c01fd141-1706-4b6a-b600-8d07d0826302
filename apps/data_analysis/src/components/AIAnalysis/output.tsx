import { CheckCircleOutlined, CloseCircleOutlined, LoadingOutlined } from '@ant-design/icons'
import { Timeline } from 'antd'
import * as echarts from 'echarts'
import MarkdownIt from 'markdown-it'
import highlightjs from 'markdown-it-highlightjs'
import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react'
import ReactMarkdown from 'react-markdown'
import rehypeHighlight from 'rehype-highlight'
import rehypeKatex from 'rehype-katex'
import remarkBreaks from 'remark-breaks'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import ChartComponent from './chart'
import styles from './output.module.scss'
import 'highlight.js/styles/github.css'

function debounce(fn: Function, delay: number) {
  let timer: NodeJS.Timeout | null = null
  return function (...args: any[]) {
    if (timer)
      clearTimeout(timer)
    timer = setTimeout(() => fn(...args), delay)
  }
}

interface OutputRef {
  onUpdate: (fullText: string) => void
  onFinish: (message: string) => void
  onError: (err: Error) => void
}
interface FlowStatus {
  status: 'running' | 'finished' | 'error'
  name: string
}

const Output = forwardRef<OutputRef>((_, ref) => {
  const [loading, setLoading] = useState(false)
  const [text, setText] = useState('')
  const [error, setError] = useState<Error | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const chartInstancesRef = useRef<echarts.ECharts[]>([])
  const [flowStatus, setFlowStatus] = useState<FlowStatus[]>([])
  const updateFlowStatus = (status: FlowStatus) => {
    setFlowStatus((prev) => {
      const existingIndex = prev.findIndex(item => item.name === status.name)
      if (existingIndex >= 0) {
        const newStatus = [...prev]
        newStatus[existingIndex] = status
        return newStatus
      }
      return [...prev, status]
    })
  }
  const getTimelineItems = () => {
    return flowStatus.map(item => ({
      dot: item.status === 'running'
        ? <LoadingOutlined style={{ color: '#1677ff' }} />
        : item.status === 'error'
          ? <CloseCircleOutlined style={{ color: '#ff4d4f' }} />
          : <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      children: item.name,
    }))
  }
  const md = new MarkdownIt({
    linkify: true,
    breaks: true,
    html: true,
    typographer: true,
  }).use(highlightjs)

  const scrollToBottom = () => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight
    }
  }

  const initCharts = (charts: any[], _cleanText: string) => {
    const container = document.querySelector('.content')
    if (!container)
      return

    // 清理旧图表实例
    chartInstancesRef.current.forEach(chart => chart.dispose())
    chartInstancesRef.current = []

    // 找到所有图表标记位置
    const paragraphs = container.querySelectorAll('p')
    const placeholders = Array.from(paragraphs).filter(p => p.textContent?.includes('#echart_start#'))

    placeholders.forEach((placeholder, index) => {
      if (charts[index]) {
        const existingContainer = placeholder.nextElementSibling
        let chartContainer: HTMLElement

        if (existingContainer?.classList.contains('chart-container')) {
          chartContainer = existingContainer as HTMLElement
        }
        else {
          chartContainer = document.createElement('div')
          chartContainer.className = 'chart-container'
          chartContainer.style.width = '100%'
          chartContainer.style.height = '400px'
          chartContainer.style.marginBottom = '20px'
          placeholder.parentNode?.insertBefore(chartContainer, placeholder.nextSibling)
        }

        const chart = echarts.init(chartContainer)
        chart.setOption(charts[index])
        chartInstancesRef.current.push(chart)
        placeholder.remove()
      }
    })
  }

  const parseEchartsConfig = (text: string) => {
    const charts: any[] = []
    const regex = /#echart_start#\s*([\s\S]*?)\s*#echart_end#/g
    let match

    while ((match = regex.exec(text)) !== null) {
      try {
        const configText = match[1].replace(/\n/g, '').trim()
        const config = JSON.parse(configText)
        charts.push(config)
      }
      catch (e) {
        console.error('解析图表配置失败:', e)
      }
    }
    return charts
  }

  const debouncedInitCharts = debounce(initCharts, 200)

  useImperativeHandle(ref, () => ({
    onUpdate: (fullText: string) => {
      setText(fullText)
      setTimeout(scrollToBottom, 0)
    },
    onFinish: (message: string) => {
      setLoading(false)
      setText(message)
      setFlowStatus(prev => prev.map(item => ({
        ...item,
        status: item.status === 'running' ? 'finished' : item.status,
      })))
    },
    onError: (err: Error) => {
      setError(err)
      setLoading(false)
      setFlowStatus(prev => prev.map(item => ({
        ...item,
        status: item.status === 'running' ? 'error' : item.status,
      })))
    },
    updateFlowStatus,
  }))

  useEffect(() => {
    return () => {
      chartInstancesRef.current.forEach(chart => chart.dispose())
      chartInstancesRef.current = []
    }
  }, [])

  const markdownComponents = {
    pre: (preProps: any) => {
      return <pre {...preProps} />
    },
    code: (codeProps: any) => {
      const { inline, className, children } = codeProps
      const match = /language-(\w+)/.exec(className || '')
      const lang = match && match[1]
      const chartDataString = children
      try {
        const chartData = JSON.parse(chartDataString)
        switch (lang) {
          case 'lineChart':
          case 'barChart':
          case 'pieChart':
          case 'radarChart':
            return <ChartComponent data={chartData} lang={lang} />
          default:
            return <code {...codeProps} />
        }
      }
      catch (e) {
        console.error('解析图表数据失败:', e)
        return <code {...codeProps} />
      }
    },
    p: (pProps: any) => {
      return <p {...pProps} dir="auto" />
    },
    // 可以根据需要添加其他自定义组件
  }

  const renderMarkdown = () => {
    if (error)
      return <div className="error">{error.message}</div>
    if (!text)
      return null
    try {
      // const charts = parseEchartsConfig(text)
      // const cleanText = text.replace(/#echart_start#[\s\S]*?#echart_end#/g, '<p>#echart_start#</p>')
      // debouncedInitCharts(charts, cleanText)

      return (
        <ReactMarkdown
          remarkPlugins={[remarkMath, remarkGfm, remarkBreaks]}
          rehypePlugins={[
            rehypeKatex,
            [rehypeHighlight, {
              detect: false,
              ignoreMissing: true,
            }],
          ]}
          components={markdownComponents}
        >
          {text}
        </ReactMarkdown>
      )
    }
    catch (err: any) {
      return (
        <div className="error">
          Markdown 渲染错误:
          {err.message}
        </div>
      )
    }
  }
  return (
    <div ref={containerRef} className={`${styles.outputContainer} markdown-body`}>
      {/* {flowStatus.length > 0 && (
        <Timeline items={getTimelineItems()} />
      )} */}
      <div className={styles.content}>
        {renderMarkdown()}
      </div>
      {loading && (
        <div className={styles.loading}>
          <LoadingOutlined spin />
        </div>
      )}
      {error && (
        <div className={styles.errorMessage}>
          {error.message}
        </div>
      )}
    </div>
  )
})

export default Output
