import type { RootState } from '@/store'
import { useMobileScreen } from '@/utils'
import { CloseOutlined, PauseCircleOutlined, RedoOutlined, RobotOutlined, SendOutlined } from '@ant-design/icons'
import { EventStreamContentType, fetchEventSource } from '@fortaine/fetch-event-source'
import { getFilterData } from '@ly/utils'
import { Button, Dropdown, message, Popover, Space } from 'antd'
import classNames from 'classnames'
import { useEffect, useRef, useState } from 'react'
import { useSelector } from 'react-redux'
import Loader from '../Loader'
import FlashButton from './flashButton'
import styles from './index.module.scss'
import Output from './output'
import Textarea from './textarea'

interface AIAnalysisProps {
  loading?: boolean
  placeholder?: string
  text?: string
  type: 'output' | 'input'
  url: string // /h5/v1/ai/analysis/productAnalysisData
  outputParams?: Record<string, any>
  modelValue?: string
  onModelValueChange?: (value: string) => void
  fixed?: boolean // 是否开启fixed
}

export default function AIAnalysis({
  loading = false,
  placeholder = '输入或黏贴下单内容',
  text = 'AI识别下单',
  type = 'input',
  outputParams = {},
  url,
  modelValue,
  onModelValueChange,
  fixed = true, // 设置默认值
}: AIAnalysisProps) {
  const [messageApi, contextHolder] = message.useMessage()
  const [isSSELoading, setIsSSELoading] = useState(false)
  const [showPopover, setShowPopover] = useState(false)
  const outputRef = useRef<any>(null)
  const controller = useRef(new AbortController())
  const REQUEST_TIMEOUT_MS = 60000
  const token = useSelector((state: RootState) => state.auth.token)

  const prettyObject = (msg: any) => {
    const obj = msg
    if (typeof msg !== 'string')
      msg = JSON.stringify(msg, null, '  ')

    if (msg === '{}')
      return obj.toString()

    if (msg.startsWith('```json'))
      return msg

    return ['```json', msg, '```'].join('\n')
  }

  const connectSSE = (url: string) => {
    const requestTimeoutId = setTimeout(
      () => controller.current.abort(),
      REQUEST_TIMEOUT_MS,
    )
    const chatPayload = {
      method: 'GET',
      signal: controller.current.signal,
      headers: {
        'Content-Type': 'application/json',
        'Platform': '6',
        'Authorization': `${token}`,
      },
    }

    let responseText = ''
    let remainText = ''
    let finished = false
    let responseRes: Response

    const animateResponseText = () => {
      console.log('aborted', controller.current.signal.aborted)
      if (finished || controller.current.signal.aborted) {
        responseText += remainText

        if (responseText?.length === 0)
          outputRef.current?.onError?.(new Error('empty response from server'))

        return
      }

      if (remainText.length > 0) {
        const fetchCount = Math.max(1, Math.round(remainText.length / 60))
        const fetchText = remainText.slice(0, fetchCount)
        responseText += fetchText
        remainText = remainText.slice(fetchCount)
        outputRef.current?.onUpdate?.(responseText, fetchText)
      }

      requestAnimationFrame(animateResponseText)
    }

    const finish = () => {
      if (!finished) {
        finished = true
        outputRef.current?.onFinish(responseText + remainText, responseRes)
      }
    }

    controller.current.signal.onabort = finish
    animateResponseText()

    fetchEventSource(url, {
      ...chatPayload,
      async onopen(res) {
        setIsSSELoading(false)
        setShowPopover(true)
        clearTimeout(requestTimeoutId)
        const contentType = res.headers.get('content-type')

        responseRes = res
        if (contentType?.startsWith('text/plain')) {
          responseText = await res.clone().text()
          return finish()
        }
        if (
          !res.ok
          || !res.headers
            .get('content-type')
            ?.startsWith(EventStreamContentType)
            || res.status !== 200
        ) {
          const responseTexts = [responseText]
          let extraInfo = await res.clone().text()
          try {
            const resJson = await res.clone().json()
            extraInfo = prettyObject(resJson)
          }
          catch { }

          if (res.status === 401)
            responseTexts.push('请先登录')

          if (extraInfo)
            responseTexts.push(extraInfo)

          responseText = responseTexts.join('\n\n')
          return finish()
        }
      },
      onmessage(msg) {
        if (msg.data === '[DONE]' || finished)
          return finish()

        const text = msg.data
        try {
          if (msg.event === 'flowNodeStatus') {
            const statusData = JSON.parse(text)
            outputRef.current?.updateFlowStatus({
              status: statusData.status,
              name: statusData.name,
            })
            return
          }
          const json = JSON.parse(text)
          const choices = json.choices as Array<{
            delta: { content: string }
          }>
          const delta = choices[0]?.delta?.content
          if (delta)
            remainText += delta
        }
        catch (e) {
          console.error('[Request] parse error', text, msg)
        }
      },
      onclose() {
        finish()
      },
      onerror(e) {
        setIsSSELoading(false)
        messageApi.error('请求失败')
        console.log('onerror e', e)
        outputRef.current?.onError?.(e)
        throw e
      },
      openWhenHidden: true,
    })
  }
  const isMobile = useMobileScreen()

  const handleSubmit = (value: string) => {
    if (!value)
      return
    onModelValueChange?.(value)
  }

  const handleClickButton = () => {
    handleStopRequest()
    controller.current = new AbortController()
    setIsSSELoading(true)
    const params = new URLSearchParams(getFilterData(outputParams))
    connectSSE(`${import.meta.env.VITE_API_BASE_URL}${url}?${params.toString()}`)
  }
  const renderInputMode = () => (
    <Dropdown
      placement="topLeft"
      trigger={['click']}
      dropdownRender={() => (
        <div className={classNames(styles.AITextareaCard, isMobile ? 'w-screen' : null)}>
          <Textarea
            value={modelValue}
            onChange={onModelValueChange}
            placeholder={placeholder}
            onSubmit={handleSubmit}
          />
        </div>
      )}
    >
      <FlashButton
        className={styles.button}
        disabled={loading}
      >
        {!loading
          ? (
              <>
                <RobotOutlined style={{ fontSize: '12px' }} className="mr-2" />
                {isMobile ? '' : text}
              </>
            )
          : (
              <>
                <Loader size={16} color="#fff" className="mr-2" />
                {isMobile ? '' : '思考中...'}
              </>
            )}
      </FlashButton>
    </Dropdown>
  )

  const [position, setPosition] = useState({ x: 120, y: 120 })
  const dragNodeRef = useRef<HTMLDivElement>(null)
  const [isDragging, setIsDragging] = useState(false)
  const dragStartPos = useRef({ x: 0, y: 0 })
  const elementStartPos = useRef({ x: 0, y: 0 })
  // 添加移动阈值常量
  const MOVE_THRESHOLD = 5
  // 添加状态来追踪是否发生了实际移动
  const hasMoved = useRef(false)
  const handleMouseDown = (e: React.MouseEvent<HTMLButtonElement> | React.TouchEvent<HTMLButtonElement>) => {
    setIsDragging(true)
    hasMoved.current = false
    const pos = 'touches' in e ? e.touches[0] : e
    dragStartPos.current = { x: pos.clientX, y: pos.clientY }
    elementStartPos.current = { x: position.x, y: position.y }
  }

  const handleMouseMove = (e: MouseEvent | TouchEvent) => {
    if (!isDragging)
      return

    const pos = 'touches' in e ? e.touches[0] : e
    const dx = pos.clientX - dragStartPos.current.x
    const dy = pos.clientY - dragStartPos.current.y
    // 判断移动距离是否超过阈值
    if (Math.abs(dx) > MOVE_THRESHOLD || Math.abs(dy) > MOVE_THRESHOLD) {
      hasMoved.current = true
    }
    const newX = elementStartPos.current.x + dx
    const newY = elementStartPos.current.y + dy

    // 添加边界检查
    const container = dragNodeRef.current?.parentElement
    if (container) {
      const containerRect = container.getBoundingClientRect()
      const elementRect = dragNodeRef.current?.getBoundingClientRect()

      if (elementRect) {
        const maxX = containerRect.width - elementRect.width
        const maxY = containerRect.height - elementRect.height

        setPosition({
          x: Math.max(0, Math.min(newX, maxX)),
          y: Math.max(0, Math.min(newY, maxY)),
        })
      }
    }
  }

  const handleMouseUp = () => {
    setIsDragging(false)
    // 如果没有发生实际移动，则触发点击事件
    if (!hasMoved.current) {
      handleClickButton()
    }
  }
  useEffect(() => {
  // 初始化位置到右下角
    const rightPosition = Math.max(120, 0)
    const bottomPosition = Math.max(120, 0)
    setPosition({
      x: rightPosition,
      y: bottomPosition,
    })
  }, [])
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('touchmove', handleMouseMove)
      window.addEventListener('mouseup', handleMouseUp)
      window.addEventListener('touchend', handleMouseUp)
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('touchmove', handleMouseMove)
      window.removeEventListener('mouseup', handleMouseUp)
      window.removeEventListener('touchend', handleMouseUp)
    }
  }, [isDragging])
  function handleStopRequest() {
    controller.current.abort()
    setIsSSELoading(false)
  }
  const renderOutputMode = () => (
    <Popover
      open={showPopover}
      classNames={{
        root: isMobile ? 'w-screen' : '',
      }}
      content={(
        <div className={styles.popoverContent}>
          <Output ref={outputRef} />
          <div className={styles.popoverHeader}>
            <div className={styles.headerActions}>
              <Space>
                <Button
                  type="link"
                  icon={<RedoOutlined />}
                  className={styles.actionBtn}
                  onClick={handleClickButton}
                >
                  重新分析
                </Button>
                <Button
                  type="link"
                  icon={<PauseCircleOutlined />}
                  className={styles.actionBtn}
                  onClick={handleStopRequest}
                >
                  终止
                </Button>
              </Space>
              <Button
                type="link"
                icon={<CloseOutlined />}
                className={styles.actionBtn}
                onClick={() => {
                  handleStopRequest()
                  setShowPopover(false)
                }}
              >
                关闭
              </Button>
            </div>
          </div>
        </div>
      )}
      trigger="click"
      placement={isMobile ? undefined : 'bottomLeft'}
    >
      <FlashButton
        className={styles.button}
        disabled={isSSELoading}
        onMouseDown={handleMouseDown}
        onTouchStart={handleMouseDown}
        // onClick={handleClickButton}
      >
        {!isSSELoading
          ? (
              <>
                <RobotOutlined style={{ fontSize: '12px' }} className={isMobile ? '' : 'mr-2'} />
                {isMobile ? '' : text}
              </>
            )
          : (
              <>
                <Loader size={16} color="#fff" className={isMobile ? '' : 'mr-2'} />
                {isMobile ? '' : '思考中...'}
              </>
            )}
      </FlashButton>
    </Popover>
  )
  return (
    <>
      {contextHolder}
      {
        type === 'input'
          ? (
              renderInputMode()
            )
          : fixed
            ? (

                <div style={{ position: 'fixed', background: 'transparent', width: '100%', height: '100%', inset: 0, pointerEvents: 'none', zIndex: 1000 }}>
                  <div
                    ref={dragNodeRef}
                    style={{
                      position: 'absolute',
                      left: position.x,
                      top: position.y,
                      pointerEvents: 'auto',
                      cursor: isDragging ? 'grabbing' : 'grab',
                      userSelect: 'none',
                      touchAction: 'none',
                    }}
                  >
                    {renderOutputMode()}
                  </div>
                </div>

              )
            : (
                <div
                  ref={dragNodeRef}
                  style={{
                    pointerEvents: 'auto',
                    cursor: isDragging ? 'grabbing' : 'grab',
                    userSelect: 'none',
                    touchAction: 'none',
                  }}
                >
                  {renderOutputMode()}
                </div>
              )
      }
    </>
  )
}
