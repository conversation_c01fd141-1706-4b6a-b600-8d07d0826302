import React from 'react'

interface FlashButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children?: React.ReactNode
  onMouseDown?: (e: React.MouseEvent<HTMLButtonElement> | React.TouchEvent<HTMLButtonElement>) => void
  onTouchStart?: (e: React.TouchEvent<HTMLButtonElement>) => void
}

export default function FlashButton({ children, onMouseDown, onTouchStart, ...props }: FlashButtonProps) {
  const handleMouseDown = (e: React.MouseEvent<HTMLButtonElement> | React.TouchEvent<HTMLButtonElement>) => {
    onMouseDown?.(e)
  }

  const handleTouchStart = (e: React.TouchEvent<HTMLButtonElement>) => {
    onTouchStart?.(e)
  }
  return (
    <button
      {...props}
      onMouseDown={handleMouseDown}
      onTouchStart={handleTouchStart}
      className={`h-12 text-white font-semibold bg-linear-to-r from-indigo-500 via-purple-500 to-pink-500 rounded-lg shadow-lg hover:scale-105 duration-200 hover:drop-shadow-2xl hover:shadow-[#7dd3fc] hover:cursor-pointer ${props.className || ''}`}
    >
      {children}
    </button>
  )
}
