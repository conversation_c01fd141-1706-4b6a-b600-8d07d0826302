import { Button, Flex, Input, Modal, Space } from 'antd'
import { useEffect, useState } from 'react'

export default function ApiPrefixEditor() {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [customUrl, setCustomUrl] = useState(localStorage.getItem('custom_api_url') || '')
  const defaultApiUrl = import.meta.env.VITE_API_BASE_URL
  const [visible, setVisible] = useState(false)
  // 添加位置状态
  const [position, setPosition] = useState(() => {
    const saved = localStorage.getItem('api_editor_position')
    return saved ? JSON.parse(saved) : { x: 0, y: 20 }
  })
  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })

  // 处理拖拽开始
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    })
  }

  // 处理拖拽过程
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      const newX = e.clientX - dragStart.x
      const newY = e.clientY - dragStart.y

      // 确保不会拖出屏幕
      const maxX = window.innerWidth - 150
      const maxY = window.innerHeight - 100
      const x = Math.min(Math.max(0, newX), maxX)
      const y = Math.min(Math.max(0, newY), maxY)

      setPosition({ x, y })
    }
  }

  // 处理拖拽结束
  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false)
      // 保存位置到 localStorage
      localStorage.setItem('api_editor_position', JSON.stringify(position))
    }
  }
  // 处理触摸开始
  const handleTouchStart = (e: React.TouchEvent) => {
    setIsDragging(true)
    setDragStart({
      x: e.touches[0].clientX - position.x,
      y: e.touches[0].clientY - position.y,
    })
  }

  // 处理触摸移动
  const handleTouchMove = (e: TouchEvent) => {
    if (isDragging) {
      const newX = e.touches[0].clientX - dragStart.x
      const newY = e.touches[0].clientY - dragStart.y

      // 确保不会拖出屏幕
      const maxX = window.innerWidth - 150
      const maxY = window.innerHeight - 100
      const x = Math.min(Math.max(0, newX), maxX)
      const y = Math.min(Math.max(0, newY), maxY)

      setPosition({ x, y })
      e.preventDefault() // 阻止页面滚动
    }
  }

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (isDragging) {
      setIsDragging(false)
      localStorage.setItem('api_editor_position', JSON.stringify(position))
    }
  }
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('touchmove', handleTouchMove, { passive: false })
      window.addEventListener('touchend', handleTouchEnd)
      window.addEventListener('mousemove', handleMouseMove)
      window.addEventListener('mouseup', handleMouseUp)
    }
    return () => {
      window.removeEventListener('touchmove', handleTouchMove)
      window.removeEventListener('touchend', handleTouchEnd)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('mouseup', handleMouseUp)
    }
  }, [isDragging, dragStart])
  const handleSave = () => {
    if (customUrl) {
      localStorage.setItem('custom_api_url', customUrl)
      window.location.reload()
    }
    else {
      localStorage.removeItem('custom_api_url')
      window.location.reload()
    }
    setIsModalVisible(false)
  }
  // 修改关闭按钮的处理函数
  const handleToggle = () => {
    setVisible(!visible)
    if (!visible) {
      // 展开时恢复之前的位置
      const saved = localStorage.getItem('api_editor_position')
      setPosition(saved ? JSON.parse(saved) : { x: 20, y: 20 })
    }
    else {
      // 收起时固定在左边缘
      setPosition({ x: 0, y: position.y })
    }
  }
  const handleReset = () => {
    localStorage.removeItem('custom_api_url')
    setCustomUrl('')
    window.location.reload()
  }

  return (
    <>
      <div
        style={{
          position: 'fixed',
          left: `${position.x}px`,
          top: `${position.y}px`,
          zIndex: 1000,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          padding: visible ? '10px' : '5px',
          borderRadius: visible ? '4px' : '0 50% 50% 0',
          color: 'white',
          cursor: isDragging ? 'grabbing' : 'grab',
          userSelect: 'none',
          // transition: visible ? 'all 0.3s ease' : 'unset',
          width: visible ? 'auto' : '30px',
          height: visible ? 'auto' : '30px',
        }}
        onMouseDown={handleMouseDown}
        onTouchStart={handleTouchStart}
        onClick={!visible ? handleToggle : undefined}
      >
        {
          visible
            ? (
                <>

                  <div style={{ marginBottom: '8px', fontSize: '12px' }}>
                    当前API:
                    {' '}
                    {customUrl || defaultApiUrl}
                  </div>
                  <Flex justify="space-between">
                    <Space>
                      <Button size="small" onClick={() => setIsModalVisible(true)}>
                        修改
                      </Button>
                      <Button size="small" danger onClick={handleReset}>
                        重置
                      </Button>
                    </Space>
                    <Button size="small" onClick={() => handleToggle()}>
                      收起
                    </Button>
                  </Flex>
                </>
              )
            : (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    fontSize: '12px',
                  }}
                >
                  API
                </div>
              )
        }
      </div>

      <Modal
        title="修改 API 前缀"
        open={isModalVisible}
        onOk={handleSave}
        onCancel={() => setIsModalVisible(false)}
      >
        <Input
          value={customUrl}
          onChange={e => setCustomUrl(e.target.value)}
          placeholder="请输入新的 API 前缀"
          style={{ marginBottom: '10px' }}
        />
        <div style={{ color: '#666', fontSize: '12px' }}>
          默认值:
          {' '}
          {defaultApiUrl}
        </div>
      </Modal>
    </>
  )
}
