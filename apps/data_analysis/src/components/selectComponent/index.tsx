import type { MutationOptions } from '@/service/request'
import type { PaginationResponse } from '@/service/request/type'
import type { UseMutationResult } from '@tanstack/react-query'
import type { SelectProps } from 'antd'
import { getFilterData } from '@ly/utils'
import { Select, Spin } from 'antd'
import { isEqual } from 'lodash-es'
import { useEffect, useRef, useState } from 'react'

// 定义选项类型
export interface SelectOption {
  label: string
  value: string | number
  disabled?: boolean
}

// 组件属性类型
export interface SelectComponentProps<TData, TParams> extends Omit<SelectProps, 'options'> {
  options?: SelectOption[]
  api?: (options?: MutationOptions<TData, TParams>) => UseMutationResult<TData, any, TParams, any>
  params?: TParams
  labelField?: string
  valueField?: string
  transformOptions?: (data: TData) => SelectOption[]
}

function SelectComponent<TData = any, TParams = any>({
  options: propOptions,
  api,
  params,
  labelField = 'name',
  valueField = 'id',
  transformOptions,
  ...selectProps
}: SelectComponentProps<TData, TParams>) {
  const [loading, setLoading] = useState(false)
  const [options, setOptions] = useState<SelectOption[]>([])
  const prevParamsRef = useRef<TParams>()

  const handleSuccess = (data: TData | PaginationResponse<any>) => {
    let newOptions: SelectOption[]
    if (transformOptions) {
      newOptions = transformOptions(data as TData)
    }
    else {
      const arrayData = Array.isArray(data)
        ? data
        : ((data as PaginationResponse<any>)?.list || [])
      newOptions = arrayData.map(item => ({
        label: item[labelField],
        value: item[valueField],
        disabled: item.disabled,
      }))
    }
    setOptions(newOptions)
    setLoading(false)
  }

  const handleError = (error: Error) => {
    console.error('Failed to fetch options:', error)
    setLoading(false)
  }

  // mutation 引用
  const mutationRef = useRef<UseMutationResult<TData, any, TParams, any> | null>(null)
  if (api) {
    mutationRef.current = api({
      onSuccess: handleSuccess,
      onError: handleError,
    })
  }

  useEffect(() => {
    if (propOptions) {
      setOptions(propOptions)
    }
    else if (api && mutationRef.current && params) {
      const filterParams = getFilterData(params)
      if (!isEqual(prevParamsRef.current, filterParams)) {
        setLoading(true)
        mutationRef.current.mutate(filterParams)
        prevParamsRef.current = filterParams
      }
    }
  }, [propOptions, params, api])

  return (
    <Select
      className="w-full"
      {...selectProps}
      allowClear
      loading={loading}
      options={options}
      notFoundContent={loading ? <Spin size="small" /> : null}
      filterOption={(input, option) =>
        (option?.label?.toString() ?? '').toLowerCase().includes(input.toLowerCase())}
    />
  )
}

export default SelectComponent
