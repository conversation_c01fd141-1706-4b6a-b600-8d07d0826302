import type { MutationOptions } from '@/service/request'
import type { PaginationResponse } from '@/service/request/type'
import type { UseMutationResult } from '@tanstack/react-query'
import type { TreeSelectProps } from 'antd'
import type { DataNode } from 'antd/es/tree'
import { TreeSelect } from 'antd'
import { isEqual } from 'lodash-es'
import { useEffect, useRef, useState } from 'react'

interface TreeSelectComponentProps<TData = any, TParams = any> extends Omit<TreeSelectProps, 'treeData'> {
  api?: (options?: MutationOptions<TData, TParams>) => UseMutationResult<TData, any, TParams, any>
  params?: TParams
  labelField?: string
  valueField?: string
  childrenField?: string
  transformData?: (data: TData) => DataNode[]
  initData?: DataNode[]
}

function TreeSelectComponent<TData = any, TParams = any>({
  api,
  params,
  labelField = 'name',
  valueField = 'id',
  childrenField = 'children',
  transformData,
  initData = [],
  ...props
}: TreeSelectComponentProps<TData, TParams>) {
  const [treeData, setTreeData] = useState<DataNode[]>(initData)
  const [loading, setLoading] = useState(false)
  const prevParamsRef = useRef<TParams>()
  const mutationRef = useRef<UseMutationResult<TData, any, TParams, any> | null>(null)

  const transformToTreeData = (data: any[]): DataNode[] => {
    return data.map(item => ({
      title: item[labelField],
      key: item[valueField],
      value: item[valueField],
      children: item[childrenField] ? transformToTreeData(item[childrenField]) : [],
      isLeaf: !item[childrenField] || item[childrenField].length === 0,
    }))
  }

  const handleSuccess = (data: TData | PaginationResponse<any>) => {
    const arrayData = Array.isArray(data)
      ? data
      : ((data as PaginationResponse<any>)?.list || [])

    const newTreeData = transformData
      ? transformData(data as TData)
      : transformToTreeData(arrayData)

    setTreeData(newTreeData)
    setLoading(false)
  }

  const handleError = (error: Error) => {
    console.error('Failed to fetch tree data:', error)
    setLoading(false)
  }
  if (api) {
    mutationRef.current = api({
      onSuccess: handleSuccess,
      onError: handleError,
    })
  }

  useEffect(() => {
    if (initData.length > 0) {
      setTreeData(initData)
      return
    }

    if (!api || !mutationRef.current || !params) {
      return
    }

    if (!isEqual(prevParamsRef.current, params)) {
      setLoading(true)
      mutationRef.current.mutate(params)
      prevParamsRef.current = params
    }
  }, [initData, params, api])

  const onLoadData = async (node: DataNode) => {
    if (!api || !mutationRef.current || !node.children?.length === undefined) {
      return
    }

    try {
      setLoading(true)
      const newParams = {
        ...params,
        parentId: node.key,
      } as TParams
      await mutationRef.current.mutateAsync(newParams)
    }
    catch (error) {
      console.error('Failed to load children:', error)
    }
    finally {
      setLoading(false)
    }
  }

  return (
    <TreeSelect<any, DataNode>
      className="w-full"
      allowClear
      treeData={treeData}
      loadData={api ? node => onLoadData(node as DataNode) : undefined}
      loading={loading}
      {...props}
      dropdownStyle={{ maxHeight: 400, overflow: 'auto', ...props.dropdownStyle }}
      filterTreeNode={props.filterTreeNode || ((input, node) => {
        return (node?.title as string)?.toLowerCase().indexOf(input.toLowerCase()) >= 0
      })}
    />
  )
}

export default TreeSelectComponent
