import type { TimeRangePickerProps } from 'antd'
import type { Dayjs } from 'dayjs'
import useWindowSize from '@/hooks/useWindowSize'
import { CalendarOutlined, CloseCircleFilled } from '@ant-design/icons'
import { formatDate, formatDateTime } from '@ly/utils'
import { Button, DatePicker, Flex, Typography } from 'antd'
import { DatePickerView, Popup } from 'antd-mobile'
import dayjs from 'dayjs'
import { useCallback, useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

const { RangePicker } = DatePicker
const { Text } = Typography

export type DateRange = [Date, Date] | undefined
interface RangeDateProps {
  minDate?: Date // 最小可选日期
  maxDate?: Date // 最大可选日期
  loading?: boolean // 加载状态
  format?: string // 自定义日期格式
  onError?: (error: string) => void // 错误回调
  value?: DateRange // 受控值
  defaultValue?: DateRange // 默认值（非受控）
  onChange?: (dates: DateRange) => void // 值变化回调
  allowClear?: boolean // 添加清除功能开关
  onConfirm?: (dates: DateRange) => void // 确认回调
  onCancel?: () => void // 取消回调
  onClear?: () => void // 清除回调
  onPopupClose?: () => void // 弹窗关闭回调
  picker?: 'date' | 'month' // 新增选择器类型
}

function RangeDate({
  minDate,
  maxDate,
  loading = false,
  picker = 'date', // 默认为日期选择
  format, // 根据 picker 类型设置默认格式
  onError,
  value,
  defaultValue,
  onChange,
  allowClear = false,
  onConfirm,
  onCancel,
  onClear,
  onPopupClose,
}: RangeDateProps) {
  const { isMobile } = useWindowSize()
  const [activeTab, setActiveTab] = useState<'start' | 'end'>('start')
  const [tempDates, setTempDates] = useState<DateRange | undefined>(value || defaultValue || undefined)
  const [visible, setVisible] = useState(false)
  const [activePreset, setActivePreset] = useState<string | null>(null)
  const isPresetClick = useRef(false)
  // 获取默认的最小和最大日期
  const currentYear = dayjs().year()
  const defaultMinDate = dayjs().year(currentYear - 15).startOf('year').toDate()
  const defaultMaxDate = dayjs().year(currentYear + 10).endOf('year').toDate()

  const finalMinDate = minDate || defaultMinDate
  const finalMaxDate = maxDate || defaultMaxDate

  // 根据 picker 类型设置默认格式
  const defaultFormat = picker === 'date' ? 'YYYY-MM-DD' : 'YYYY-MM'
  const finalFormat = format || defaultFormat
  const selectStartDate = tempDates ? new Date(tempDates[0]) : new Date()
  const selectEndDate = tempDates ? new Date(tempDates[1]) : new Date()
  // 日期验证函数
  const validateDateRange = useCallback((start: Date, end: Date): boolean => {
    try {
    // 检查日期是否有效
      if (!start || !end || isNaN(start.getTime()) || isNaN(end.getTime())) {
        onError?.('请选择有效的日期')
        return false
      }

      // 统一使用日期部分进行比较，忽略时间部分
      const startDate = dayjs(start).startOf('day')
      const endDate = dayjs(end).startOf('day')
      const minDate = dayjs(finalMinDate).startOf('day')
      const maxDate = dayjs(finalMaxDate).startOf('day')

      // 开始日期不能大于结束日期
      if (startDate.isAfter(endDate)) {
        const errorMsg = `开始日期(${formatDate(start)})不能大于结束日期(${formatDate(end)})`
        console.warn(errorMsg)
        onError?.(errorMsg)
        return false
      }

      // 开始日期不能小于最小日期
      if (startDate.isBefore(minDate)) {
        const errorMsg = `开始日期(${formatDate(start)})不能早于${formatDate(finalMinDate)}`
        console.warn(errorMsg)
        onError?.(errorMsg)
        return false
      }

      // 结束日期不能大于最大日期（只比较日期部分）
      if (endDate.isAfter(maxDate)) {
        const errorMsg = `结束日期(${formatDate(end)})不能晚于${formatDate(finalMaxDate)}`
        console.warn(errorMsg)
        onError?.(errorMsg)
        return false
      }

      return true
    }
    catch (error: any) {
      const errorMsg = `日期验证失败: ${error.message}`
      console.error(errorMsg)
      onError?.(errorMsg)
      return false
    }
  }, [finalMinDate, finalMaxDate, onError])

  // 监听外部 value 变化
  useEffect(() => {
    if (value) {
      setTempDates(value)
    }
  }, [value])

  const handleShowDatePicker = () => {
    setVisible(true)
  }

  const handleCancel = () => {
    onCancel?.()
    setVisible(false)
  }

  const handleMobileConfirm = () => {
    // 如果没有选择日期，使用当天日期
    const finalDates = tempDates || [new Date(), new Date()]
    console.log('validateDateRange', validateDateRange(finalDates[0], finalDates[1]))
    if (validateDateRange(finalDates[0], finalDates[1])) {
      onChange?.(finalDates)
      onConfirm?.(finalDates)
      setVisible(false)
    }
  }

  const handleMobileDateChange = (date: Date, type: 'start' | 'end') => {
    const newDates = [...(tempDates || [new Date(), new Date()])]
    const index = type === 'start' ? 0 : 1
    if (picker === 'month') {
      newDates[index] = type === 'start'
        ? dayjs(date).startOf('month').toDate()
        : dayjs(date).endOf('month').toDate()
    }
    else {
      newDates[index] = date
    }

    setTempDates(newDates as [Date, Date])
  }

  const handlePCDateChange = (
    dates: [Dayjs, Dayjs] | undefined | null | any,
    _dateStrings: [string, string],
  ) => {
    console.log('dates', dates, _dateStrings)
    if (!dates) {
      onChange?.(undefined)
      return
    }

    if (picker === 'month') {
      const [start, end] = dates
      const startDate = start.startOf('month').toDate()
      const endDate = end.endOf('month').toDate()
      onChange?.([startDate, endDate])
    }
    else {
      onChange?.([dates[0].toDate(), dates[1].toDate()])
    }
  }

  // 添加清除处理函数
  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止冒泡，避免触发日期选择器
    onChange?.(undefined as any)
    onClear?.()
  }

  // 修改弹窗关闭处理函数
  const handlePopupClose = () => {
    onPopupClose?.()
    setVisible(false)
  }
  const rangePresets: TimeRangePickerProps['presets'] = [
    {
      label: '今天',
      value: [dayjs().startOf('day'), dayjs().endOf('day')],
    },
    {
      label: '昨天',
      value: [dayjs().subtract(1, 'day').startOf('day'), dayjs().subtract(1, 'day').endOf('day')],
    },
    {
      label: '本周',
      value: [dayjs().startOf('week'), dayjs().endOf('week')],
    },
    {
      label: '本月',
      value: [dayjs().startOf('month'), dayjs().endOf('month')],
    },
    {
      label: '近7天',
      value: [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')],
    },
    {
      label: '近30天',
      value: [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')],
    },
  ]

  useEffect(() => {
    function handleWheel(e: Event) {
      if (e instanceof WheelEvent) {
        e.preventDefault()
        setActivePreset(null)
      }
    }
    // 处理触摸事件
    function handleTouch() {
      setActivePreset(null)
    }
    const wheels = document.querySelectorAll('.adm-picker-view-column-wheel')
    // 添加事件监听
    wheels.forEach((wheel) => {
      wheel.addEventListener('wheel', handleWheel, { passive: false })
      wheel.addEventListener('touchstart', handleTouch)
      wheel.addEventListener('touchmove', handleTouch)
    })
    return () => {
      wheels.forEach((wheel) => {
        wheel.removeEventListener('wheel', handleWheel)
        wheel.removeEventListener('touchstart', handleTouch)
        wheel.removeEventListener('touchmove', handleTouch)
      })
    }
  }, [visible])
  // PC端渲染
  if (!isMobile) {
    return (
      <RangePicker
        className={`${styles.rangeDate} ${loading ? styles.disabled : ''}`}
        picker={picker}
        presets={rangePresets}
        variant="borderless"
        format={finalFormat}
        value={value ? [dayjs(value[0]), dayjs(value[1])] : null}
        onChange={handlePCDateChange}
        onOpenChange={(open) => {
          // 当日期选择器关闭时触发 popupClose 回调
          if (!open) {
            onPopupClose?.()
          }
        }}
        disabled={loading}
        // disabledDate={(current) => {
        //   if (!current)
        //     return false
        //   return current.valueOf() < dayjs(finalMinDate).valueOf()
        //     || current.valueOf() > dayjs(finalMaxDate).valueOf()
        // }}
        allowClear={allowClear}
        allowEmpty={[true, true]}
      />
    )
  }
  const handlePresetClick = (preset: TimeRangePickerProps['presets'][0]) => {
    const [start, end] = preset.value as [Dayjs, Dayjs]
    setTempDates([start.toDate(), end.toDate()])
    isPresetClick.current = true
    setActivePreset(preset.label)
  }
  // 移动端渲染
  return (
    <>
      <div
        className={`${styles.mobileDateBox} ${loading ? styles.disabled : ''}`}
        onClick={loading ? undefined : handleShowDatePicker}
      >
        <div className={styles.dateBoxLeft}>
          <>
            <Text className={`${styles.placeholdertime} ${value ? styles.selected : ''}`}>
              {value?.[0] ? dayjs(value[0]).format(finalFormat) : '开始日期'}
            </Text>
            <Text className={styles.placeholdertime}>-</Text>
            <Text className={`${styles.placeholdertime} ${value ? styles.selected : ''}`}>
              {value?.[1] ? dayjs(value[1]).format(finalFormat) : '结束日期'}
            </Text>
          </>
        </div>
        <div className={styles.dateBoxRight}>
          {allowClear && value
            ? (
                <CloseCircleFilled className={styles.clearIcon} onClick={handleClear} />
              )
            : (
                <CalendarOutlined />
              )}
        </div>
      </div>

      <Popup
        visible={visible}
        onMaskClick={handlePopupClose}
        position="bottom"
        bodyStyle={{
          height: '60vh',
          touchAction: 'none',
        }}
      >
        <div className={styles.popupHeader}>
          <div className={styles.cancel} onClick={handleCancel}>取消</div>
          <div className={styles.title}>选择日期范围</div>
          <div className={styles.confirm} onClick={handleMobileConfirm}>确定</div>
        </div>

        <div className={styles.tabContainer}>
          <Flex wrap="nowrap" justify="space-between" className={styles.presetsContainer}>
            {rangePresets.map(preset => (
              <Button
                key={preset.label}
                size="small"
                type={activePreset === preset.label ? 'primary' : 'default'}
                onClick={() => handlePresetClick(preset)}
                className={styles.presetButton}
              >
                {preset.label}
              </Button>
            ))}
          </Flex>
          <Flex gap="small">
            <div
              className={`${styles.tab} ${activeTab === 'start' ? styles.active : ''}`}
              onClick={() => setActiveTab('start')}
            >
              <div className={styles.label}>开始时间</div>
              <div className={styles.date}>
                {tempDates ? dayjs(tempDates[0]).format(finalFormat) : dayjs().format(finalFormat)}
              </div>
            </div>
            <div
              className={`${styles.tab} ${activeTab === 'end' ? styles.active : ''}`}
              onClick={() => setActiveTab('end')}
            >
              <div className={styles.label}>结束时间</div>
              <div className={styles.date}>
                {tempDates ? dayjs(tempDates[1]).format(finalFormat) : dayjs().format(finalFormat)}
              </div>
            </div>
          </Flex>
        </div>

        <div className={styles.pickerContainer}>
          <div className={`${styles.pickerWrapper} ${activeTab === 'start' ? styles.show : ''}`}>
            <DatePickerView
              className={styles.datePickerView}
              value={selectStartDate}
              min={finalMinDate}
              max={finalMaxDate}
              precision={picker === 'date' ? 'day' : 'month'} // 根据 picker 类型设置精度
              onChange={val => handleMobileDateChange(val, 'start')}
            />
          </div>
          <div className={`${styles.pickerWrapper} ${activeTab === 'end' ? styles.show : ''}`}>
            <DatePickerView
              className={styles.datePickerView}
              value={selectEndDate}
              min={selectStartDate}
              max={finalMaxDate}
              precision={picker === 'date' ? 'day' : 'month'} // 根据 picker 类型设置精度
              onChange={val => handleMobileDateChange(val, 'end')}
            />
          </div>
        </div>
      </Popup>
    </>
  )
}

export default RangeDate
