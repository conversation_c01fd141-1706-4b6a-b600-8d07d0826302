@reference 'tailwindcss';
.rangeDate {
  @apply w-full border-none bg-[#f5f5f5] h-8;

  :global {
    .ant-picker-input {
      @apply w-full;
    }
  }
}

.mobileDateBox {
  @apply flex justify-between items-center w-full h-8 px-2 bg-[#f5f5f5] rounded gap-5;

  .dateBoxLeft {
    @apply flex justify-between items-center w-full h-8;

    .placeholder {
      @apply text-[rgba(0,0,0,0.2)];
    }
  }

  .dateBoxRight {
    @apply flex justify-center items-center w-8 h-[30px] text-[rgba(0,0,0,0.4)];

    .clearIcon {
      @apply text-[rgba(0,0,0,0.25)] hover:text-[rgba(0,0,0,0.45)];
    }
  }
}

.popupHeader {
  @apply flex items-center justify-between px-4 py-3 border-b border-gray-100;
  
  .cancel {
    @apply text-gray-500 text-sm;
  }
  
  .title {
    @apply text-base font-medium;
  }
  
  .confirm {
    @apply text-blue-500 text-sm;
  }
}

.tabContainer {
  @apply  px-4 py-3 gap-4 border-b border-gray-100;

  .tab {
    @apply flex-1 p-3 rounded-lg cursor-pointer transition-all;
    background: #f5f5f5;

    &.active {
      @apply bg-blue-50;

      .label {
        @apply text-blue-600;
      }

      .date {
        @apply text-blue-600;
      }
    }

    .label {
      @apply text-xs text-gray-500 mb-1;
    }

    .date {
      @apply text-sm font-medium;
    }
  }
}

.pickerContainer {
  @apply relative h-[calc(60vh-120px)];

  .pickerWrapper {
    @apply absolute inset-0 opacity-0 pointer-events-none transition-all duration-300;
    transform: translateX(100px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &.show {
      @apply opacity-100 pointer-events-auto;
      transform: translateX(0);
    }
  }
}


:global {
  .adm-picker-view {
    @apply px-4;
    touch-action: none;
    height: 100%;
  }

  .adm-picker-view-column {
    @apply text-base;
    touch-action: none;
  }

  .adm-picker,
  .adm-picker-view,
  .adm-picker-view-column,
  .adm-picker-view-column-wheel,
  .adm-popup-body {
    touch-action: none !important;
  }

  .adm-picker-view-column-wheel {
    & > * {
      touch-action: none !important;
    }
  }

  .adm-popup {
    .adm-popup-body {
      overscroll-behavior: none;
    }
  }
}

.placeholdertime {
  color: rgba(0, 0, 0, 0.25);
  
  &.selected {
    color: #000;
  }
}

.disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}
.presetsContainer {
  // padding: 12px;
  margin-bottom: 10px;
  // border-bottom: 1px solid #f0f0f0;
}

.presetButton {
  min-width: 50px;
  margin: 4px;
}
