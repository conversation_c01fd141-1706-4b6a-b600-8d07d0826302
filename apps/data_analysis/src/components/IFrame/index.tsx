import type { Ref, RefObject } from 'react'
import { useBoolean } from 'ahooks'
import { forwardRef, useEffect, useState } from 'react'
import Skeleton from '../skeleton'

interface IFrameProps {
  src: string
  style?: React.CSSProperties
  innerStringStyle?: string
  onLoad?: () => void
}

export const IFrame = forwardRef<HTMLIFrameElement, IFrameProps>((props, ref: Ref<HTMLIFrameElement>) => {
  const { src, style, innerStringStyle, onLoad } = props
  const [height, setHeight] = useState(0)
  const [loading, { setFalse: endLoading }] = useBoolean(true)

  useEffect(() => {
    setHeight(document.body.clientHeight)
  }, [])

  function insetInnerStyle(style: string) {
    if (!(ref as RefObject<HTMLIFrameElement>).current)
      return
    console.log('load', (ref as RefObject<HTMLIFrameElement>).current)
    try {
      const iframeDoc = (ref as RefObject<HTMLIFrameElement>).current?.contentDocument || (ref as RefObject<HTMLIFrameElement>).current?.contentWindow?.document
      console.log('iframeDoc', iframeDoc)
      if (iframeDoc) {
        const styleElement = iframeDoc.createElement('style')
        styleElement.textContent = `${style}`
        console.log('styleElement', styleElement.textContent)
        iframeDoc.head.appendChild(styleElement)
        console.log('head', iframeDoc.head)
      }
    }
    catch (e) {
      console.warn('无法访问 iframe 内容，可能是跨域限制:', e)
    }
  }

  function onLoadIFrame() {
    endLoading()
    if (innerStringStyle) {
      insetInnerStyle(innerStringStyle)
    }
    onLoad?.()
  }
  return (
    <>
      {loading && <Skeleton />}
      <div className={loading ? 'h-0' : 'flex justify-center items-center'}>
        <iframe
          ref={ref}
          src={src}
          scrolling="no"
          onLoad={onLoadIFrame}
          style={{
            width: '100%',
            height: `${height}px`,
            overflow: 'hidden',
            border: 'none',
            ...style,
          }}
        />
      </div>
    </>
  )
})
