.containers {
  width: 100%;
  background-color: #fff;
  border-radius: 8px;
}

.gridContainer {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 5px;
}
.gridContainer.threeColumns {
  grid-template-columns: repeat(3, 1fr);
}
@media (min-width: 768px) {
  .gridContainer {
    grid-template-columns: repeat(2, 1fr);
  }
}

.gridItem {
  min-width: 0;
}

.card {
  padding: 10px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}
.card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardTitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.cardValue {
  font-size: 20px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 0 !important;
}