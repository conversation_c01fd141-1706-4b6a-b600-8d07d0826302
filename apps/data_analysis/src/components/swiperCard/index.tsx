import Skeleton from '@/components/skeleton'
import { formatValue } from '@/utils/math'
import { Col, Row, Typography } from 'antd'
import { Swiper, SwiperSlide } from 'swiper/react'
import styles from './index.module.scss'
import 'swiper/css'

const { Title, Paragraph } = Typography

export interface CardData {
  readonly id: number
  readonly title: string
  readonly key: keyof Api.ProductAnalysis.ProductAnalysisDataResponse
  readonly icon: React.ReactNode
}

function MetricCard({ constKey, data, loading }: {
  constKey: CardData // 指标key
  data?: Api.ProductAnalysis.ProductAnalysisDataResponse // 数据
  loading: boolean // 是否加载中
}) {
  if (loading) {
    return (
      <div className={styles.card}>
        <div className={styles.cardHeader}>
          <span className={styles.cardTitle}>{constKey.title}</span>
          {constKey.icon}
        </div>
        <div
          className={styles.cardValue}
        >
          <Skeleton width={140} height={20} />
        </div>
      </div>
    )
  }

  if (!data) {
    return (
      <div className={styles.card}>
        <div className={styles.cardHeader}>
          <span className={styles.cardTitle}>{constKey.title}</span>
          {constKey.icon}
        </div>
        <div
          className={styles.cardValue}
        >
          --
        </div>
      </div>
    )
  }
  // console.log(data)
  return (
    <div className={styles.card}>
      <div className={styles.cardHeader}>
        <Title level={5}>{constKey.title}</Title>
        {constKey.icon}
      </div>
      <Paragraph className={styles.cardValue} ellipsis={{ rows: 1, tooltip: true }}>{formatValue(data[constKey.key], '--')}</Paragraph>
    </div>
  )
}

function SwiperCard({ cards, data, loading }: { cards: any, data: any, loading: boolean }) {
  return (
    <div className={styles.containers}>
      <Row gutter={[10, 10]} className={`${styles.gridContainer} ${cards.length === 3 ? styles.threeColumns : ''}`}>
        {cards.map(card => (
          <Col xs={24} sm={24} md={12} lg={12} key={card.id} className={styles.gridItem}>
            <MetricCard constKey={card} data={data} loading={loading} />
          </Col>
        ))}
      </Row>
    </div>
  )
}

export default SwiperCard
