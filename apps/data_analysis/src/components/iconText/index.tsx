import type Icon from 'antd/es/icon'
import classnames from 'classnames'
import React from 'react'

interface IconTextProps {
  icon: typeof Icon
  className?: string
  type?: 'vertical' | 'horizontal'
  text: React.ReactNode
}

const IconText: React.FC<IconTextProps> = ({ icon, className, text, type = 'horizontal' }) => (

  <div className={classnames(className, 'flex justify-center items-center', type === 'vertical' ? 'flex-col' : 'flex-row')}>
    {React.createElement(icon as React.ComponentType<any>, { style: { marginInlineEnd: 8 } })}
    {text}
  </div>
)
export default IconText
