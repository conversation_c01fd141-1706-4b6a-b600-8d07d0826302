@reference 'tailwindcss';

.headerBox {
  @apply w-full bg-white shrink-0;
  height: 56px;
  box-shadow: 2px 2px 5px 0px rgba(215,215,215,0.35);
  position: sticky;
  top: 0;
  z-index: 100;
  animation: fadeInUp 0.25s ease-out;
  .content {
    @apply w-full h-full flex items-center justify-between px-[25px];
    
    // 如果页面大于1200px
    @screen xl {
      @apply min-w-[1250px] max-w-[1920px] w-[96vw] mx-auto;
    }
  }

  // 自定义面包屑样式
  .breadcrumb {
    :global {
      .ant-breadcrumb-link {
        @apply text-base font-medium cursor-pointer;
      }
      
      // 面包屑分隔符样式
      .ant-breadcrumb-separator {
        @apply text-base mx-2;  // 同样调整分隔符大小并增加间距
      }
    }
  }
}

// 移动端样式
.mobileHeader {
  @apply w-full bg-white flex items-center px-4 shrink-0;
  height: 44px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  animation: fadeInUp 0.25s ease-out;
  .back {
    @apply flex items-center justify-center;
    height: 44px;
  }

  .title {
    @apply flex-1 text-center text-base font-medium truncate;
  }

  .userInfo {
  @apply flex items-center;
    min-width: 24px;
  }
}

// 共用样式
.avatar {
@apply w-8 h-8 rounded-full;
}
