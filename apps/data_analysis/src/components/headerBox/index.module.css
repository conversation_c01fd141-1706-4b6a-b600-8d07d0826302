@reference "tailwindcss";
.headerBox {
  @apply w-full bg-white shrink-0;
  height: 56px;
  box-shadow: 2px 2px 5px 0px rgba(215, 215, 215, 0.35);
  position: sticky;
  top: 0;
  z-index: 100;
  animation: fadeInUp 0.25s ease-out;
}
.headerBox .content {
  @apply w-full h-full flex items-center justify-between px-[25px];
}
@screen xl {
  .headerBox .content {
    @apply min-w-[1250px] max-w-[1920px] w-[96vw] mx-auto;
  }
}
.headerBox .breadcrumb :global .ant-breadcrumb-link {
  @apply text-base font-medium cursor-pointer;
}
.headerBox .breadcrumb :global .ant-breadcrumb-separator {
  @apply text-base mx-2;
}

.mobileHeader {
  @apply w-full bg-white flex items-center px-4 shrink-0;
  height: 44px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
  animation: fadeInUp 0.25s ease-out;
}
.mobileHeader .back {
  @apply flex items-center justify-center;
  height: 44px;
}
.mobileHeader .title {
  @apply flex-1 text-center text-base font-medium truncate;
}
.mobileHeader .userInfo {
  @apply flex items-center;
  min-width: 24px;
}

.avatar {
  @apply w-8 h-8 rounded-full;
}/*# sourceMappingURL=index.module.css.map */