import type { AppDispatch, RootState } from '@/store'
import type { MenuProps } from 'antd'
import type { FC, ReactNode } from 'react'
import { basePrefix } from '@/router'
import { logoutAsync } from '@/store/slice/auth'
import { isMobile } from '@/utils/device'
import { useMessage } from '@/utils/message'
import { LoadingOutlined } from '@ant-design/icons'
import { Avatar, Breadcrumb, Dropdown, Flex, Space, Typography } from 'antd'
import { useEffect, useState } from 'react'
import { BiChevronLeft } from 'react-icons/bi'
import { useDispatch, useSelector } from 'react-redux'
import { useLocation, useMatches, useNavigate } from 'react-router-dom'
import styles from './index.module.scss'

const { Text } = Typography

// 定义路由 meta 类型
export interface RouteMeta {
  title: string
  icon?: ReactNode
}

// 定义路由 handle 类型
export interface RouteHandle {
  meta: () => Promise<RouteMeta>
}

interface HeaderBoxProps {
  // 原有属性保持不变
  className?: string
  style?: React.CSSProperties
  // 新增可选配置
  showUserInfo?: boolean
  isMobile?: boolean
  // 新增面包屑配置
  breadcrumbs?: Array<{
    title: string
    href?: string
    onClick?: () => void
  }>
  rightSlot?: ReactNode
}

const HeaderBox: FC<HeaderBoxProps> = (props) => {
  const { className, showUserInfo = false, breadcrumbs } = props
  const location = useLocation() // 获取当前路由
  const matches = useMatches()
  const navigate = useNavigate()
  // eslint-disable-next-line react/prefer-destructuring-assignment
  const isMobileView = props.isMobile || isMobile() // 是否是移动端
  const { showMessage, contextHolder } = useMessage()
  const isPending = useSelector((state: RootState) => state.auth.isPending)
  const userInfo = useSelector((state: RootState) => state.auth.userInfo)
  const dispatch = useDispatch<AppDispatch>()

  // 获取当前路由的 meta 信息
  const meta = matches[matches.length - 1].handle as RouteMeta
  const [pageTitle, setPageTitle] = useState('工作台')
  useEffect(() => {
    const getMeta = async () => {
      if (meta) {
        try {
          setPageTitle(meta?.title || '工作台')
        }
        catch (error) {
          console.error('Failed to load meta:', error)
          setPageTitle('工作台')
        }
      }
    }
    getMeta()
  }, [meta])

  const handleLogout = async () => {
    try {
      await dispatch(logoutAsync()).unwrap()
      showMessage.success('登出成功')
      navigate('/login')
    }
    catch (error) {
      console.error('登出失败:', error)
      showMessage.error('登出失败')
    }
  }

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: '个人信息',
    },
    {
      key: '4',
      danger: true,
      disabled: isPending, // 在加载时禁用菜单项
      label: (
        <Space>
          {isPending && <LoadingOutlined spin />}
          退出登录
        </Space>
      ),
      onClick: () => {
        if (isPending)
          return
        handleLogout()
      },
    },
  ] as const

  // 构建面包屑项
  const defaultBreadcrumbs = pageTitle === '工作台'
    ? [{ title: '工作台' }]
    : [
        {
          title: '工作台',
          href: `/${basePrefix}/dashboard`,
        },
        {
          title: pageTitle,
          href: `/${basePrefix}${location.pathname}`,
        },
      ]

  // 优先使用传入的面包屑，如果没有则使用默认的
  const breadcrumbItems = breadcrumbs || defaultBreadcrumbs

  // 移动端布局
  if (isMobileView) {
    return (
      <>
        {contextHolder}
        <div className={`${styles.mobileHeader} ${className || ''}`} style={props.style}>

          {pageTitle !== '工作台' && (
            <div className={styles.back} onClick={() => navigate(-1)}>
              <BiChevronLeft size={28} color="#333333" />
            </div>
          )}
          <div className={styles.title} style={{ marginLeft: showUserInfo ? '24px' : '0', marginRight: pageTitle !== '工作台' ? '28px' : '0' }}>{pageTitle}</div>
          {showUserInfo && (
            <Flex gap={8} className={styles.userInfo}>
              {props?.rightSlot}
              <Dropdown menu={{ items }}>
                <Space>
                  <Avatar style={{ backgroundColor: '#1677ff', verticalAlign: 'middle' }}>
                    {pageTitle}
                  </Avatar>
                </Space>
              </Dropdown>
            </Flex>
          )}
        </div>
      </>

    )
  }

  // PC端布局
  return (
    <>
      {contextHolder}
      <div className={`${styles.headerBox} ${className || ''}`}>
        <div className={styles.content}>
          <Breadcrumb className={styles.breadcrumb} items={breadcrumbItems} />
          {showUserInfo && (
            <Space>
              {props?.rightSlot}
              <Text>{userInfo?.default_sale_system_name}</Text>
              <Dropdown menu={{ items }}>
                <Space>
                  <Avatar style={{ backgroundColor: '#1677ff', verticalAlign: 'middle' }}>
                    {pageTitle}
                  </Avatar>
                </Space>
              </Dropdown>
            </Space>
          )}
        </div>
      </div>
    </>
  )
}

export default HeaderBox
