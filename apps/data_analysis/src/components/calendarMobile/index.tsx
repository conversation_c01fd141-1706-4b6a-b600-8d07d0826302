import { Typography } from 'antd'
import { CalendarPicker } from 'antd-mobile'
import classnames from 'classnames'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import { FaRegCalendar } from 'react-icons/fa'
import styles from './index.module.scss'

interface Props {
  // 默认范围 默认一个月
  defaultRange?: [Date, Date]
  className?: string
  style?: React.CSSProperties
  // 添加onChange回调属性
  onChange?: (dates?: [Date, Date] | null) => void
}
function CalendarMobile(props: Props) {
  const {
    defaultRange,
  } = props
  const [value, setDateValue] = useState(defaultRange)
  const [dateVisible, setDateVisible] = useState(false)
  const showDateCalendar = () => {
    setDateVisible(true)
  }
  const handleChange = (dates: [Date, Date] | null) => {
    if (dates && Array.isArray(dates) && dates.length === 2) {
      setDateValue(dates)
      props.onChange?.(dates)
      setDateVisible(false)
    }
  }
  return (
    <>
      <span style={props.style} className={classnames(styles.date, props.className)} onClick={showDateCalendar}>
        <FaRegCalendar className={styles.icon} />
        <Typography.Text className={styles.text}>
          {dayjs(value?.[0]).format('YYYY-MM-DD')}
          ~
          {dayjs(value?.[1]).format('YYYY-MM-DD')}
        </Typography.Text>
      </span>
      <CalendarPicker
        {...props}
        value={value}
        visible={dateVisible}
        defaultValue={defaultRange}
        selectionMode="range"
        onClose={() => setDateVisible(false)}
        onMaskClick={() => setDateVisible(false)}
        onChange={handleChange}
      />
    </>
  )
}
export default CalendarMobile
