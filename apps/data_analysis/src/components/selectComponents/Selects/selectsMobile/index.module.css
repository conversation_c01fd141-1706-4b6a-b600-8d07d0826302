@reference "tailwindcss";
.mobileDateBox {
  @apply flex justify-between items-center w-full h-8 px-3 bg-[#f5f5f5] rounded-lg;
}
.mobileDateBox .dateBoxLeft {
  @apply flex justify-between items-center flex-1;
}
.mobileDateBox .dateBoxLeft .placeholder {
  @apply text-[rgba(0,0,0,0.25)] text-sm;
}
.mobileDateBox .dateBoxLeft .value {
  @apply text-[rgba(0,0,0,0.85)] text-sm;
}
.mobileDateBox .dateBoxRight {
  @apply flex justify-center items-center w-4 text-[rgba(0,0,0,0.25)];
}

.popupContent {
  @apply h-[80vh] bg-white flex flex-col;
}
.popupContent .cancel {
  @apply text-[rgba(0,0,0,0.45)] text-base;
}
.popupContent .confirm {
  @apply text-[#1677ff] text-base font-medium;
}
.popupContent .searchBox {
  @apply px-3 py-2;
  border-bottom: 0.5px solid rgba(0, 0, 0, 0.06);
}
.popupContent .searchBox .searchInput {
  @apply rounded-lg bg-[#f5f5f5] border-none h-9;
}
.popupContent .listContainer {
  @apply flex-1 overflow-y-auto;
  -webkit-overflow-scrolling: touch;
}

.selected {
  @apply text-[#1677ff];
  background: rgba(119, 165, 230, 0.2);
}/*# sourceMappingURL=index.module.css.map */