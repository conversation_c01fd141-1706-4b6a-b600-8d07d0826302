@reference 'tailwindcss';

.mobileDateBox {
  @apply flex justify-between items-center w-full h-8 px-3 bg-[#f5f5f5] rounded-lg;

  .dateBoxLeft {
    @apply flex justify-between items-center flex-1;

    .placeholder {
      @apply text-[rgba(0,0,0,0.25)] text-sm;
    }
    
    .value {
      @apply text-[rgba(0,0,0,0.85)] text-sm;
    }
  }

  .dateBoxRight {
    @apply flex justify-center items-center w-4 text-[rgba(0,0,0,0.25)];
  }
}
.popupContent{
  @apply h-[80vh] bg-white flex flex-col;
  .cancel{
    @apply text-[rgba(0,0,0,0.45)] text-base;
  }
  .confirm{
    @apply text-[#1677ff] text-base font-medium;
  }
  .searchBox{
    @apply px-3 py-2;
    border-bottom: 0.5px solid rgba(0, 0, 0, 0.06);
    .searchInput{
      @apply rounded-lg bg-[#f5f5f5] border-none h-9;
    }
  }
  .listContainer {
    @apply flex-1 overflow-y-auto;
    -webkit-overflow-scrolling: touch;
  }
}

.selected {
    @apply text-[#1677ff];
    background: rgba(119, 165, 230, 0.2);
  }
