import type { LabeledValue } from 'antd/es/select'
import { DownOutlined, SearchOutlined } from '@ant-design/icons'
import { Input, Typography } from 'antd'
import { InfiniteScroll, List, NavBar, Popup } from 'antd-mobile'
import { debounce } from 'lodash-es'
import { useEffect, useMemo, useState } from 'react'
import styles from './index.module.scss'

const { Text } = Typography
const DEFAULTPAGE = 1 // 默认页码
const DEFAULTSIZE = 20 // 默认页码大小

interface SelectPaginationProps {
  placeholder?: string // 占位符
  pagination?: boolean | { defaultPage: number, defaultSize: number } // 是否分页
  query: Record<string, any> // 查询参数
  renderOption?: (option: any) => React.ReactNode // 渲染选项（可以重写下拉的每一条内容）
  fieldNames?: { label: string, value: string } // 字段
  isSearch?: boolean // 新增：是否启用搜索功能
  keywordFile?: string // 新增：搜索关键词字段
  value?: string | string[] |
    number | number[] |
    LabeledValue | LabeledValue[] | null // 新增：选中的值
  onChange?: (value: any, option: any) => void // 修改：增加 option 参数
  disabled?: boolean // 新增：是否禁用
  fetchApi: (params: any) => Promise<any> | any // 新增：获取数据的主体函数方法
}
const defaultFieldNames = { label: 'name', value: 'id' } // 默认字段
// 移动端Select组件
function SelectsMobile(
  {
    placeholder = '请选择', // 占位符
    pagination = false, // 是否分页
    query, // 查询参数
    renderOption, // 渲染选项（可以重写下拉的每一条内容）
    fieldNames = defaultFieldNames, // 字段
    isSearch = false, // 是否启用搜索功能
    keywordFile = 'name', // 新增：搜索关键词字段
    value, // 选中的值
    onChange, // 修改：增加 option 参数
    disabled = false, // 新增：是否禁用
    fetchApi, // 新增：获取数据的主体函数方法
  }: SelectPaginationProps,
) {
  const [isInit, setIsInit] = useState(false)
  const [_total, setTotal] = useState<number | undefined>(0) // 总条数
  const [currentPage, setCurrentPage] = useState<number | undefined>(undefined)
  const [pageSize, setPageSize] = useState<number | undefined>(undefined)
  const [options, setOptions] = useState<any[]>([]) // 可以选择的选项,必须是数组
  const [_localOptions, setLocalOptions] = useState<any[]>([]) // 新增：本地过滤后的选项
  const { mutateAsync: fetchDatas, isPending } = fetchApi({}) // 使用 mutateAsync
  const [searchValue, setSearchValue] = useState<string>('') // 搜索关键词
  const [loading, setLoading] = useState(false) // 是否加载中
  const [hasMore, setHasMore] = useState(true) // 是否还有更多
  // 弹窗相关状态
  const [visible, setVisible] = useState(false) // 是否显示弹窗
  // 处理初始值，兼容对象格式
  const getInitialValue = (val: any) => {
    if (val && typeof val === 'object' && 'value' in val) {
      return val.value
    }
    return val
  }
  const [selectedValue, setSelectedValue] = useState<any>(getInitialValue(value)) // 选中的值
  const [tempValue, setTempValue] = useState<any>(getInitialValue(value)) // 临时选中的值

  // 监听value变化，同步更新selectedValue
  useEffect(() => {
    const newValue = getInitialValue(value)
    setSelectedValue(newValue)
    setTempValue(newValue)
  }, [value])

  // 合并为一个 useEffect
  useEffect(() => {
    const initializeData = async () => {
      // 准备参数
      let newPage = 1
      let newSize = 2000
      if (typeof pagination === 'boolean' && pagination === true) {
        newPage = DEFAULTPAGE
        newSize = DEFAULTSIZE
      }
      else if (typeof pagination === 'object' && pagination !== null) {
        newPage = pagination.defaultPage
        newSize = pagination.defaultSize
      }

      // 获取数据
      const res: any = await fetchDatas({
        ...query,
        page: newPage,
        size: newSize,
      })

      // 批量更新状态
      setCurrentPage(newPage) // 设置当前页码
      setPageSize(newSize) // 设置每页大小
      setOptions(res.list) // 设置可以选择的选项
      setLocalOptions(res.list) // 设置本地过滤后的选项
      setTotal(res.total) // 设置总条数
      setIsInit(true) // 设置初始化状态
    }

    if (!isInit) {
      initializeData()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []) // 只在组件挂载时执行一次

  // 加载更多数据
  const loadMore = async (isRefresh = false) => {
    if (loading || isPending)
      return

    setLoading(true)
    try {
      const nextPage = isRefresh ? 1 : (currentPage || 1)
      const res = await fetchDatas({
        ...query,
        page: nextPage,
        size: pageSize,
        ...(searchValue ? { [keywordFile]: searchValue } : {}),
      })

      setOptions(prev => isRefresh ? res.list : [...prev, ...res.list])
      setHasMore(res.list.length === pageSize)
      if (!isRefresh) {
        setCurrentPage(nextPage + 1)
      }
    }
    catch (error) {
      console.error('加载更多数据失败:', error)
    }
    finally {
      setLoading(false)
    }
  }

  // 修改取消处理
  const handleCancel = () => {
    setVisible(false)
    setTempValue(selectedValue)
    setSearchValue('')
    setCurrentPage(1)
    setOptions([])
    setHasMore(true)
    loadMore(true) // 重新加载数据
  }

  // 修改确认处理
  const handleConfirm = async () => {
    try {
      // 如果在当前选项中找不到选中值，需要单独请求获取选中项的详情
      let selectedOption = options.find(item => item[fieldNames.value] === tempValue)

      if (!selectedOption && tempValue) {
        // 发起请求获取选中项的详情
        const res = await fetchDatas({
          ...query,
          [fieldNames.value]: tempValue,
        })
        if (res.list?.length > 0) {
          selectedOption = res.list[0]
        }
      }

      // 确保有 selectedOption 时才更新值
      if (selectedOption || tempValue === undefined) {
        setSelectedValue(tempValue)
        // 确保 onChange 被调用
        if (onChange) {
          // 构造与输入格式一致的返回值
          const returnValue = value && typeof value === 'object' && 'value' in value
            ? { value: tempValue, label: selectedOption?.[fieldNames.label] }
            : tempValue
          onChange(returnValue, selectedOption)
        }
      }

      // 重置状态
      setVisible(false)
      setSearchValue('')
      setCurrentPage(1)
      setOptions([])
      setHasMore(true)
      loadMore(true)
    }
    catch (error) {
      console.error('确认选择失败:', error)
    }
  }

  // 创建防抖函数
  const debouncedGetData = useMemo(
    () => debounce(async (searchValue: string) => {
      if (!isInit)
        return

      // 重置分页参数
      setCurrentPage(1)
      setOptions([])
      setHasMore(true)

      const res: any = await fetchDatas({
        ...query,
        page: 1,
        size: pageSize,
        [keywordFile]: searchValue,
      })

      setOptions(res.list)
      setTotal(res.total)
      setHasMore(res.list.length === pageSize)
    }, 300),
    [fetchDatas, query, keywordFile, isInit, pageSize],
  )

  // 搜索处理函数
  const handleSearchImpl = (value: string) => {
    setSearchValue(value)
    debouncedGetData(value)
  }

  // 清除搜索处理
  const handleClearSearch = () => {
    setSearchValue('')
    setCurrentPage(1)
    setOptions([])
    setHasMore(true)

    // 重新加载初始数据
    fetchDatas({
      ...query,
      page: 1,
      size: pageSize,
    }).then((res: any) => {
      setOptions(res.list)
      setTotal(res.total)
      setHasMore(res.list.length === pageSize)
    })
  }

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      debouncedGetData.cancel()
    }
  }, [debouncedGetData])

  const handleClickShow = () => { // 点击显示弹窗
    setVisible(true)
    setTempValue(selectedValue)
  }

  const handleSelect = (value: any) => { // 选择处理
    setTempValue(value)
  }
  const getLabel = useMemo(() => {
    // 首先尝试从options中找到匹配的项
    const foundOption = options.find(item => item[fieldNames.value] === selectedValue)
    if (foundOption) {
      console.log('foundOption', foundOption[fieldNames.label])
      return foundOption[fieldNames.label]
    }

    // 如果options中找不到，但value是对象且有label属性，使用value.label
    if (value && typeof value === 'object' && 'label' in value && value.label) {
      console.log('getLabel value', value)
      return value.label
    }

    // 如果selectedValue存在但不是对象，直接返回selectedValue
    if (selectedValue !== undefined && selectedValue !== null && selectedValue !== '') {
      console.log('selectedValue', selectedValue)
      return selectedValue
    }

    return undefined
  }, [options, selectedValue, value, fieldNames])
  console.log('getLabel', getLabel, value)
  return (
    <>
      <div className={styles.mobileDateBox} onClick={handleClickShow}>
        <div className={styles.dateBoxLeft}>
          {selectedValue
            ? (
                <Text className={styles.value}>
                  {getLabel}
                </Text>
              )
            : (
                <Text className={styles.placeholder}>{placeholder}</Text>
              )}
        </div>
        <div className={styles.dateBoxRight}>
          <DownOutlined />
        </div>
      </div>
      <Popup
        visible={visible}
        onMaskClick={handleCancel}
        onClose={handleCancel}
        bodyStyle={{ height: '88vh' }}
      >
        <div className={styles.popupContent}>
          <NavBar
            onBack={handleCancel}
            back={<Text className={styles.cancel}>取消</Text>}
            backIcon={null}
            right={<Text className={styles.confirm} onClick={handleConfirm}>确定</Text>}
          >
            {placeholder}
          </NavBar>
          {isSearch && (
            <div className={styles.searchBox}>
              <Input
                disabled={disabled}
                prefix={<SearchOutlined />}
                placeholder="请输入搜索"
                allowClear
                className={styles.searchInput}
                value={searchValue}
                onChange={e => handleSearchImpl(e.target.value)}
                onClear={handleClearSearch}
                variant="filled"
              />
            </div>
          )}
          <div className={styles.listContainer}>
            <List>
              {options.map((item: any, index: number) => {
                const key = `${item[fieldNames.value]}_${index}` // 使用值和索引组合作为key
                return (
                  <List.Item
                    key={key}
                    onClick={() => handleSelect(item[fieldNames.value])}
                    className={tempValue === item[fieldNames.value] ? styles.selected : ''}
                  >
                    {renderOption ? renderOption({ data: item, label: item[fieldNames.label] }) : item[fieldNames.label]}
                  </List.Item>
                )
              })}
              <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
            </List>
          </div>
        </div>
      </Popup>
    </>
  )
}

export default SelectsMobile
