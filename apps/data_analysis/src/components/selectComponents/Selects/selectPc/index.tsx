import type { SelectProps } from 'antd'
import type { LabeledValue } from 'antd/es/select'
import type { FC, ReactNode } from 'react'
import { Divider, Pagination, Select } from 'antd'
import { useEffect, useRef, useState } from 'react'
import styles from './index.module.scss'

const DEFAULTPAGE = 1 // 默认页码
const DEFAULTSIZE = 20 // 默认页码大小

interface SelectPaginationProps extends SelectProps {
  placeholder?: string // 占位符
  pagination?: boolean | { defaultPage: number, defaultSize: number } // 是否分页
  query: Record<string, any> // 查询参数
  renderOption?: (option: any) => ReactNode // 渲染选项（可以重写下拉的每一条内容）
  fieldNames?: { label: string, value: string } // 字段
  isSearch?: boolean // 新增：是否启用搜索功能
  keywordFile?: string // 新增：搜索关键词字段
  value?: string | string[] |
    number | number[] |
    LabeledValue | LabeledValue[] | null // 新增：选中的值
  onChange?: (value: any, option: any) => void // 修改：增加 option 参数
  disabled?: boolean // 新增：是否禁用
  fetchApi: (params: any) => Promise<any> | any // 新增：获取数据的主体函数方法
}
const defaultFieldNames = { label: 'name', value: 'id' } // 默认字段
const SelectPc: FC<SelectPaginationProps> = (props) => {
  const {
    placeholder = '请选择',
    pagination = false,
    query,
    renderOption,
    fieldNames = defaultFieldNames,
    isSearch = false, // 新增：默认不启用搜索
    keywordFile = 'name', // 新增：默认使用 name 字段
    value,
    onChange,
    disabled = false,
    fetchApi,
    ...restProps
  } = props
  const [isInit, setIsInit] = useState(false) // 是否初始化
  const [total, setTotal] = useState<number | undefined>(0) // 总条数
  const [currentPage, setCurrentPage] = useState<number | undefined>(undefined) // 当前页码
  const [pageSize, setPageSize] = useState<number | undefined>(undefined) // 每页大小
  const { mutateAsync: fetchDatas, isPending } = fetchApi({}) // 获取数据的主体函数方法
  const [options, setOptions] = useState<any[]>([]) // 可以选择的选项,必须是数组
  const [searchValue, setSearchValue] = useState<string>('') // 新增：搜索关键词
  const [localOptions, setLocalOptions] = useState<any[]>([]) // 新增：本地过滤后的选项

  // 修改：添加一个 useEffect 来监听 options 变化
  useEffect(() => {
    setLocalOptions(options)
  }, [options])

  // 修改：本地搜索处理函数
  const handleLocalSearch = (keyword: string) => {
    setSearchValue(keyword)
    if (!keyword.trim()) {
      setLocalOptions(options)
      return
    }
    const filtered = options.filter((item) => {
      const label = typeof fieldNames.label === 'function'
        ? fieldNames.label()
        : String(item[fieldNames.label] || '')
      return label.toLowerCase().includes(keyword.toLowerCase())
    })
    setLocalOptions(filtered)
  }
  const getInitData = async () => {
    const querys = {
      ...query,
    } // 初始化查询参数
    if (typeof pagination === 'boolean' && pagination === true) { // 如果开启了分页，并且是boolean,而且为true的情况下，那就使用默认的
      querys.page = currentPage // 默认页码
      querys.size = pageSize // 默认页码大小
      setCurrentPage(DEFAULTPAGE) // 设置默认页码
      setPageSize(DEFAULTSIZE) // 设置默认页码大小
    }
    if (typeof pagination === 'object' && pagination !== null) { // 如果开启了分页，并且是对象，那就使用该对象的 defaultPage作为页码，defaultSize作为页码大小
      querys.page = pagination.defaultPage
      querys.size = pagination.defaultSize
      setCurrentPage(pagination.defaultPage) // 设置默认页码
      setPageSize(pagination.defaultSize) // 设置默认页码大小
    }
    if (!pagination) { // 如果未开启分页，则默认查询所有数据
      querys.page = 1 // 默认第一页
      querys.size = 2000 // 默认 两千条
    }
    const res: any = await fetchDatas(querys) // 获取数据
    setOptions(res.list) // 设置可以选择的选项
    setLocalOptions(res.list) // 新增：初始化本地选项
    setTotal(res.total) // 设置总条数
    // 新增：如果有传入 value 但找不到对应选项，尝试从后端数据中查找匹配项
    if (value && !options.find(item => item[fieldNames.value] === value)) {
      const matchedOption = res.list.find((item: any) => item[fieldNames.value] === value)
      if (matchedOption) {
        onChange?.(value, matchedOption)
      }
    }
    setIsInit(true) // 设置初始化状态
  }
  useEffect(() => { // 加载组件的时候先获取数据进行初始化
    getInitData()
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  // 在组件顶部添加 prevQueryRef
  const prevQueryRef = useRef<Record<string, any>>()
  // 修改 useEffect，监听 query 变化
  useEffect(() => {
    const isQueryChanged = JSON.stringify(prevQueryRef.current) !== JSON.stringify(query)
    if (isInit && isQueryChanged) {
      setCurrentPage(DEFAULTPAGE)
      const getData = async () => {
        const querys = {
          ...query,
          page: DEFAULTPAGE,
          size: pageSize,
        }
        const res: any = await fetchDatas(querys)
        setOptions(res.list)
        setTotal(res.total)
      }
      getData()
    }
    prevQueryRef.current = query
  }, [query])
  useEffect(() => { // 当分页发生变化的时候，需要重新获取数据
    const getData = async () => {
      const querys = {
        ...query,
      } // 初始化查询参数
      querys.page = currentPage // 页码
      querys.size = pageSize // 页码大小
      const res: any = await fetchDatas(querys) // 获取数据
      setOptions(res.list) // 设置可以选择的选项
      setTotal(res.total) // 设置总条数
    }
    if (isInit) {
      getData()
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, pageSize])

  const changePage = (page: number, pageSize: number) => { // 切换页的时候
    setCurrentPage(page) // 设置页码
    setPageSize(pageSize) // 设置页码大小
  }

  // 修改：处理搜索
  const handleSearch = async (value: string) => {
    setSearchValue(value)
    if (!pagination) {
      // 本地搜索
      handleLocalSearch(value)
    }
    else {
      // 远程搜索
      const querys = {
        ...query,
        page: currentPage,
        size: pageSize,
        [keywordFile]: value, // 修改：使用动态字段名
      }
      const res: any = await fetchDatas(querys)
      setOptions(res.list)
      setTotal(res.total)
      setCurrentPage(1)
      if (typeof pagination === 'boolean' && pagination === true) {
        setPageSize(DEFAULTSIZE)
      }
      if (typeof pagination === 'object' && pagination !== null) {
        setPageSize(pagination.defaultSize)
      }
    }
  }

  // 新增：处理选择变化
  const handleChange = (value: any, option: any) => {
    onChange?.(value, option?.data || option)
  }

  return (
    <Select
      {...restProps}
      value={value}
      disabled={disabled}
      onChange={handleChange} // 修改：使用自定义的 handleChange
      placeholder={placeholder}
      variant="borderless"
      classNames={{
        root: styles.selectPc,
        popup: {
          root: styles.selectPc_popup,
        },
      }}
      loading={isPending}
      allowClear
      showSearch={isSearch}
      searchValue={searchValue}
      onSearch={isSearch ? handleSearch : undefined}
      filterOption={false} // 修改：关闭内置的过滤，使用我们自己的过滤逻辑
      dropdownRender={(menu) => {
        return (
          <>
            {menu}
            {pagination && (
              <>
                <Divider style={{ margin: '8px 0' }} orientation="left" plain>
                  <span style={{ color: '#999', fontSize: 12 }}>
                    共
                    {total}
                    条数据
                  </span>
                </Divider>
                <Pagination
                  showSizeChanger={false}
                  align="center"
                  size="small"
                  pageSize={pageSize}
                  current={currentPage}
                  total={total}
                  showLessItems
                  simple
                  onChange={changePage}
                />
              </>
            )}
          </>
        )
      }}
      fieldNames={fieldNames}
      options={pagination ? options : localOptions} // 使用过滤后的选项
      optionRender={renderOption}
    />
  )
}

export default SelectPc
