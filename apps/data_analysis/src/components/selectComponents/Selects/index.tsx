import useWindowSize from '@/hooks/useWindowSize'
import { useMemo } from 'react'
import SelectsPC from './selectPc'
import SelectsMobile from './selectsMobile'

// 主组件
function Selects(props: any) {
  const { isMobile } = useWindowSize() // 获取窗口大小
  // 使用 useMemo 来缓存组件实例
  const Component = useMemo(() => {
    return isMobile ? SelectsMobile : SelectsPC
  }, [isMobile])

  return <Component {...props} />
}

export default Selects
