import type { FC, ReactNode } from 'react'
import { Typography } from 'antd'
import styles from './index.module.scss'

const { Text } = Typography

interface LabelBoxProps {
  label: ReactNode
  children: ReactNode
  className?: string
  width?: number | string // 可以传入数字或具体的宽度值（如 '200px'）
  fullWidth?: boolean // 是否占满父容器宽度
}

const LabelBox: FC<LabelBoxProps> = ({
  label,
  children,
  className,
  width = 300, // 默认宽度 300px
  fullWidth = false, // 默认不占满
}) => {
  const style = fullWidth
    ? undefined
    : {
        width: typeof width === 'number' ? `${width}px` : width,
      }

  return (
    <div
      className={`${styles.labelBox} ${fullWidth ? styles.full : ''} ${className}`}
      style={style}
    >
      <div className={styles.label}><Text>{label}</Text></div>
      <div className={styles.content}>{children}</div>
    </div>
  )
}

export default LabelBox
