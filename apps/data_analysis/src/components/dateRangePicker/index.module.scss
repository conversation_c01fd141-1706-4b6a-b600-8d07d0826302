@reference 'tailwindcss';
.mobileSelector {
  @apply flex items-center px-3 py-1.5 text-sm;
  // border: 1px solid #d9d9d9;

  &:active {
    @apply bg-gray-50;
  }
}
.border{
  background: rgba(0, 0, 0, 0.04);
  @apply rounded-sm;
}
  .popupHeader {
    @apply flex items-center justify-between px-4 py-3 border-b border-gray-100;
    
    .cancel {
      @apply text-gray-500 text-sm;
    }
    
    .title {
      @apply text-base font-medium;
    }
    
    .confirm {
      @apply text-blue-500 text-sm;
    }
  }
  
  .tabContainer {
    @apply flex px-4 py-3 gap-4 border-b border-gray-100;
  
    .tab {
      @apply flex-1 p-3 rounded-lg cursor-pointer transition-all;
      background: #f5f5f5;
  
      &.active {
        @apply bg-blue-50;
  
        .label {
          @apply text-blue-600;
        }
  
        .date {
          @apply text-blue-600;
        }
      }
  
      .label {
        @apply text-xs text-gray-500 mb-1;
      }
  
      .date {
        @apply text-sm font-medium;
      }
    }
  }
  
  .pickerContainer {
    @apply relative h-[calc(60vh-120px)];
  
    .pickerWrapper {
      @apply absolute inset-0 opacity-0 pointer-events-none transition-all duration-300;
      transform: translateX(100px);
  
      &.show {
        @apply opacity-100 pointer-events-auto;
        transform: translateX(0);
      }
    }
  }
  .datePickerView {
    touch-action: none;
    height: 100%;
  }
  
  :global {
    .adm-picker-view {
      @apply px-4;
      touch-action: none;
      height: 100%;
    }
  
    .adm-picker-view-column {
      @apply text-base;
      touch-action: none;
    }
  
    .adm-picker,
    .adm-picker-view,
    .adm-picker-view-column,
    .adm-picker-view-column-wheel,
    .adm-popup-body {
      touch-action: none !important;
    }
  
    .adm-picker-view-column-wheel {
      & > * {
        touch-action: none !important;
      }
    }
  
    .adm-popup {
      .adm-popup-body {
        overscroll-behavior: none;
      }
    }
  }
