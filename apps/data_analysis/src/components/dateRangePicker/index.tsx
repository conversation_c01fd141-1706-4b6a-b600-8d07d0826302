import type { RangePickerProps } from 'antd/es/date-picker'
import type { Dayjs } from 'dayjs'
import { isMobile } from '@/utils/device'
import { DatePicker as AntDatePicker } from 'antd'
import { DatePickerView, Popup } from 'antd-mobile'

import classNames from 'classnames'
import dayjs from 'dayjs'
import { useEffect, useState } from 'react'
import styles from './index.module.scss'

type DateRange = [Date, Date]

interface DateRangePickerProps extends Omit<RangePickerProps, 'value' | 'onChange'> {
  value?: DateRange
  border?: boolean
  onChange?: (dates: DateRange) => void
  className?: string
  minDate?: Dayjs
  maxDate?: Dayjs
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  value,
  onChange,
  border = true,
  className,
  minDate = dayjs('2020-01-01'),
  maxDate = dayjs('2030-12-31'),
  ...restProps
}) => {
  const [isMobileView, setIsMobileView] = useState(isMobile())
  const [visible, setVisible] = useState(false)
  const now = new Date()
  const [selectedDates, setSelectedDates] = useState<DateRange>(() => {
    if (value) {
      return [
        dayjs(value[0]).isValid() ? new Date(value[0]) : now,
        dayjs(value[1]).isValid() ? new Date(value[1]) : now,
      ]
    }
    return [now, now]
  })
  const [activeTab, setActiveTab] = useState<'start' | 'end'>('start')
  const [tempDates, setTempDates] = useState<DateRange>(selectedDates)

  // 监听屏幕变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(isMobile())
    }
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // 监听外部 value 变化
  useEffect(() => {
    if (value && !visible) {
      const newStart = dayjs(value[0]).isValid() ? value[0] : selectedDates[0]
      const newEnd = dayjs(value[1]).isValid() ? value[1] : selectedDates[1]

      if (
        dayjs(newStart).valueOf() !== dayjs(selectedDates[0]).valueOf()
        || dayjs(newEnd).valueOf() !== dayjs(selectedDates[1]).valueOf()
      ) {
        setSelectedDates([
          dayjs(newStart).toDate(),
          dayjs(newEnd).toDate(),
        ])
      }
    }
  }, [value, visible, selectedDates])

  // 处理 PC 端日期变化
  const handlePcChange: RangePickerProps['onChange'] = (dates) => {
    if (dates && dates[0] && dates[1]) {
      const newDates: DateRange = [dates[0].toDate(), dates[1].toDate()]
      setSelectedDates(newDates)
      onChange?.(newDates)
    }
  }

  // 处理移动端日期选择
  const handleMobileSelect = () => {
    setVisible(true)
    setTempDates(selectedDates)
  }

  // 处理移动端确认
  const handleMobileConfirm = () => {
    // 确保结束日期不早于开始日期
    if (tempDates[1] < tempDates[0]) {
      const [start] = tempDates
      setTempDates([start, start])
      setSelectedDates([start, start])
      onChange?.([start, start])
    }
    else {
      setSelectedDates(tempDates)
      onChange?.(tempDates)
    }
    setVisible(false)
  }

  // 处理移动端取消
  const handleCancel = () => {
    setVisible(false)
    setTempDates(selectedDates)
  }

  // 处理移动端日期更新
  const handleMobileDateChange = (date: Date | null, type: 'start' | 'end') => {
    if (!date)
      return

    if (type === 'start') {
      setTempDates((prev) => {
        if (date > prev[1]) {
          return [date, date]
        }
        return [date, prev[1]]
      })
    }
    else {
      setTempDates((prev) => {
        if (date < prev[0]) {
          return [date, date]
        }
        return [prev[0], date]
      })
    }
  }

  const renderMobileView = () => (
    <>
      <div
        className={classNames(styles.mobileSelector, { [styles.border]: border })}
        onClick={handleMobileSelect}
      >
        <span>
          {dayjs(selectedDates[0]).format('YYYY-MM-DD')}
          {' '}
          至
          {dayjs(selectedDates[1]).format('YYYY-MM-DD')}
        </span>
      </div>

      <Popup
        visible={visible}
        onMaskClick={handleCancel}
        position="bottom"
        bodyStyle={{
          height: '60vh',
          touchAction: 'none',
        }}
      >
        <div className={styles.popupHeader}>
          <div className={styles.cancel} onClick={handleCancel}>
            取消
          </div>
          <div className={styles.title}>选择日期范围</div>
          <div className={styles.confirm} onClick={handleMobileConfirm}>
            确定
          </div>
        </div>

        <div className={styles.tabContainer}>
          <div
            className={`${styles.tab} ${activeTab === 'start' ? styles.active : ''}`}
            onClick={() => setActiveTab('start')}
          >
            <div className={styles.label}>开始时间</div>
            <div className={styles.date}>{dayjs(tempDates[0]).format('YYYY-MM-DD')}</div>
          </div>
          <div
            className={`${styles.tab} ${activeTab === 'end' ? styles.active : ''}`}
            onClick={() => setActiveTab('end')}
          >
            <div className={styles.label}>结束时间</div>
            <div className={styles.date}>{dayjs(tempDates[1]).format('YYYY-MM-DD')}</div>
          </div>
        </div>

        <div className={styles.pickerContainer}>
          <div className={`${styles.pickerWrapper} ${activeTab === 'start' ? styles.show : ''}`}>
            <DatePickerView
              className={styles.datePickerView}
              value={new Date(tempDates[0])}
              min={minDate.toDate()}
              max={maxDate.toDate()}
              onChange={val => handleMobileDateChange(val, 'start')}
            />
          </div>
          <div className={`${styles.pickerWrapper} ${activeTab === 'end' ? styles.show : ''}`}>
            <DatePickerView
              className={styles.datePickerView}
              value={new Date(tempDates[1])}
              min={minDate.toDate()}
              max={maxDate.toDate()}
              onChange={val => handleMobileDateChange(val, 'end')}
            />
          </div>
        </div>
      </Popup>
    </>
  )

  return isMobileView
    ? renderMobileView()
    : (
        <AntDatePicker.RangePicker
          {...restProps}
          value={[dayjs(selectedDates[0]), dayjs(selectedDates[1])]}
          onChange={handlePcChange}
          className={className}
          size="middle"
          variant="filled"
        />
      )
}

export default DateRangePicker
