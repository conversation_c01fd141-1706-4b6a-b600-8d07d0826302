@reference "tailwindcss";
.tableWrapper {
  @apply w-full overflow-auto;
  height: 300px;
  overflow-x: auto;
  overflow-y: auto;
  position: relative;
}
.tableWrapper .table {
  width: 100%;
  border-collapse: collapse;
  @apply w-full;
  border-collapse: collapse;
}
.tableWrapper .table th, .tableWrapper .table td {
  @apply px-4 py-2 text-left;
  white-space: nowrap;
}
.tableWrapper .table th {
  @apply bg-gray-50 text-gray-500 font-normal;
  position: sticky;
  top: 0;
  z-index: 1;
}
.tableWrapper .table td {
  @apply text-gray-700 border-b border-gray-100;
  transition: background-color 0.2s;
  background-color: #fff;
}
.tableWrapper .table td[style*="position: sticky"] {
  z-index: 2;
  background-color: inherit !important;
}
.tableWrapper .table tbody tr {
  background-color: #fff;
}
.tableWrapper .table tbody tr:hover {
  background-color: #eff6ff;
}
.tableWrapper .table tbody tr:hover td {
  background-color: inherit;
}

.loadingWrapper {
  @apply w-full flex justify-center py-8;
}/*# sourceMappingURL=index.module.css.map */