import type { ColumnDef } from '@tanstack/react-table'
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from '@tanstack/react-table'
import { Empty, Spin } from 'antd'
import styles from './index.module.scss'

// 添加自定义列类型
export type CustomColumnDef<T> = ColumnDef<T, any> & {
  fixed?: 'left' | 'right'
}

interface TableProps<T> {
  columns: CustomColumnDef<T>[] // 使用自定义列类型
  data: T[]
  loading?: boolean
  emptyText?: string
  height?: number | string // 新增高度配置
  onReachBottom?: () => void // 添加滚动到底部的回调
}

function Table<T>({
  columns,
  data,
  loading = false,
  emptyText = '暂无数据',
  height = 300,
  onReachBottom,
}: TableProps<T>) {
  const table = useReactTable({
    data: data || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  })
  // 添加滚动处理函数
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
    // 当距离底部小于 20px 时触发加载
    if (scrollHeight - scrollTop - clientHeight < 20 && onReachBottom && !loading) {
      onReachBottom()
    }
  }
  return (
    <div
      className={styles.tableWrapper}
      style={{ height, overflowY: 'auto' }}
      onScroll={handleScroll}
    >
      <table className={styles.table}>
        <thead>
          {table.getHeaderGroups().map(headerGroup => (
            <tr key={headerGroup.id}>
              {headerGroup.headers.map(header => (
                <th
                  key={header.id}
                  style={{
                    width: header.getSize(),
                    ...(header.column.columnDef as CustomColumnDef<T>).fixed && {
                      position: 'sticky',
                      left: (header.column.columnDef as CustomColumnDef<T>).fixed === 'left' ? 0 : undefined,
                      right: (header.column.columnDef as CustomColumnDef<T>).fixed === 'right' ? 0 : undefined,
                      zIndex: 3,
                      backgroundColor: '#fafafa',
                    },
                  }}
                >
                  {flexRender(
                    header.column.columnDef.header,
                    header.getContext(),
                  )}
                </th>
              ))}
            </tr>
          ))}
        </thead>
        <tbody>
          {data.length === 0 && loading
            ? (
                <tr>
                  <td colSpan={columns.length} style={{ border: 'none' }}>
                    <div className={styles.loadingWrapper}>
                      <Spin />
                    </div>
                  </td>
                </tr>
              )
            : data.length === 0
              ? (
                  <tr>
                    <td colSpan={columns.length} style={{ border: 'none' }}>
                      <Empty description={emptyText} />
                    </td>
                  </tr>
                )
              : (
                  <>
                    {
                      table.getRowModel().rows.map(row => (
                        <tr key={row.id}>
                          {row.getVisibleCells().map(cell => (
                            <td
                              key={cell.id}
                              style={{
                                width: cell.column.getSize(),
                                ...(cell.column.columnDef as CustomColumnDef<T>).fixed && {
                                  position: 'sticky',
                                  left: (cell.column.columnDef as CustomColumnDef<T>).fixed === 'left' ? 0 : undefined,
                                  right: (cell.column.columnDef as CustomColumnDef<T>).fixed === 'right' ? 0 : undefined,
                                  backgroundColor: 'inherit',
                                  zIndex: 2,
                                },
                              }}
                            >
                              {flexRender(
                                cell.column.columnDef.cell,
                                cell.getContext(),
                              )}
                            </td>
                          ))}
                        </tr>
                      ))
                    }
                    {
                      loading && (
                        <tr>
                          <td colSpan={columns.length} style={{ border: 'none' }}>
                            <div className={styles.loadingWrapper}>
                              <Spin />
                            </div>
                          </td>
                        </tr>
                      )
                    }
                  </>
                )}
        </tbody>
      </table>
    </div>
  )
}

export default Table
