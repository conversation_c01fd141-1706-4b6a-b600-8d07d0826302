@reference 'tailwindcss';
.tableWrapper {
    @apply w-full overflow-auto;
    height: 300px;
    overflow-x: auto;
    overflow-y: auto;
    position: relative;
    .table {
    width: 100%;
    border-collapse: collapse;
      @apply w-full;
      border-collapse: collapse;
  
      th, td {
        @apply px-4 py-2 text-left;
        white-space: nowrap;
      }
  
      th {
        @apply bg-gray-50 text-gray-500 font-normal;
        position: sticky;
        top: 0;
        z-index: 1;
      }
  
      td {
        @apply text-gray-700 border-b border-gray-100;
        transition: background-color 0.2s;
        background-color: #fff;
  
        &[style*="position: sticky"] {
          z-index: 2;
          background-color: inherit !important;
        }
      }
  
      tbody {
        tr {
          background-color: #fff;
          &:hover {
            background-color: #eff6ff;
            td {
              background-color: inherit;
            }
          }
        }
      }
    }
  }

.loadingWrapper {
  @apply w-full flex justify-center py-8;
}
