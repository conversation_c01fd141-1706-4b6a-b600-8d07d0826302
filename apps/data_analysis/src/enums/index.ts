// 应收单类型
export enum CollectType {
  CollectTypeProductSale = 1, // 销售送货单
  CollectTypeProductReturn = 2, // 销售退货单
  CollectTypeRawMaterial = 3, // 原料销售应收单
  CollectTypeRawMaterialReturn = 4, // 原料销售退货应收单
  CollectTypeGreyFabric = 5, // 坯布销售应收单
  CollectTypeGreyFabricReturn = 6, // 坯布销售退货应收单
  CollectTypeOther = 7, // 其他应收单
  CollectTypeActual = 8, // 实收单
  CollectTypeAdvance = 9, // 预收单
}

export enum SupplierOrderType {
  OrderTypeDNF = 1, // 染整
  OrderTypeProcessing = 2, // 加工
  OrderTypeRawMatlPur = 3, // 原料采购
  OrderTypeGreyFabricPur = 4, // 坯布采购
  OrderTypeProductPur = 5, // 成品采购
  OrderTypeOther = 6, // 其他
  OrderTypeRawDNF = 7, // 原料染整
  OrderTypeRawProcessing = 8, // 原料加工
  OrderTypeActually = 9, // 实付
  OrderTypeAdvance = 10, // 预付
  OrderTypeSaleTransfer = 11, //  调货销售单
}
