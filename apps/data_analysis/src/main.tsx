import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
// 导入组件和样式
import App from './App'
import { setupDayjs } from './plugins/dayjs'
import { store } from './store'
// import '/widget/abu-ai.css'
// for date-picker i18n
import 'dayjs/locale/zh-cn'
import '@/styles/index.scss'
// import { init } from '/widget/abu-ai.es.js'
// 添加响应式设置
function setRem() {
  const docEl = document.documentElement
  const resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize'
  const recalc = () => {
    const clientWidth = docEl.clientWidth
    if (!clientWidth)
      return
    // 基于1920设计稿，但设置最小字体大小
    // 当屏幕宽度小于1366px时，保持最小字体大小为12px
    const minFontSize = 14
    const calculatedSize = 16 * (clientWidth / 1920)
    const fontSize = Math.max(calculatedSize, minFontSize)
    docEl.style.fontSize = `${fontSize}px` // 基于1920设计稿
  }
  window.addEventListener(resizeEvt, recalc, false)
  document.addEventListener('DOMContentLoaded', recalc, false)
}
// 初始化
setRem()
// 改变窗口大小时重新设置
window.addEventListener('resize', setRem)
function Root() {
  setupDayjs()

  return (
    <Provider store={store}>
      <App />
    </Provider>
  )
}
createRoot(document.getElementById('root')!).render(
  <Root />
  ,
)
