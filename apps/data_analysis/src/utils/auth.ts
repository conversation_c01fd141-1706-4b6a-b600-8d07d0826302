import { basePrefix } from '@/router'

const TOKEN_KEY = 'token'
export function getToken() {
  return localStorage.getItem(TOKEN_KEY)
}

export function setToken(token: string) {
  localStorage.setItem(TOKEN_KEY, token)
}

export function clearToken() {
  localStorage.removeItem(TOKEN_KEY)
}

export function getOAuth2Url(corp_id: string, agent_id: string, redirectUri: string) {
  const redirectbase = `${window.location.origin}/${basePrefix}/login?appid=${corp_id}&agentid=${agent_id}&redirectUri=${redirectUri}`
  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corp_id}&redirect_uri=${encodeURIComponent(redirectbase)}&agentid=${agent_id}&response_type=code&scope=snsapi_base&state=#wechat_redirect`
}

export function redirectToOAuth2(corp_id: string, agent_id: string, redirectUri: string) {
  const oauth2Url = getOAuth2Url(corp_id, agent_id, redirectUri)
  window.location.href = oauth2Url
}
