import { UPLOAD_CDN_URL } from '@/constants'
import { GetSignApi, GetSignApiWithRequest } from '@/service/api/upload'
import { type RootState, store } from '@/store'
import { message } from 'antd'
import { useSelector } from 'react-redux'
import { isBuild } from '.'

// 上传图片 获取auth，Policy
/*
 scene 场景值，区分上传文件的根路径
 type  类型值，区分上传业务bucket
 name 文件名称
 status: 1 测试， 2正式
*/
export enum UploadWay {
  TEST = 1,
  PROD = 2,
}

function getSecret(scene: string, name: string = '', status = UploadWay.TEST) {
  return new Promise(async (resolve, reject) => {
    // const { mutateAsync: GetSign, status, error } = GetSignApi()
    // const SAVE_PATH = name ? `/${scene}/${name}` : `/${scene}/{filemd5}{day}{hour}{min}{sec}{.suffix}`
    const SAVE_PATH = `/${scene}/{filemd5}/${name}`

    const params = {
      method: 'post',
      save_key: SAVE_PATH,
      status,
    }
    try {
    // 获取签名
      const res = await GetSignApiWithRequest(params)
      resolve(res)
    }
    catch (e) {
      reject({
        code: status || '9999',
        msg: error,
      })
    }
  })
}
/**
 * 获取文件类型，这里取得地址后缀前几位进行匹配
 * @param name
 * @returns
 */
export function getFileType(name: string) {
  if (!name)
    return false
  const suffixStr = name.slice(((name.lastIndexOf('.') - 1) >>> 0) + 2)
  const imgType = ['gif', 'jpeg', 'jpg', 'bmp', 'png', 'jfif']
  const videoType = ['avi', 'wmv', 'mkv', 'mp4', 'mov', 'rm', '3gp', 'flv', 'mpg', 'rmvb', 'quicktime']
  const officeType = ['xls', 'XLS', 'xlsx', 'XLSX', 'doc', 'DOC', 'docx', 'DOCX', 'pdf', 'PDF', 'PPT', 'PPTX', 'ppt', 'pptx']
  if (new RegExp(`^(${imgType.join('|')})`, 'i').test(suffixStr?.toLowerCase())) {
    return 'image'
  }
  else if (new RegExp(`^(${videoType.join('|')})`, 'i').test(suffixStr?.toLowerCase())) {
    return 'video'
  }
  else if (new RegExp(`^(${officeType.join('|')})`, 'i').test(suffixStr?.toLowerCase())) {
    return 'office'
  }
  else {
    return false
  }
}

// 判断文件是否为pdf
export function fileIsPdf(name: string) {
  if (!name)
    return false
  const suffixStr = name.slice(((name.lastIndexOf('.') - 1) >>> 0) + 2)
  const officeType = ['pdf', 'PDF']
  if (new RegExp(`^(${officeType.join('|')})`, 'i').test(suffixStr?.toLowerCase())) {
    return 'office'
  }
  else {
    return false
  }
}

/**
 *
 * @param {*} file 传入文件
 * @param {string} secene 传入 'product'
 * @param name
 * @param {UploadWay} status
 * @returns
 */
function uploadCDNImg(file: any, secene: string = 'product', name: string = '', status = UploadWay.TEST) {
  // var file = event.target.files[0];
  // var filetype = file.type
  const token = store.getState().auth.token

  return new Promise<Api.UploadQNY.Response>((resolve: any, reject: any) => {
    const filetype = file.type

    let messageKey: any
    if (!getFileType(file.name)) {
      message.error('上传文件类型错误')
      return false
    }

    getSecret(secene, name, status)
      .then((result: any) => {
        const formdata = new FormData()
        formdata.append('authorization', result.authorization)
        formdata.append('policy', result.policy)
        formdata.append('file', file)
        // message = ElMessage({
        //   message: '上传中...',
        //   type: 'info',
        //   duration: 0,
        // })
        messageKey = message.info('上传中...')
        const baseURL = isBuild() ? `${UPLOAD_CDN_URL}` : `/upyun`
        const url = `${baseURL}/${result.bucket}`
        console.log('url', url)
        fetch(url, {
          method: 'POST',
          // headers: {
          //   'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryzmjJOl7BNy2UskMb',
          // },
          body: formdata,
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }
            return response.json()
          })
          .then((data) => {
            message.destroy(messageKey)
            resolve(data)
          })
          .catch((error) => {
            message.destroy(messageKey)
            message.error('上传失败')
            reject(error)
          })
      })
      .catch((result) => {
        reject(result)
        message.error('获取密钥失败！')
      })
  })
}

/**
 *
 * @param {*} file 传入文件
 * @param {string} secene 传入 'product'
 * @param {string} type  传入 'product'
 * @returns
 */
export function uploadCDNFile(file: any, secene: 'product', type: 'product') {
  return new Promise<Api.UploadQNY.Response>((resolve: any, reject: any) => {
    const filetype = file.type
    let messageKey: any
    getSecret(secene)
      .then((result: any) => {
        const formdata = new FormData()
        formdata.append('authorization', result.authorization)
        formdata.append('policy', result.policy)
        formdata.append('file', file)
        messageKey = message.info('上传中...')
        // messages = ElMessage({
        //   message: '上传中...',
        //   type: 'info',
        //   duration: 0,
        // })
        const baseURL = isBuild() ? `${UPLOAD_CDN_URL}` : `/upyun`
        const url = `${baseURL}/${result.bucket}`

        fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          body: formdata,
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error(`上传失败: ${response.status}`)
            }
            return response.json()
          })
          .then((data) => {
            message.destroy(messageKey)
            resolve(data)
          })
          .catch((error) => {
            message.destroy(messageKey)
            message.error('上传失败')
            reject(error)
          })
      })
      .catch((result) => {
        reject(result)
        message.error('获取密钥失败！')
      })
  })
}

export default uploadCDNImg
