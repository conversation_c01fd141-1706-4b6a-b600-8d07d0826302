import childProcess from 'node:child_process'

function getTimeByTimeZone(timeZone: number) {
  const d = new Date()
  const localTime = d.getTime()
  const localOffset = d.getTimezoneOffset() * 60000 // 获得当地时间偏移的毫秒数,这里可能是负数
  const utc = localTime + localOffset // utc即GMT时间
  const offset = timeZone // 时区，北京市+8  美国华盛顿为 -5
  const localSecondTime = utc + 3600000 * offset // 本地对应的毫秒数
  const date = new Date(localSecondTime)
  //
  //

  return date
}
function formatNumber(n: number) {
  return n < 10 ? `0${n}` : n.toString()
}
function formatTime(date: Date) {
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  const day = date.getDate()
  const hour = date.getHours()
  const minute = date.getMinutes()
  const second = date.getSeconds()

  return `${[year, month, day].map(formatNumber).join('/')} ${[hour, minute, second].map(formatNumber).join(':')}`
}
export function getVersion() {
  const versions
    = process.env.DRONE_TAG || process.env.DRONE_BRANCH
      ? process.env.DRONE_TAG
        ? process.env.DRONE_TAG
        : process.env.DRONE_BRANCH
      : JSON.stringify(
          childProcess.execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }) != 'HEAD\n'
            ? childProcess.execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' })
            : childProcess.execSync('git describe --tags --abbrev=0', { encoding: 'utf8' }),
        )
  const githash = process.env.DRONE_COMMIT ? process.env.DRONE_COMMIT.substring(0, 8) : JSON.stringify(childProcess.execSync('git rev-parse --short HEAD', { encoding: 'utf8' }))

  const CURRENT_GITHASH = githash.replace(/"|\\n/g, '')
  const CURRENT_VERSION = `Version: ${versions} ${process.env.DRONE_TAG ? '' : CURRENT_GITHASH} ${formatTime(getTimeByTimeZone(8))}`.replace(/"|\\n/g, '')
  return {
    CURRENT_GITHASH,
    CURRENT_VERSION,
  }
}
