import { FetchQYWechatSignatureWithRequest, GetIDByQywxUserIdWithRequest } from '@/service/api/customer.ts'
import { message } from 'antd'
import { randomString } from '.'
// https://hcscmtest.zzfzyc.com/qywx_workspace/oauth2?appid=wpr_caCgAAD6gDyRB6UURymefIKEn0zg&agentid=1000030&redirectUri=/customer/infos

interface WechatConfig {
  timestamp: number
  nonceStr: string
  agentSignature: string
  signature: string
}

interface CustomerInfo {
  customerId: number
  customerName: string
  corpGroupChatId?: string
  entry: string
}

const jsApiList = ['getContext', 'getCurExternalContact', 'getCurCorpGroupContact', 'getCurExternalChat', 'launchMiniprogram']
function getWecomParams() {
  const searchParams = new URLSearchParams(window.location.search)
  const appid = searchParams.get('appid') || ''
  const agentid = searchParams.get('agentid') || ''
  // 兼容老代码，APP_ID 就用 appid
  return {
    CORP_ID: appid,
    AGENT_ID: agentid,
    APP_ID: appid,
  }
}
/**
 * 初始化企业微信配置
 */
async function initWechatConfig(config: WechatConfig, corpId?: string, agentId?: string): Promise<void> {
  // 优先使用传入的参数，否则从 URL 参数获取 CORP_ID 是企业微信公司后台的企业ID 在 我的企业 -》 企业ID可以看到
  const { CORP_ID, AGENT_ID } = corpId && agentId
    ? { CORP_ID: corpId, AGENT_ID: agentId }
    : getWecomParams() // 动态获取
  return new Promise<void>((resolve, reject) => {
    wx.config({
      beta: true,
      debug: false,
      appId: CORP_ID,
      timestamp: config.timestamp,
      nonceStr: config.nonceStr,
      signature: config.signature,
      jsApiList,
    })

    wx.ready(() => {
      console.log('wx ready', corpId, agentId)
      wx.agentConfig({
        corpid: CORP_ID,
        agentid: AGENT_ID,
        timestamp: config.timestamp,
        nonceStr: config.nonceStr,
        signature: config.agentSignature,
        jsApiList,
        success: (res) => {
          console.log('agentConfig success', res)
          resolve()
        },
        fail: (res) => {
          if (res.errMsg.includes('function not exist')) {
            message.error('版本过低请升级')
          }
          reject(res)
        },
      })
    })

    wx.error((res: any) => {
      console.error('wx config信息验证', res)
      reject(res)
    })
  })
}

/**
 * 获取企业微信签名
 */
async function getWechatSignature(): Promise<WechatConfig> {
  const timestamp = Number((+new Date() / 1000).toFixed(0))
  const noncestr = randomString(6)
  try {
    const res = await FetchQYWechatSignatureWithRequest({
      nonceStr: noncestr,
      timestamp,
      url: window.location.href,
    })

    if (!res.app_signature || !res.corp_signature) {
      message.error('企业接口认证过期')
      throw new Error('企业接口认证过期')
    }
    return {
      timestamp,
      nonceStr: noncestr,
      agentSignature: res.app_signature,
      signature: res.corp_signature,
    }
  }
  catch (e) {
    throw new Error('获取企业微信签名失败')
  }
}

/**
 * 获取当前上下文信息
 */
function getContext(): Promise<{ entry: string }> {
  return new Promise((resolve, reject) => {
    wx.invoke('getContext', {}, (res) => {
      if (res.err_msg === 'getContext:ok') {
        console.log('getContext', res)
        resolve({ entry: res.entry })
      }
      else {
        console.error('错误: getContext', res)
        reject(new Error('获取上下文失败'))
      }
    })
  })
}

/**
 * 通过外部联系人ID获取客户ID
 */
async function getCurrentExternalId(): Promise<{ customer_id: number, customer_name: string }> {
  return new Promise<{ customer_id: number, customer_name: string }>((resolve, reject) => {
    wx.invoke('getCurExternalContact', {}, async (res) => {
      if (res.err_msg === 'getCurExternalContact:ok') {
        try {
          const result = await GetIDByQywxUserIdWithRequest({
            qywx_customer_id: res.userId,
          })

          resolve({ customer_id: result.customer_id || 0, customer_name: result.customer_name || '' })
        }
        catch (error) {
          message.error(`获取客户ID失败：${error.message}`)
          reject(new Error('请求失败：获取客户ID失败'))
        }
      }
      else {
        const error = '错误: getCurExternalContact'
        message.error(error)
        console.error(error, res)
        reject(new Error(error))
      }
    })
  })
}

/**
 * 获取外部群聊ID
 */
function getCurExternalChatId(): Promise<{ customer_id: number, customer_name: string }> {
  return new Promise<{ customer_id: number, customer_name: string }>((resolve, reject) => {
    wx.invoke('getCurExternalChat', {}, async (res) => {
      if (res.err_msg === 'getCurExternalChat:ok') {
        try {
          const result = await GetIDByQywxUserIdWithRequest({
            qywx_customer_id: res.chatId,
          })

          resolve({ customer_id: result.customer_id || 0, customer_name: result.customer_name || '' })
        }
        catch (e) {
          message.error(`获取客户ID失败：${e.message}`)
          reject(new Error('请求失败：获取客户ID失败'))
        }
      }
      else {
        const error = '错误: getCurExternalChat'
        message.error(error)
        console.error(error, res)
        reject(new Error(error))
      }
    })
  })
}

/**
 * 判断是否为单聊场景
 */
function isSingleChatEntry(entry: string): boolean {
  const singleEntries = ['single_chat_tools', 'contact_profile', 'chat_attachment_menu']
  return singleEntries.includes(entry)
}

/**
 * 判断是否为群聊场景
 */
function isGroupChatEntry(entry: string): boolean {
  const groupEntries = ['group_chat_tools', 'group_chat_attachment_menu']
  return groupEntries.includes(entry)
}

/**
 * 主要方法：获取客户信息
 * @param options 配置选项
 * @returns Promise<CustomerInfo> 客户信息
 */
export async function getWechatCustomerInfo(options?: {
  skipWechatInit?: boolean // 是否跳过微信初始化（用于小程序环境）
  corpId?: string // 企业ID
  agentId?: string // 应用ID
}): Promise<CustomerInfo> {
  try {
    // 如果不跳过微信初始化，则进行企业微信配置
    if (!options?.skipWechatInit) {
      const config = await getWechatSignature()
      console.log('getWechatSignature', config)
      await initWechatConfig(config, options?.corpId, options?.agentId)
    }

    // 获取上下文信息
    const { entry } = await getContext()
    console.log('getContext', entry)

    let customerId = 0
    let customerName = ''
    const corpGroupChatId = ''

    // 根据entry类型获取相应的ID
    if (isSingleChatEntry(entry)) {
      // 单聊场景：获取外部联系人ID
      const customer = await getCurrentExternalId()
      customerId = customer.customer_id
      customerName = customer.customer_name
    }
    else if (isGroupChatEntry(entry)) {
      // 群聊场景：获取群聊ID
      const customer = await getCurExternalChatId()
      customerId = customer.customer_id
      customerName = customer.customer_name
    }
    else {
      throw new Error(`不支持的entry类型: ${entry}`)
    }

    return {
      customerId,
      customerName,
      corpGroupChatId,
      entry,
    }
  }
  catch (error) {
    console.error('获取客户信息失败:', error)
    throw error
  }
}
