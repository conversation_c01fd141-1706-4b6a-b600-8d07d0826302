// 判断是否为移动设备
export function isMobile() {
  // 方法一：通过屏幕宽度判断
  const screenWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth
  return screenWidth <= 768

  // 方法二：通过 userAgent 判断（更准确但可能被伪装）
  // return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 监听屏幕变化
export function listenScreenChange(callback: (isMobile: boolean) => void) {
  const handleResize = () => {
    callback(isMobile())
  }

  window.addEventListener('resize', handleResize)
  return () => window.removeEventListener('resize', handleResize)
}
