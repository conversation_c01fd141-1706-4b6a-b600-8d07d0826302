export const IMG_CND_Prefix = import.meta.env.VITE_IMG_CND_PREFIX
// 匹配前缀 例如：//xxx.com  或者 http://xxx.com 或者 https://xxx.com

export const URL_REGEXP = /(https?:)?\/\/(www\.)?[-\w@:%.+~#=]{1,256}\.[a-z0-9()]{1,6}\b([-\w()!@:%+.~#?&=]*)/gi

/**
 * 判断是否有前缀
 * @param {string} url url路径
 */
export const withBaseUrl = function (url: string) {
  return URL_REGEXP.test(url)
}
/**
 * 去除前缀 //xxx.com  或者 http://xxx.com 或者 https://xxx.com
 * @param {string} url url路径
 */
export function breakupUrl(url: string) {
  return url?.replace(URL_REGEXP, '') ?? url
}
/**
 * 拼接前缀 http://xxx.com + /asdfsafdas/154531asdf465413.png
 * @param {string} url url路径
 */
export function jointUrl(url: string) {
  return withBaseUrl(url) ? url : `${IMG_CND_Prefix}${url}`
}

export function formatUrl(url: string, suffix = '!w200') {
  if (url) {
    url = url.includes(',') ? url.split(',')[0] : url

    // 检查URL是否已经包含!w开头的suffix（如!w200、!w400等）
    const hasSuffix = /!w\d+/.test(url)
    console.log('withBaseUrl',withBaseUrl(url))
    if (hasSuffix) {
      // 如果已经有suffix，直接返回原URL
      return withBaseUrl(url) ? url : jointUrl(url)
    }
    else {
      // 如果没有suffix，添加新的suffix
      return withBaseUrl(url) ? url + suffix : jointUrl(url) + suffix
    }
  }
  else {
    return `${IMG_CND_Prefix}/mall/no_img.png`
  }
}
export function getUuid() {
  const s: string[] = []
  const hexDigits = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4'
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1)
  s[8] = s[13] = s[18] = s[23] = '-'
  const uuid = s.join('')
  return uuid
}
