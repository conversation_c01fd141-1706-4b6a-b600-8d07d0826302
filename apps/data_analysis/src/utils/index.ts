import { REG_PHONE } from '@/constants/reg'
import { useEffect, useState } from 'react'

export function useWindowSize() {
  const [size, setSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  })

  useEffect(() => {
    const onResize = () => {
      setSize({
        width: window.innerWidth,
        height: window.innerHeight,
      })
    }

    window.addEventListener('resize', onResize)

    return () => {
      window.removeEventListener('resize', onResize)
    }
  }, [])

  return size
}

export const MOBILE_MAX_WIDTH = 600

export function useMobileScreen() {
  const { width } = useWindowSize()

  return width <= MOBILE_MAX_WIDTH
}

export function isPhoneValid(phone: string) {
  if (phone.trim() === '') {
    return false
  }

  if (!REG_PHONE.test(phone)) {
    return false
  }

  return true
}

export function isBuild() {
  return import.meta.env.PROD || import.meta.env.MODE === 'production'
}
export function randomString(length: number) {
  const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length)
    result += characters.charAt(randomIndex)
  }
  return result
}
/**
 * 判断是否在企业微信内置浏览器中
 */
export function isWxWork(): boolean {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('wxwork') || ua.includes('wechat')
}

/**
 * 判断是否在企业微信移动端内置浏览器中
 */
export function isWxWorkMobile(): boolean {
  const ua = navigator.userAgent.toLowerCase()
  return isWxWork() && (ua.includes('android') || ua.includes('iphone'))
}
export function full(element: HTMLElement) {
  if (element.requestFullscreen) {
    element.requestFullscreen()
  }
  else if ((element as any).webkitRequestFullscreen) {
    (element as any).webkitRequestFullscreen()
  }
  else if ((element as any).mozRequestFullScreen) {
    (element as any).mozRequestFullScreen()
  }
  else if ((element as any).msRequestFullscreen) {
    (element as any).msRequestFullscreen()
  }
}
export function exitFullscreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen()
  }
  else if ((document as any).webkitExitFullscreen) {
    (document as any).webkitExitFullscreen()
  }
  else if ((document as any).mozCancelFullScreen) {
    (document as any).mozCancelFullScreen()
  }
  else if ((document as any).msExitFullscreen) {
    (document as any).msExitFullscreen()
  }
}
export function isFullScreen() {
  return !!(
    document.fullscreen
    || document.mozFullScreen
    || document.webkitIsFullScreen
    || document.webkitFullScreen
    || document.msFullScreen
  )
}
export function getFullscreenElement() {
  return (
    document.fullscreenElement
    || (document as any).webkitFullscreenElement
    || (document as any).mozFullScreenElement
    || (document as any).msFullscreenElement
  )
}
export function isFullscreenEnabled() {
  return (
    document.fullscreenEnabled
    || document.mozFullScreenEnabled
    || document.webkitFullscreenEnabled
    || document.msFullscreenEnabled
  )
}

/**
 * gcj坐标计算距离
 */
export function calculateLineDistance(start, end) {
  const d1 = 0.01745329251994329
  let d2 = start.longitude
  let d3 = start.latitude
  let d4 = end.longitude
  let d5 = end.latitude
  d2 *= d1
  d3 *= d1
  d4 *= d1
  d5 *= d1
  const d6 = Math.sin(d2)
  const d7 = Math.sin(d3)
  const d8 = Math.cos(d2)
  const d9 = Math.cos(d3)
  const d10 = Math.sin(d4)
  const d11 = Math.sin(d5)
  const d12 = Math.cos(d4)
  const d13 = Math.cos(d5)
  const arrayOfDouble1 = []
  const arrayOfDouble2 = []
  arrayOfDouble1.push(d9 * d8)
  arrayOfDouble1.push(d9 * d6)
  arrayOfDouble1.push(d7)
  arrayOfDouble2.push(d13 * d12)
  arrayOfDouble2.push(d13 * d10)
  arrayOfDouble2.push(d11)
  const d14 = Math.sqrt(
    (arrayOfDouble1[0] - arrayOfDouble2[0]) * (arrayOfDouble1[0] - arrayOfDouble2[0])
    + (arrayOfDouble1[1] - arrayOfDouble2[1]) * (arrayOfDouble1[1] - arrayOfDouble2[1])
    + (arrayOfDouble1[2] - arrayOfDouble2[2]) * (arrayOfDouble1[2] - arrayOfDouble2[2]),
  )

  return Math.asin(d14 / 2.0) * 12742001.579854401
}
interface CalculateMinResult {
  errCode: number
  errMsg: string
  minData: Record<string, any>
  res: Record<string, any>
}
export function calculateMin({ res, location }): CalculateMinResult {
  const list = res.result.pois
  const minData = {
    minDis: 10000,
    data: -1,
  }
  for (let i = 0; i < list.length; i++) {
    const value = list[i]
    const dis = calculateLineDistance(location, {
      latitude: value.location.lat,
      longitude: value.location.lng,
    })
    if (minData.minDis > dis) {
      minData.minDis = dis
      minData.data = value
    }
  }
  res.result.address = res.result.address.replace('\n', ',')
  const str
    = `${res.result.address
    }距离${
      minData.data.title
    }约${
      minData.minDis.toFixed(0)
    }米`
  return {
    errCode: 1,
    errMsg: str,
    minData,
    res,
  }
}
