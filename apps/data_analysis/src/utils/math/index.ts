/**
 * 处理数值显示
 * @param source 任意类型的输入值
 * @param defaultValue 默认返回值
 * @returns 如果是有效数字则返回格式化后的字符串，空值或非法值返回默认值，其他情况返回原始值
 */
export function formatValue(source: any, defaultValue: string = '--'): number | string {
  try {
    // 处理空值情况
    if (source === null || source === undefined || source === '') {
      return defaultValue
    }

    // 处理非法值（函数或对象）
    if (typeof source === 'function'
      || (typeof source === 'object' && !Array.isArray(source))) {
      return JSON.stringify(source)
    }

    // 如果是字符串，尝试提取数字部分
    if (typeof source === 'string') {
      // 使用正则表达式匹配数字部分（包括小数）
      const numberMatch = source.match(/(\d+(?:\.\d+)?)/)
      if (numberMatch) {
        const numberPart = numberMatch[1]
        const num = Number(numberPart)
        if (!Number.isNaN(num)) {
          // 格式化数字部分，然后替换原字符串中的数字
          const formattedNumber = num.toLocaleString('zh-CN')
          return source.replace(numberPart, formattedNumber)
        }
      }
      // 如果字符串中没有找到数字，尝试直接转换
      const num = Number(source)
      if (!Number.isNaN(num)) {
        return num.toLocaleString('zh-CN')
      }
      // 无法转换的字符串直接返回
      return source
    }

    // 转换为数字
    const num = Number(source)

    // 如果是有效数字（包括0），返回千分位格式化的字符串
    if (!Number.isNaN(num)) {
      return num.toLocaleString('zh-CN')
    }

    // 其他情况返回原始值
    return source
  }
  catch {
    return defaultValue
  }
}
