import { message } from 'antd'
import { Toast } from 'antd-mobile'
import { useMobileScreen } from '.'

type MessageType = 'success' | 'error' | 'info' | 'warning' | 'loading'

interface MessageOptions {
  duration?: number
  icon?: React.ReactNode
}
// 统一的消息提示函数

export function useMessage() {
  const isMobile = useMobileScreen()
  const [messageApi, contextHolder] = message.useMessage()
  const showMessage: Record<MessageType, (content: string, options?: MessageOptions) => void> = {
    success(content: string, options?: MessageOptions) {
      if (isMobile) {
        Toast.show({
          content,
          icon: 'success',
          duration: options?.duration ?? 2000,
        })
      }
      else {
        messageApi.success({
          content,
          duration: options?.duration ?? 2,
          icon: options?.icon,
        })
      }
    },

    error(content: string, options?: MessageOptions) {
      if (isMobile) {
        Toast.show({
          content,
          icon: 'fail',
          duration: options?.duration ?? 2000,
        })
      }
      else {
        messageApi.error({
          content,
          duration: options?.duration ?? 2,
          icon: options?.icon,
        })
      }
    },

    warning(content: string, options?: MessageOptions) {
      if (isMobile) {
        Toast.show({
          content,
          icon: 'warn',
          duration: options?.duration ?? 2000,
        })
      }
      else {
        messageApi.warning({
          content,
          duration: options?.duration ?? 2,
          icon: options?.icon,
        })
      }
    },

    info(content: string, options?: MessageOptions) {
      if (isMobile) {
        Toast.show({
          content,
          duration: options?.duration ?? 2000,
        })
      }
      else {
        messageApi.info({
          content,
          duration: options?.duration ?? 2,
          icon: options?.icon,
        })
      }
    },

    loading(content: string, options?: MessageOptions) {
      if (isMobile) {
        Toast.show({
          content,
          icon: 'loading',
          duration: options?.duration ?? 2000,
        })
      }
      else {
        messageApi.loading({
          content,
          duration: options?.duration ?? 2,
          icon: options?.icon,
        })
      }
    },
  }
  return {
    contextHolder,
    showMessage,
  }
}
