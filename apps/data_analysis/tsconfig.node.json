{
  "compilerOptions": {
    "jsx": "react",
    "composite": true,
    "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.node.tsbuildinfo",
    "target": "ES2022",
    "lib": ["ES2023"],
    "moduleDetection": "force",
    "baseUrl": ".",
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "paths": {
      "@/*": [
        "./src/*"
      ]
    },
    "allowImportingTsExtensions": true,

    /* Linting */
    "strict": true,
    "noFallthroughCasesInSwitch": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noEmit": true,
    "isolatedModules": true,
    "skipLibCheck": true
  },
  "include": [
    "vite.config.ts",
    "src/**/*"  // 添加这一行
  ],
  "exclude": ["node_modules", "dist"]
}
