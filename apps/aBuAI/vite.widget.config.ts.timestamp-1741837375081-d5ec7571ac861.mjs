// vite.widget.config.ts
import { defineConfig } from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/vite@5.4.11_@types+node@22.9.0_sass@1.83.0_terser@5.36.0/node_modules/vite/dist/node/index.js";
import react from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/@vitejs+plugin-react@4.3.3_vite@5.4.11_@types+node@22.9.0_sass@1.83.0_terser@5.36.0_/node_modules/@vitejs/plugin-react/dist/index.mjs";
import { resolve } from "path";
import svgr from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/vite-plugin-svgr@4.3.0_rollup@4.29.1_typescript@5.6.3_vite@5.4.11_@types+node@22.9.0_sass@1.83.0_terser@5.36.0_/node_modules/vite-plugin-svgr/dist/index.js";
import copy from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/rollup-plugin-copy@3.5.0/node_modules/rollup-plugin-copy/dist/index.commonjs.js";
import { createHtmlPlugin } from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/vite-plugin-html@3.2.2_vite@5.4.11_@types+node@22.9.0_sass@1.83.0_terser@5.36.0_/node_modules/vite-plugin-html/dist/index.mjs";
import dynamicImport from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/@rollup+plugin-dynamic-import-vars@2.1.5_rollup@4.29.1/node_modules/@rollup/plugin-dynamic-import-vars/dist/es/index.js";
import tailwindcss from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/tailwindcss@3.4.17/node_modules/tailwindcss/lib/index.js";
import autoprefixer from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/autoprefixer@10.4.20_postcss@8.4.49/node_modules/autoprefixer/lib/autoprefixer.js";
import postcssprefixselector from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/postcss-prefix-selector@2.1.0_postcss@8.4.49/node_modules/postcss-prefix-selector/index.js";
import dts from "file:///D:/learn/wx-qy-program/node_modules/.pnpm/vite-plugin-dts@4.5.3_@types+node@22.9.0_rollup@4.29.1_typescript@5.6.3_vite@5.4.11_@types+no_cvzq7zmopmpf6hphywpo27c5cq/node_modules/vite-plugin-dts/dist/index.mjs";
var __vite_injected_original_dirname = "D:\\learn\\wx-qy-program\\apps\\aBuAI";
var vite_widget_config_default = defineConfig(({ command }) => {
  console.log("command", command);
  return {
    plugins: [
      react({
        babel: {
          plugins: [
            ["babel-plugin-react-css-modules", {
              generateScopedName: "[local]_[hash:base64:5]",
              filetypes: {
                ".scss": {
                  syntax: "postcss-scss"
                }
              }
            }]
          ]
        }
      }),
      svgr({
        include: "**/*.svg",
        exclude: "",
        exportAsDefault: true,
        svgrOptions: {
          icon: true,
          svgoConfig: {
            plugins: [
              {
                name: "removeViewBox",
                active: false
              }
            ]
          }
        }
      }),
      createHtmlPlugin({
        template: "widget.template.html",
        inject: {
          data: {
            title: "\u963F\u5E03\u667A\u80FD\u52A9\u624B"
          },
          tags: command === "serve" ? [
            {
              injectTo: "head",
              tag: "script",
              attrs: {
                src: "https://unpkg.com/react@18/umd/react.development.js"
              }
            },
            {
              injectTo: "head",
              tag: "script",
              attrs: {
                src: "https://unpkg.com/react-dom@18/umd/react-dom.development.js"
              }
            },
            {
              injectTo: "body",
              tag: "script",
              attrs: {
                type: "module"
              },
              children: `
                import { init } from '/app/init.tsx'
                window.ABuAI = { init }
              `
            }
          ] : []
        }
      }),
      dynamicImport({
        include: [
          "./app/components/interactive/**/*.tsx"
        ],
        exclude: [
          "node_modules/**"
        ],
        warnOnError: true
      }),
      dts({
        insertTypesEntry: true,
        outDir: ".widget",
        include: ["app/init.tsx"],
        exclude: ["app/**/*.test.ts", "app/**/*.test.tsx"]
      })
    ],
    build: {
      lib: {
        entry: resolve(__vite_injected_original_dirname, "app/init.tsx"),
        name: "ABuAI",
        formats: ["es"],
        fileName: (format) => `abu-ai.${format}.js`
      },
      rollupOptions: {
        external: ["react", "react-dom"],
        output: {
          globals: {
            react: "React",
            "react-dom": "ReactDOM"
          },
          // format: 'umd',
          name: "ABuAI",
          assetFileNames: (assetInfo) => {
            if (assetInfo.name === "style.css") {
              return "abu-ai.css";
            }
            return assetInfo.name || "assets/[name]-[hash][extname]";
          },
          inlineDynamicImports: true,
          // manualChunks: (id) => {
          //   if (id.includes('node_modules')) {
          //     return 'vendor';
          //   }
          //   // interactive 相关的模块单独打包
          //   if (id.includes('interactive')) {
          //     return 'interactive';
          //   }
          // },
          // 配置动态导入的文件命名格式
          chunkFileNames: (chunkInfo) => {
            if (chunkInfo.name?.includes("interactive")) {
              return "interactive/[name].[hash].js";
            }
            return "[name].[hash].js";
          },
          // 确保生成的文件名一致
          entryFileNames: "abu-ai.es.js"
        },
        plugins: [
          copy({
            targets: [
              { src: "public/prompts.json", dest: ".widget" },
              { src: "app/asserts/icons/*", dest: ".widget/icons" }
            ]
          })
        ]
      },
      chunkSizeWarningLimit: 1e3,
      modulePreload: {
        polyfill: false
      },
      sourcemap: true,
      cssCodeSplit: false,
      outDir: ".widget",
      emptyOutDir: true
    },
    resolve: {
      alias: {
        "@": resolve(__vite_injected_original_dirname, "app"),
        "@asserts": resolve(__vite_injected_original_dirname, "app/asserts"),
        "abu-ai": resolve(__vite_injected_original_dirname, ".widget/abu-ai.es.js")
      },
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".scss", ".svg"]
    },
    css: {
      modules: {
        localsConvention: "camelCase",
        generateScopedName: "[local]_[hash:base64:5]",
        scopeBehaviour: "local"
      },
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "sass:math";
            @use "sass:color";
          `,
          javascriptEnabled: true
        }
      },
      // extract: {
      //   filename: 'abu-ai.css'
      // },
      devSourcemap: true,
      postcss: {
        plugins: [
          tailwindcss(),
          autoprefixer(),
          {
            postcssPlugin: "shadow-dom-scope",
            Rule(rule) {
              if (!rule.selector.includes(":host")) {
                rule.selector = `:host ${rule.selector}`;
              }
            }
          },
          postcssprefixselector({
            prefix: ":host",
            // 你想要添加的前缀
            exclude: [":root", ":host", "body", "html"],
            // 排除不需要添加前缀的选择器
            transform: function(prefix, selector, prefixedSelector, filePath) {
              if (selector.match(/^(html|body|:root|:host)/)) {
                return selector;
              }
              return prefixedSelector;
            }
          })
        ]
      }
    },
    optimizeDeps: {
      include: ["react", "react-dom"],
      esbuildOptions: {
        target: "es2020"
      }
    },
    define: {
      "process.env": {},
      global: "window",
      // 添加动态导入的全局函数
      "__dynamicImport": "window.__dynamicImport",
      "import.meta.env.BUILD_MODE": JSON.stringify("widget")
    },
    server: {
      port: 8888,
      fs: {
        strict: false,
        allow: ["."]
      }
    },
    base: "./"
  };
});
export {
  vite_widget_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
