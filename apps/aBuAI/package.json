{"name": "@ly/abu-ai", "version": "1.0.0", "private": true, "description": "企业微信侧边栏", "type": "module", "main": ".widget/abu-ai.es.js", "module": ".widget/abu-ai.es.js", "types": ".widget/index.d.ts", "exports": {".": {"import": "./.widget/abu-ai.es.js"}, "./abu-ai.css": "./.widget/abu-ai.css"}, "templateInfo": {"name": "redux", "typescript": true, "css": "Sass", "framework": "React"}, "scripts": {"dev": "next dev --hostname 0.0.0.0", "dev:prod": "cross-env NODE_ENV=production next dev --hostname 0.0.0.0", "build": "cross-env BUILD_MODE=standalone next build", "start": "next start", "lint": "next lint", "lint:fix": "eslint . --fix", "export": "cross-env BUILD_MODE=export next build", "export:dev": "cross-env BUILD_MODE=export next dev", "build:widget": "vite build --config vite.widget.config.ts", "dev:widget": "vite --config vite.widget.config.ts", "preview:widget": "vite preview --config vite.widget.config.ts"}, "browserslist": ["defaults and fully supports es6-module", "maintained node versions"], "author": "", "dependencies": {"@ant-design/cssinjs": "^1.22.1", "@ant-design/icons": "^5.5.2", "@ant-design/nextjs-registry": "^1.0.2", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@ant-design/x": "^1.0.5", "@antfu/eslint-config": "^3.12.1", "@babel/runtime": "^7.21.5", "@fortaine/fetch-event-source": "^3.0.6", "@hello-pangea/dnd": "^17.0.0", "@ly/utils": "workspace:*", "@ly/wecom": "workspace:*", "@rollup/plugin-dynamic-import-vars": "^2.1.5", "@svgr/webpack": "^8.1.0", "@tanstack/react-query": "^5.62.10", "@types/node": "^22.9.0", "@vitejs/plugin-react": "^4.2.0", "antd": "^5.22.5", "classnames": "^2.5.1", "clsx": "^2.1.1", "copy-webpack-plugin": "^12.0.2", "cross-env": "^7.0.3", "currency.js": "^2.0.4", "drag-kit": "^1.0.9", "echarts": "^5.6.0", "emoji-picker-react": "^4.12.0", "eslint": "^9.17.0", "eslint-plugin-unused-imports": "^4.1.4", "fuse.js": "^7.0.0", "heic2any": "^0.0.4", "html-webpack-plugin": "^5.6.3", "idb-keyval": "^6.2.1", "katex": "^0.16.11", "lodash-es": "^4.17.21", "mermaid": "^11.4.0", "mini-css-extract-plugin": "^2.9.2", "nanoid": "^5.0.8", "next": "^15.2.0", "postcss-nested": "^7.0.2", "react": "^18.0.0", "react-dom": "^18.0.0", "react-draggable": "^4.4.6", "react-markdown": "^9.0.1", "react-resizable": "^3.0.5", "react-router": "^6.28.0", "react-router-dom": "^6.28.0", "rehype-highlight": "^7.0.1", "rehype-katex": "^7.0.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0", "sass": "^1.80.7", "spark-md5": "^3.0.2", "use-debounce": "^10.0.4", "vite": "^5.0.0", "zustand": "^5.0.1"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/preset-env": "^7.26.0", "@babel/preset-react": "^7.25.9", "@babel/preset-typescript": "^7.26.0", "@types/react": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.20", "babel-loader": "^9.2.1", "babel-plugin-react-css-modules": "^5.2.6", "css-loader": "^7.1.2", "eslint-config-next": "15.0.3", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "html-loader": "^5.1.0", "markdown-loader": "^8.0.0", "postcss": "^8.4.49", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^2.1.0", "postcss-scss": "^4.0.9", "react-refresh": "^0.11.0", "rollup-plugin-copy": "^3.5.0", "sass": "^1.80.7", "sass-loader": "^16.0.3", "style-loader": "^4.0.0", "stylelint": "^14.4.0", "tailwindcss": "^3.4.17", "terser": "^5.16.8", "ts-loader": "^9.5.1", "typescript": "^5.6.3", "vite": "^4.2.0", "vite-plugin-dts": "^4.5.3", "vite-plugin-html": "^3.2.2", "vite-plugin-svgr": "^4.3.0", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.2.0"}}