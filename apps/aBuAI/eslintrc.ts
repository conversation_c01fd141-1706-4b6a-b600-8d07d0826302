import antfu from '@antfu/eslint-config'
import { FlatCompat } from '@eslint/eslintrc'

const compat = new FlatCompat()
export default antfu(
  {
    react: true,
    typescript: true,
  },
  ...compat.config({
    "extends": ["next/core-web-vitals", "next/typescript"],
    "plugins": ["unused-imports"],
    "rules": {
      "react/jsx-uses-react": "off",
      "react/react-in-jsx-scope": "off",
      "unused-imports/no-unused-imports": "warn",
      "@typescript-eslint/no-explicit-any": "off"
    }
  }))
