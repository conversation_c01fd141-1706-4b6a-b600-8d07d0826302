这是一段文字内容...

```chart
{
  "title": {
    "text": "各客户总购买金额占比",
    "textStyle": {
      "fontSize": 18,
      "color": "#000"
    },
    "top": "center"
  },
  "tooltip": {
    "trigger": "item"
  },
  "series": [{
    "name": "总购买金额",
    "type": "pie",
    "radius": "50%",
    "data": [
      {"value": 18392297, "name": "客户A"},
      {"value": 19578889, "name": "客户B"}
    ]
  }]
}

系统会自动识别 ````chart` 标记，并将其渲染为图表，同时保持文字内容的流式输出。

## 注意事项

1. 确保 JSON 格式正确，否则图表无法渲染
2. 图表配置应遵循 ECharts 的配置格式
3. 在流式输出中，图表可能会在完整接收到图表配置后才开始渲染

这样的实现可以满足你的需求，在大模型流式输出文字的同时，还能在同一段消息中展示图表。
