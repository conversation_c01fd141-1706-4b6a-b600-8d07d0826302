import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'
import svgr from 'vite-plugin-svgr'
import copy from 'rollup-plugin-copy'
import { createHtmlPlugin } from 'vite-plugin-html'
import dynamicImport from '@rollup/plugin-dynamic-import-vars'
import tailwindcss from 'tailwindcss'
import autoprefixer from 'autoprefixer'
import postcssprefixselector from 'postcss-prefix-selector'
import dts from 'vite-plugin-dts'
export default defineConfig(({ command }) => {
  console.log('command', command)
  return {
    plugins: [
      react({
        babel: {
          plugins: [
            ['babel-plugin-react-css-modules', {
              generateScopedName: '[local]_[hash:base64:5]',
              filetypes: {
                '.scss': {
                  syntax: 'postcss-scss'
                }
              }
            }]
          ]
        }
      }),
      svgr({
        include: '**/*.svg',
        exclude: '',
        exportAsDefault: true,
        svgrOptions: {
          icon: true,
          svgoConfig: {
            plugins: [
              {
                name: 'removeViewBox',
                active: false
              }
            ]
          }
        }
      }),
      createHtmlPlugin({
        template: 'widget.template.html',
        inject: {
          data: {
            title: '阿布智能助手',
          },
          tags: command === 'serve' ? [
            {
              injectTo: 'head',
              tag: 'script',
              attrs: {
                src: 'https://unpkg.com/react@18/umd/react.development.js'
              }
            },
            {
              injectTo: 'head',
              tag: 'script',
              attrs: {
                src: 'https://unpkg.com/react-dom@18/umd/react-dom.development.js'
              }
            },
            {
              injectTo: 'body',
              tag: 'script',
              attrs: {
                type: 'module'
              },
              children: `
                import { init } from '/app/init.tsx'
                window.ABuAI = { init }
              `
            }
          ] : []
        },
      }),
      dynamicImport({
        include: [
          './app/components/interactive/**/*.tsx'
        ],
        exclude: [
          'node_modules/**'
        ],
        warnOnError: true
      }),
      dts({
        insertTypesEntry: true,
        outDir: '.widget',
        include: ['app/init.tsx'],
        exclude: ['app/**/*.test.ts', 'app/**/*.test.tsx'],
      }),
    ],

    build: {
      lib: {
        entry: resolve(__dirname, 'app/init.tsx'),
        name: 'ABuAI',
        formats: ['es'],
        fileName: (format) => `abu-ai.${format}.js`
      },
      rollupOptions: {
        external: ['react', 'react-dom'],
        output: {
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
          },
          // format: 'umd',
          name: 'ABuAI',
          assetFileNames: (assetInfo) => {
            if (assetInfo.name === 'style.css') {
              return 'abu-ai.css';
            }
            return assetInfo.name || 'assets/[name]-[hash][extname]';
          },
          inlineDynamicImports: true,
          // manualChunks: (id) => {
          //   if (id.includes('node_modules')) {
          //     return 'vendor';
          //   }
          //   // interactive 相关的模块单独打包
          //   if (id.includes('interactive')) {
          //     return 'interactive';
          //   }
          // },
          // 配置动态导入的文件命名格式
          chunkFileNames: (chunkInfo) => {
            if (chunkInfo.name?.includes('interactive')) {
              return 'interactive/[name].[hash].js';
            }
            return '[name].[hash].js';
          },
          // 确保生成的文件名一致
          entryFileNames: 'abu-ai.es.js',
        },
        plugins: [
          copy({
            targets: [
              { src: 'public/prompts.json', dest: '.widget' },
              { src: 'app/asserts/icons/*', dest: '.widget/icons' },
            ],
          }),
        ],
      },
      chunkSizeWarningLimit: 1000,
      modulePreload: {
        polyfill: false
      },
      sourcemap: true,
      cssCodeSplit: false,
      outDir: '.widget',
      emptyOutDir: true,
    },

    resolve: {
      alias: {
        '@': resolve(__dirname, 'app'),
        '@asserts': resolve(__dirname, 'app/asserts'),
        'abu-ai': resolve(__dirname, '.widget/abu-ai.es.js')
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.svg'],
    },

    css: {
      modules: {
        localsConvention: 'camelCase',
        generateScopedName: '[local]_[hash:base64:5]',
        scopeBehaviour: 'local',
      },
      preprocessorOptions: {
        scss: {
          additionalData: `
            @use "sass:math";
            @use "sass:color";
          `,
          javascriptEnabled: true,
        }
      },
      // extract: {
      //   filename: 'abu-ai.css'
      // },
      devSourcemap: true,
      postcss: {
        plugins: [
          tailwindcss(),
          autoprefixer(),
          {
            postcssPlugin: 'shadow-dom-scope',
            Rule(rule) {
              if (!rule.selector.includes(':host')) {
                rule.selector = `:host ${rule.selector}`;
              }
            },
          },
          postcssprefixselector({
            prefix: ':host',  // 你想要添加的前缀
            exclude: [':root', ':host', 'body', 'html'], // 排除不需要添加前缀的选择器
            transform: function (prefix, selector, prefixedSelector, filePath) {
              if (selector.match(/^(html|body|:root|:host)/)) {
                return selector;
              }
              return prefixedSelector;
            }
          })
        ]
      },
    },

    optimizeDeps: {
      include: ['react', 'react-dom'],
      esbuildOptions: {
        target: 'es2020',
      },
    },

    define: {
      'process.env': {},
      global: 'window',
      // 添加动态导入的全局函数
      '__dynamicImport': 'window.__dynamicImport',
      'import.meta.env.BUILD_MODE': JSON.stringify('widget')
    },

    server: {
      port: 8888,
      fs: {
        strict: false,
        allow: ['.'],
      },
    },

    base: './',
  }
}) 
