declare module 'abu-ai' {
  export interface InitOptions {
    publicPath?: string;
  }

  export function init(containerId: string, options?: InitOptions): Promise<(() => void) | undefined>;
  export function destroy(containerId: string): void;
}

// 声明打包后的文件模块
declare module '/widget/abu-ai.es.js' {
  export * from 'abu-ai';
}

// 声明相对路径导入
declare module './abu-ai.es.js' {
  export * from 'abu-ai';
}

// 全局声明
declare global {
  interface Window {
    ABuAI: {
      init: (containerId: string, options?: import('abu-ai').InitOptions) => Promise<(() => void) | undefined>;
      destroy: (containerId: string) => void;
    };
    __dynamicImport: (path: string) => Promise<any>;
  }
}
