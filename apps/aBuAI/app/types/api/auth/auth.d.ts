declare namespace Api {
  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {

    // 登出响应数据类型
    interface LogoutResponse {
      api_domain: string
      api_prefix: string
    }
    // 企业微信扫码登录请求参数类型
    interface ScanCodeLoginReqest {
      code: string
      corp_id: string
      agent_id: string
    }
    // 登录响应数据类型
    interface LoginResponse {
      success: boolean
      code: number
      message: string
      data: {
        access_token: string
        refresh_token: string
        expires_in: string
      }
      timestamp: string
      path: string
      method: string
    }

    // 旧版本登录响应数据类型（保留兼容性）
    interface LegacyLoginResponse {
      api_domain: string
      api_prefix: string
      default_physical_warehouse_id: number
      default_physical_warehouse_name: string
      default_sale_system_id: number
      default_sale_system_name: string
      tenant_management_id: number
      token: string
      user_id: number
      user_name: string
    }
    /**
     * system.GetLoginInformationData
     */
    export interface UserInfo {
      /**
       * 头像
       */
      avatar_url?: string
      /**
       * 按钮名称
       */
      button_codes?: string[]
      /**
       * 数据脱敏权限(菜单/路由)
       */
      data_access_scope?: string[]
      /**
       * 过期时间
       */
      deadline?: string
      /**
       * 默认所属客户ID
       */
      default_customer_id?: number
      follow_user_id?: string
      /**
       * 默认所属客户名称
       */
      default_customer_name?: string
      /**
       * 默认营销体系ID
       */
      default_sale_system_id?: number
      /**
       * 默认营销体系名称
       */
      default_sale_system_name?: string
      /**
       * 员工id
       */
      employee_id?: number
      /**
       * key_id
       */
      key_id?: string
      leader_menu_list?: SystemLeaderMenu[]
      /**
       * 电话
       */
      phone?: string
      /**
       * 资源路由名称
       */
      resource_router_names?: string[]
      /**
       * 角色ID
       */
      role_id?: number[]
      /**
       * 用户ID
       */
      user_id?: number
      /**
       * 用户名
       */
      user_name?: string
      [property: string]: any
    }

    /**
     * system.LeaderMenu
     */
    export interface SystemLeaderMenu {
      /**
       * 图标
       */
      avatar_url?: string
      id?: number
      /**
       * 菜单名称
       */
      name?: string
      /**
       * 父级菜单ID
       */
      parent_id?: number
      /**
       * 资源ID
       */
      resource_id?: number
      /**
       * 资源名称
       */
      resource_name?: string
      /**
       * 资源路由名称
       */
      resource_router_name?: string
      sub_menu?: SystemLeaderMenu[]
      [property: string]: any
    }

  }

  /**
   * namespace ShouldCollectOrder
   *
   * backend api module: "should_collect_order"
   */
  namespace ShouldCollectOrder {
    // 获取客户欠款单列表请求参数类型
    export interface FetchCustomerBillListParams {
      page?: number
      size?: number
      customer_id?: number
      department_id?: number
      customer_name?: string
      order_follower_id?: number // 销售跟单
      sale_system_id?: number // 营销体系
      sale_user_id?: number // 销售员
      start_time?: string
      end_time?: string
      owe_status?: number
    }
    // 客户欠款单
    interface CustomerBill {
      /**
       * 本期预收(预收总额)
       */
      advance_price?: number
      /**
       * 本期使用预收
       */
      advance_used_price?: number
      /**
       * 本期结余预收(上期预收结余+本期预收-本期使用预收)
       */
      balance_advance_price?: number
      /**
       * 本期结余(上期欠款+本期欠款-本期核销)
       */
      balance_price?: number
      /**
       * 扣款金额
       */
      chargeback_money?: number
      /**
       * 收款金额
       */
      collect_price?: number
      /**
       * 信用额度(取客户的)
       */
      credit_limit?: number
      /**
       * 结算天数或月份或周期(取客户的)
       */
      custom_cycle?: number
      /**
       * 所属客户编号
       */
      customer_code?: string
      /**
       * 所属客户id
       */
      customer_id?: number
      /**
       * 所属客户名称
       */
      customer_name?: string
      /**
       * 折扣金额
       */
      discount_money?: number
      /**
       * 本期实际欠款(本期结余-本期结余预收)
       */
      end_period?: number
      /**
       * 上期预收结余
       */
      last_advance_price?: number
      /**
       * 欠款状态
       */
      owe_status?: number
      /**
       * 欠款状态名称
       */
      owe_status_name?: string
      /**
       * 上期欠款
       */
      period?: number
      /**
       * 优惠金额
       */
      remove_money?: number
      /**
       * 营销体系ID
       */
      sale_system_id?: number
      /**
       * 营销体系名称
       */
      sale_system_name?: string
      /**
       * 销售员id
       */
      sale_user_id?: number
      /**
       * 销售员名称
       */
      sale_user_name?: string
      /**
       * 结算类型(取客户的)
       */
      settle_type?: number
      /**
       * 结算类型名称(取客户的)
       */
      settle_type_name?: string
      /**
       * 应收金额
       */
      total_settle_money?: number
      /**
       * 核销金额
       */
      uncollect_money?: number
      /**
       * 核销金额（实收金额+优惠金额+折扣金额+扣款金额）
       */
      write_off_price?: number
      [property: string]: any
    }
    // 获取客户对账单列表请求参数类型
    export interface FetchCustomerReconciliationListParams {
      page?: number
      size?: number
      customer_id: number
      start_time?: string
      end_time?: string
    }
    /**
     * 客户对账单
     * should_collect_order.GetCustomerReconciliationData
     */
    export interface CustomerReconciliationResponse {
      /**
       * 实收金额
       */
      actually_collect_price?: number
      /**
       * 本期结余(汇总)
       */
      balance?: number
      /**
       * 扣款金额
       */
      chargeback_money?: number
      /**
       * 应收单类型
       */
      collect_type?: number
      /**
       * 应收单类型名称
       */
      collect_type_name?: string
      /**
       * 已收金额(汇总)
       */
      collected_money?: number
      /**
       * 创建时间
       */
      create_time?: string
      /**
       * 创建人
       */
      creator_id?: number
      /**
       * 创建人
       */
      creator_name?: string
      /**
       * 所属客户名称
       */
      customer_name?: string
      /**
       * 折扣金额
       */
      discount_price?: number
      /**
       * 本期实际欠款(本期结余-本期结余预收)
       */
      end_period?: number
      /**
       * 结束日期
       */
      end_time?: string
      /**
       * 记录ID
       */
      id?: number
      /**
       * 单据详情
       */
      item_list?: ShouldCollectOrderOrderDetail[]
      /**
       * 上期结余(汇总)
       */
      last_balance_price?: number
      /**
       * 单据id
       */
      order_id?: number
      /**
       * 单据编号
       */
      order_no?: string
      /**
       * 单据时间
       */
      order_time?: string
      /**
       * 其他金额
       */
      other_price?: number
      /**
       * 备注
       */
      remark?: string
      /**
       * 优惠金额
       */
      remove_money?: number
      /**
       * 应收金额(汇总)
       */
      should_collect_money?: number
      /**
       * 开始日期
       */
      start_time?: string
      /**
       * 账套名称
       */
      tenant_management_name?: string
      /**
       * 汇总金额
       */
      total_price?: number
      /**
       * 修改时间
       */
      update_time?: string
      /**
       * 修改人
       */
      update_user_name?: string
      /**
       * 修改人
       */
      updater_id?: number
      /**
       * 核销金额（实收金额+优惠金额+折扣金额+扣款金额）
       */
      write_off_price?: number
      [property: string]: any
    }

    /**
     * should_collect_order.OrderDetail
     */
    export interface ShouldCollectOrderOrderDetail {
      /**
       * 物料编号
       */
      code?: string
      /**
       * 颜色编号
       */
      color_code?: string
      /**
       * 颜色名称
       */
      color_name?: string
      /**
       * 染厂缸号
       */
      dyelot_number?: string
      /**
       * 计量单位id
       */
      measurement_unit_id?: number
      /**
       * 计量单位名称
       */
      measurement_unit_name?: string
      /**
       * 物料名称
       */
      name?: string
      /**
       * 件数/匹数
       */
      roll?: number
      /**
       * 件数/匹数
       */
      roll_string?: string
      /**
       * 单价/长度单价
       */
      sale_price?: number
      /**
       * 汇总金额
       */
      total_price?: number
      /**
       * 数量总计/长度
       */
      weight?: number
      /**
       * 数量拼接
       */
      weight_string?: string
      [property: string]: any
    }
  }
}
