declare namespace Api.Record {
  export interface Request {
    page?: number;
    size?: number;
    [property: string]: any;
  }
  export interface Response {
    list: List[];
      total: number;
      [property: string]: any;
  }

  export interface List {
      chat_id: string;
      create_time: string;
      title: string;
      [property: string]: any;
  }

}
declare namespace Api.RecordDetail {
  export interface Request {
    chat_id?: string;
    [property: string]: any;
  }
  export interface Response {
    data: {
      code: number;
      data: Data;
      message: string;
      statusText: string;
    }
  }

  export interface Data {
    list: RecordItem[];
    total: number;
  }

  export interface RecordItem {
    _id: string;
    dataId: string;
    hideInUI: boolean;
    historyPreviewLength: number;
    llmModuleAccount: number;
    obj: string;
    time: string;
    totalQuoteList: string;
    totalRunningTime: number;
    value: RecordValue[];
  }

  export interface RecordValue {
    Text: {
      content: string;
    };
    type: string;
  }

}
