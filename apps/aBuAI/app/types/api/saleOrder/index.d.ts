declare namespace Api.GetSaleProductOrder{
  export interface Request {
    /**
     * id
     */
    id: number;
    [property: string]: any;
}
  /**
 * sale.GetSaleProductOrderData
 */
export interface Response {
    /**
     * 审核时间
     */
    audit_date?: string;
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    audit_status?: number;
    /**
     * 订单状态 1待审核 2已审核 3已驳回 4已作废
     */
    audit_status_name?: string;
    /**
     * 审核人ID
     */
    auditor_id?: number;
    /**
     * 审核人ID名称
     */
    auditor_name?: string;
    /**
     * 业务关闭
     */
    business_close?: number;
    /**
     * 业务关闭
     */
    business_close_name?: string;
    /**
     * 业务关闭时间
     */
    business_close_time?: string;
    /**
     * 业务关闭操作人
     */
    business_close_user_id?: number;
    /**
     * 业务关闭操作人名称
     */
    business_close_user_name?: string;
    /**
     * 公司ID
     */
    company_id?: number;
    /**
     * 联系电话
     */
    contact_phone?: string;
    /**
     * 联系人
     */
    contacts?: string;
    /**
     * 创建时间
     */
    create_time?: string;
    /**
     * 创建人
     */
    creator_id?: number;
    /**
     * 创建人
     */
    creator_name?: string;
    /**
     * 所属客户编号
     */
    customer_code?: string;
    /**
     * 所属客户id
     */
    customer_id?: number;
    /**
     * 所属客户名称
     */
    customer_name?: string;
    /**
     * 下单用户所属部门
     */
    department_id?: number;
    /**
     * 下单用户所属部门名称
     */
    department_name?: string;
    /**
     * 记录ID
     */
    id?: number;
    /**
     * 含税项目id
     */
    info_sale_taxable_item_id?: number;
    /**
     * 含税项目名称
     */
    info_sale_taxable_item_name?: string;
    /**
     * 内部备注
     */
    internal_remark?: string;
    /**
     * 单价是否含税
     */
    is_with_tax_rate?: boolean;
    /**
     * 成品销售详情
     */
    item_data?: SaleGetSaleProductOrderDetailData[];
    /**
     * 物流区域
     */
    logistics_area?: string;
    /**
     * 物流公司id
     */
    logistics_company_id?: number;
    /**
     * 物流公司名称
     */
    logistics_company_name?: string;
    /**
     * 编号
     */
    number?: number;
    /**
     * 成品销售单号
     */
    order_no?: string;
    /**
     * 订单日期
     */
    order_time?: string;
    /**
     * 邮费项目 1包邮 2不包邮
     */
    postage_items?: number;
    /**
     * 邮费项目 1包邮 2不包邮
     */
    postage_items_name?: string;
    /**
     * 打印标签(出货标签)
     */
    print_tag?: string;
    /**
     * 加工厂id
     */
    process_factory_id?: number;
    /**
     * 加工厂名称
     */
    process_factory_name?: string;
    /**
     * 收货地址
     */
    receipt_address?: string;
    /**
     * 销售跟单员id
     */
    sale_follower_id?: number;
    /**
     * 销售跟单员名称
     */
    sale_follower_name?: string;
    /**
     * 销售群体id
     */
    sale_group_id?: number;
    /**
     * 销售群体名称
     */
    sale_group_name?: string;
    /**
     * 营销体系ID
     */
    sale_system_id?: number;
    /**
     * 营销体系ID名称
     */
    sale_system_name?: string;
    /**
     * 销售税率
     */
    sale_tax_rate?: number;
    /**
     * 销售员id
     */
    sale_user_id?: number;
    /**
     * 销售员名称
     */
    sale_user_name?: string;
    /**
     * 出货备注
     */
    send_product_remark?: string;
    /**
     * 出货类型 1出货 2销调
     */
    send_product_type?: number;
    /**
     * 出货类型 1出货 2销调
     */
    send_product_type_name?: string;
    /**
     * 结算类型
     */
    settle_type?: number;
    /**
     * 结算类型
     */
    settle_type_name?: string;
    /**
     * 修改时间
     */
    update_time?: string;
    /**
     * 修改人
     */
    update_user_name?: string;
    /**
     * 修改人
     */
    updater_id?: number;
    /**
     * 凭证单号
     */
    voucher_number?: string;
    /**
     * 调入仓库id
     */
    warehouse_id?: number;
    /**
     * 调入仓库名称
     */
    warehouse_name?: string;
    [property: string]: any;
}

/**
 * sale.GetSaleProductOrderDetailData
 */
export interface SaleGetSaleProductOrderDetailData {
    /**
     * 调整空差 /0.1g
     */
    adjust_weight_error?: number;
    /**
     * 辅助单位id
     */
    auxiliary_unit_id?: number;
    /**
     * 辅助单位名称
     */
    auxiliary_unit_name?: string;
    /**
     * 可用数量
     */
    available_weight?: number;
    /**
     * 预约匹数
     */
    book_roll?: number;
    /**
     * 创建时间
     */
    create_time?: string;
    /**
     * 创建人
     */
    creator_id?: number;
    /**
     * 创建人
     */
    creator_name?: string;
    /**
     * 客户款号
     */
    customer_account_num?: string;
    /**
     * 所属客户编号
     */
    customer_code?: string;
    /**
     * 所属客户id
     */
    customer_id?: number;
    /**
     * 所属客户名称
     */
    customer_name?: string;
    /**
     * 缸号
     */
    dyelot_number?: string;
    /**
     * 记录ID
     */
    id?: number;
    /**
     * 是否显示单价
     */
    is_display_price?: boolean;
    /**
     * 长度
     */
    length?: number;
    /**
     * 剪版销售单价(剪板销售价格-剪版优惠单价)
     */
    length_cut_sale_price?: number;
    /**
     * 计量单位id
     */
    measurement_unit_id?: number;
    /**
     * 计量单位名称
     */
    measurement_unit_name?: string;
    /**
     * 剪版优惠单价
     */
    offset_length_cut_sale_price?: number;
    /**
     * 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
     */
    offset_sale_price?: number;
    /**
     * 优惠空差 /0.1g
     */
    offset_weight_error?: number;
    /**
     * 其他金额
     */
    other_price?: number;
    /**
     * 计划单详细id
     */
    plan_detail_id?: number;
    /**
     * StockLength                int    `json:"stock_length"`                   // 库存可用长度
     */
    pmc_grey_plan_order_summary_detail_id?: number;
    /**
     * 成品编号
     */
    product_code?: string;
    /**
     * 颜色编号
     */
    product_color_code?: string;
    /**
     * 颜色id
     */
    product_color_id?: number;
    /**
     * 颜色类别id(关联type_finished_product_kind_id)
     */
    product_color_kind_id?: number;
    /**
     * 颜色类别id(关联type_finished_product_kind_id)名称
     */
    product_color_kind_name?: string;
    /**
     * 颜色名称
     */
    product_color_name?: string;
    /**
     * 成品id
     */
    product_id?: number;
    /**
     * 布种类型id(关联type_grey_fabric_id)
     */
    product_kind_id?: number;
    /**
     * 布种类型id(关联type_grey_fabric_id)名称
     */
    product_kind_name?: string;
    /**
     * 成品等级id
     */
    product_level_id?: number;
    /**
     * 成品等级名称
     */
    product_level_name?: string;
    /**
     * 成品名称
     */
    product_name?: string;
    /**
     * 成品备注
     */
    product_remark?: string;
    /**
     * 采购长度
     */
    purchase_length?: number;
    /**
     * 采购匹数
     */
    purchase_roll?: number;
    /**
     * 采购数量
     */
    purchase_weight?: number;
    /**
     * 备注
     */
    remark?: string;
    /**
     * 匹数
     */
    roll?: number;
    /**
     * 销售等级ID
     */
    sale_level_id?: number;
    /**
     * 销售等级ID名称
     */
    sale_level_name?: string;
    /**
     * 销售单价(销售报价-优惠单价)(大货 散剪)
     */
    sale_price?: number;
    /**
     * 成品销售单id
     */
    sale_product_order_id?: number;
    /**
     * 结算空差 /0.1g
     */
    settle_weight_error?: number;
    /**
     * 欠货长度
     */
    shortage_length?: number;
    /**
     * 欠货匹数
     */
    shortage_roll?: number;
    /**
     * 欠货数量
     */
    shortage_weight?: number;
    /**
     * 剪板销售价格
     */
    standard_length_cut_sale_price?: number;
    /**
     * 标准销售报价(大货 散剪)
     */
    standard_sale_price?: number;
    /**
     * 库存汇总id
     */
    stock_product_id?: number;
    /**
     * 库存备注
     */
    stock_remark?: string;
    /**
     * 库存可用匹数(可用库存)
     */
    stock_roll?: number;
    /**
     * 修改时间
     */
    update_time?: string;
    /**
     * 修改人
     */
    update_user_name?: string;
    /**
     * 修改人
     */
    updater_id?: number;
    /**
     * 出货仓库id
     */
    warehouse_id?: number;
    /**
     * 出货仓库名称
     */
    warehouse_name?: string;
    /**
     * 数量
     */
    weight?: number;
    /**
     * 标准空差 /0.1g
     */
    weight_error?: number;
    [property: string]: any;
}
}
