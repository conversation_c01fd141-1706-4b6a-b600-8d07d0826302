import { Metadata, Viewport } from 'next';
import './app.scss'
import { AntdRegistry } from '@ant-design/nextjs-registry';
export const metadata: Metadata = {
  title: "阿布智能",
  description: "Your personal ChatGPT Chat Bot.",
  appleWebApp: {
    title: "阿布智能",
    statusBarStyle: "default",
  },
  icons: {
    icon: '/chat/favicon.png',
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#fafafa" },
    { media: "(prefers-color-scheme: dark)", color: "#151515" },
  ],
};
export default function RootLayout({
   children,
 }: {
  children: React.ReactNode
}) {
  // 使用常量值或从环境变量获取
  const locale = 'zh_CN';
  return (
    <html lang="en">
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <body monica-locale={locale}>
      <AntdRegistry>
        {children}
      </AntdRegistry>
    </body>
    </html>
  )
}
