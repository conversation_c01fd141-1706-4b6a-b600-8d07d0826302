@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}
@keyframes slide-in-from-top {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0px);
  }
}
.window-header {
  padding: 14px 20px;
  border-bottom: rgba(0, 0, 0, 0.1) 1px solid;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.window-header-title {
  max-width: calc(100% - 100px);
  overflow: hidden;
}
.window-header-title .window-header-main-title {
  font-size: 20px;
  font-weight: bolder;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  max-width: 50vw;
}
.window-header-title .window-header-sub-title {
  font-size: 14px;
}

.window-actions {
  display: inline-flex;
}

.window-action-button:not(:first-child) {
  margin-left: 10px;
}

.light {
  --theme: light;
  /* color */
  --white: white;
  --black: rgb(48, 48, 48);
  --gray: rgb(250, 250, 250);
  --primary: rgb(48, 92, 242);
  --second: rgb(231, 248, 255);
  --hover-color: #f3f3f3;
  --bar-color: rgba(0, 0, 0, 0.1);
  --theme-color: var(--gray);
  /* shadow */
  --shadow: 50px 50px 100px 10px rgb(0, 0, 0, 0.1);
  --card-shadow: 0px 2px 4px 0px rgb(0, 0, 0, 0.05);
  /* stroke */
  --border-in-light: 1px solid rgb(222, 222, 222);
}

.dark {
  --theme: dark;
  /* color */
  --white: rgb(30, 30, 30);
  --black: rgb(187, 187, 187);
  --gray: rgb(21, 21, 21);
  --primary: rgb(48, 92, 242);
  --second: rgb(27 38 42);
  --hover-color: #323232;
  --bar-color: rgba(255, 255, 255, 0.1);
  --border-in-light: 1px solid rgba(255, 255, 255, 0.192);
  --theme-color: var(--gray);
}
.dark div:not(.no-dark) > svg {
  filter: invert(0.5);
}

.mask {
  filter: invert(0.8);
}

:root {
  --theme: light;
  /* color */
  --white: white;
  --black: rgb(48, 48, 48);
  --gray: rgb(250, 250, 250);
  --primary: rgb(48, 92, 242);
  --second: rgb(231, 248, 255);
  --hover-color: #f3f3f3;
  --bar-color: rgba(0, 0, 0, 0.1);
  --theme-color: var(--gray);
  /* shadow */
  --shadow: 50px 50px 100px 10px rgb(0, 0, 0, 0.1);
  --card-shadow: 0px 2px 4px 0px rgb(0, 0, 0, 0.05);
  /* stroke */
  --border-in-light: 1px solid rgb(222, 222, 222);
  --window-width: 90vw;
  --window-height: 90vh;
  --sidebar-width: 300px;
  --window-content-width: calc(100% - var(--sidebar-width));
  --message-max-width: 80%;
  --full-height: 100%;
}

@media only screen and (max-width: 600px) {
  :root {
    --window-width: 100vw;
    --window-height: var(--full-height);
    --sidebar-width: 100vw;
    --window-content-width: var(--window-width);
    --message-max-width: 100%;
  }
  .no-mobile {
    display: none;
  }
}
@media (prefers-color-scheme: dark) {
  :root {
    --theme: dark;
    /* color */
    --white: rgb(30, 30, 30);
    --black: rgb(187, 187, 187);
    --gray: rgb(21, 21, 21);
    --primary: rgb(48, 92, 242);
    --second: rgb(27 38 42);
    --hover-color: #323232;
    --bar-color: rgba(255, 255, 255, 0.1);
    --border-in-light: 1px solid rgba(255, 255, 255, 0.192);
    --theme-color: var(--gray);
  }
  :root div:not(.no-dark) > svg {
    filter: invert(0.5);
  }
}
html {
  height: var(--full-height);
  font-family: "Noto Sans", "SF Pro SC", "SF Pro Text", "SF Pro Icons", "PingFang SC", "Helvetica Neue", "Helvetica", "Arial", sans-serif;
}

body {
  background-color: var(--gray);
  color: var(--black);
  margin: 0;
  padding: 0;
  height: var(--full-height);
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  touch-action: pan-x pan-y;
  overflow: hidden;
}
@media only screen and (max-width: 600px) {
  body {
    background-color: var(--second);
  }
}
body *:focus-visible {
  outline: none;
}

::-webkit-scrollbar {
  --bar-width: 10px;
  width: var(--bar-width);
  height: var(--bar-width);
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--bar-color);
  border-radius: 20px;
  background-clip: content-box;
  border: 1px solid transparent;
}

select {
  border: var(--border-in-light);
  padding: 10px;
  border-radius: 10px;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  cursor: pointer;
  background-color: var(--white);
  color: var(--black);
  text-align: center;
}

label {
  cursor: pointer;
}

input {
  text-align: center;
  font-family: inherit;
}

input[type=checkbox] {
  cursor: pointer;
  background-color: var(--white);
  color: var(--black);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border: var(--border-in-light);
  border-radius: 5px;
  height: 16px;
  width: 16px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

input[type=checkbox]:checked::after {
  display: inline-block;
  width: 8px;
  height: 8px;
  background-color: var(--primary);
  content: " ";
  border-radius: 2px;
}

input[type=range] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: var(--white);
  color: var(--black);
}

input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
          appearance: none;
  height: 8px;
  width: 20px;
  background-color: var(--primary);
  border-radius: 10px;
  cursor: pointer;
  -webkit-transition: all ease 0.3s;
  transition: all ease 0.3s;
  margin-left: 5px;
  border: none;
}

input[type=range]::-moz-range-thumb {
  -moz-appearance: none;
       appearance: none;
  height: 8px;
  width: 20px;
  background-color: var(--primary);
  border-radius: 10px;
  cursor: pointer;
  -moz-transition: all ease 0.3s;
  transition: all ease 0.3s;
  margin-left: 5px;
  border: none;
}

input[type=range]::-ms-thumb {
  appearance: none;
  height: 8px;
  width: 20px;
  background-color: var(--primary);
  border-radius: 10px;
  cursor: pointer;
  -ms-transition: all ease 0.3s;
  transition: all ease 0.3s;
  margin-left: 5px;
  border: none;
}

input[type=range]::-webkit-slider-thumb:hover {
  transform: scaleY(1.2);
  width: 24px;
}

input[type=range]::-moz-range-thumb:hover {
  transform: scaleY(1.2);
  width: 24px;
}

input[type=range]::-ms-thumb:hover {
  transform: scaleY(1.2);
  width: 24px;
}

input[type=number],
input[type=text],
input[type=password] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 10px;
  border: var(--border-in-light);
  min-height: 36px;
  box-sizing: border-box;
  background: var(--white);
  color: var(--black);
  padding: 0 10px;
  max-width: 50%;
  font-family: inherit;
}

div.math {
  overflow-x: auto;
}

.modal-mask {
  z-index: 9999;
  position: fixed;
  top: 0;
  left: 0;
  height: var(--full-height);
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
@media screen and (max-width: 600px) {
  .modal-mask {
    align-items: flex-end;
  }
}

.link {
  font-size: 12px;
  color: var(--primary);
  text-decoration: none;
}
.link:hover {
  text-decoration: underline;
}

pre {
  position: relative;
}
pre:hover .copy-code-button {
  pointer-events: all;
  transform: translateX(0px);
  opacity: 0.5;
}
pre .copy-code-button {
  position: absolute;
  right: 10px;
  top: 1em;
  cursor: pointer;
  padding: 0px 5px;
  background-color: var(--black);
  color: var(--white);
  border: var(--border-in-light);
  border-radius: 10px;
  transform: translateX(10px);
  pointer-events: none;
  opacity: 0;
  transition: all ease 0.3s;
}
pre .copy-code-button:after {
  content: "copy";
}
pre .copy-code-button:hover {
  opacity: 1;
}

pre .show-hide-button {
  border-radius: 10px;
  position: absolute;
  inset: 0 0 auto 0;
  width: 100%;
  margin: auto;
  height: -moz-fit-content;
  height: fit-content;
  display: inline-flex;
  justify-content: center;
  pointer-events: none;
}
pre .show-hide-button button {
  pointer-events: auto;
  margin-top: 3em;
  margin-bottom: 4em;
  padding: 5px 16px;
  border: 0;
  cursor: pointer;
  border-radius: 14px;
  text-align: center;
  color: white;
  background: #464e4e;
}
pre .expanded {
  background-image: none;
}
pre .collapsed {
  background-image: linear-gradient(to bottom, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.06));
}

.clickable {
  cursor: pointer;
}
.clickable:hover {
  filter: brightness(0.9);
}
.clickable:focus {
  filter: brightness(0.95);
}

.error {
  width: 80%;
  border-radius: 20px;
  border: var(--border-in-light);
  box-shadow: var(--card-shadow);
  padding: 20px;
  overflow: auto;
  background-color: var(--white);
  color: var(--black);
}
.error pre {
  overflow: auto;
}

.password-input-container {
  max-width: 50%;
  display: flex;
  justify-content: flex-end;
  transition: all 0.3s;
}
.password-input-container .password-eye {
  margin-right: 4px;
  padding: 10px 16px;
}
.password-input-container .password-input {
  min-width: 80%;
  min-height: auto !important;
}
.password-input-container .password-input:hover {
  border-color: #40a9ff;
}
.password-input-container .password-input:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.phone-input-wrapper {
  position: relative;
  max-width: 50%;
  margin-bottom: 12px;
  margin-top: 12px;
}

.phone-input-container {
  display: flex;
  align-items: center;
  transition: all 0.3s;
}

.phone-prefix {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  padding: 10px;
  padding-left: 0px;
}

.phone-input {
  flex: 1;
  min-height: 36px;
  min-width: 80%;
  outline: none;
  padding: 0;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 10px;
  padding: 4px 11px;
}
.phone-input:hover {
  border-color: #40a9ff;
}
.phone-input:focus-within {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}
.phone-input::-moz-placeholder {
  color: #bfbfbf;
}
.phone-input::placeholder {
  color: #bfbfbf;
}

.phone-error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 4px;
}

.user-avatar {
  height: 30px;
  min-height: 30px;
  width: 30px;
  min-width: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: var(--border-in-light);
  box-shadow: var(--card-shadow);
  border-radius: 11px;
}

.one-line {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.copyable {
  -webkit-user-select: text;
     -moz-user-select: text;
          user-select: text;
}/*# sourceMappingURL=globals.css.map */