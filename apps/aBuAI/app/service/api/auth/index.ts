import { MutationOptions, request, useCustomMutation } from "@/service/request";

// 登录请求参数类型
interface LoginParams {
  phoneNumber: string
  password: string
}
// 登录请求 hook
export function FetchLogin(options?: MutationOptions<Api.Auth.LoginResponse, LoginParams>) {
  return useCustomMutation<Api.Auth.LoginResponse, LoginParams>(
    {
      url: '@/auth/login',
      method: 'POST',
    },
    options,
  )
}

// 登录请求 hook
export function FetchLoginWithRequest(params?: LoginParams) {
  return request<Api.Auth.LoginResponse>({
    url: '@/auth/login',
    method: 'POST',
    data: params,
  },
  )
}

// 登出请求 hook
export function FetchLogout(options?: MutationOptions<Api.Auth.LogoutResponse, void>) {
  return useCustomMutation<Api.Auth.LogoutResponse, void>(
    {
      url: '@/auth/logout',
      method: 'POST',
    },
    options,
  )
}
// 登出请求 hook
export function FetchLogoutWithRequest() {
  return request<Api.Auth.LogoutResponse>({
    url: '@/auth/logout',
    method: 'POST',
  })
}
// 获取用户信息请求 hook
export function FetchInformation(options?: MutationOptions<Api.Auth.UserInfo, void>) {
  return useCustomMutation<Api.Auth.UserInfo, void>(
    {
      url: '@/auth/profile',
      method: 'GET',
    },
    options,
  )
}
// 获取用户信息请求 hook
export function FetchInformationWithRequest() {
  return request<Api.Auth.UserInfo>({
    url: '@/auth/profile',
    method: 'GET',
  })
}
// 聊天记录列表
export function FetchChatRecordList(options?: MutationOptions<Api.Record.Response, Api.Record.Request>) {
  return useCustomMutation<Api.Record.Response, Api.Record.Request>(
    {
      url: '@/admin/v1/ai/recordList',
      method: 'GET',
    },
    options,
  )
}
// 聊天对话详情
export function FetchChatRecordDetail(options?: MutationOptions<Api.RecordDetail.Response, Api.RecordDetail.Request>) {
  return useCustomMutation<Api.RecordDetail.Response, Api.RecordDetail.Request>(
    {
      url: '@/admin/v1/ai/recordDetail',
      method: 'GET',
    },
    options,
  )
}
