import { MutationOptions, request, useCustomMutation } from "@/service/request";

// 更新成品销售单状态-审核
export function FetchUpdateSaleProductOrderAuditStatusPass(options?: MutationOptions<Api.UpdateSaleProductOrderAuditStatusPass.Response, Api.UpdateSaleProductOrderAuditStatusPass.Request>) {
  return useCustomMutation<Api.UpdateSaleProductOrderAuditStatusPass.Response, Api.UpdateSaleProductOrderAuditStatusPass.Request>(
    {
      url: '@/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusPass',
      method: 'PUT',
    },
    options,
  )
}
// 换token
export function FetchSwitchH5Token(options?: MutationOptions<{token: string}, {token: string}>) {
  return useCustomMutation<{token: string}, {token: string}>(
    {
      url: '@/admin/v1/switchH5Token',
      method: 'GET',
    },
    options,
  )
}
