// 分页请求参数接口
export interface PaginationParams {
  page?: number
  size?: number
  pagination?: RequestConfig['pagination']
  [key: string]: any // 其他可能的查询参数
}

// 分页响应数据结构
export interface PaginationResponse<T = any, S = T> {
  list: T[]
  total: number
  summary?: S
}

// 扩展基础响应结构
export interface BaseResponse<T = any> {
  data: T
  code: number
  msg: string
  version: string
}

// 定义基础响应结构
export interface BaseResponse<T = any> {
  data: T
  code: number
  msg: string
  version: string
}
// 请求配置
export interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
  headers?: Record<string, string>
  data?: any
  pagination?: { // 是否分页
    page?: number
    size?: number
  } | boolean
}

// 员工分类
export enum EmployeeType {
  unknownPosition = 0, // 未知职务
  salesman = 20001, // 销售员
  biller = 20002, // 打单员
  porter = 20003, // 搬运工
  clothChecker = 20004, // 查布员
  warehouseManager = 20005, // 仓管员
  businessManager = 20006, // 业务经理
  follower = 20007, // 跟单员
  followerQC = 20008, // 跟单员qc
  driver = 20009, // 司机
  quarantine = 20011, // 质检员
  distributionCloth = 20010, // 配布员
  qualityInspectionSupervisor = 20014, // 质检主管
}

export enum PrintTemplateType {
  PrintTemplateTypeRawMatlPurOrder = 1, // 原料采购单
  PrintTemplateTypeGreyFabricPurOrder = 2, // 坯布采购单
  PrintTemplateTypeProductPurOrder = 3, // 成品采购单
  PrintTemplateTypeProductPurReturnOrder = 4, // 成品采购退货单
  PrintTemplateTypeProduceNotify = 5, // 生产通知单
  PrintTemplateTypeDNFNotify = 6, // 染整通知单
  PrintTemplateTypeRawMaterial = 7, // 原料染整通知单
  PrintTemplateTypeSaleOrderProduct = 8, // 成品销售单
  PrintTemplateTypeSaleOrderGreyFabric = 9, // 坯布销售单
  PrintTemplateTypeSaleOrderRawMaterial = 10, // 原料销售单
  PrintTemplateTypeShouldCollectOrderProductSale = 11, // 成品销售送货单
  PrintTemplateTypeShouldCollectOrderGreyFabric = 12, // 坯布销售应收单
  PrintTemplateTypeShouldCollectOrderRawMaterial = 13, // 原料销售应收单
  PrintTemplateTypeSaleTransferOrder = 14, // 调货销售单
  PrintTemplateTypeSaleTransferOrderRtn = 15, // 调货退货单
  PrintTemplateTypeRawMatlPurReturnOrder = 16, // 原料采购退货单
  PrintTemplateTypeGreyFabricPurReturnOrder = 17, // 坯布采购退货单
  PrintTemplateTypeShouldCollectOrderProductReturn = 18, // 成品销售退货单
  PrintTemplateTypeProductStock = 50, // 成品库存打码
  PrintTemplateTypeWarehouseBin = 51, // 仓位打印
  PrintTemplateTypeProduct = 52, // 成品资料标签打印
}
// PrintType 结合 PrintDataType 二者确定可打印的模板s
export enum PrintType {
  PrintTemplateTypePurOrder = 1, // 采购单
  PrintTemplateTypePurReturnOrder = 2, // 采购退货单
  PrintTemplateTypeProduceNotify = 3, // 生产通知单
  PrintTemplateTypeDNFNotify = 4, // 染整通知单
  PrintTemplateTypeSaleOrder = 5, // 销售单
  PrintTemplateTypeShouldCollectOrder = 6, // 销售送货单
  PrintTemplateTypeSaleTransferOrder = 14, // 调货销售单
  PrintTemplateTypeSaleTransferOrderRtn = 15, // 调货退货单
  PrintTemplateQuarantine = 16, // 质检
  PrintTemplateTypeStock = 50, // 库存打码
  PrintTemplateTypeWarehouseBin = 51, // 仓位打印
  PrintTemplateTypeLabel = 52, // 标签打印
  PrintTemplateTypeColorLabel = 53, // 颜色标签打印
  PrintTemplateTypePurchaserReconciliation = 17, // 客户对账表
  PrintTemplateTypeSupplierReconciliation = 18, // 供应商对账单
}
export enum PrintDataType {
  Raw = 1, // 原料
  Grey, // 坯布
  Product, // 成品
}

export enum AuditStatus {
  AuditStatusPending = 1, // 待审核
  AuditStatusPass = 2, // 审核通过
  AuditStatusReject = 3, // 审核驳回
  AuditStatusClose = 4, // 已作废
}
