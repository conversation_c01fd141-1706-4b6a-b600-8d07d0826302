export const PAGE = 1
export const SIZE = 200

// 处理请求参数 序列化
export function paramsSerializer(params: Record<string, any>) {
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      // 处理数组
      if (Array.isArray(value)) {
        value.forEach(item => searchParams.append(key, String(item)))
      }
      // 处理对象
      else if (typeof value === 'object') {
        searchParams.append(key, JSON.stringify(value))
      }
      // 处理基础类型
      else {
        searchParams.append(key, String(value))
      }
    }
  })
  return searchParams.toString()
}
