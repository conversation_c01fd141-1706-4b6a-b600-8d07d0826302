import {
  BASE_URL,
  ServiceProvider,
  StoreKey,
} from "@/common/constant";
import { getClientConfig } from "@/config/client";
import { createPersistStore } from "@/common/utils/store";

const DEFAULT_ACCESS_STATE = {
  accessCode: "",
  useCustomConfig: false,
  provider: ServiceProvider.Alibaba,
  // server config
  customModels: "",
  defaultModel: "",
  // 登录相关状态
  isLoggedIn: false,
  userInfo: null as Api.Auth.UserInfo | null,
  token: "",
  corpId: '',
  agentId: '',
};
// 分离运行时状态
const DEFAULT_RUNTIME_STATE = {
  isPending: false,
};
type AccessState = {
  accessCode: string;
  useCustomConfig: boolean;
  provider: ServiceProvider;
  customModels: string;
  defaultModel: string;
  isLoggedIn: boolean;
  userInfo: Api.Auth.UserInfo | null;
  token: string;
  corpId: string;
  agentId: string;
  isPending: boolean;
}

// 定义 Store 方法类型
type AccessMethods = {
  isAuthorized: () => boolean;
  setToken: (token: string) => void;
  setUserInfo: (userInfo: Api.Auth.UserInfo) => void;
  setIsLoggedIn: (isLogin: boolean) => void;
  setPending: (isPending: boolean) => void;
  setWecomInfo: (params: { corpId: string; agentId: string }) => void;
  getUserInfo: () => Promise<boolean>;
  login: (phoneNumber: string, password: string) => Promise<{ success: boolean; message?: string }>;
  scanCodeLogin: (params: { code: string; corp_id: string; agent_id: string }) => Promise<{ success: boolean; message?: string }>;
  logout: () => void;
}
export const useAccessStore = createPersistStore<AccessState, AccessMethods>(
  { ...DEFAULT_ACCESS_STATE, ...DEFAULT_RUNTIME_STATE },
  (set, get) => ({
    isAuthorized() {
      return get().isLoggedIn;
    },
    setToken(token: string) {
      set((state) => ({ ...state, token }));
    },
    setIsLoggedIn(isLogin: boolean) {
      set((state) => ({ ...state, isLoggedIn: isLogin }));
    },
    setUserInfo(userInfo: Api.Auth.UserInfo) {
      set((state) => ({ ...state, userInfo }));
    },
    setPending(isPending: boolean) {
      set((state) => ({ ...state, isPending }));
    },
    setWecomInfo: ({ corpId, agentId }) => {
      set((state) => ({ ...state, corpId, agentId }));
    },
    async getUserInfo() {
      try {
        const response = await fetch(`${BASE_URL}/admin/v1/information`, {
          method: "GET",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
            Authorization: `${get().token}`,
          },
        });
        
        const data = await response.json();
        if (data.code === 0) {
          set((state) => ({ 
            ...state,
            userInfo: data.data,
          }));
          return true;
        }
        return false;
      } catch (error) {
        console.error("获取用户信息失败", error);
        return false;
      }
    },
    async login(phoneNumber, password) {
      set((state) => ({ ...state, isPending: true })); // 开始登录，设置 loading 状态
      try {
        const response = await fetch(`${BASE_URL}/auth/login`, {
          method: "POST",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phoneNumber, password }),
        });
        
        const data = await response.json();
        if (data.code === 0) {
          set((state) => ({ 
            ...state,
            isLoggedIn: true,
            token: data.data.token,
          }));
          // 登录成功后获取用户信息
          const userInfoSuccess = await this.getUserInfo();
          if (!userInfoSuccess) {
            set((state) => ({ ...state, isPending: false })); // 获取用户信息失败，关闭 loading
            return { success: false, message: "获取用户信息失败" };
          }
          set((state) => ({ ...state, isPending: false })); // 获取用户信息失败，关闭 loading
          return { success: true };
        }
        set((state) => ({ ...state, isPending: false })); // 获取用户信息失败，关闭 loading
        return { success: false, message: data.message };
      } catch (error) {
        set((state) => ({ ...state, isPending: false })); // 获取用户信息失败，关闭 loading
        return { success: false, message: "登录失败，请稍后重试" };
      }
    },
    // 扫码登录方法
    async scanCodeLogin({ code, corp_id, agent_id }: { code: string; corp_id: string; agent_id: string }) {
      set((state) => ({ ...state, isPending: true }));
      try {
        const response = await fetch(`${BASE_URL}/admin/v1/scanCodeLogin`, {
          method: "POST",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ code, corp_id, agent_id }),
        });
        
        const data = await response.json();
        if (data.code === 0) {
          set((state) => ({ 
            ...state,
            isLoggedIn: true,
            token: data.data.token,
          }));
          // 登录成功后获取用户信息
          const userInfoSuccess = await this.getUserInfo();
          set((state) => ({ ...state, isPending: false }));
          if (!userInfoSuccess) {
            return { success: false, message: "获取用户信息失败" };
          }
          return { success: true };
        }
        set((state) => ({ ...state, isPending: false }));
        return { success: false, message: data.message };
      } catch (error: any) {
        set((state) => ({ ...state, isPending: false }));
        return { success: false, message: error.message || "登录失败，请稍后重试" };
      }
    },
    // 添加登出方法
    logout() {
      set((state) => ({ 
        ...state,
        isLoggedIn: false,
        userInfo: null,
        token: "",
      }));
    },
  }),
  {
    name: StoreKey.Access,
    version: 2,
    migrate(persistedState, version) {
      if (version < 2) {
        const state = persistedState as {
          token: string;
          openaiApiKey: string;
          azureApiVersion: string;
          googleApiKey: string;
        };
        state.openaiApiKey = state.token;
        state.azureApiVersion = "2023-08-01-preview";
        
      }
      // 每次加载时重置 isPending 状态
      return persistedState as typeof DEFAULT_ACCESS_STATE;
    },
    // 添加 partialize 配置，排除不需要持久化的字段
    partialize: (state) => {
      const { isPending, ...persistedState } = state;
      return persistedState;
    },
  },
);
