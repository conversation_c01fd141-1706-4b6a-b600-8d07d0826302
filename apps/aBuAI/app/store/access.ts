import {
  BASE_URL,
  ServiceProvider,
  StoreKey,
} from "@/common/constant";
import { getClientConfig } from "@/config/client";
import { createPersistStore } from "@/common/utils/store";

const DEFAULT_ACCESS_STATE = {
  accessCode: "",
  useCustomConfig: false,
  provider: ServiceProvider.Alibaba,
  // server config
  customModels: "",
  defaultModel: "",
  // 登录相关状态
  isLoggedIn: false,
  userInfo: null as Api.Auth.UserInfo | null,
  token: "",
  corpId: '',
  agentId: '',
};
// 分离运行时状态
const DEFAULT_RUNTIME_STATE = {
  isPending: false,
};
type AccessState = {
  accessCode: string;
  useCustomConfig: boolean;
  provider: ServiceProvider;
  customModels: string;
  defaultModel: string;
  isLoggedIn: boolean;
  userInfo: Api.Auth.UserInfo | null;
  token: string;
  corpId: string;
  agentId: string;
  isPending: boolean;
}

// 定义 Store 方法类型
type AccessMethods = {
  isAuthorized: () => boolean;
  setToken: (token: string) => void;
  setUserInfo: (userInfo: Api.Auth.UserInfo) => void;
  setIsLoggedIn: (isLogin: boolean) => void;
  setPending: (isPending: boolean) => void;
  setWecomInfo: (params: { corpId: string; agentId: string }) => void;
  getUserInfo: () => Promise<boolean>;
  login: (phoneNumber: string, password: string) => Promise<{ success: boolean; message?: string }>;
  scanCodeLogin: (params: { code: string; corp_id: string; agent_id: string }) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
}
export const useAccessStore = createPersistStore<AccessState, AccessMethods>(
  { ...DEFAULT_ACCESS_STATE, ...DEFAULT_RUNTIME_STATE },
  (set, get) => ({
    isAuthorized() {
      return get().isLoggedIn;
    },
    setToken(token: string) {
      set((state) => ({ ...state, token }));
    },
    setIsLoggedIn(isLogin: boolean) {
      set((state) => ({ ...state, isLoggedIn: isLogin }));
    },
    setUserInfo(userInfo: Api.Auth.UserInfo) {
      set((state) => ({ ...state, userInfo }));
    },
    setPending(isPending: boolean) {
      set((state) => ({ ...state, isPending }));
    },
    setWecomInfo: ({ corpId, agentId }) => {
      set((state) => ({ ...state, corpId, agentId }));
    },
    async getUserInfo() {
      try {
        const response = await fetch(`${BASE_URL}/auth/profile`, {
          method: "GET",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
            Authorization: `Bearer ${get().token}`, // 添加 Bearer 前缀
          },
        });

        const data = await response.json();
        console.log('data',data)
        // 适配新的响应结构，同时兼容旧格式
        if ((data.success && data.code === 200) || data.code === 0) {
          set((state) => ({
            ...state,
            userInfo: data.data.user,
          }));
          return true;
        }
        console.error("获取用户信息失败:", data.message || data.msg);
        return false;
      } catch (error) {
        console.error("获取用户信息失败", error);
        return false;
      }
    },
    async login(phoneNumber, password) {
      set((state) => ({ ...state, isPending: true })); // 开始登录，设置 loading 状态
      try {
        const response = await fetch(`${BASE_URL}/auth/login`, {
          method: "POST",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ phoneNumber, password }),
        });

        const data = await response.json();
        // 适配新的响应结构：success: true, code: 200
        if (data.success && data.code === 200) {
          set((state) => ({
            ...state,
            isLoggedIn: true,
            token: data.data.access_token, // 使用新的 access_token 字段
          }));
          // 登录成功后获取用户信息
          const userInfoSuccess = await this.getUserInfo();
          if (!userInfoSuccess) {
            set((state) => ({ ...state, isPending: false })); // 获取用户信息失败，关闭 loading
            return { success: false, message: "获取用户信息失败" };
          }
          set((state) => ({ ...state, isPending: false })); // 登录成功，关闭 loading
          return { success: true };
        }
        set((state) => ({ ...state, isPending: false })); // 登录失败，关闭 loading
        return { success: false, message: data.message || "登录失败" };
      } catch (error) {
        set((state) => ({ ...state, isPending: false })); // 网络错误，关闭 loading
        return { success: false, message: "登录失败，请稍后重试" };
      }
    },
    // 扫码登录方法
    async scanCodeLogin({ code, corp_id, agent_id }: { code: string; corp_id: string; agent_id: string }) {
      set((state) => ({ ...state, isPending: true }));
      try {
        const response = await fetch(`${BASE_URL}/admin/v1/scanCodeLogin`, {
          method: "POST",
          headers: {
            Platform: '1',
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ code, corp_id, agent_id }),
        });

        const data = await response.json();
        // 适配新的响应结构：success: true, code: 200
        if (data.success && data.code === 200) {
          set((state) => ({
            ...state,
            isLoggedIn: true,
            token: data.data.access_token, // 使用新的 access_token 字段
          }));
          // 登录成功后获取用户信息
          const userInfoSuccess = await this.getUserInfo();
          set((state) => ({ ...state, isPending: false }));
          if (!userInfoSuccess) {
            return { success: false, message: "获取用户信息失败" };
          }
          return { success: true };
        }
        set((state) => ({ ...state, isPending: false }));
        return { success: false, message: data.message || "登录失败" };
      } catch (error: any) {
        set((state) => ({ ...state, isPending: false }));
        return { success: false, message: error.message || "登录失败，请稍后重试" };
      }
    },
    // 添加登出方法
    async logout() {
      try {
        const token = get().token;
        if (token) {
          const response = await fetch(`${BASE_URL}/auth/logout`, {
            method: "POST",
            headers: {
              Platform: '1',
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          });

          const data = await response.json();
          if (data.success && data.code === 200) {
            console.log("登出成功:", data.message);
          } else {
            console.warn("登出接口调用失败:", data.message);
          }
        }
      } catch (error) {
        console.error("登出请求失败:", error);
      } finally {
        // 无论接口调用是否成功，都清理本地状态
        set((state) => ({
          ...state,
          isLoggedIn: false,
          userInfo: null,
          token: "",
        }));
      }
    },
  }),
  {
    name: StoreKey.Access,
    version: 2,
    migrate(persistedState, version) {
      if (version < 2) {
        const state = persistedState as {
          token: string;
          openaiApiKey: string;
          azureApiVersion: string;
          googleApiKey: string;
        };
        state.openaiApiKey = state.token;
        state.azureApiVersion = "2023-08-01-preview";
        
      }
      // 每次加载时重置 isPending 状态
      return persistedState as typeof DEFAULT_ACCESS_STATE;
    },
    // 添加 partialize 配置，排除不需要持久化的字段
    partialize: (state) => {
      const { isPending, ...persistedState } = state;
      return persistedState;
    },
  },
);
