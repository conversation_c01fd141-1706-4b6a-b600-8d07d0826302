import { create } from "zustand";
import { persist } from "zustand/middleware";
import { StoreKey } from "../common/constant";
import { View } from "../common/constant";

export interface ViewParams {
  autoSend?: boolean;
  content?: string;
  type?: string;
  [key: string]: any;
}

export interface ViewState {
  currentView: View;
  params: ViewParams;
  setCurrentView: (view: View, params?: ViewParams) => void;
}

export const useViewStore = create<ViewState>()(
  persist(
    (set) => ({
      params: {},
      currentView: View.Welcome,
      setCurrentView: (view: View, params: ViewParams = {}) => {
        set({ currentView: view, params });
      },
    }),
    {
      name: StoreKey.View,
      version: 1,
    }
  )
);
