import React from 'react';
import { createRoot } from 'react-dom/client';
import { FloatButton } from './components/floatButton/floatButton';
import { StyleProvider } from '@ant-design/cssinjs';

// 样式导入
import './app.scss';
import { AntdRegistry } from '@ant-design/nextjs-registry';
export let rootId = 'abu-ai-root';
// 样式注入函数
async function injectStyles(shadowRoot: ShadowRoot) {
  const allStyles = document.createElement('style');
  const processedRules = new Set();

  const stylePromises = Array.from(document.styleSheets)
    .map(async sheet => {
      try {
        if (sheet.href) {
          const response = await fetch(sheet.href);
          const text = await response.text();
          return text;
        }

        return Array.from(sheet.cssRules)
          .map(rule => {
            const ruleText = rule.cssText;
            if (processedRules.has(ruleText)) {
              return '';
            }
            processedRules.add(ruleText);

            if (rule instanceof CSSStyleRule) {
              // 处理选择器
              return ruleText.replace(
                /([^}]*){/g,
                (match, selector) => {
                  const trimmedSelector = selector.trim();
                  // 处理特殊选择器
                  switch (trimmedSelector) {
                    case ':root':
                    case ':host .light':
                      return `:host .${rootId}{`;
                    case ':host .dark':
                      return `:host(.dark){`;
                  }

                  // 处理其他选择器
                  if (selector.includes(':root') || selector.includes(':host')) {
                    return match;
                  }

                  // 移除多余的空格，但保持选择器结构
                  const cleanSelector = selector.replace(/\s+/g, ' ').trim();
                  return `:host ${cleanSelector}{`;
                }
              );
            }
            // 保留 @media 和其他规则
            return ruleText;
          })
          .filter(Boolean)
          .join('\n');
      } catch (e) {
        console.warn('Failed to process stylesheet:', e);
        return '';
      }
    });

  const styles = await Promise.all(stylePromises);
  allStyles.textContent = styles.join('\n');
  const existingStyle = shadowRoot.querySelector('style[data-injected="true"]');
  if (existingStyle) {
    existingStyle.textContent = allStyles.textContent;
  } else {
    allStyles.setAttribute('data-injected', 'true');
    shadowRoot.appendChild(allStyles);
  }
}

// 优化 MutationObserver 的处理逻辑
function observeStyleChanges(shadowRoot: ShadowRoot) {
  let timeoutId: NodeJS.Timeout;
  
  const observer = new MutationObserver((mutations) => {
    // 使用防抖，避免频繁更新
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      let hasNewStyles = false;
      
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (
              node instanceof HTMLStyleElement || 
              node instanceof HTMLLinkElement
            ) {
              hasNewStyles = true;
            }
          });
        }
      });

      if (hasNewStyles) {
        injectStyles(shadowRoot);
      }
    }, 100);
  });

  // 同时观察 head 和 body
  observer.observe(document.head, {
    childList: true,
    subtree: true
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  return observer;
}

interface InitOptions {
  publicPath?: string;
  token?: string
}

// 添加一个全局变量来存储公共路径
let publicPath = './';

async function init(containerId: string, options: InitOptions = {}) {
  rootId = containerId;
  
  // 设置公共路径
  if (options.publicPath) {
    publicPath = options.publicPath;
  }

  // 创建一个动态导入函数来处理公共路径
  const dynamicImport = (path: string) => {
    const componentPath = path.replace(/(app\/)?components\/interactive\//, '/interactive/');
    // const fullPath = publicPath + componentPath.replace(/^\.\//, '');
    console.log('componentPath',componentPath)
    return import(/* @vite-ignore */ componentPath);
  };

  // 将动态导入函数挂载到全局，供其他模块使用
  (window as any).__dynamicImport = dynamicImport;

  const container = document.createElement('div');
  container.id = containerId;
  container.style.cssText = 'position: fixed; right: 20px; bottom: 20px; z-index: 99999;';
  document.body.appendChild(container);

  const shadowRoot = container.attachShadow({ mode: 'open' });
  
  const mountPoint = document.createElement('div');
  mountPoint.id = 'abu-ai-mount';
  mountPoint.className = rootId;
  shadowRoot.appendChild(mountPoint);

  // 注入基础样式
  const globalStyle = document.createElement('style');
  globalStyle.textContent = `
    :host {
      all: initial;
      display: block;
    }
    :host(*) {
      all: unset;
      display: block;
    }
    .${rootId} * {
      box-sizing: border-box;
    }
    .${rootId} {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.5;
      -webkit-font-smoothing: antialiased;
      color: var(--black);
      background-color: var(--white);
    }
  `;
  shadowRoot.appendChild(globalStyle);

  // 初始注入所有现有样式
  await injectStyles(shadowRoot);

  // 开始观察样式变化
  const styleObserver = observeStyleChanges(shadowRoot);

  try {
    const root = createRoot(mountPoint);
    root.render(
      <React.StrictMode>
        <StyleProvider container={shadowRoot}>
          <AntdRegistry>
            <FloatButton container={shadowRoot} token={options.token}/>
          </AntdRegistry>
        </StyleProvider>
      </React.StrictMode>
    );
  } catch (error) {
    console.error('Failed to initialize ABuAI:', error);
  }

  // 返回清理函数
  return () => {
    styleObserver.disconnect();
  };
}

// 添加销毁方法
function destroy(containerId: string) {
  const container = document.getElementById(containerId);
  if (container) {
    if (container.shadowRoot) {
      const root = container.shadowRoot.getElementById('abu-ai-mount');
      if (root) {
        createRoot(root).unmount();
      }
      container.shadowRoot.innerHTML = '';
    }
    // 移除整个容器元素
    container.remove();
  }
}
export { init, destroy };
// 暴露给全局
// (window as any).ABuAI = { 
//   init,
//   destroy
// };
