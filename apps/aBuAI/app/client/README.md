## 通信技术

使用 Server-Sent Events (SSE) 进行流式响应

##  消息格式

```typescript
interface Message {
  id: string;           // 消息唯一ID
  date: string;         // 消息时间
  role: "assistant" | "user" | "system";  // 消息角色
  content: MessageContent[]; // 消息内容
}

// 消息内容可以是以下几种类型
type MessageContent = {
  // 1. 文本消息
  type: "text";
  text: string;
  
  // 2. 图片消息 
  type: "image_url";
  image_url: {
    url: string;
  };
  
  // 3. Excel文件
  type: "excel_url";
  excel_url: {
    name: string;
    url: string;
  };
  
  // 4. PDF文件
  type: "pdf_url"; 
  pdf_url: {
    name: string;
    url: string;
  };
  
  // 5. Word文件
  type: "word_url";
  word_url: {
    name: string;
    url: string;
  };
  
  // 6. 可交互组件
  type: "interactive";
  interactive: {
    template: string;     // 模板名称
    props: Record<string, any>;  // 组件属性
    events: {            // 事件处理
      onClick?: () => void;
      [key: string]: Function;
    }
  };
}
```

## API 接口示例

```typescript
// 1. 聊天接口
POST /api/chat
Request:
{
  messages: Message[];  // 历史消息
  stream: boolean;     // 是否使用流式响应
  model: string;       // 模型名称
  temperature?: number;  // 温度参数
}

Response: 
{
  code: number;        // 状态码
  data: Message;       // 返回的消息
  msg?: string;        // 错误信息
}

// 2. 文件上传接口
POST /api/upload
Request: FormData
{
  file: File
}

Response:
{
  code: number;
  data: string;  // 文件URL
  msg?: string;
}
```

##  错误处理

```JSON
{
  "code": 0,      // 错误码
  "msg": "success",       // 错误信息
  "data": {}        // 额外数据
}
```
