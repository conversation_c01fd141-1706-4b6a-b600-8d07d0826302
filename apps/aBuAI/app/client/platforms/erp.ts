"use client";
import { ApiPath, TENCENT_BASE_URL, REQUEST_TIMEOUT_MS, BASE_URL, View, NEST_SSE_BASE_URL } from "@/common/constant";
import { SessionType } from "@/types/session";
import { useAccessStore, useAppConfig, useChatStore, useViewStore } from "@/store";
import { processDataOut} from '@ly/utils'
import {
  ChatOptions,
  LLMApi,
  LLMModel,
  SpeechOptions,
  ThoughtBody,
} from "../api";
import Locale from "../../locales";
import {
  EventStreamContentType,
  fetchEventSource,
} from "@fortaine/fetch-event-source";
import { prettyObject } from "@/common/utils/format";
import { getClientConfig } from "@/config/client";
import { getMessageTextContent, isVisionModel, MultimodalContent } from "@/common/utils/utils";
import mapKeys from "lodash-es/mapKeys";
import mapValues from "lodash-es/mapValues";
import isArray from "lodash-es/isArray";
import isObject from "lodash-es/isObject";
import { fetch } from "@/common/utils/stream";
import { SSEClient, createSSEUrl } from "@/common/utils/sse";

export interface OpenAIListModelResponse {
  object: string;
  data: Array<{
    id: string;
    object: string;
    root: string;
  }>;
}

export interface RequestPayload {
  Messages: {
    Role: "system" | "user" | "assistant";
    Content: string | MultimodalContent[];
    Id: string
  }[];
  Stream?: boolean;
  Model: string;
  Temperature: number;
  TopP: number;
  ChatId: string
}

function capitalizeKeys(obj: any): any {
  if (isArray(obj)) {
    return obj.map(capitalizeKeys);
  } else if (isObject(obj)) {
    return mapValues(
      mapKeys(obj, (value: any, key: string) =>
        key.replace(/(^|_)(\w)/g, (m, $1, $2) => $2.toUpperCase()),
      ),
      capitalizeKeys,
    );
  } else {
    return obj;
  }
}

export class ERPApi implements LLMApi {
  path(): string {
    // const accessStore = useAccessStore.getState();

    let baseUrl = "";

    // if (accessStore.useCustomConfig) {
    //   baseUrl = accessStore.tencentUrl;
    // }

    if (baseUrl.length === 0) {
      const isApp = !!getClientConfig()?.isApp;
      baseUrl = isApp ? TENCENT_BASE_URL : ApiPath.ERP;
    }

    if (baseUrl.endsWith("/")) {
      baseUrl = baseUrl.slice(0, baseUrl.length - 1);
    }
    if (!baseUrl.startsWith("http") && !baseUrl.startsWith(ApiPath.ERP)) {
      baseUrl = "https://" + baseUrl;
    }

    console.log("[Proxy Endpoint] ", baseUrl);
    return baseUrl;
  }

  extractMessage(res: any) {
    return res.Choices?.at(0)?.Message?.Content ?? "";
  }
  // 添加处理401的方法
  private async handle401Error() {
    // 清除token
    const accessStore = useAccessStore.getState();
    const viewStore = useViewStore.getState();

    try {
      await accessStore.logout();
    } catch (error) {
      console.error("登出请求失败:", error);
    }

    // 保存当前视图状态
    const currentView = viewStore.currentView;

    // 切换到登录视图，并携带重定向信息
    viewStore.setCurrentView(View.Login, {
      redirect: currentView
    });
  }
  getHeaders() {
    const accessStore = useAccessStore.getState();
    return {
      "Content-Type": "application/json",
      "Platform": "1",
      "Authorization": accessStore.token ? `${accessStore.token}` : "",
    };
  }
  speech(options: SpeechOptions): Promise<ArrayBuffer> {
    throw new Error("Method not implemented.");
  }
  // 面料报价 不需实现，走 chat 逻辑
  async fabricQuotation(options: ChatOptions) {
    throw new Error("Method not implemented.");
  }
  // 查百科 不需实现，重定向到 SSE 方法
  async encyclopaedia(options: ChatOptions) {
    if (this.encyclopaediaSSE) {
      return this.encyclopaediaSSE(options);
    }
    throw new Error("Encyclopaedia method not implemented.");
  }
  // 查百科 SSE 流式对话 - 使用 FastGPT
  async encyclopaediaSSE(options: ChatOptions) {
    const accessStore = useAccessStore.getState();
    
    // 获取最后一条用户消息
    const lastMessage = options.messages[options.messages.length - 1];
    const query = getMessageTextContent(lastMessage);
    
    // FastGPT SSE 端点
    const nestBaseUrl = NEST_SSE_BASE_URL;
    const fastgptSseUrl = `${nestBaseUrl}/fastgpt/chat/sse`;
    
    // 构建查询参数
    const queryParams = new URLSearchParams({
      query,
      user: accessStore.token || "default-user",
      chatId: options.config.chat_id,
      detail: "true"
    });

    let responseText = "";
    let thoughtNodes: ThoughtBody[] = [];
    let isCompleted = false;
    let lastMessageTime = Date.now();

    // 创建 AbortController 用于取消请求
    const controller = new AbortController();
    options.onController?.(controller);
    
    console.log("[FastGPT SSE] Connecting to:", `${fastgptSseUrl}?${queryParams}`);
    console.log("[FastGPT SSE] Query params:", Object.fromEntries(queryParams));

    try {
      const eventSource = new EventSource(`${fastgptSseUrl}?${queryParams}`, {
        withCredentials: false
      });

      eventSource.onopen = () => {
        console.log("[FastGPT SSE] Connection opened successfully");
        lastMessageTime = Date.now(); // 重置时间
      };

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("[FastGPT SSE] Received message:", data);

          // 更新最后消息时间
          lastMessageTime = Date.now();

          // 处理不同的事件类型
          if (data.event === 'message') {
            // 解析 message 事件中的数据
            const messageData = JSON.parse(data.data);
            if (messageData.type === 'delta' && messageData.content) {
              responseText += messageData.content;
              options.onUpdate?.(responseText, messageData.content);
            }
          } else if (data.event === 'event') {
            // 解析 event 事件中的数据
            const eventData = JSON.parse(data.data);
            if (eventData.type === 'answer' && eventData.data?.choices?.[0]?.delta?.content) {
              const content = eventData.data.choices[0].delta.content;
              responseText += content;
              options.onUpdate?.(responseText, content);
            }
            // 检查是否完成
            if (eventData.data?.choices?.[0]?.finish_reason === 'stop') {
              isCompleted = true;
              console.log("[FastGPT SSE] Chat completed by finish_reason");
              options.onFinish(responseText, new Response());
              eventSource.close();
              return;
            }
          }

          // 处理直接的数据格式（兼容性）
          switch (data.type) {
            case 'delta':
              if (data.content) {
                responseText += data.content;
                options.onUpdate?.(responseText, data.content);
              }
              break;

            case 'complete':
              isCompleted = true;
              console.log("[FastGPT SSE] Chat completed:", data);
              options.onFinish(responseText, new Response());
              eventSource.close();
              break;
          }
        } catch (error) {
          console.error("[FastGPT SSE] Failed to parse message:", error, event.data);
        }
      };

      // 处理命名事件 - 这些可能不会被触发，因为数据都在 onmessage 中处理
      eventSource.addEventListener('event', (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("[FastGPT SSE] Received named event:", data);

          if (data.type === 'flowNodeStatus') {
            // 工作流节点状态事件
            thoughtNodes.push({
              status: 'running',
              name: data.data.name || '处理中'
            });
            options.onThought?.(thoughtNodes);
          }
        } catch (error) {
          console.error("[FastGPT SSE] Failed to parse named event:", error);
        }
      });

      eventSource.addEventListener('complete', (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log("[FastGPT SSE] Final completion event:", data);
          if (!isCompleted) {
            isCompleted = true;
            if (data.answer) {
              responseText = data.answer;
            }
            options.onFinish(responseText, new Response());
            eventSource.close();
          }
        } catch (error) {
          console.error("[FastGPT SSE] Failed to parse completion:", error);
        }
      });

      eventSource.onerror = (error) => {
        console.error("[FastGPT SSE] Connection error:", error);
        console.error("[FastGPT SSE] EventSource readyState:", eventSource.readyState);

        // 如果已经有内容且未完成，尝试完成而不是报错
        if (responseText.length > 0 && !isCompleted) {
          console.log("[FastGPT SSE] Error occurred but have content, finishing...");
          isCompleted = true;
          options.onFinish(responseText, new Response());
        } else if (eventSource.readyState !== EventSource.CLOSED) {
          // 只有在连接真正失败时才报错，正常结束不应该触发错误
          options.onError?.(new Error("FastGPT SSE connection failed"));
        }
        eventSource.close();
      };

      // 监听取消信号
      controller.signal.addEventListener('abort', () => {
        eventSource.close();
      });

      // 添加超时检测机制
      const checkTimeout = setInterval(() => {
        const now = Date.now();
        // 如果超过 5 秒没有新消息，且已经有内容，则认为完成
        if (now - lastMessageTime > 5000 && responseText.length > 0 && !isCompleted) {
          console.log("[FastGPT SSE] Timeout detected, finishing...");
          isCompleted = true;
          clearInterval(checkTimeout);
          options.onFinish(responseText, new Response());
          eventSource.close();
        }
        // 如果超过 30 秒没有任何响应，则认为失败
        if (now - lastMessageTime > 30000) {
          console.log("[FastGPT SSE] Connection timeout");
          clearInterval(checkTimeout);
          options.onError?.(new Error("FastGPT SSE connection timeout"));
          eventSource.close();
        }
      }, 1000);

      // 确保在连接关闭时清理定时器
      const originalClose = eventSource.close.bind(eventSource);
      eventSource.close = () => {
        clearInterval(checkTimeout);
        originalClose();
      };

    } catch (error) {
      console.error("[FastGPT SSE] Failed to connect:", error);
      options.onError?.(error as Error);
    }
  }
  // 数据分析
  async dataAnalysis(options: ChatOptions) {
    throw new Error("Method not implemented.");
  }
  // 销售预测
  async salesforecast(options: ChatOptions) {
    throw new Error("Method not implemented.");
  }
  // 下单
  async order(options: ChatOptions & {content: string }) {
    const chatPath = ApiPath.Order;
    const requestPayload = {
      content: options.content,
    };
    const chatPayload = {
      method: "POST",
      body: JSON.stringify(requestPayload),
      headers: this.getHeaders(),
    };
    const controller = new AbortController();
    const requestTimeoutId = setTimeout(
        () => controller.abort(),
        REQUEST_TIMEOUT_MS,
      );
    let orderId: number
    let oweDays: number
    let oweMoney: number
    let lostColors: number
    try{
      const res = await fetch(`${BASE_URL}${chatPath}`, chatPayload);
      console.log('res',res)
      if (res.status === 401) {
        this.handle401Error();
        return;
      }
      clearTimeout(requestTimeoutId);
      const resJson = await res.json();
      if(resJson.code !== 0){
        options.onError?.(new Error(resJson.msg));
        return
      }
      if(!resJson.data.id){
        options.onFinish(resJson.data.content, res);
        return
      }
      orderId = resJson.data.id;
      oweDays = resJson.data.owe_days;
      oweMoney = resJson.data.owe_money;
      lostColors = resJson.data.lost_colors;
    } catch(e) {
      console.log('e',e)
      options.onError?.(new Error('请求失败'));
      return
    }
    console.log('orderId',orderId, options)
    if(orderId){
      // 请求订单详情
      const orderPayload = {
        method: "GET",
        headers: this.getHeaders(),
      }
      try {
        const res = await fetch(`${BASE_URL}${ApiPath.OrderDetail}?id=${orderId}`, orderPayload);
        console.log('res',res)
        if (res.status === 401) {
          this.handle401Error();
          return;
        }
        const resJson = await res.json();
        const data: Api.GetSaleProductOrder.Response = processDataOut(resJson.data)

        console.log('resJson',resJson)
        // 构造交互消息
        const message = [{
          type: "interactive",
          interactive: {
            template: 'order.template.tsx',
            props: {
              id: data.id,
              oweDays: oweDays,
              oweMoney: oweMoney,
              lostColors: lostColors,
              customerName: data.customer_name,
              orderNo: data.order_no,
              saleUserName: data.sale_user_name,
              saleFollowerName: data.sale_follower_name,
              saleMode: data.sale_mode,
              saleModeName: data.sale_mode_name,
              auditStatus: data.audit_status,
              auditStatusName: data.audit_status_name,
              items: data.item_data,
              totalItems: data.total_item,
              totalColors: data.total_color,
              totalQuantity: data.total_roll,
              totalAmount: data.total_price,
            },
          }
        } as MultimodalContent];
        options.onFinish(message, res);

      }catch(e) {
        options.onError?.(new Error('请求订单失败'));
      }
    }
  }
  // 聊天 http://************:50002/hcscm/admin/v1/ai/chat
  async chat(options: ChatOptions) {
    let chatPath = this.path();
    if(options.sessionType){
      switch(options.sessionType){
        case SessionType.Encyclopaedia:
          chatPath = ApiPath.Encyclopaedia;
          break;
        case SessionType.DataAnalysis:
          chatPath = ApiPath.DataAnalysis;
          break;
        case SessionType.FabricQuotation:
          chatPath = ApiPath.FabricQuotation;
          break;
        case SessionType.Stock:
          chatPath = ApiPath.Stock;
          break;
        default:
          chatPath = this.path();
      }
    }
    const visionModel = isVisionModel(options.config.model);
    console.log('chat options', options)
    const messages = options.messages.map((v, index) => ({
      // "Messages 中 system 角色必须位于列表的最开始"
      role: index !== 0 && v.role === "system" ? "user" : v.role,
      content: visionModel ? v.content : getMessageTextContent(v),
      id: v.id
    }));

    const modelConfig = {
      ...useAppConfig.getState().modelConfig,
      ...useChatStore.getState().currentSession().mask.modelConfig,
      ...{
        model: options.config.model,
      },
    };

    const requestPayload: RequestPayload = capitalizeKeys({
      model: modelConfig.model,
      messages,
      temperature: modelConfig.temperature,
      top_p: modelConfig.top_p,
      stream: options.config.stream,
      chat_id: options.config.chat_id,
    });

    console.log("[Request] ERP payload: ", requestPayload);

    const shouldStream = !!options.config.stream;
    const controller = new AbortController();
    options.onController?.(controller);

    try {
      const chatPayload = {
        method: "POST",
        body: JSON.stringify(requestPayload),
        signal: controller.signal,
        headers: this.getHeaders(),
      };

      // make a fetch request
      const requestTimeoutId = setTimeout(
        () => controller.abort(),
        REQUEST_TIMEOUT_MS,
      );

      if (shouldStream) {
        let responseText = "";
        let remainText = "";
        let question: string[] = []
        let finished = false;
        let responseRes: Response;
        let thoughtNodes: ThoughtBody[] = []; // 添加思考节点数组

        // animate response to make it looks smooth
        function animateResponseText() {
          if (finished || controller.signal.aborted) {
            responseText += remainText;
            console.log("[Response Animation] finished");
            if (responseText?.length === 0) {
              options.onError?.(new Error("empty response from server"));
            }
            return;
          }

          if (remainText.length > 0) {
            const fetchCount = Math.max(1, Math.round(remainText.length / 60));
            const fetchText = remainText.slice(0, fetchCount);
            responseText += fetchText;
            remainText = remainText.slice(fetchCount);
            options.onUpdate?.(responseText, fetchText);
          }

          requestAnimationFrame(animateResponseText);
        }

        // start animaion
        animateResponseText();

        const finish = () => {
          if (!finished) {
            finished = true;
            options.onFinish(responseText + remainText, responseRes);
          }
        };
        const collectQuestion = () => {
          options.onQuestion?.(question)
        }
        controller.signal.onabort = finish;

        fetchEventSource(`${BASE_URL}${chatPath}`, {
          fetch: fetch as any,
          ...chatPayload,
          async onopen(res) {
            clearTimeout(requestTimeoutId);
            const contentType = res.headers.get("content-type");
            console.log(
              "[Tencent] request response content type: ",
              contentType,
            );
            responseRes = res;
            if (contentType?.startsWith("text/plain")) {
              responseText = await res.clone().text();
              return finish();
            }
            console.log('onopen res',res)
            if (
              !res.ok ||
              !res.headers
                .get("content-type")
                ?.startsWith(EventStreamContentType) ||
              res.status !== 200
            ) {
              const responseTexts = [responseText];
              let extraInfo = await res.clone().text();
              try {
                const resJson = await res.clone().json();
                extraInfo = prettyObject(resJson);
              } catch { }

              if (res.status === 401) {
                responseTexts.push(Locale.Error.Unauthorized);
              }

              if (extraInfo) {
                responseTexts.push(extraInfo);
              }

              responseText = responseTexts.join("\n\n");
              console.log('responseText', responseText)
              return finish();
            }
          },
          onmessage(msg) {
            if (msg.data === "[DONE]" || finished) {
              finish();
            }
            if(msg.event === 'question'){
              question.push(msg.data)
              collectQuestion()
              console.log('Received question:', msg.data, question);
              return
            }
            if(msg.event === 'flowNodeStatus'){
              try {
                const nodeData = JSON.parse(msg.data);
                thoughtNodes.push(nodeData);
                // 触发思考过程更新
                options.onThought?.(thoughtNodes);
              } catch (e) {
                console.error("[Request] parse flowNodeStatus error", msg.data);
              }
              return;
            }
            if(msg.event === 'answer'){
              const text = msg.data;
              try {
                const json = JSON.parse(text);
                // console.log('json',json)
                const choices = json.choices as Array<{
                  delta: { content: string };
                }>;
                const delta = choices[0]?.delta?.content;
                if (delta) {
                  // 普通文本
                  remainText += delta;
                }
              } catch (e) {
                console.error("[Request] parse error", text, msg);
              }
            }
          },
          onclose() {
            console.log('Closing with questions:', question);
            finish();
          },
          onerror(e) {
            options.onError?.(e);
            throw e;
          },
          openWhenHidden: true,
        });
      } else {
        const res = await fetch(`${BASE_URL}${chatPath}`, chatPayload);
        clearTimeout(requestTimeoutId);

        const resJson = await res.json();
        const message = this.extractMessage(resJson);
        options.onFinish(message, res);
      }
    } catch (e) {
      console.log("[Request] failed to make a chat request", e);
      options.onError?.(e as Error);
    }
  }
  async usage() {
    return {
      used: 0,
      total: 0,
    };
  }

  async models(): Promise<LLMModel[]> {
    return [];
  }
}
