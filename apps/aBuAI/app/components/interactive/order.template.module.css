.order-card {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 14px;
}
.order-card .order-header {
  margin-bottom: 10px;
}
.order-card .order-header .customer-info {
  margin-bottom: 8px;
}
.order-card .order-header .customer-info .customer-name {
  font-weight: bold;
  margin-right: 12px;
}
.order-card .order-header .order-no {
  color: #666;
}
.order-card .order-content .order-item {
  display: flex;
  width: 100%;
  font-size: 12px;
  margin-bottom: 12px;
}
.order-card .order-content .order-item .item-code {
  display: flex;
  align-items: center;
}
.order-card .order-content .order-item .item-code .color-block {
  width: 26px;
  height: 26px;
  border-radius: 4px;
}
.order-card .order-content .order-item .item-info {
  display: flex;
  align-items: center;
  color: #666;
  flex: 1;
  font-size: 14px;
  margin-left: 6px;
}
.order-card .order-content .order-item .item-info > span {
  flex: 1;
}
.order-card .order-content .order-item .item-info > span.item-price {
  text-align: center;
  justify-items: center;
}
.order-card .order-content .order-item .item-info > span.item-name {
  @apply truncate;
  flex: 1;
}
.order-card .order-content .order-item .item-info > span.item-total {
  text-align: right;
  justify-items: flex-end;
}
.order-card .order-content .order-item .item-info .item-total {
  color: #333;
  font-weight: 500;
}
.order-card .order-summary {
  margin-top: 10px;
  padding-top: 10px;
  border-top: 1px solid #eee;
}
.order-card .order-summary .summary-info {
  color: #666;
}
.order-card .order-summary .summary-info .total-amount {
  color: #333;
}
.order-card .order-summary .warnings {
  margin-top: 12px;
  color: #ff4d4f;
  font-size: 12px;
}
.order-card .order-summary .warnings .warning-item {
  margin-top: 4px;
}

.product-group {
  margin-bottom: 16px;
}
.product-group .product-header {
  padding: 8px 0;
  border-top: 1px solid #f0f0f0;
}
.product-group .product-header .product-code {
  margin-right: 8px;
}
.product-group .product-header .product-name {
  color: #666;
}

.order-actions {
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}/*# sourceMappingURL=order.template.module.css.map */