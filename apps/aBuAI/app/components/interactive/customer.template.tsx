import React from 'react';
import styles from './customer.template.module.scss';
import Arrow from '@/asserts/icons/arrow.svg'
interface CustomerInfoProps {
  props: {
    customerName: string;
    level: string;
    debt: number;
    note: string;
    mostPurchased: string;
    lastOrder: string;
    followUpPerson: string;
    salesperson: string;
  },
  events: {
    onClick: () => void;
  }
}

const CustomerInfo: React.FC<CustomerInfoProps> = (props) => {
  const { props: { customerName, level, debt, note, mostPurchased, lastOrder, followUpPerson, salesperson }, events } = props;
  return (
    <div className={styles.container}>
      <div className={styles.infoItem}>
        <span className={styles.label}>客户名称:</span>
        <span className={styles.value}>{customerName}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>等级登记:</span>
        <span className={styles.value}>{level}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>累欠金额:</span>
        <span className={styles.value}>¥ {debt.toFixed(2)}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>备注信息:</span>
        <span className={styles.value}>{note}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>采购最多:</span>
        <span className={styles.value}>{mostPurchased}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>最近下单:</span>
        <span className={styles.value}>{lastOrder}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>跟单人员:</span>
        <span className={styles.value}>{followUpPerson}</span>
      </div>
      <div className={styles.infoItem}>
        <span className={styles.label}>销售人员:</span>
        <span className={styles.value}>{salesperson}</span>
      </div>
      <button className={styles.moreButton} onClick={events.onClick}>更多 <Arrow/></button>
    </div>
  );
};

export default CustomerInfo;
