.container {
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  position: relative;
  max-width: 400px;
  overflow: scroll;
}

.infoItem {
  display: flex;
  margin-bottom: 12px;
  line-height: 1.5;
}

.label {
  color: #666;
  min-width: 80px;
  margin-right: 8px;
}

.value {
  color: #333;
  flex: 1;
}

.moreButton {
  position: absolute;
  bottom: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  border: none;
  background: none;
  color: #1890ff;
  cursor: pointer;
  padding: 0;
}

.moreButton:hover {
  opacity: 0.8;
}/*# sourceMappingURL=customer.template.module.css.map */