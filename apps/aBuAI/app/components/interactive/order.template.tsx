import { formatHashTag, formatPriceDiv, formatRollDiv, formatWeightDiv, isBulk, isUseMainUnit } from '@ly/utils';
import styles from './order.template.module.scss';
import { Button, Divider, message, Popconfirm, Tag } from 'antd';
import classNames from 'classnames';
import currency from 'currency.js';
import { useEffect, useState } from 'react';
import { FetchSwitchH5Token, FetchUpdateSaleProductOrderAuditStatusPass } from '@/service/api/saleOrder';
import { useAccessStore, useChatStore } from '@/store';
import Tupian from '@/asserts/icons/tupian.svg'
import { AuditStatus } from '@/service/request/type';
interface OrderGroup {
  product_code: string;
  product_name: string;
  items: Api.GetSaleProductOrder.SaleGetSaleProductOrderDetailData[];
}
interface OrderInfoProps {
  props: {
    oweDays: number // 超过多少天未还款
    oweMoney: number // 欠款金额
    lostColors: number
    id: number
    auxiliaryUnitId: number
    measurementUnitId: number
    auditStatus: number
    auditStatusName: string
    saleMode: number
    saleModeName: string
    customerName: string;
    saleUserName: string
    saleFollowerName: string
    orderNo: string;
    items: Api.GetSaleProductOrder.SaleGetSaleProductOrderDetailData[];
    totalItems: number;
    totalColors: number;
    totalQuantity: number;
    totalAmount: number;
  },
  events: {
    onClick: () => void;
  }
}
// 数量合并字段
const mergeWeightUnit = (
  list: {
    weight: number
    unit_name: string
    [key: string]: any
  }[]
) => {
  const mergeWeightInfoMap = list.reduce((acc, curr) => {
    acc[curr.unit_name] = !(curr.unit_name in acc)
      ? curr.weight
      : currency(acc[curr.unit_name]).add(curr.weight)
    return acc
  }, {})
  return Object.entries(mergeWeightInfoMap).reduce((acc, [key, value]) => {
    acc += acc ? `、${value}${key}` : `${value}${key}`
    return acc
  }, '')
}
const OrderInfo = (props: OrderInfoProps) => {
  const { props: orderInfoProps, events } = props;
  const [messageApi, contextHolder] = message.useMessage();
  // const [totalRoll, setTotalRoll] = useState(0)
  // const [totalPrice, setTotalPrice] = useState(0)
  // const [groupedItems, setGroupedItems] = useState<Record<string, OrderGroup>>()
  // 按产品代码分组
  const groupedItems = orderInfoProps.items?.reduce((groups, item) => {
    const key = `${item.product_code}-${item.product_name}`;
    if (!groups[key]) {
      groups[key] = {
        product_code: item.product_code!,
        product_name: item.product_name!,
        items: []
      };
    }
    groups[key].items.push(item);
    return groups;
  }, {} as Record<string, OrderGroup>);

  const [merge_weight_unit, setMergeWeightUnit] = useState('')
  useEffect(() => {
    let unitWeightArr: { weight: number; unit_name: string }[] = [] // 数量数据
    orderInfoProps.items?.forEach((item) => {
      const isMain = isUseMainUnit({auxiliary_unit_id: item.auxiliary_unit_id!, measurement_unit_id: item.measurement_unit_id!})
      const unitName = isMain ? item.measurement_unit_name : item.auxiliary_unit_name
      unitWeightArr = [...unitWeightArr, { weight: isMain ? item.weight! : item.length!, unit_name: unitName! }]
    })
    const merge_weight_unit = mergeWeightUnit(unitWeightArr)
    setMergeWeightUnit(merge_weight_unit || '0条')
  }, [orderInfoProps])
 
  const {mutateAsync: fetchPass} = FetchUpdateSaleProductOrderAuditStatusPass({
    onSuccess: () => {
      messageApi.success('审核成功')
      // 更新当前显示状态
      orderInfoProps.auditStatusName = '已审核'
      // 更新会话缓存
      const chatStore = useChatStore.getState()
      const currentSession = chatStore.currentSession()
      console.log('currentSession',currentSession)
      if (currentSession && currentSession.messages.length > 0) {
      // 遍历所有消息，找到包含当前订单的消息
      currentSession.messages.forEach(msg => {
        if (!msg.content) return
        
        try {
          if (Array.isArray(msg.content)) {
            // 处理数组类型的消息内容
            msg.content.forEach(item => {
              if (item.type === 'interactive' && 
                  item.interactive?.props?.id === orderInfoProps.id) {
                item.interactive.props.auditStatus = AuditStatus.AuditStatusPass
                item.interactive.props.auditStatusName = '已审核'
              }
            })
          }
        } catch (e) {
          console.error('更新消息内容失败', e)
        }
      })

      // 更新会话
      chatStore.updateTargetSession(currentSession, (session) => {
        session.messages = session.messages.concat()
      })
    }
    },
    onError: (e) => {
      messageApi.error(e.message)
    }
  })
  const {mutateAsync: switchH5Token, isPending} = FetchSwitchH5Token({
    onSuccess: (res) => {
      
    },
    onError: (e) => {
      messageApi.error(e.message)
    }
  })
  // 手动调整
  async function onAdjustClick() {
    const token = useAccessStore.getState().token;
    if (!token) {
      messageApi.error('请先登录');
      return;
    }
    const res = await switchH5Token({
      token
    })
    
    const baseUrl = process.env.NODE_ENV === 'production' ? '' : process.env.NEXT_PUBLIC_H5_BASE_URL
    const url = `${baseUrl}/qywx_back/finishProductSaleOrderAdd?type=edit&orderId=${encodeURIComponent(orderInfoProps.id)}&token=${encodeURIComponent(res.token)}`
    window.open(url, '_blank')
  }
  async function onDetail() {
    const token = useAccessStore.getState().token;
    if (!token) {
      messageApi.error('请先登录');
      return;
    }
    const res = await switchH5Token({
      token
    })
    const baseUrl = process.env.NODE_ENV === 'production' ? '' : process.env.NEXT_PUBLIC_H5_BASE_URL
    const url = `${baseUrl}/qywx_back/finishProductSaleOrderDetail?id=${encodeURIComponent(orderInfoProps.id)}&token=${encodeURIComponent(res.token)}`
    window.open(url, '_blank')
  }
  // 审核
  async function onSaveAndAuditClick() {
    const token = useAccessStore.getState().token;
    if (!token) {
      messageApi.error('请先登录');
      return;
    }
      // const chatStore = useChatStore.getState()
      // const currentSession = chatStore.currentSession()
      // console.log('currentSession',currentSession)
      // return
    await fetchPass({
      id: orderInfoProps.id,
    })
  }
  // 构建警告信息
  const warnings = [
    // 1. 未找到的产品
    // orderInfoProps.lostColors > 0 ? `${orderInfoProps.lostColors}个色号未找到相关产品` : null,
    // 2. 存在未识别的色号
    orderInfoProps.lostColors > 0 ? `存在${orderInfoProps.lostColors}个色号未识别成功，请手动处理。` : null,
    // 3. 欠款超期提示
    orderInfoProps.oweDays > 60 ? `该客户欠款金额${formatPriceDiv(orderInfoProps.oweMoney)}，超过${orderInfoProps.oweDays}天未还款，请注意风险` : null,
    // 4. 欠款未超期提示
    orderInfoProps.oweMoney > 0 && orderInfoProps.oweDays <= 60 ? `该客户欠款金额${orderInfoProps.oweMoney}，未还款，请注意风险` : null,
  ].filter(Boolean)
  return (
    <div className={styles["order-card"]}>
      {contextHolder}
      <div className={classNames(styles["order-header"])}>
        <div className={classNames(styles["customer-info"], 'flex justify-between')}>
          <span className={styles["customer-name"]}>{orderInfoProps.customerName}</span>
          <div className={styles["action-buttons"]}>
            <Tag color="processing">{orderInfoProps.saleModeName}</Tag>
            <Tag color={orderInfoProps.auditStatus === AuditStatus.AuditStatusPass ? 'success' : 'processing'}>
              {orderInfoProps.auditStatusName}
            </Tag>
          </div>
        </div>
        <div className='flex justify-between'>
          <span className={styles["order-no"]}>{orderInfoProps.orderNo}</span>
          <span className='mr-2'>{orderInfoProps.saleUserName}</span>
          <span >{orderInfoProps.saleFollowerName}</span>
        </div>
      </div>
      <div className={styles["order-content"]}>
        {Object.values(groupedItems || {}).map((group, groupIndex) => (
            <div key={groupIndex} className={styles["product-group"]}>
              <div className={styles["product-header"]}>
                <span className={styles["product-code"]}>{formatHashTag(group.product_code, group.product_name)}</span>
              </div>
              {group.items.map((item, index) => {
                const isMain = isUseMainUnit({auxiliary_unit_id: item.auxiliary_unit_id!, measurement_unit_id: item.measurement_unit_id!})
                const unitName = isMain ? item.measurement_unit_name : item.auxiliary_unit_name
                const weight = isMain ? item.weight : item.length
                const salePrice = (isBulk(orderInfoProps.saleMode) ? item.standard_sale_price : item.standard_length_cut_sale_price) || 0 // 销售价
                return <div key={index} className={styles["order-item"]}>
                  <div className={styles["item-code"]}>
                    {item.texture_url ? (
                      <span 
                        className={styles["color-block"]} 
                        style={{ 
                          backgroundImage: `url(${item.texture_url})`,
                          backgroundSize: 'cover',
                          backgroundPosition: 'center'
                        }}
                      />
                    ) : (
                      <span className={styles["color-block"]} style={{ backgroundColor: '#f5f5f5' }}>
                        <Tupian className="w-[26px] h-[26px]"/>
                      </span>
                    )}
                  </div>
                  <div className={styles["item-info"]}>
                    <span className={styles["item-name"]}>{formatHashTag(item.product_color_code, item.product_color_name)}</span>
                    <span className={styles["item-price"]}>x{isBulk(orderInfoProps.saleMode) ? `${item.roll}条` : `${weight}${unitName}`}</span>
                    <span className={styles["item-price"]}>￥{salePrice}/{unitName}</span>
                    <span className={styles["item-total"]}>¥{currency(Number(weight)).multiply(salePrice).toString()}</span>
                  </div>
                </div>
              })
              }
            </div>
        ))}
      </div>
      <div className={styles["order-summary"]}>
        <div className={styles["summary-info"]}>
          <div className='mb-2'>{orderInfoProps.totalItems}种面料，{orderInfoProps.totalColors}个颜色，共{merge_weight_unit}</div>
          <div className={styles["total-amount"]}>预估金额：¥{currency(orderInfoProps.totalAmount).toString()}</div>
        </div>
        {warnings.length > 0 && (
          <div className={styles["warnings"]}>
            <span style={{ color: 'red' }}>温馨提示：</span>
            {warnings.map((warning, index) => (
              <div key={index} className={styles["warning-item"]} style={{ color: 'red' }}>{warning}</div>
            ))}
          </div>
        )}
      </div>
      {/* 只在未审核状态下显示按钮 */}
      {orderInfoProps.auditStatus !== AuditStatus.AuditStatusPass ? (
        <div className={classNames(styles["order-actions"], 'flex justify-end gap-2 mt-4')}>
          <Button onClick={onAdjustClick} loading={isPending}>手动调整</Button>
          <Popconfirm
            title="是否保存并审核？"
            onConfirm={onSaveAndAuditClick}
            okText="确认"
            cancelText="取消"
          >
            <Button type="primary">保存并审核</Button>
          </Popconfirm>
        </div>
      ) : <div className={classNames(styles["order-actions"], 'flex justify-end gap-2 mt-4')}>
          <Button onClick={onDetail} loading={isPending}>查看详情</Button>
        </div>}
    </div>
  );
}

export default OrderInfo;
