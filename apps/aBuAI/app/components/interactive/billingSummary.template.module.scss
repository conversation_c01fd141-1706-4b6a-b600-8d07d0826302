.container {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.purchase {
  color: #333;
  font-size: 15px;
}

.debtRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.debtInfo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.debtLabel {
  color: #666;
  font-size: 14px;
}

.debtAmount {
  color: #333;
  font-size: 15px;
  font-weight: 500;
}

.detailsButton {
  border: none;
  background: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 0;
}

.arrow {
  margin-left: 4px;
  font-size: 16px;
}

.detailsButton:hover {
  color: #333;
}
