## 模板组件统一格式 Props入参

props 为 模板组件普通入参，events 为事件

在 chat.tsx 通过 type 为 interactive 来判断是否是可交互组件

后端传递给前端的消息列表中如果展示可交互组件时。需要包含字段

```JSON
{
  "type": "interactive",
  "props": {
    "age": 18,
    "name": "张三"
  },
  "events": {
    "onClick": "() => void"
  }
}
```


interactive

```tsx
interface OrderInfoProps {
  props: {
    name: string
    age: number
  },
  events: {
    onClick: () => void
  }
}
const OrderInfo = (props: OrderInfoProps) => {
  return <div className={styles["order-card"]}>
    <div className={styles["order-header"]}>
      <span className={styles["order-id"]}>XS-ZT-20240012447</span>
    </div>
  </div>
}
```

