.order-card {
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  margin: 12px 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  .order-header {
    margin-bottom: 10px;

    .customer-info {
      margin-bottom: 8px;
      .customer-name {
        font-weight: bold;
        margin-right: 12px;
      }
    }

    .order-no {
      color: #666;
    }
  }

  .order-content {
    .order-item {
      display: flex;
      width: 100%;
      font-size: 12px;
      margin-bottom: 12px;
      
      .item-code {
        display: flex;
        align-items: center;

        .color-block {
          width: 26px;
          height: 26px;
          border-radius: 4px;
        }
      }

      .item-info {
        display: flex;
        align-items: center;
        color: #666;
        flex: 1;
        font-size: 14px;
        margin-left: 6px;
        > span {
          flex: 1;
          // margin-right: 16px;
        }
        > span.item-price{
          text-align: center;
          justify-items: center
        }
        > span.item-name{
          @apply truncate;
          flex: 1;
        }
        >span.item-total{
          text-align: right;
          justify-items: flex-end;
        }

        .item-total {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  .order-summary {
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;

    .summary-info {
      color: #666;

      .total-amount {
        color: #333;
      }
    }

    .warnings {
      margin-top: 12px;
      color: #ff4d4f;
      font-size: 12px;

      .warning-item {
        margin-top: 4px;
      }
    }
  }
}
.product-group {
  margin-bottom: 16px;
  
  .product-header {
    padding: 8px 0;
    border-top: 1px solid #f0f0f0;
    
    .product-code {
      margin-right: 8px;
    }
    
    .product-name {
      color: #666;
    }
  }
}
.order-actions {
  padding: 12px 0;
  border-top: 1px solid #f0f0f0;
}
