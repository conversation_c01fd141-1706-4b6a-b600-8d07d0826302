import React from 'react';
import styles from './billingSummary.template.module.scss';

interface BillingSummaryProps {
  props: {
    monthlyPurchase: number;
    debt: number;
  },
  events: {
    onClick: () => void;
  }
}

const BillingSummary: React.FC<BillingSummaryProps> = (props) => {
  const { props: { monthlyPurchase, debt }, events } = props;
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <div className={styles.purchase}>
          该客户本月共采购 {monthlyPurchase.toLocaleString()}元
        </div>
        <div className={styles.debtRow}>
          <div className={styles.debtInfo}>
            <span className={styles.debtLabel}>累欠:</span>
            <span className={styles.debtAmount}>¥ {debt.toFixed(2)}</span>
          </div>
          <button className={styles.detailsButton} onClick={events.onClick}>
            账单明细 <span className={styles.arrow}>›</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default BillingSummary;
