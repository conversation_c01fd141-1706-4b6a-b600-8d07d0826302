import { useMobileScreen } from "@/common/utils/utils";
import { Flex, Typography } from "antd";
import { SenderComp } from "../order";
import { IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import {  useChatStore, useViewStore } from "@/store";
import { PromptProps, Prompts, PromptsProps } from "@ant-design/x";
import { BarChartOutlined, CoffeeOutlined, FallOutlined, FireOutlined, PieChartOutlined, RadarChartOutlined, SmileOutlined, StockOutlined } from "@ant-design/icons";
const {Title} = Typography

// 销售预测
export const Salesforecast = () => {
  const isMobileScreen = useMobileScreen();
  const { setCurrentView } = useViewStore();
  const chatStore = useChatStore();
  // const navigate = useNavigate()
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.Salesforecast
    });
  }
  const items: PromptsProps['items'] = [
    {
      key: '1',
      icon: <StockOutlined style={{ color: '#964B00' }} />,
      description: '预测未来一周的销售情况',
      disabled: false,
    },
    {
      key: '2',
      icon: <BarChartOutlined style={{ color: '#FAAD14' }} />,
      description: '预测卖得最高面料的销售情况',
      disabled: false,
    },
    {
      key: '3',
      icon: <PieChartOutlined style={{ color: '#FF4D4F' }} />,
      description: '预测卖得最高面料色号的销售情况',
      disabled: false,
    },
    {
      key: '4',
      icon: <RadarChartOutlined style={{ color: '#964B00' }} />,
      description: '预测整体的客户生命周期情况',
      disabled: false,
    },
    {
      key: '5',
      icon: <FallOutlined style={{ color: '#FAAD14' }} />,
      description: '客户流失预测',
      disabled: false,
    },
  ];
  function handleClickItem(item: { data: PromptProps; }) {
    handleSubmit(item.data.description as string)
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <Title className="text-center" level={3}>BI智能可视化分析，请要分析哪方面的内容</Title>
        <div className="w-[75%] mx-auto">
          <SenderComp onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
          <Prompts
            classNames={{title: 'text-center'}}
            title="🤔 分析方向"
            className="mt-2"
            items={items}
            wrap
            styles={{
              item: {
                flex: 'none',
                // width: 'calc(30% - 6px)',
                // backgroundImage: `linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)`,
                // border: 0,
              },
              subItem: {
                background: 'rgba(255,255,255,0.45)',
                border: '1px solid #FFF',
              },
            }}
            onItemClick={handleClickItem}
          />
        </div>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1 px-[20px]' justify="center" vertical>
          <Title className="text-center" level={4}>BI智能可视化分析<br></br>请要分析哪方面的内容</Title>
          {/* <Prompts title={<div className="text-center">🤔 分析方向</div>} items={items} wrap /> */}
          <Prompts
            title={<div className="text-center">🤔 分析方向</div>}
            items={items}
            wrap
            styles={{
              list: {
                display: 'flex',
                justifyContent: 'center',
                flexDirection: 'column'
              },
              item: {
                flex: 'none',
                // width: 'calc(30% - 6px)',
                // backgroundImage: `linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)`,
                // border: 0,
              },
              subItem: {
                background: 'rgba(255,255,255,0.45)',
                border: '1px solid #FFF',
              },
            }}
            onItemClick={handleClickItem}
          />
        </Flex>
        <SenderComp onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </Flex>}
    </div>
  );
};

