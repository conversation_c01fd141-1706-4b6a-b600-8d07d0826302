import { useMobileScreen } from "@/common/utils/utils";
import { Button, Flex, Typography } from "antd";
import { SenderComp } from "../order";
import { IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import { useChatStore, useViewStore } from "@/store";
import { HomeOutlined } from "@ant-design/icons";


const {Title} = Typography
// 查百科
export const Encyclopaedia = () => {
  const isMobileScreen = useMobileScreen();
  const chatStore = useChatStore();
  const {setCurrentView} = useViewStore()
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.Encyclopaedia  // 添加会话类型
    });
  }
  function handleBack() {
    setCurrentView(View.Welcome);
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <Title className="text-center relative" level={3}>
          <div className="absolute top-0 left-0">
            <Button shape="round" onClick={handleBack} icon={<HomeOutlined />} size={'middle'} >返回</Button>
          </div>
          查公司面料、颜色资料</Title>
        <SenderComp placeholder="查看面料幅宽、克重、成份等基础信息" onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1' justify="center" vertical>
          <Title className="text-center" level={5}>查公司面料、颜色资料</Title>
        </Flex>
        <SenderComp placeholder="查看面料幅宽、克重、成份等基础信息" onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </Flex>}
    </div>
  );
};
