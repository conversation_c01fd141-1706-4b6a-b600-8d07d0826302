"use client";

import "@/polyfill";

import { useState, useEffect, useMemo, memo, useCallback, useRef } from "react";
import styles from "./home.module.scss";
import { message, Typography  } from "antd";
import ABuAISvg from "@/asserts/icons/aBu.svg";
import LoadingIconSvg from "@/asserts/icons/three-dots.svg";
import GongzuotaiSvg from "@/asserts/icons/gongzuotai.svg";
// import '@ant-design/v5-patch-for-react-19';
import { checkKey, getCSSVar, useMobileScreen } from "@/common/utils/utils";
import ABUuPng from '@/asserts/icons/aBu.png'
import dynamic from "next/dynamic";
import { IS_WIDGET, Path, SlotID, View } from "@/common/constant";
import { ErrorBoundary } from "@/components/error/error";
import { BarChartOutlined, BookOutlined, LeftOutlined, LineChartOutlined, <PERSON>u<PERSON>oldOutlined, <PERSON>uUn<PERSON>Outlined, MessageOutlined, PlusOutlined, FileSearchOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { getISOLang, getLang } from "@/locales";
import classnames from 'classnames'
import {
  HashRouter as Router,
  useLocation,
} from "react-router-dom";
import { useAppConfig } from "@/store/config";
import { LoginPage } from "@/components/login/login";
import { OAuth } from "@/components/oauth2/oauth2";
import { getClientConfig } from "@/config/client";
import clsx from "clsx";
import { Button, Drawer } from "antd";
import Image from "next/image";  // 使用 Next.js 的 Image 组件
import { SessionType } from "@/types/session";
import { ChatMessage, createEmptySession, useAccessStore, useChatStore, useViewStore } from "@/store";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/service/request";
import { FetchChatRecordDetail, FetchChatRecordList } from "@/service/api/auth";
import { RequestPayload } from "@/client/platforms/erp";
import { AuthorityKey } from "@/common/authorityKey";
export function Loading(props: { noLogo?: boolean }) {
  return (
    <div className={clsx("no-dark", styles["loading-content"])}>
      {!props.noLogo && <ABuAISvg />}
      <LoadingIconSvg />
    </div>
  );
}

const Chat = dynamic(async () => (await import("@/components/chat/chat")).Chat, {
  loading: () => <Loading noLogo />,
});

const Welcome = dynamic(async () => (await import("@/components/welcome/welcome")).WelcomePage, {
  loading: () => <Loading />,
});

const Order = dynamic(async () => (await import("@/components/order/index")).Order, {
  loading: () => <Loading />,
});

const Encyclopaedia = dynamic(async () => (await import("@/components/encyclopaedia/index")).Encyclopaedia, {
  loading: () => <Loading />,
});
const DataAnalysis = dynamic(async () => (await import("@/components/dataAnalysis/index")).DataAnalysis, {
  loading: () => <Loading />,
});
const Salesforecast = dynamic(async () => (await import("@/components/salesforecast/index")).Salesforecast, {
  loading: () => <Loading />,
});
const FabricQuotation = dynamic(async () => (await import("@/components/fabricQuotation/index")).FabricQuotation, {
  loading: () => <Loading />,
});
const Stock = dynamic(async () => (await import("@/components/stock/index")).Stock, {
  loading: () => <Loading />,
});


export function useSwitchTheme() {
  // const config = useAppConfig();

  useEffect(() => {
    document.body.classList.remove("light");
    document.body.classList.remove("dark");
    document.body.classList.add("light"); // 默认白色
    const metaDescriptionDark = document.querySelector(
      'meta[name="theme-color"][media*="dark"]',
    );
    const metaDescriptionLight = document.querySelector(
      'meta[name="theme-color"][media*="light"]',
    );

    metaDescriptionDark?.setAttribute("content", "#151515");
    metaDescriptionLight?.setAttribute("content", "#fafafa");

  }, []);
}

function useHtmlLang() {
  useEffect(() => {
    const lang = getISOLang();
    const htmlLang = document.documentElement.lang;

    if (lang !== htmlLang) {
      document.documentElement.lang = lang;
    }
  }, []);
}

const useHasHydrated = () => {
  const [hasHydrated, setHasHydrated] = useState<boolean>(false);

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  return hasHydrated;
};

const loadAsyncGoogleFont = () => {
  const linkEl = document.createElement("link");
  linkEl.rel = "stylesheet";
  linkEl.href = "https://fonts.loli.net/css2?family=Noto+Sans:wght@300;400;700;900&display=swap";
  document.head.appendChild(linkEl);
};

export function WindowContent(props: { children: React.ReactNode }) {
  return (
    <div className={classnames(styles["window-content"], 'flex flex-col h-full overflow-hidden')} id={SlotID.AppBody}>
      {props?.children}
    </div>
  );
}

function PersonalDrawer({ open, onClose }: { open: boolean;  onClose: () => void }) {
  const { currentView, setCurrentView } = useViewStore();
  const accessStore = useAccessStore();
  const isLoggedIn = accessStore.isAuthorized();
  const userInfo = accessStore.userInfo;
  const [messageApi, contextHolder] = message.useMessage();

  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [records, setRecords] = useState<Api.Record.List[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);

  const {mutateAsync: fetchChatRecordList} = FetchChatRecordList({
    onSuccess: (res) => {
      console.log('fetchChatRecordList',res)
    },
    onError: (err) => {
      console.log('fetchChatRecordList',err)
    }
  })
  // const location = useLocation();
  const chatStore = useChatStore();
  const handleNavigate = (view: View) => {
    setCurrentView(view);
    // navigate(path);
    onClose();
  };

  const handleLogin = () => {
    // navigate(Path.Login);
    setCurrentView(View.Login);
    onClose();
  };

  const handleLogout = () => {
    accessStore.logout();
    messageApi.success("已退出登录");
    // navigate(Path.Login);
    setCurrentView(View.Login);
    onClose();
  };
  const handleNewChat = () => {
    chatStore.newSession();
    // 清空当前会话消息
    // chatStore.updateTargetSession(chatStore.currentSession(), (session) => {
    //   session.messages = [];
    // });
    // 如果当前已经在聊天页面
    if (currentView !== View.Chat) {
      // 不在聊天页面则导航过去
      setCurrentView(View.Chat, {
        autoSend: false,
        type: SessionType.Chat
      });
    }
    onClose();
  }
  function handleWelcome() {
    setCurrentView(View.Welcome);
    onClose();
  }
  const {mutateAsync: fetchChatRecordDetail} = FetchChatRecordDetail()
  const handleClickSession = async(sessionId: string) => {
    try {
      const res = await fetchChatRecordDetail({chat_id: sessionId})

      if (res.data.data.list) {
        // 创建新的会话
        const newSession = createEmptySession();
        newSession.id = sessionId;
        newSession.topic = records.find(r => r.chat_id === sessionId)?.title || '未命名对话';

        // 将后端返回的消息转换为 ChatMessage 格式
        const messages: ChatMessage[] = res.data.data.list.reduce((acc, curr) => {
          const validMessages = curr.value
            .filter(subItem => {
              console.log('content',subItem)
              return subItem.Text.content.trim() !== ''
            })
            .map(subItem => ({
              id: curr._id,
              role: curr.obj === 'Human' ? 'user' : 'assistant' as RequestPayload['Messages'][number]['Role'],
              content: subItem.Text.content,
              date: new Date(curr.time).toLocaleString(),
              isError: false,
            }));

          return [...acc, ...validMessages];
        }, [] as ChatMessage[]);

        newSession.messages = messages;
        newSession.lastUpdate = new Date(messages[messages.length - 1]?.date || Date.now()).getTime();

        // 添加或更新会话
        const existingIndex = chatStore.sessions.findIndex(s => s.id === sessionId);
        if (existingIndex !== -1) {
          chatStore.updateSession(existingIndex, newSession);
          chatStore.selectSession(existingIndex);
        } else {
          chatStore.insertSession(newSession);
          chatStore.selectSession(0);
        }

        setCurrentView(View.Chat);
        onClose();
      }
    } catch(error) {
      console.error('获取聊天记录详情失败:', error);
      messageApi.error('获取聊天记录详情失败');
    }
  };
  async function getData(pageNum: number = 1) {
    try {
      setLoading(true);
      const res = await fetchChatRecordList({
        page: pageNum,
        size: 10
      });
      console.log('res',res)
      if (res.list) {
        if (pageNum === 1) {
          setRecords(res.list);
        } else {
          setRecords(prev => [...prev, ...res.list]);
        }
        setHasMore(res.list.length === 10);
      }
    } catch (error) {
      console.error('获取聊天记录失败:', error);
    } finally {
      setLoading(false);
    }

  }
  // 处理滚动加载
  const handleScroll = useCallback(() => {
    if (!containerRef.current || loading || !hasMore) return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    if (scrollHeight - scrollTop - clientHeight < 50) {
      setPage(prev => prev + 1);
    }
  }, [loading, hasMore]);

  // 监听页码变化
  useEffect(() => {
    if (open && page > 1) {
      getData(page);
    }
  }, [page]);

  // 初始化加载
  useEffect(() => {
    if (open) {
      setPage(1);
      getData(1);
    }
  }, [open]);

  // 添加滚动监听
  useEffect(() => {
    const currentRef = containerRef.current;
    if (currentRef) {
      currentRef.addEventListener('scroll', handleScroll);
      return () => currentRef.removeEventListener('scroll', handleScroll);
    }
  }, [handleScroll]);
  const groupedRecords = useMemo(() => {
    const groups: Record<string, Api.Record.List[]> = {};
    records.forEach(record => {
      const date = new Date(record.create_time).toLocaleDateString();
      if (!groups[date]) {
        groups[date] = [];
      }
      groups[date].push(record);
    });
    return groups;
  }, [records]);
  // 按日期对消息进行分组
  // const groupedMessages = useMemo(() => {
  //   const sessions = chatStore.sessions
  //     .filter(session => session.messages.length > 0)
  //     .sort((a, b) => b.lastUpdate - a.lastUpdate);

  //   const groups: Record<string, Array<{
  //     sessionId: string,
  //     message: string,
  //     date: string
  //     topic: string
  //   }>> = {};
  //   sessions.forEach(session => {
  //     if (session.messages.length > 0) {
  //       const lastMessage = session.messages[session.messages.length - 1];
  //       const date = new Date(session.lastUpdate).toLocaleDateString();

  //       if (!groups[date]) {
  //         groups[date] = [];
  //       }

  //       groups[date].push({
  //         sessionId: session.id,
  //         message: lastMessage.content.toString(),
  //         topic: session.topic || lastMessage.content.toString().slice(0, 20), // 如果没有topic则使用消息内容前20个字符
  //         date: new Date(session.lastUpdate).toLocaleString(),
  //       });
  //     }
  //   });

  //   return groups;
  // }, [chatStore.sessions]);
  return <Drawer
      getContainer={false}
      closeIcon={null}
      classNames={{
        header: '!border-b-0 !pt-2 !pb-2',
        body: '!p-0'
      }}
      title={
        <div className="flex justify-end items-center">
          <div className="w-[30px] h-[30px] flex justify-center items-center cursor-pointer" onClick={onClose}>
            <LeftOutlined />
          </div>
        </div>
      }
      placement={'left'}
      onClose={onClose}
      open={open}
      width={300}
    >
      <div className="flex flex-col h-full">
        {contextHolder}
        {/* 用户信息区域 */}
        <div className="p-4 flex flex-col items-center">
          <div className="w-[100px] h-[100px] rounded-full mb-2 cursor-pointer" onClick={handleWelcome}>
            {/* 这里可以放头像 */}
            {
              IS_WIDGET ? <img src={ABUuPng} alt="用户头像" className="w-full h-full object-cover"></img> : <Image
              alt="用户头像"
              width={100}
              height={100}
              src={ABUuPng}
              className="w-full h-full object-cover"
            />
            }
          </div>
          <div className="text-base">
          {isLoggedIn ? `您好${`，${userInfo?.user_name}` || ''}` : '未登录用户'}
          </div>
          {isLoggedIn ? (
            <div className="flex gap-2">
              <div
                className="text-xs text-blue-500 cursor-pointer hover:text-blue-600"
                onClick={handleLogout}
              >
                退出登录
              </div>
            </div>
          ) : (
            <div
              className="text-base text-blue-500 cursor-pointer hover:text-blue-600"
              onClick={handleLogin}
            >
              立即登录
            </div>
          )}
        </div>

        {/* 功能导航区域 */}
        <div className="grid grid-cols-4 gap-2 px-4 py-6 border-b">
          {checkKey(AuthorityKey.sale_order) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.Order)}>
            <div className="w-10 h-10 bg-blue-50 rounded-lg flex items-center justify-center mb-1">
              <ShoppingCartOutlined className="text-blue-500 text-lg" />
            </div>
            <span className="text-xs">下单</span>
          </div> : null}

          {checkKey(AuthorityKey.encyclopaedia) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.Encyclopaedia)}>
            <div className="w-10 h-10 bg-orange-50 rounded-lg flex items-center justify-center mb-1">
              <BookOutlined className="text-orange-500 text-lg" />
            </div>
            <span className="text-xs">查百科</span>
          </div> : null}
          {checkKey(AuthorityKey.data_analysis) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.DataAnalysis)}>
            <div className="w-10 h-10 bg-green-50 rounded-lg flex items-center justify-center mb-1">
              <LineChartOutlined className="text-green-500 text-lg" />
            </div>
            <span className="text-xs">数据分析</span>
          </div> : null}
          {/* {checkKey(AuthorityKey.sales_forecast) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.Salesforecast)}>
            <div className="w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center mb-1">
              <BarChartOutlined className="text-purple-500 text-lg" />
            </div>
            <span className="text-xs">销售预测</span>
          </div> : null} */}
          {checkKey(AuthorityKey.fabric_quotation) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.FabricQuotation)}>
            <div className="w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center mb-1">
              <BarChartOutlined className="text-purple-500 text-lg" />
            </div>
            <span className="text-xs">面料价格</span>
          </div> : null}
          {checkKey(AuthorityKey.stock) ? <div className="flex flex-col items-center hover:bg-slate-100 rounded cursor-pointer" onClick={() => handleNavigate(View.Stock)}>
            <div className="w-10 h-10 bg-purple-50 rounded-lg flex items-center justify-center mb-1">
              <FileSearchOutlined className="text-purple-500 text-lg" />
            </div>
            <span className="text-xs">查库存</span>
          </div> : null}
        </div>

        {/* 聊天记录区域 */}
        {isLoggedIn ? (
          <div
            ref={containerRef}
            className="flex-1 overflow-y-auto"
          >
            <div className="p-4">
              {Object.entries(groupedRecords).map(([date, dateRecords]) => (
                <div key={date} className="mb-6">
                  <div className="text-xs text-gray-500 mb-2 sticky top-0 bg-white py-2">
                    {date}
                  </div>
                  {dateRecords.map((record) => (
                    <div
                      key={record.chat_id}
                      className="text-sm p-3 mb-3 border border-gray-200 rounded-lg hover:border-[#387be9] transition-colors duration-200 cursor-pointer"
                      onClick={() => handleClickSession(record.chat_id)}
                    >
                      <MessageOutlined className="text-blue-500 mr-2" />
                      <span>{record.title || '未命名对话'}</span>
                      <div className="text-xs text-gray-500 mt-1">
                        {new Date(record.create_time).toLocaleTimeString()}
                      </div>
                    </div>
                  ))}
                </div>
              ))}

              {loading && (
                <div className="text-center py-4 text-gray-500">
                  加载中...
                </div>
              )}

              {!hasMore && records.length > 0 && (
                <div className="text-center py-4 text-gray-500">
                  没有更多记录了
                </div>
              )}

              {!loading && records.length === 0 && (
                <div className="text-center py-4 text-gray-500">
                  暂无聊天记录
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center text-gray-500">
            登录后查看聊天记录
          </div>
        )}

        {/* 新建聊天按钮 */}
        <div className="p-4 border-t">
          <Button
            type="primary"
            icon={<PlusOutlined className="mr-2" />}
            className="w-full h-10 bg-blue-500 text-white rounded-lg flex items-center justify-center"
            onClick={() => isLoggedIn ? handleNewChat() : handleLogin()}
          >
            {isLoggedIn ? '新建聊天' : '登录后开始聊天'}
          </Button>
        </div>
      </div>
    </Drawer>
}
const { Link } = Typography;
function Screen() {
  const { currentView, setCurrentView } = useViewStore();
  const config = useAppConfig();
  const location = useLocation();
  const isAuth = location.pathname === View.OAuth;
  const isMobileScreen = useMobileScreen();
  const shouldTightBorder =
    getClientConfig()?.isApp || (config.tightBorder && !isMobileScreen);
  useEffect(() => {
    loadAsyncGoogleFont();
  }, []);
  const [open, setOpen] = useState(false);
  const handleClick = () => {
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };

  // const navigate = useNavigate();
  const isWelcomePage = currentView === View.Welcome
  const handleBack = () => {
    setCurrentView(View.Welcome);
    // navigate(Path.Welcome);
  };
  // 添加获取页面标题的函数
  const getPageTitle = () => {
    switch (currentView) {
      case View.Welcome:
      case View.Home:
        return '阿布智能';
      case View.Chat:
        return '智能对话';
      case View.Order:
        return '智能下单';
      case View.Encyclopaedia:
        return '智能百科';
      case View.DataAnalysis:
        return '数据分析';
      case View.Salesforecast:
        return '销售预测';
      case View.FabricQuotation:
        return '面料报价';
      case View.Stock:
        return '查库存';
      case View.Login:
        return '用户登录';
      default:
        return '阿布智能';
    }
  };
  const renderContent = () => {
    switch (currentView) {
      case View.Welcome:
        return <Welcome />;
      case View.Chat:
        return <Chat />;
      case View.Order:
        return <Order />;
      case View.Encyclopaedia:
        return <Encyclopaedia />;
      case View.DataAnalysis:
        return <DataAnalysis />;
      case View.Salesforecast:
        return <Salesforecast />;
      case View.FabricQuotation:
        return <FabricQuotation />;
      case View.Stock:
        return <Stock />;
      case View.Login:
        return <LoginPage />;
      case View.OAuth:
        return <OAuth />;
      default:
        return <Welcome />;
    }
  };
  useEffect(() => {
    console.log('location', location.state)
    const params = new URLSearchParams(window.location.search);
    const pageView = params.get('jumpPage')
    console.log('pageView', pageView)
    if(pageView){
      setCurrentView(pageView as View)
    }
  }, [])
  console.log('process.env.NODE_ENV', process.env.NODE_ENV)
  return (
    <div
      className={clsx(styles.container, {
        [styles["tight-container"]]: shouldTightBorder,
        [styles["rtl-screen"]]: getLang() === "ar",
      })}
    >
      <WindowContent>
        {currentView !== View.Login ? <>
        <div className="flex relative justify-center h-[56px] items-center bg-slate-100 flex-shrink-0">
          <span className="h-[56px] absolute top-0 left-0 text-lg flex justify-center items-center">
            <div className="flex justify-center w-[56px] h-full hover:bg-black/5 rounded cursor-pointer" onClick={handleClick}>
              {!open ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
            </div>
            {!isWelcomePage? (
            <div className="flex justify-center w-[56px] h-full hover:bg-black/5 rounded cursor-pointer" onClick={handleBack}>
              <LeftOutlined />
            </div>
            ) : null}
          </span>
          <span className="select-none font-bold">{getPageTitle()}</span>
          {/* {!IS_WIDGET ? <div className="h-[56px] absolute top-0 right-0 flex items-center mr-2" >
            <GongzuotaiSvg className="w-[20px] h-[20px]"/>
            <Link className="ml-1" target="_blank" href={process.env.NODE_ENV === 'production'
                ? 'https://hcscmtest.zzfzyc.com/qywx_workspace'
                : 'http://192.168.1.82:5173/qywx_workspace'
              }>工作台</Link>
          </div> : null} */}

        </div>
        <PersonalDrawer open={open} onClose={handleClose}/>
        </> : null}

        <div className="flex-1 overflow-hidden">
          {renderContent()}
        </div>
      </WindowContent>
    </div>
  );
}

// export function useLoadData() {
  // const config = useAppConfig();

  // const api: ClientApi = getClientApi(config.modelConfig.providerName);
  //
  // useEffect(() => {
  //   (async () => {
  //     const models = await api.llm.models();
  //     config.mergeModels(models);
  //   })();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);
// }

export function Home() {
  useSwitchTheme();
  // useLoadData();
  useHtmlLang();

  if (!useHasHydrated()) {
    return <Loading />;
  }

  return (
    <ErrorBoundary>
      <Router>
        <QueryClientProvider client={queryClient}>
          <Screen />
        </QueryClientProvider>
      </Router>
    </ErrorBoundary>
  );
}
