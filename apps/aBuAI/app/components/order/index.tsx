import { useMobileScreen } from "@/common/utils/utils";
import { CloudUploadOutlined, HomeOutlined, LeftOutlined, LinkOutlined } from "@ant-design/icons";
import { Attachments, AttachmentsProps, Sender } from "@ant-design/x";
import { <PERSON><PERSON>, Divider, Flex, GetProp, GetRef, Typography } from "antd"
import React, { useEffect, useState } from "react";
import styles from '../welcome/welcome.module.scss';
import classNames from "classnames";
import LoadingButton from "@ant-design/x/es/sender/components/LoadingButton";
import SendButton from "@ant-design/x/es/sender/components/SendButton";
import { IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import { useChatStore, useViewStore } from "@/store";
const {Title} = Typography

interface SenderCompProps {
  placeholder?: string
  isMobileScreen: boolean;
  onSubmit: (text: string) => void;
}

export const SenderComp: React.FC<SenderCompProps> = (props) => {
  const { isMobileScreen, onSubmit } = props;
  const [open, setOpen] = useState(false);
  const [items, setItems] = useState<GetProp<AttachmentsProps, 'items'>>([]);
  const [text, setText] = useState('');
  const [loading, setIsLoading] = useState(false);

  const attachmentsRef = React.useRef<GetRef<typeof Attachments>>(null);
  const senderRef = React.useRef<GetRef<typeof Sender>>(null);
  
  const senderHeader = (
    <Sender.Header
      title="Attachments"
      styles={{
        content: {
          padding: 0,
        },
      }}
      open={open}
      onOpenChange={setOpen}
      forceRender
    >
      <Attachments
        ref={attachmentsRef}
        beforeUpload={() => false}
        items={items}
        onChange={({ fileList }) => setItems(fileList)}
        placeholder={(type) =>
          type === 'drop'
            ? {
                title: 'Drop file here',
              }
            : {
                icon: <CloudUploadOutlined />,
                title: 'Upload files',
                description: 'Click or drag files to this area to upload',
              }
        }
        getDropContainer={() => senderRef.current?.nativeElement}
      />
    </Sender.Header>
  );
  function handleSubmit() {
    onSubmit(text)
    setItems([]);
    setText('');
  }
  return (
    <Sender
      {...(!isMobileScreen && !IS_WIDGET ? {
        rootClassName: classNames(styles.sender, '!w-[800px]'),
        classNames:{
          input: '!h-[100px] !max-h-[100px]',
        }}
      : { rootClassName: classNames(styles.sender, '!w-[auto] m-2') })}
      className='mt-2'
      placeholder={props.placeholder}
      ref={senderRef}
      header={senderHeader}
      actions={
        <div>
          <Button
            type="text"
            icon={<LinkOutlined />}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <Divider type='vertical'></Divider>
          {loading ? <LoadingButton></LoadingButton> : <SendButton></SendButton>}
        </div>
      }
      loading={loading}
      value={text}
      onChange={setText}
      onPasteFile={(file) => {
        attachmentsRef.current?.upload(file);
        setOpen(true);
      }}
      onSubmit={handleSubmit}
    />
  );
};

export const Order = () => {
  const isMobileScreen = useMobileScreen();

  const { setCurrentView } = useViewStore();
  const chatStore = useChatStore();
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.Order  // 添加会话类型
    });
  }
  function handleBack() {
    setCurrentView(View.Welcome);
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <Title className="text-center relative" level={3}>
          <div className="absolute top-0 left-0">
            <Button shape="round" onClick={handleBack} icon={<HomeOutlined />} size={'middle'} >返回</Button>
          </div>
          快捷下单，粘贴文本内容即可
        </Title>
        <SenderComp placeholder='输入下单客户名称、面料编号、色号、条数等信息' onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1' justify="center" vertical>
          <Title className="text-center" level={5}>快捷下单，粘贴文本内容即可</Title>
        </Flex>
        <SenderComp placeholder='输入下单客户名称、面料编号、色号、条数等信息' onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </Flex>}
    </div>
  );
}
