import type { FormProps } from 'antd'
import { BASE_PREFIX, IS_WIDGET, REG_PWD, View } from '@/common/constant'
import { useAccessStore, useViewStore } from "@/store";
import { LoadingOutlined } from '@ant-design/icons'
import { isPhoneValid } from '@ly/utils'
import WecomLogin, { WWLoginType } from '@ly/wecom'
import { Button, Form, Input, message } from 'antd'
import classNames from 'classnames'
import { useEffect, useRef, useState } from 'react'
import Image from "next/image";  // 使用 Next.js 的 Image 组件
import styles from './index.module.scss'
import Locale from "@/locales";
import ABUuPng from '@/asserts/icons/aBu.png'
import clsx from 'clsx';
// 免登录路径
// https://hcscmtest.zzfzyc.com/chat#/oauth?appid=wpr_caCgAAD6gDyRB6UURymefIKEn0zg&agentid=1000030&redirectUri=%2Fwelcome
export function LoginPage() {
  const [messageApi, contextHolder] = message.useMessage()
  const accessStore = useAccessStore();
  const { setCurrentView, params } = useViewStore();

  // 从 viewStore 获取参数，替代原来的 useSearchParams
  const corp_id = params?.corp_id || '';
  const agent_id = params?.agent_id || '';
  const code = params?.code || '';
  const redirectView = (params?.redirectView as View) || View.Welcome;
  console.log('redirect', corp_id, agent_id, redirectView)

  const handleScanCodeLogin = async ({ code, corp_id, agent_id }: { code: string; corp_id: string; agent_id: string }) => {
    try {
      const result = await accessStore.scanCodeLogin({ code, corp_id, agent_id });
      if (result.success) {
        messageApi.success('登录成功');
        setTimeout(() => {
          setCurrentView(redirectView as View);
        }, 500);
      } else {
        messageApi.error(`登录失败: ${result.message}`);
      }
    } catch (error: any) {
      console.error(error);
      messageApi.error(`登录失败: ${error.message}`);
    }
  };
  const [isWecomLogin, setIsWecomLogin] = useState(false)
  const wecomLogin = useRef<WecomLogin | null>(null)
  const isPending = accessStore.isPending

  const handleLogin: FormProps<FieldType>['onFinish'] = async (values) => {
    try {
      // 使用 aBuAI 的登录逻辑
      const result = await accessStore.login(values.phone!, values.password!);
      setCurrentView(redirectView as View);
      // if (result.success) {
      //   messageApi.success('登录成功');
      //   setTimeout(() => {
      //     setCurrentView(redirectView as View);
      //   }, 500);
      // } else {
      //   messageApi.error(`登录失败: ${result.message}`);
      // }
    }
    catch (_error) {
      messageApi.error('登录失败')
      accessStore.setPending(false)
    }
  }
  useEffect(() => {
    console.log('useEffect code', code, corp_id, agent_id)
    // 如果有企业微信授权码，自动进行登录
    if (code && corp_id && agent_id) {
      accessStore.setWecomInfo({ corpId: corp_id, agentId: agent_id });
      handleScanCodeLogin({ code, corp_id, agent_id });
    }
  }, [])
  useEffect(() => {
    if (isWecomLogin) {
      const url = `${window.location.origin}${BASE_PREFIX}`
      wecomLogin.current = new WecomLogin({
        el: '#wecom-login',
        appid: corp_id,
        agentid: agent_id,
        redirectUri: url,
        loginType: WWLoginType.corpApp,
        onSuccess: (code) => {
          handleScanCodeLogin({ code, corp_id, agent_id })
        },
        onFail: (error) => {
          console.error('error', error)
          messageApi.error(`登录失败: ${error.message}`)
        },
        onCheck: (isWeComLogin) => {
          if (isWeComLogin) {
            setIsWecomLogin(true)
          }
        },
      })
      wecomLogin.current?.mount()
    }
    return () => {
      wecomLogin.current?.unmount()
    }
  }, [isWecomLogin])
  interface FieldType {
    phone?: string
    password?: string
  }
  return (
    <>
      <div className={styles.wrapper}>
      {contextHolder}

        <div className={styles.loginCard}>
          <div className={clsx("no-dark mb-[20px] mx-auto", styles["auth-logo"])}>
            <div className="w-[100px] h-[100px] rounded-full">
              {/* 这里可以放头像 */}
              {
                IS_WIDGET ? <img src={ABUuPng} alt="用户头像" className="w-full h-full object-cover"></img> : <Image
                alt="用户头像"
                width={100}
                height={100}
                src={ABUuPng}
                className="w-full h-full object-cover"
              />
              }
            </div>
          </div>

          <div className={clsx(styles["auth-title"], 'text-center')}>{Locale.Auth.Title}</div>
          <div className={styles["auth-tips"]}>{Locale.Auth.Tips}</div>
          <div className={styles.tabs}>
            <span
              className={classNames(styles.tab, !isWecomLogin && styles.active)}
              onClick={() => setIsWecomLogin(false)}
            >
              密码登录
            </span>
            <span
              className={classNames(styles.tab, isWecomLogin && styles.active)}
              onClick={() => setIsWecomLogin(true)}
            >
              企业微信登录
            </span>
          </div>

          <div className={styles.content}>
            {!isWecomLogin
              ? (
                  <Form className={styles.form} onFinish={handleLogin}>
                    <Form.Item<FieldType>
                      name="phone"
                      rules={[
                        { required: true, message: '请输入手机号!' },
                        {
                          validator: (_, value) =>
                            isPhoneValid(value)
                              ? Promise.resolve()
                              : Promise.reject(new Error('请输入正确的手机号!')),
                        },
                      ]}
                    >
                      <Input className={styles.input} placeholder="请输入手机号" />
                    </Form.Item>
                    <Form.Item<FieldType>
                      name="password"
                      rules={[
                        { required: true, message: '请输入密码!' },
                        {
                          validator: (_, value) =>
                            REG_PWD.test(value)
                              ? Promise.resolve()
                              : Promise.reject(new Error('请输入6-18位密码!')),
                        },
                      ]}
                    >
                      <Input.Password className={styles.input} placeholder="请输入密码" />
                    </Form.Item>
                    <Form.Item>
                      <Button
                        htmlType="submit"
                        className={styles.button}
                        disabled={isPending}
                      >
                        {isPending ? <LoadingOutlined spin /> : '登录'}
                      </Button>
                    </Form.Item>
                  </Form>
                )
              : (
                  <div id="wecom-login" className={styles.wecomLogin}></div>
                )}
          </div>

        </div>
      </div>
    </>
  )
}
