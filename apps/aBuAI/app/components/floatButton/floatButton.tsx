import {useEffect, useRef, useState} from "react";
import styles from "./floatButton.module.scss";
import ABUuPng from '@/asserts/icons/aBu.png'
import {Home} from "../home/<USER>";
import {Modal} from 'antd';
import type {DraggableData, DraggableEvent} from 'react-draggable';
import Draggable from 'react-draggable';
import { FloatButton as AntdFloatButton } from 'antd'; 
import '../../styles/globals.scss'
import { ResizableBox } from "react-resizable";
import type { ResizeCallbackData } from "react-resizable";
import clsx from "clsx";
import { useMobileScreen } from "@/common/utils/utils";
import { rootId } from "@/init";
import { IS_WIDGET, View } from "@/common/constant";
import { useAccessStore, useViewStore } from "@/store";

const defaultWidth = 414
const defaultHeight = 800
interface Props {
  token?: string;
  container?: ShadowRoot;
}
export function FloatButton(props: Props) {
  const {token, container} = props
  
  // 外部容器是否是移动端屏幕大小
  const isMobileScreen = useMobileScreen()
  
  const [open, setOpen] = useState(false);
  const [disabled, setDisabled] = useState(true);
  const moveThreshold = 5
  const [height, setHeight] = useState(defaultHeight);
  const [width, setWidth] = useState(defaultWidth);
  const handleClose = () => {
    setOpen(false);
  };
  const [bounds, setBounds] = useState({left: 0, top: 0, bottom: 0, right: 0});
  const draggleRef = useRef<HTMLDivElement>(null);
  const handleOk = (e: React.MouseEvent<HTMLElement>) => {
    console.log(e);
    setOpen(false);
  };

  const onStart = (_event: DraggableEvent, uiData: DraggableData) => {
    const {clientWidth, clientHeight} = window.document.documentElement;
    const targetRect = draggleRef.current?.getBoundingClientRect();
    console.log('targetRect',targetRect)
    if (!targetRect) {
      return;
    }
    setBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
  };
  const onResize = (event, { size }: ResizeCallbackData) => {
    setHeight(size.height);
    setWidth(size.width);
  };
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [btnBounds, setBtnBounds] = useState({left: 0, top: 0, bottom: 0, right: 0});
  const prevBtnBoundsRef = useRef({left: 0, top: 0, bottom: 0, right: 0});
  const btnRef = useRef<HTMLDivElement>(null)
  const [isBtnDragging, setIsBtnDragging] = useState(false)
  // 添加一个标记来追踪拖拽状态
  const handleClick = () => {
    console.log('handleClick draggingRef', isBtnDragging)
  
    setOpen(!open);
  }
  
  const onBtnDragStart = (_event: DraggableEvent, uiData: DraggableData) => {
    setIsBtnDragging(true);
    const {clientWidth, clientHeight} = window.document.documentElement;
    const targetRect = btnRef.current?.getBoundingClientRect();
    
    if (!targetRect) return;
    
    // 处理触摸事件和鼠标事件
    const touchEvent = _event as TouchEvent | MouseEvent;
    if ('touches' in touchEvent) {
      const touch = touchEvent.touches[0];
      setMousePosition({
        x: touch.clientX,
        y: touch.clientY
      });
    } else {
      setMousePosition({
        x: touchEvent.clientX,
        y: touchEvent.clientY
      });
    }

    setBtnBounds({
      left: -targetRect.left + uiData.x,
      right: clientWidth - (targetRect.right - uiData.x),
      top: -targetRect.top + uiData.y,
      bottom: clientHeight - (targetRect.bottom - uiData.y),
    });
    prevBtnBoundsRef.current = btnBounds;
  }
  const [btnDragDisabled, setBtnDragDisabled] = useState(false)
  const onBtnDragStop = (event: DraggableEvent) => {
    if(isBtnDragging) {  
      setIsBtnDragging(false);
    }

    let deltaX = 0;
    let deltaY = 0;

    // 处理触摸事件和鼠标事件
    const touchEvent = event as TouchEvent | MouseEvent;
    if ('changedTouches' in touchEvent) {
      const touch = touchEvent.changedTouches[0];
      deltaX = Math.abs(touch.clientX - mousePosition.x);
      deltaY = Math.abs(touch.clientY - mousePosition.y);
    } else {
      deltaX = Math.abs(touchEvent.clientX - mousePosition.x);
      deltaY = Math.abs(touchEvent.clientY - mousePosition.y);
    }

    // 如果移动距离小于阈值，则触发点击事件
    if (deltaX < moveThreshold && deltaY < moveThreshold) {
      handleClick();
    }
  }
  const onBtnDragging = () => {
    console.log('onBtnDragging', prevBtnBoundsRef.current.left !== btnBounds.left, prevBtnBoundsRef.current.top !== btnBounds.top)
    if(prevBtnBoundsRef.current.left !== btnBounds.left || prevBtnBoundsRef.current.top !== btnBounds.top){
      setIsBtnDragging(true);
    }
  }
  
  const accessStore = useAccessStore()
  const viewStore = useViewStore()
  async function quickLogin(token: string) {
    accessStore.setToken(token)
    accessStore.setIsLoggedIn(true) // 添加登录状态更新
    const isSuccess = await accessStore.getUserInfo()
    if(isSuccess){
      viewStore.setCurrentView(View.Welcome)
    }else{
      accessStore.setToken('')
      accessStore.setIsLoggedIn(false) // 如果获取用户信息失败，重置登录状态
    }
  }
  useEffect(() => {
    if(open && token){
      quickLogin(token)
    }
  }, [open, token])
  const mountPoint = useRef<HTMLElement | ShadowRoot>()
  // 组件卸载时清理定时器
  useEffect(() => {
    setBtnDragDisabled(false)
    setTimeout(() => {
      mountPoint.current = getMountPoint();
    }, 0);
    return () => {
      setBtnDragDisabled(true);
    };
  }, []);

  // 获取挂载点元素
  const getMountPoint = () => {
    if (!props?.container) return document.body;
    return props.container
  };

  return <>
    <Draggable
      disabled={btnDragDisabled}
      bounds={btnBounds}
      nodeRef={btnRef}
      onStart={onBtnDragStart}
      onStop={onBtnDragStop}
      onDrag={onBtnDragging}
      enableUserSelectHack={false}
      allowAnyClick={true}
      cancel=".modal-content"
    >
      <div 
        ref={btnRef} 
        style={{ 
          position: 'fixed', 
          right: 0, 
          bottom: 20,
          touchAction: 'none',
          zIndex: 1001
        }}
      >
        <AntdFloatButton
          className={clsx(
            styles['float-btn'],
            isBtnDragging && styles['float-btn-dragging']
          )}
          icon={<img src={ABUuPng as unknown as string} className={styles['float-btn-icon']}/>}
        >
        </AntdFloatButton>
      </div>
    </Draggable>
    <Modal
      getContainer={mountPoint.current as HTMLElement}
      width={isMobileScreen ? '100%' : width}
      height={isMobileScreen ? '100%' : undefined}
      style={{
        top: isMobileScreen ? 0 : undefined,
        padding: isMobileScreen ? 0 : undefined,
        maxWidth: '100vw',
      }}
      title={
        <div
          style={{width: '100%', cursor: 'move', height: '20px'}}
          onMouseOver={() => {
            if (disabled && !isMobileScreen) {
              setDisabled(false);
            }
          }}
          onMouseOut={() => {
            if (!isMobileScreen) {
              setDisabled(true);
            }
          }}
        >
        </div>
      }
      className={styles.modal}
      classNames={{
        wrapper: styles['modal-wrapper'],
        header: styles['modal-header'],
        content: styles['modal-content'],
        body: 'h-full'
      }}
      footer={null}
      open={open}
      mask={false}
      onOk={handleOk}
      onCancel={handleClose}
      modalRender={(modal) => (
        isMobileScreen ? modal :<Draggable
          disabled={disabled}
          bounds={bounds}
          nodeRef={draggleRef}
          onStart={(event, uiData) => onStart(event, uiData)}
        >
          <div ref={draggleRef}>{modal}</div>
        </Draggable>
      )}
    >
      <ResizableBox
        width={isMobileScreen ? window.innerWidth : width}
        height={isMobileScreen ? window.innerHeight : height}
        resizeHandles={isMobileScreen ? [] : ['sw', 'se', 'nw', 'ne', 'w', 'e', 'n', 's']}
        minConstraints={[defaultWidth, defaultHeight]}
        handle={(h, ref) => <span className={clsx(styles['custom-handle'], styles[`custom-handle-${h}`])} ref={ref} />}
        onResize={onResize}
      >
        <div
          className={styles['dialog-wrapper']}
          style={{width: '100%', height: '100%'}}
          onMouseOver={() => {
            setDisabled(true);
          }}
        >
          <Home/>
        </div>
      </ResizableBox>
    </Modal>
  </>
;
}
