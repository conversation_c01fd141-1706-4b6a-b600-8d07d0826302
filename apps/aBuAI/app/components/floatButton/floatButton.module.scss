:global {
  .ant-float-btn .ant-float-btn-body .ant-float-btn-content .ant-float-btn-icon {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ant-modal-content{
    overflow: hidden;
  }
}

.float-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  transition: all 0.3s ease;
  touch-action: none;
  
  &-dragging {
    transform: scale(1.2);
    opacity: 0.8;
  }

  &-icon {
    height: 35px;
    min-height: 35px;
    width: 40px;
    min-width: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
  }
  
  @media (max-width: 768px) {
    width: 48px;
    height: 48px;
    
    &-icon {
      height: 36px;
      min-height: 36px;
      width: 36px;
      min-width: 36px;
    }
  }
}
.modal-wrapper{
  @media (max-width: 768px) {
    :global(.ant-modal){
      margin: 0;
      &>div{
        height: 100%;
      }
      
    }
  }
  .modal-header{
    position: absolute;
    right: 0;
    width: 72%;
    padding: 20px;
    z-index: 2;
    background-color: transparent;
  }
  .modal-content{
    padding: 0;
    height: 100%;
    border-radius: 24px;
  }
}

.dialog-wrapper {
  overflow: auto;
  //重写全局变量
  --window-height: 100%;
  --window-width: 100%;
  --container-min-width: '';
  --container-min-height: '';
}

.custom-handle {
  position: absolute;
  width: 8px;
  background-color: transparent;
  opacity: 0.75;
  border-radius: 4px;
}
.custom-handle-sw {
  z-index: 1;
  height: 8px;
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}
.custom-handle-se {
  height: 8px;
  bottom: -4px;
  right: -4px;
  z-index: 1;
  cursor: se-resize;
}
.custom-handle-nw {
  z-index: 1;
  height: 8px;
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}
.custom-handle-ne {
  height: 8px;
  z-index: 1;
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}
//东西
.custom-handle-w,
.custom-handle-e {
  top: 50%;
  height: 100%;
  transform: translateY(-50%);
  margin-top: -4px;
  cursor: ew-resize;
}
.custom-handle-w {
  left: -4px;
}
.custom-handle-e {
  right: -4px;
}
//南北
.custom-handle-n,
.custom-handle-s {
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 8px;
  margin-left: -4px;
  cursor: ns-resize;
}
.custom-handle-n {
  top: -4px;
}
.custom-handle-s {
  bottom: -4px;
}
