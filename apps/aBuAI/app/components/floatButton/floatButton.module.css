:global .ant-float-btn .ant-float-btn-body .ant-float-btn-content .ant-float-btn-icon {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
:global .ant-modal-content {
  overflow: hidden;
}

.float-btn {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 50px;
  height: 50px;
  transition: all 0.3s ease;
  touch-action: none;
}
.float-btn-dragging {
  transform: scale(1.2);
  opacity: 0.8;
}
.float-btn-icon {
  height: 35px;
  min-height: 35px;
  width: 40px;
  min-width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}
@media (max-width: 768px) {
  .float-btn {
    width: 48px;
    height: 48px;
  }
  .float-btn-icon {
    height: 36px;
    min-height: 36px;
    width: 36px;
    min-width: 36px;
  }
}

@media (max-width: 768px) {
  .modal-wrapper :global(.ant-modal) {
    margin: 0;
  }
  .modal-wrapper :global(.ant-modal) > div {
    height: 100%;
  }
}
.modal-wrapper .modal-header {
  position: absolute;
  right: 0;
  width: 72%;
  padding: 20px;
  z-index: 2;
  background-color: transparent;
}
.modal-wrapper .modal-content {
  padding: 0;
  height: 100%;
  border-radius: 24px;
}

.dialog-wrapper {
  overflow: auto;
  --window-height: 100%;
  --window-width: 100%;
  --container-min-width: "";
  --container-min-height: "";
}

.custom-handle {
  position: absolute;
  width: 8px;
  background-color: transparent;
  opacity: 0.75;
  border-radius: 4px;
}

.custom-handle-sw {
  z-index: 1;
  height: 8px;
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.custom-handle-se {
  height: 8px;
  bottom: -4px;
  right: -4px;
  z-index: 1;
  cursor: se-resize;
}

.custom-handle-nw {
  z-index: 1;
  height: 8px;
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.custom-handle-ne {
  height: 8px;
  z-index: 1;
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.custom-handle-w,
.custom-handle-e {
  top: 50%;
  height: 100%;
  transform: translateY(-50%);
  margin-top: -4px;
  cursor: ew-resize;
}

.custom-handle-w {
  left: -4px;
}

.custom-handle-e {
  right: -4px;
}

.custom-handle-n,
.custom-handle-s {
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 8px;
  margin-left: -4px;
  cursor: ns-resize;
}

.custom-handle-n {
  top: -4px;
}

.custom-handle-s {
  bottom: -4px;
}/*# sourceMappingURL=floatButton.module.css.map */