import { useMobileScreen } from "@/common/utils/utils";
import { Button, Flex, Typography } from "antd";
import { SenderComp } from "../order";
import { IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import {  useChatStore, useViewStore } from "@/store";
import { HomeOutlined } from "@ant-design/icons";
const {Title} = Typography
export function Stock() {
  const isMobileScreen = useMobileScreen();
  const { setCurrentView } = useViewStore();
  const chatStore = useChatStore();
  // const navigate = useNavigate()
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.Stock
    });
  }
  function handleBack() {
    setCurrentView(View.Welcome);
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <Title className="text-center relative" level={3}>
          <div className="absolute top-0 left-0">
            <Button shape="round" onClick={handleBack} icon={<HomeOutlined />} size={'middle'} >返回</Button>
          </div>
          查库存</Title>
        <div className="mx-auto">
          <SenderComp placeholder={'支持面料编号、色号、缸号等查询'} onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </div>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1 px-[20px]' justify="center" vertical>
          <Title className="text-center" level={4}>查库存</Title>
        </Flex>
        <SenderComp placeholder={'支持面料编号、色号、缸号等查询'} onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
      </Flex>}
    </div>
  );
}
