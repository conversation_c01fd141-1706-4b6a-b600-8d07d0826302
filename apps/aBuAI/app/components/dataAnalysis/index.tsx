import { useMobileScreen } from "@/common/utils/utils";
import { Button, Flex, Typography } from "antd";
import { SenderComp } from "../order";
// import { useNavigate } from "react-router-dom";
import {  IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import {  useChatStore, useViewStore } from "@/store";
import { PromptProps, Prompts, PromptsProps } from "@ant-design/x";
import { CoffeeOutlined, FireOutlined, HomeOutlined, SmileOutlined } from "@ant-design/icons";
const {Title} = Typography

export const DataAnalysis = () => {
  const isMobileScreen = useMobileScreen();
  const chatStore = useChatStore();
  const {setCurrentView} = useViewStore()
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.DataAnalysis  // 添加会话类型
    });
  }
  const items: PromptsProps['items'] = [
    {
      key: '5',
      icon: <SmileOutlined style={{ color: '#FAAD14' }} />,
      description: '近7天的销售分析',
      disabled: false,
    },
    {
      key: '6',
      icon: <FireOutlined style={{ color: '#FF4D4F' }} />,
      description: '近30天的销售分析',
      disabled: false,
    },
    {
      key: '1',
      icon: <CoffeeOutlined style={{ color: '#964B00' }} />,
      description: '本周的销售分析',
      disabled: false,
    },
    {
      key: '2',
      icon: <SmileOutlined style={{ color: '#FAAD14' }} />,
      description: '本月的销售分析',
      disabled: false,
    },
  ];
  function handleClickItem(item: { data: PromptProps; }) {
    handleSubmit(item.data.description as string)
  }
  function handleBack() {
    setCurrentView(View.Welcome);
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        
        <div className="w-[75%] mx-auto flex flex-col justify-center items-center">
          <Title className="w-[800px] text-center relative" level={3}>
            <div className="absolute top-0 left-0">
              <Button shape="round" onClick={handleBack} icon={<HomeOutlined />} size={'middle'} >返回</Button>
            </div>
            BI智能可视化分析，请要分析哪方面的内容
          </Title>
          <SenderComp placeholder="查找某个时间段的销售情况或查询业务员、客户、地区" onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
          <Prompts className="mt-2" classNames={{title: 'text-center'}} title="🤔 你可能会问？" items={items} wrap 
            styles={{
              item: {
                flex: 'none',
                width: 'calc(50% - 6px)',
              },
              subItem: {
                background: 'rgba(255,255,255,0.45)',
                border: '1px solid #FFF',
              },
            }} 
            onItemClick={handleClickItem}
          />
        </div>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1 px-[20px]' justify="center" vertical>
          <Title className="text-center" level={4}>BI智能可视化分析<br></br>请要分析哪方面的内容</Title>
          <Prompts 
            title={<div className="text-center">🤔 你可能会问？</div>} 
            items={items} 
            wrap 
            styles={{
              list: {
                display: 'flex',
                justifyContent: 'center',
                flexDirection: 'column'
              },
              item: {
                flex: 'none',
                // backgroundImage: `linear-gradient(137deg, #e5f4ff 0%, #efe7ff 100%)`,
                // border: 0,
              },
              subItem: {
                background: 'rgba(255,255,255,0.45)',
                border: '1px solid #FFF',
              },
            }}
            onItemClick={handleClickItem}
          />
        </Flex>
        <SenderComp placeholder="查找某个时间段的销售情况或查询业务员、客户、地区" onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        
        </Flex>}
    </div>
  );
};
