import styles from "./button.module.scss";
import { CSSProperties } from "react";
import clsx from "clsx";
import { LoadingOutlined } from "@ant-design/icons";

export type ButtonType = "primary" | "danger" | null;

export function IconButton(props: {
  onClick?: () => void;
  icon?: JSX.Element;
  type?: ButtonType;
  text?: string;
  bordered?: boolean;
  shadow?: boolean;
  className?: string;
  title?: string;
  disabled?: boolean;
  loading?: boolean; // 添加 loading 属性
  tabIndex?: number;
  autoFocus?: boolean;
  style?: CSSProperties;
  aria?: string;
}) {
  return (
    <button
      className={clsx(
        "clickable",
        styles["icon-button"],
        {
          [styles.border]: props.bordered,
          [styles.shadow]: props.shadow,
        },
        styles[props.type ?? ""],
        props.className,
      )}
      onClick={props.onClick}
      title={props.title}
      disabled={props.disabled || props.loading} // loading 时禁用按钮
      role="button"
      tabIndex={props.tabIndex}
      autoFocus={props.autoFocus}
      style={props.style}
      aria-label={props.aria}
    >
      {props.loading ? (
        <div className={styles["icon-button-icon"]}>
          <LoadingOutlined spin />
        </div>
      ) : (
        <>
          {props.icon && (
            <div
              aria-label={props.text || props.title}
              className={clsx(styles["icon-button-icon"], {
                "no-dark": props.type === "primary",
              })}
            >
              {props.icon}
            </div>
          )}

          {props.text && (
            <div
              aria-label={props.text || props.title}
              className={styles["icon-button-text"]}
            >
              {props.text}
            </div>
          )}
        </>
      )}
    </button>
  );
}
