.icon-button {
  background-color: var(--white);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  outline: none;
  border: none;
  color: var(--black);
}
.icon-button[disabled] {
  cursor: not-allowed;
  opacity: 0.5;
}
.icon-button.primary {
  background-color: var(--primary);
  color: white;
}
.icon-button.primary path {
  fill: white !important;
}
.icon-button.danger {
  color: rgba(255, 0, 0, 0.8);
  border-color: rgba(255, 0, 0, 0.5);
  background-color: rgba(255, 0, 0, 0.05);
}
.icon-button.danger:hover {
  border-color: red;
  background-color: rgba(255, 0, 0, 0.1);
}
.icon-button.danger path {
  fill: red !important;
}
.icon-button:hover, .icon-button:focus {
  border-color: var(--primary);
}

.shadow {
  box-shadow: var(--card-shadow);
}

.border {
  border: var(--border-in-light);
}

.icon-button-icon {
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media only screen and (max-width: 600px) {
  .icon-button {
    padding: 16px;
  }
}
.icon-button-text {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.icon-button-text:not(:first-child) {
  margin-left: 5px;
}/*# sourceMappingURL=button.module.css.map */