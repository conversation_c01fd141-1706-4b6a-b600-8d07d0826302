import { useMobileScreen } from "@/common/utils/utils";
import { Button, Flex, Typography } from "antd";
import { SenderComp } from "../order";
import { IS_WIDGET, View } from "@/common/constant";
import { SessionType } from "@/types/session";
import { useChatStore, useViewStore } from "@/store";
import { PromptProps, Prompts, PromptsProps } from "@ant-design/x";
import { BarChartOutlined, CoffeeOutlined, FallOutlined, FireOutlined, HomeOutlined, PieChartOutlined, RadarChartOutlined, SmileOutlined, StockOutlined } from "@ant-design/icons";
const {Title} = Typography
export function FabricQuotation() {
  const isMobileScreen = useMobileScreen();
  const { setCurrentView } = useViewStore();
  const chatStore = useChatStore();
  // const navigate = useNavigate()
  function handleSubmit(text: string) {
    chatStore.newSession();
    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.FabricQuotation
    });
  }
  const items: PromptsProps['items'] = [
    {
      key: '1',
      icon: <StockOutlined style={{ color: '#964B00' }} />,
      description: '预测未来一周的销售情况',
      disabled: false,
    },
    {
      key: '2',
      icon: <BarChartOutlined style={{ color: '#FAAD14' }} />,
      description: '预测卖得最高面料的销售情况',
      disabled: false,
    },
    {
      key: '3',
      icon: <PieChartOutlined style={{ color: '#FF4D4F' }} />,
      description: '预测卖得最高面料色号的销售情况',
      disabled: false,
    },
    {
      key: '4',
      icon: <RadarChartOutlined style={{ color: '#964B00' }} />,
      description: '预测整体的客户生命周期情况',
      disabled: false,
    },
    {
      key: '5',
      icon: <FallOutlined style={{ color: '#FAAD14' }} />,
      description: '客户流失预测',
      disabled: false,
    },
  ];
  function handleBack() {
    setCurrentView(View.Welcome);
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <Title className="text-center relative" level={3}>
          <div className="absolute top-0 left-0">
            <Button shape="round" onClick={handleBack} icon={<HomeOutlined />} size={'middle'} >返回</Button>
          </div>
          查面料销售价</Title>
        <div className="mx-auto">
          <SenderComp placeholder={'输入面料编号查询'} onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </div>
      </Flex>: <Flex vertical className='w-full h-full'>
        <Flex className='flex-1 px-[20px]' justify="center" vertical>
          <Title className="text-center" level={4}>查面料销售价</Title>
        </Flex>
        <SenderComp placeholder={'输入面料编号查询"'} onSubmit={handleSubmit} isMobileScreen={isMobileScreen}/>
        </Flex>}
    </div>
  );
}
