import { useDebouncedCallback } from "use-debounce";
import React, {
  useState,
  useRef,
  useEffect,
  useMemo,
  useCallback,
  Fragment,
  RefObject,
} from "react";
import "../../styles/highlight.scss";
import SendWhiteIcon from "@/asserts/icons/send-white.svg";
import CopyIcon from "@/asserts/icons/copy.svg";
import StopIcon from "@/asserts/icons/pause.svg";
import LoadingButtonIcon from "@/asserts/icons/loading.svg";
import PromptIcon from "@/asserts/icons/prompt.svg";
import ResetIcon from "@/asserts/icons/reload.svg";
import BreakIcon from "@/asserts/icons/break.svg";
import DeleteIcon from "@/asserts/icons/clear.svg";
import ExcelIcon from "@/asserts/icons/excel.svg";
import PDFIcon from "@/asserts/icons/pdf.svg";
import WordIcon from "@/asserts/icons/word.svg";
import ConfirmIcon from "@/asserts/icons/confirm.svg";
import CloseIcon from "@/asserts/icons/close.svg";
import ImageIcon from "@/asserts/icons/image.svg";
import BottomIcon from "@/asserts/icons/bottom.svg";
import { SessionType } from "@/types/session";
import {
  ChatMessage,
  useChatStore,
  BOT_HELLO,
  createMessage,
  DEFAULT_CONFIG,
  useAccessStore,
  SubmitKey,
  useAppConfig,
  useViewStore,
  FABRIC_QUOTAION_BOT_HELLO,
} from "@/store";

import {
  copyToClipboard,
  selectOrCopy,
  autoGrowTextArea,
  useMobileScreen,
  getMessageTextContent,
  getMessageImages,
  isVisionModel,
  safeLocalStorage,
  getMessageFiles,
  FileType,
  getMessageInteractives,
} from "@/common/utils/utils";

import { uploadImage as uploadImageRemote } from "@/common/utils/chat";
import { Prompts, PromptsProps, ThoughtChain } from '@ant-design/x';
// import dynamic from "next/dynamic";

import Locale from "@/locales";

import { IconButton } from "@/components/button/button";
import styles from "./chat.module.scss";


import {
  CHAT_PAGE_SIZE,
  REQUEST_TIMEOUT_MS,
  UNFINISHED_INPUT,
  View,
} from "@/common/constant";
import { Avatar } from "@/components/emoji/emoji";
import { MaskAvatar } from "@/components/mask/mask";
import {ChatCommandPrefix, useChatCommand} from "@/common/command";
import { prettyObject } from "@/common/utils/format";

import { isEmpty } from "lodash-es";
import clsx from "clsx";
import {Prompt, usePromptStore} from "@/store/prompt";
import {Markdown} from "@/components/markdown/markdown";
import dynamic from "next/dynamic";
import {nanoid} from "nanoid";
// import { useLocation } from "react-router-dom";
import { message } from "antd";
import { BarChartOutlined, BookOutlined, BulbOutlined, CheckCircleOutlined, FileSearchOutlined, LineChartOutlined, MessageOutlined, ShoppingCartOutlined } from "@ant-design/icons";
import { ChatControllerPool } from "@/client/controller";

const localStorage = safeLocalStorage();


// const Markdown = dynamic(async () => (await import("@/componets/markdown/markdown")).Markdown, {
//   loading: () => <LoadingIcon />,
// });

function useSubmitHandler() {
  const isComposing = useRef(false);
  const config = useAppConfig()
  config.submitKey = SubmitKey.Enter
  const submitKey = config.submitKey
  useEffect(() => {
    const onCompositionStart = () => {
      isComposing.current = true;
    };
    const onCompositionEnd = () => {
      isComposing.current = false;
    };

    window.addEventListener("compositionstart", onCompositionStart);
    window.addEventListener("compositionend", onCompositionEnd);

    return () => {
      window.removeEventListener("compositionstart", onCompositionStart);
      window.removeEventListener("compositionend", onCompositionEnd);
    };
  }, []);

  const shouldSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // Fix Chinese input method "Enter" on Safari
    if (e.keyCode == 229) return false;
    if (e.key !== "Enter") return false;
    if (e.key === "Enter" && (e.nativeEvent.isComposing || isComposing.current))
      return false;
    return (
      (config.submitKey === SubmitKey.AltEnter && e.altKey) ||
      (config.submitKey === SubmitKey.CtrlEnter && e.ctrlKey) ||
      (config.submitKey === SubmitKey.ShiftEnter && e.shiftKey) ||
      (config.submitKey === SubmitKey.MetaEnter && e.metaKey) ||
      (config.submitKey === SubmitKey.Enter &&
        !e.altKey &&
        !e.ctrlKey &&
        !e.shiftKey &&
        !e.metaKey)
    );
  };

  return {
    submitKey,
    shouldSubmit,
  };
}

export type RenderPrompt = Pick<Prompt, "title" | "content">;

export function PromptHints(props: {
  prompts: RenderPrompt[];
  onPromptSelect: (prompt: RenderPrompt) => void;
}) {
  const noPrompts = props.prompts.length === 0;
  const [selectIndex, setSelectIndex] = useState(0);
  const selectedRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectIndex(0);
  }, [props.prompts.length]);

  useEffect(() => {
    const onKeyDown = (e: KeyboardEvent) => {
      if (noPrompts || e.metaKey || e.altKey || e.ctrlKey) {
        return;
      }
      // arrow up / down to select prompt
      const changeIndex = (delta: number) => {
        e.stopPropagation();
        e.preventDefault();
        const nextIndex = Math.max(
          0,
          Math.min(props.prompts.length - 1, selectIndex + delta),
        );
        setSelectIndex(nextIndex);
        selectedRef.current?.scrollIntoView({
          block: "center",
        });
      };

      if (e.key === "ArrowUp") {
        changeIndex(1);
      } else if (e.key === "ArrowDown") {
        changeIndex(-1);
      } else if (e.key === "Enter") {
        const selectedPrompt = props.prompts.at(selectIndex);
        if (selectedPrompt) {
          props.onPromptSelect(selectedPrompt);
        }
      }
    };

    window.addEventListener("keydown", onKeyDown);

    return () => window.removeEventListener("keydown", onKeyDown);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.prompts.length, selectIndex]);

  if (noPrompts) return null;
  return (
    <div className={styles["prompt-hints"]}>
      {props.prompts.map((prompt, i) => (
        <div
          ref={i === selectIndex ? selectedRef : null}
          className={clsx(styles["prompt-hint"], {
            [styles["prompt-hint-selected"]]: i === selectIndex,
          })}
          key={prompt.title + i.toString()}
          onClick={() => props.onPromptSelect(prompt)}
          onMouseEnter={() => setSelectIndex(i)}
        >
          <div className={styles["hint-title"]}>{prompt.title}</div>
          <div className={styles["hint-content"]}>{prompt.content}</div>
        </div>
      ))}
    </div>
  );
}
function ClearContextDivider() {
  const chatStore = useChatStore();
  const session = chatStore.currentSession();

  return (
    <div
      className={styles["clear-context"]}
      onClick={() =>
        chatStore.updateTargetSession(
          session,
          (session) => (session.clearContextIndex = undefined),
        )
      }
    >
      <div className={styles["clear-context-tips"]}>{Locale.Context.Clear}</div>
      <div className={styles["clear-context-revert-btn"]}>
        {Locale.Context.Revert}
      </div>
    </div>
  );
}

export function ChatAction(props: {
  animation?: boolean, // 是否需要开启动画，开启动画则初始不展示text，只展示icon
  text: string;
  icon: JSX.Element;
  onClick: () => void;
  className?: string; // 添加自定义类名支持
}) {
  const {animation = true, className} = props
  const iconRef = useRef<HTMLDivElement>(null);
  const textRef = useRef<HTMLDivElement>(null);
  const [width, setWidth] = useState({
    full: 16,
    icon: 16,
  });
  useEffect(() => {
    if(!animation){
      setTimeout(updateWidth, 1);
    }
  }, [animation]);
  function updateWidth() {
    if (!iconRef.current || !textRef.current) return;
    const getWidth = (dom: HTMLDivElement) => dom.getBoundingClientRect().width;
    const textWidth = getWidth(textRef.current);
    const iconWidth = getWidth(iconRef.current);
    setWidth({
      full: textWidth + iconWidth,
      icon: iconWidth,
    });
  }

  return (
    <div
      className={clsx(
        styles["chat-input-action"],
        "clickable",
        !animation ? styles['always-show'] : null,
        className // 添加自定义类名
      )}
      onClick={() => {
        props.onClick();
        setTimeout(updateWidth, 1);
      }}
      onMouseEnter={updateWidth}
      onTouchStart={updateWidth}
      style={
        {
          "--icon-width": `${width.icon}px`,
          "--full-width": `${width.full}px`,
        } as React.CSSProperties
      }
    >
      <div ref={iconRef} className={styles["icon"]}>
        {props.icon}
      </div>
      <div className={styles["text"]} ref={textRef}>
        {props.text}
      </div>
    </div>
  );
}

function useScrollToBottom(
  scrollRef: RefObject<HTMLDivElement>,
  detach: boolean = false,
) {
  // for auto-scroll

  const [autoScroll, setAutoScroll] = useState(true);
  function scrollDomToBottom() {
    const dom = scrollRef.current;
    if (dom) {
      requestAnimationFrame(() => {
        setAutoScroll(true);
        dom.scrollTo(0, dom.scrollHeight);
      });
    }
  }

  // auto scroll
  useEffect(() => {
    if (autoScroll && !detach) {
      scrollDomToBottom();
    }
  });

  return {
    scrollRef,
    autoScroll,
    setAutoScroll,
    scrollDomToBottom,
  };
}

export function ChatActions(props: {
  uploadImage: () => void;
  // showPromptHints?: () => void;
  setAttachImages: (images: string[]) => void;
  setUploading: (uploading: boolean) => void;
  scrollToBottom: () => void;
  hitBottom: boolean;
  uploading: boolean;
  setUserInput: (input: string) => void;
}) {
  const chatStore = useChatStore();
  const session = chatStore.currentSession();

  const couldStop = ChatControllerPool.hasPending();
  const stopAll = () => ChatControllerPool.stopAll();
  // switch model
  const currentModel = session.mask.modelConfig.model;
  const [showUploadImage, setShowUploadImage] = useState(false);

  useEffect(() => {
    // 是否视觉模型
    const show = isVisionModel(currentModel);
    setShowUploadImage(show);
    if (!show) {
      props.setAttachImages([]);
      props.setUploading(false);
    }

  }, [chatStore, currentModel, session]);
  const [currentType, setCurrentType] = useState(session.type || SessionType.Chat);
  return (
    <div className={styles["chat-input-actions"]}>
      <>
        {couldStop && (
          <ChatAction
            onClick={stopAll}
            text={Locale.Chat.InputActions.Stop}
            icon={<StopIcon />}
          />
        )}
        {!props.hitBottom && (
          <ChatAction
            onClick={props.scrollToBottom}
            text={Locale.Chat.InputActions.ToBottom}
            icon={<BottomIcon />}
          />
        )}

        {showUploadImage && (
          <ChatAction
            onClick={props.uploadImage}
            text={Locale.Chat.InputActions.UploadImage}
            icon={props.uploading ? <LoadingButtonIcon /> : <ImageIcon />}
          />
        )}
        {/* <ChatAction
          onClick={props.showPromptHints}
          text={Locale.Chat.InputActions.Prompt}
          icon={<PromptIcon />}
        /> */}
        <ChatAction
          text={Locale.Chat.InputActions.Clear}
          icon={<BreakIcon />}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              if (session.clearContextIndex === session.messages.length) {
                session.clearContextIndex = undefined;
              } else {
                session.clearContextIndex = session.messages.length;
                session.memoryPrompt = ""; // will clear memory
              }
            });
          }}
        />
        <ChatAction
          animation={false}
          text={'聊天模式'}
          icon={<MessageOutlined className="text-blue-500"/>}
          className={currentType === SessionType.Chat ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.Chat;
            });
            setCurrentType(SessionType.Chat);
          }}
        />
        <ChatAction
          animation={false}
          text={'下单'}
          icon={<ShoppingCartOutlined className={"text-blue-500"}/>}
          className={currentType === SessionType.Order ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.Order;
            });
            setCurrentType(SessionType.Order);
          }}
        />
        <ChatAction
          animation={false}
          text={'查百科'}
          icon={<BookOutlined className={"text-orange-500"}/>}
          className={currentType === SessionType.Encyclopaedia ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.Encyclopaedia;
            });
            setCurrentType(SessionType.Encyclopaedia);
          }}
        />
        <ChatAction
          animation={false}
          text={'数据分析'}
          icon={<BarChartOutlined className={"text-purple-500"}/>}
          className={currentType === SessionType.DataAnalysis ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.DataAnalysis;
            });
            setCurrentType(SessionType.DataAnalysis);
          }}
        />
        {/* <ChatAction
          animation={false}
          text={'销售预测'}
          icon={<LineChartOutlined className={"text-green-500"}/>}
          className={currentType === SessionType.Salesforecast ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.Salesforecast;
            });
            setCurrentType(SessionType.Salesforecast);
          }}
        /> */}
        <ChatAction
          animation={false}
          text={'面料价格'}
          icon={<LineChartOutlined className={"text-green-500"}/>}
          className={currentType === SessionType.FabricQuotation ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.FabricQuotation;
            });
            setCurrentType(SessionType.FabricQuotation);
          }}
        />
        <ChatAction
          animation={false}
          text={'查库存'}
          icon={<FileSearchOutlined  className={"text-green-500"}/>}
          className={currentType === SessionType.Stock ? styles["chat-action-selected"] : ""}
          onClick={() => {
            chatStore.updateTargetSession(session, (session) => {
              session.type = SessionType.Stock;
            });
            setCurrentType(SessionType.Stock);
          }}
        />
      </>
    </div>
  );
}

export function DeleteImageButton(props: { deleteImage: () => void }) {
  return (
    <div className={styles["delete-image"]} onClick={props.deleteImage}>
      <DeleteIcon />
    </div>
  );
}

// 将动态导入移到组件外部
const interactiveComponents = new Map();

function CChat() {
  type RenderMessage = ChatMessage & { preview?: boolean };

  const chatStore = useChatStore();
  const session = chatStore.currentSession();
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const [userInput, setUserInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { shouldSubmit } = useSubmitHandler();
  const scrollRef = useRef<HTMLDivElement>(null);
  const isScrolledToBottom = scrollRef?.current
    ? Math.abs(
    scrollRef.current.scrollHeight -
    (scrollRef.current.scrollTop + scrollRef.current.clientHeight),
  ) <= 1
    : false;
  const { setAutoScroll, scrollDomToBottom } = useScrollToBottom(
    scrollRef,
    isScrolledToBottom,
  );
  const [hitBottom, setHitBottom] = useState(true);
  const isMobileScreen = useMobileScreen();
  const [attachImages, setAttachImages] = useState<string[]>([]);
  const [uploading, setUploading] = useState(false);

  // auto grow input
  const [inputRows, setInputRows] = useState(2);
  const measure = useDebouncedCallback(
    () => {
      const rows = inputRef.current ? autoGrowTextArea(inputRef.current) : 1;
      const inputRows = Math.min(
        20,
        Math.max(2 + Number(!isMobileScreen), rows),
      );
      setInputRows(inputRows);
    },
    100,
    {
      leading: true,
      trailing: true,
    },
  );

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(measure, [userInput]);

  // chat commands shortcuts
  // const chatCommands = useChatCommand({
  //   new: () => chatStore.newSession(),
  //   prev: () => chatStore.nextSession(-1),
  //   next: () => chatStore.nextSession(1),
  //   clear: () =>
  //     chatStore.updateTargetSession(
  //       session,
  //       (session) => (session.clearContextIndex = session.messages.length),
  //     ),
  //   fork: () => chatStore.forkSession(),
  //   del: () => chatStore.deleteSession(chatStore.currentSessionIndex),
  // });

  // only search prompts when user input is short
  // const SEARCH_TEXT_LIMIT = 30;
  const onInput = (text: string) => {
    setUserInput(text);
    // const n = text.trim().length;

    // clear search results
    // if (n === 0) {
    //   setPromptHints([]);
    // } else if (text.match(ChatCommandPrefix)) {
    //   setPromptHints(chatCommands.search(text));
    // } else if (!DEFAULT_CONFIG.disablePromptHint && n < SEARCH_TEXT_LIMIT) {
    //   // check if need to trigger auto completion
    //   if (text.startsWith("/")) {
    //     const searchText = text.slice(1);
    //     onSearch(searchText);
    //   }
    // }
  };
  const accessStore = useAccessStore();
  // const navigate = useNavigate();
  const { setCurrentView, currentView, params } = useViewStore();

  const [messageApi, contextHolder] = message.useMessage();
  // 发送
  const doSubmit = (userInput: string) => {
    if (userInput.trim() === "" && isEmpty(attachImages)) return;
    // 添加登录校验
    if (!accessStore.isAuthorized()) {
      messageApi.warning("请先登录后再发送消息");
      setTimeout(() => {
        setCurrentView(View.Login);
      }, 500)
      return;
    }
    // const matchCommand = chatCommands.match(userInput);
    // if (matchCommand.matched) {
    //   setUserInput("");
    //   matchCommand.invoke();
    //   return;
    // }
    setIsLoading(true);
    chatStore
      .onUserInput(userInput, attachImages)
      .then(() => setIsLoading(false));
    setAttachImages([]);
    chatStore.setLastInput(userInput);
    setUserInput("");
    if (!isMobileScreen) inputRef.current?.focus();
    setAutoScroll(true);
  };
  // useEffect(() => {
  //   // 文件消息
  //   session.messages.push(createMessage({
  //     id: nanoid(),
  //     date: new Date().toLocaleString(),
  //     role: "assistant",
  //     content: [
  //       {
  //         type: "excel_url",
  //         excel_url: {
  //           name: 'excel.xlsx',
  //           url: "https://example.com/excel.xlsx"
  //         }
  //       }
  //     ]
  //   }))
  //   // 可交互消息
  //   session.messages.push(createMessage({
  //     id: nanoid(),
  //     date: new Date().toLocaleString(),
  //     role: "assistant",
  //     content: [
  //       {
  //         type: "interactive",
  //         interactive: {
  //           template: 'order.template.tsx',
  //           props: {
  //             orderNo: 'XS-ZT-20240012447',
  //             roll: 13,
  //             weight: 200,
  //             length: 10,
  //             statusName: '已下单-配布中',
  //             time: '14:21',
  //             price: 1000,
  //             paymentStatus: '未收款'
  //           },
  //           events: {
  //             onClick: () => {
  //               // 跳转到订单进度
  //               console.log('onClick')
  //             }
  //           }
  //         }
  //       }
  //     ]
  //   }))
  //   // 可交互消息
  //   session.messages.push(createMessage({
  //     id: nanoid(),
  //     date: new Date().toLocaleString(),
  //     role: "assistant",
  //     content: [
  //       {
  //         type: "interactive",
  //         interactive: {
  //           template: 'customer.template.tsx',
  //           props: {
  //             customerName: '张三',
  //             level: 'VIP',
  //             debt: 1000,
  //             note: '备注信息',
  //             mostPurchased: '布料',
  //             lastOrder: '2024-01-01',
  //             followUpPerson: '李四',
  //             salesperson: '王五',
  //           },
  //           events: {
  //             onClick: () => {
  //               // 跳转到客户信息
  //               console.log('onClick')
  //             }
  //           }
  //         }
  //       }
  //     ]
  //   }))
  //   // 可交互消息
  //   session.messages.push(createMessage({
  //     id: nanoid(),
  //     date: new Date().toLocaleString(),
  //     role: "assistant",
  //     content: [
  //       {
  //         type: "interactive",
  //         interactive: {
  //           template: 'billingSummary.template.tsx',
  //           props: {
  //             monthlyPurchase: 2000,
  //             debt: 30.99
  //           },
  //           events: {
  //             onClick: () => {
  //               // 跳转到客户对账单
  //               console.log('onClick')
  //             }
  //           }
  //         }
  //       }
  //     ]
  //   }))
  // }, [])

  useEffect(() => {
    chatStore.updateTargetSession(session, (session) => {
      const stopTiming = Date.now() - REQUEST_TIMEOUT_MS;

      session.messages.forEach((m) => {
        // check if should stop all stale messages
        if (m.isError || new Date(m.date).getTime() < stopTiming) {
          if (m.streaming) {
            m.streaming = false;
          }
          if (m.content.length === 0) {
            m.isError = true;
            m.content = prettyObject({
              error: true,
              message: "empty response",
            });
          }
        }
      });

    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session]);

  // check if should send message
  const onInputKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    // if ArrowUp and no userInput, fill with last input
    if (
      e.key === "ArrowUp" &&
      userInput.length <= 0 &&
      !(e.metaKey || e.altKey || e.ctrlKey)
    ) {
      setUserInput(chatStore.lastInput ?? "");
      e.preventDefault();
      return;
    }
    if (shouldSubmit(e)) {
      doSubmit(userInput);
      e.preventDefault();
    }
  };
  const onRightClick = (e: any, message: ChatMessage) => {
    // copy to clipboard
    if (selectOrCopy(e.currentTarget, getMessageTextContent(message))) {
      if (userInput.length === 0) {
        setUserInput(getMessageTextContent(message));
      }

      e.preventDefault();
    }
  };

  const deleteMessage = (msgId?: string) => {
    chatStore.updateTargetSession(
      session,
      (session) =>
        (session.messages = session.messages.filter((m) => m.id !== msgId)),
    );
  };

  const onDelete = (msgId: string) => {
    deleteMessage(msgId);
  };

  const onResend = (message: ChatMessage) => {
    // when it is resending a message
    // 1. for a user's message, find the next bot response
    // 2. for a bot's message, find the last user's input
    // 3. delete original user input and bot's message
    // 4. resend the user's input

    const resendingIndex = session.messages.findIndex(
      (m) => m.id === message.id,
    );

    if (resendingIndex < 0 || resendingIndex >= session.messages.length) {
      console.error("[Chat] failed to find resending message", message);
      return;
    }

    let userMessage: ChatMessage | undefined;
    let botMessage: ChatMessage | undefined;

    if (message.role === "assistant") {
      // if it is resending a bot's message, find the user input for it
      botMessage = message;
      for (let i = resendingIndex; i >= 0; i -= 1) {
        if (session.messages[i].role === "user") {
          userMessage = session.messages[i];
          break;
        }
      }
    } else if (message.role === "user") {
      // if it is resending a user's input, find the bot's response
      userMessage = message;
      for (let i = resendingIndex; i < session.messages.length; i += 1) {
        if (session.messages[i].role === "assistant") {
          botMessage = session.messages[i];
          break;
        }
      }
    }

    if (userMessage === undefined) {
      console.error("[Chat] failed to resend", message);
      return;
    }

    // delete the original messages
    deleteMessage(userMessage.id);
    deleteMessage(botMessage?.id);

    // resend the message
    setIsLoading(true);
    const textContent = getMessageTextContent(userMessage);
    const images = getMessageImages(userMessage);
    chatStore.onUserInput(textContent, images).then(() => setIsLoading(false));
    inputRef.current?.focus();
  };

  const context: RenderMessage[] = useMemo(() => {
    console.log('mask',session.mask.context)
    return session.mask.hideContext ? [] : session.mask.context.slice();
  }, [session.mask.context, session.mask.hideContext]);

  // 自定义问候语
  let botHello = BOT_HELLO
  // 面料报价 问候语
  if(session.type === SessionType.FabricQuotation){
    botHello = FABRIC_QUOTAION_BOT_HELLO
    console.log('botHello',botHello)
  }
  if (
    context.length === 0 &&
    session.messages.at(0)?.content !== botHello.content
  ) {
    const copiedHello = Object.assign({}, botHello);

    context.push(copiedHello);
  }

  // preview messages
  const renderMessages = useMemo(() => {
    return context
      .concat(session.messages as RenderMessage[])
      // .concat(
      //   isLoading
      //     ? [
      //       {
      //         ...createMessage({
      //           role: "assistant",
      //           content: "……",
      //         }),
      //         preview: true,
      //       },
      //     ]
      //     : [],
      // )
      // .concat(
      //   userInput.length > 0
      //     ? [
      //       {
      //         ...createMessage({
      //           role: "user",
      //           content: userInput,
      //         }),
      //         preview: true,
      //       },
      //     ]
      //     : [],
      // );
  }, [
    context,
    isLoading,
    session.messages,
    userInput,
  ]);

  const [msgRenderIndex, _setMsgRenderIndex] = useState(
    Math.max(0, renderMessages.length - CHAT_PAGE_SIZE),
  );
  function setMsgRenderIndex(newIndex: number) {
    newIndex = Math.min(renderMessages.length - CHAT_PAGE_SIZE, newIndex);
    newIndex = Math.max(0, newIndex);
    _setMsgRenderIndex(newIndex);
  }

  const messages = useMemo(() => {
    const endRenderIndex = Math.min(
      msgRenderIndex + 3 * CHAT_PAGE_SIZE,
      renderMessages.length,
    );
    return renderMessages.slice(msgRenderIndex, endRenderIndex);
  }, [msgRenderIndex, renderMessages]);

  const onChatBodyScroll = (e: HTMLElement) => {
    const bottomHeight = e.scrollTop + e.clientHeight;
    const edgeThreshold = e.clientHeight;

    const isTouchTopEdge = e.scrollTop <= edgeThreshold;
    const isTouchBottomEdge = bottomHeight >= e.scrollHeight - edgeThreshold;
    const isHitBottom =
      bottomHeight >= e.scrollHeight - (isMobileScreen ? 4 : 10);

    const prevPageMsgIndex = msgRenderIndex - CHAT_PAGE_SIZE;
    const nextPageMsgIndex = msgRenderIndex + CHAT_PAGE_SIZE;

    if (isTouchTopEdge && !isTouchBottomEdge) {
      setMsgRenderIndex(prevPageMsgIndex);
    } else if (isTouchBottomEdge) {
      setMsgRenderIndex(nextPageMsgIndex);
    }

    setHitBottom(isHitBottom);
    setAutoScroll(isHitBottom);
  };
  function scrollToBottom() {
    setMsgRenderIndex(renderMessages.length - CHAT_PAGE_SIZE);
    scrollDomToBottom();
  }

  // clear context index = context length + index in messages
  const clearContextIndex =
    (session.clearContextIndex ?? -1) >= 0
      ? session.clearContextIndex! + context.length - msgRenderIndex
      : -1;

  const autoFocus = !isMobileScreen; // wont auto focus on mobile screen
  const showMaxIcon = !isMobileScreen

  // remember unfinished input
  useEffect(() => {
    // try to load from local storage
    const key = UNFINISHED_INPUT(session.id);
    const mayBeUnfinishedInput = localStorage.getItem(key);
    if (mayBeUnfinishedInput && userInput.length === 0) {
      setUserInput(mayBeUnfinishedInput);
      localStorage.removeItem(key);
    }

    const dom = inputRef.current;
    return () => {
      localStorage.setItem(key, dom?.value ?? "");
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handlePaste = useCallback(
    async (event: React.ClipboardEvent<HTMLTextAreaElement>) => {
      const currentModel = chatStore.currentSession().mask.modelConfig.model;
      if (!isVisionModel(currentModel)) {
        return;
      }
      // @ts-expect-error window.clipboardData IE support
      const items = (event.clipboardData || window.clipboardData).items;
      for (const item of items) {
        if (item.kind === "file" && item.type.startsWith("image/")) {
          event.preventDefault();
          const file = item.getAsFile();
          if (file) {
            const images: string[] = [];
            images.push(...attachImages);
            images.push(
              ...(await new Promise<string[]>((res, rej) => {
                setUploading(true);
                const imagesData: string[] = [];
                uploadImageRemote(file)
                  .then((dataUrl) => {
                    imagesData.push(dataUrl);
                    setUploading(false);
                    res(imagesData);
                  })
                  .catch((e) => {
                    setUploading(false);
                    rej(e);
                  });
              })),
            );
            const imagesLength = images.length;

            if (imagesLength > 3) {
              images.splice(3, imagesLength - 3);
            }
            setAttachImages(images);
          }
        }
      }
    },
    [attachImages, chatStore],
  );

  async function uploadImage() {
    const images: string[] = [];
    images.push(...attachImages);

    images.push(
      ...(await new Promise<string[]>((res, rej) => {
        const fileInput = document.createElement("input");
        fileInput.type = "file";
        fileInput.accept =
          "image/png, image/jpeg, image/webp, image/heic, image/heif";
        fileInput.multiple = true;
        fileInput.onchange = (event: any) => {
          setUploading(true);
          const files = event.target.files;
          const imagesData: string[] = [];
          for (let i = 0; i < files.length; i++) {
            const file = event.target.files[i];
            uploadImageRemote(file)
              .then((dataUrl) => {
                imagesData.push(dataUrl);
                if (
                  imagesData.length === 3 ||
                  imagesData.length === files.length
                ) {
                  setUploading(false);
                  res(imagesData);
                }
              })
              .catch((e) => {
                setUploading(false);
                rej(e);
              });
          }
        };
        fileInput.click();
      })),
    );

    const imagesLength = images.length;
    if (imagesLength > 3) {
      images.splice(3, imagesLength - 3);
    }
    setAttachImages(images);
  }
  // const promptStore = usePromptStore();
  // const [promptHints, setPromptHints] = useState<RenderPrompt[]>([]);
  // const onPromptSelect = (prompt: RenderPrompt) => {
  //   setTimeout(() => {
  //     setPromptHints([]);

  //     const matchedChatCommand = chatCommands.match(prompt.content);
  //     if (matchedChatCommand.matched) {
  //       // if user is selecting a chat command, just trigger it
  //       matchedChatCommand.invoke();
  //       setUserInput("");
  //     } else {
  //       // or fill the prompt
  //       setUserInput(prompt.content);
  //     }
  //     inputRef.current?.focus();
  //   }, 30);
  // };
  // const onSearch = useDebouncedCallback(
  //   (text: string) => {
  //     const matchedPrompts = promptStore.search(text);
  //     setPromptHints(matchedPrompts);
  //   },
  //   100,
  //   { leading: true, trailing: true },
  // );
  const onFileClick = (file: FileType) => {
    console.log("file", file)
    if (file.url) {
      if (file.type === "excel_url") {
        // Excel预览 - 可以使用在线Office预览或其他Excel预览组件
        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(file.url)}`, '_blank');
      } else if (file.type === "word_url") {
        // Word预览 - 同样可以使用在线Office预览
        window.open(`https://view.officeapps.live.com/op/view.aspx?src=${encodeURIComponent(file.url)}`, '_blank');
      } else if (file.type === "pdf_url") {
        // PDF预览 - 可以直接在新标签页打开或使用PDF预览组件
        window.open(file.url, '_blank');
      }
    }
  }
  // stop response
  const onUserStop = (messageId: string) => {
    ChatControllerPool.stop(session.id, messageId);
  };
  // 添加缓存逻辑
  const getInteractiveComponent = useCallback((template: string) => {
    console.log('interactiveComponents',interactiveComponents, template)
    const templateName = template.replace('.tsx', '');
    if (!interactiveComponents.has(template)) {
      const Component = dynamic(
        () => import(`../interactive/${templateName}.tsx`),
        {
          loading: () => <div>Loading...</div>,
          ssr: false,
        }
      );
      console.log('Component',Component)

      // 包装组件以确保样式正确应用
      const WrappedComponent = (props: any) => {
        useEffect(() => {
          // 触发样式更新
          const styleElement = document.createElement('style');
          document.head.appendChild(styleElement);
          document.head.removeChild(styleElement);
        }, []);

        return <Component {...props} />;
      };

      interactiveComponents.set(template, WrappedComponent);
    }
    return interactiveComponents.get(template);
  }, []);

  useEffect(() => {
    const handleKeyDown = (event: any) => {
      // 聚焦聊天输入 shift + esc
      if (event.shiftKey && event.key.toLowerCase() === "escape") {
        event.preventDefault();
        inputRef.current?.focus();
      }
      // 复制最后一个代码块 command + shift + ;
      else if (
        (event.metaKey || event.ctrlKey) &&
        event.shiftKey &&
        event.code === "Semicolon"
      ) {
        event.preventDefault();
        const copyCodeButton =
          document.querySelectorAll<HTMLElement>(".copy-code-button");
        if (copyCodeButton.length > 0) {
          copyCodeButton[copyCodeButton.length - 1].click();
        }
      }
      // 复制最后一个回复 command + shift + c
      // else if (
      //   (event.metaKey || event.ctrlKey) &&
      //   event.shiftKey &&
      //   event.key.toLowerCase() === "c"
      // ) {
      //   event.preventDefault();
      //   const lastNonUserMessage = messages
      //     .filter((message) => message.role !== "user")
      //     .pop();
      //   if (lastNonUserMessage) {
      //     const lastMessageContent = getMessageTextContent(lastNonUserMessage);
      //     copyToClipboard(lastMessageContent);
      //   }
      // }
    };

    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [messages, chatStore]);
  // const location = useLocation();
  // 处理自动发送和会话类型
  const hasExecuted = useRef(false);
  useEffect(() => {
    console.log('useEffect onUserInput', params)
    if (!hasExecuted.current) {
      console.log('useEffect onUserInput');
      const state = params as {
        autoSend?: boolean;
        content?: string;
        type?: SessionType;
      } | null;

      if (state?.type) {
        if (!session || session.type !== state.type) {
          chatStore.newTypedSession(state.type);
        }
      }

      if (state?.autoSend && state.content) {
        chatStore.updateTargetSession(session, (session) => {
          session.messages = [];
        });
        chatStore.onUserInput(state.content);
        window.history.replaceState({}, document.title);
      }

      hasExecuted.current = true;
    }
  }, [params]);
  // 在组件加载完成后触发样式注入
  useEffect(() => {
    // 触发一次样式更新
    const styleElement = document.createElement('style');
    document.head.appendChild(styleElement);
    document.head.removeChild(styleElement);
  }, []);
  return (
    <>
    {contextHolder}
      <div className={styles.chat} key={session.id}>
        <div className={styles["chat-main"]}>
          <div className={styles["chat-body-container"]}>
            <div
              className={styles["chat-body"]}
              ref={scrollRef}
              onScroll={(e) => onChatBodyScroll(e.currentTarget)}
              onMouseDown={() => inputRef.current?.blur()}
              onTouchStart={() => {
                inputRef.current?.blur();
                setAutoScroll(false);
              }}
            >
              {messages.map((message, i) => {
                const isUser = message.role === "user";
                const isContext = i < context.length;
                const isLast = i === messages.length - 1
                const showActions =
                  i > 0 &&
                  !(message.preview || message.content.length === 0) &&
                  !isContext;
                const showTyping = message.preview || message.streaming

                const shouldShowClearContextDivider =
                  i === clearContextIndex - 1;
                const prompts: PromptsProps['items'] = message.prompts?.map((item, index) => {
                  return {
                    icon: <BulbOutlined style={{ color: '#FFD700' }} />,
                    description: item,
                    key: `${index}`,
                  }
                }) || []
                // 添加底部提示词
                let showPrompts = !isUser && i > 0 &&
                  !(message.preview || message.content.length === 0 || message.streaming) &&
                  !isContext && prompts.length > 0 && isLast
                // 查百科
                if(session.type === SessionType.Encyclopaedia) {
                  showPrompts = false
                }
                return (
                  <Fragment key={message.id}>
                    <div
                      className={
                        isUser
                          ? styles["chat-message-user"]
                          : styles["chat-message"]
                      }
                    >
                      <div className={styles["chat-message-container"]}>
                        <div className={styles["chat-message-header"]}>
                          <div className={styles["chat-message-avatar"]}>
                            {isUser ? (
                              null
                            ) : (
                              <>
                                {["system"].includes(message.role) ? (
                                  <Avatar avatar="2699-fe0f" />
                                ) : (
                                  <Avatar model={message.model ||
                                    session.mask.modelConfig.model} />
                                )}
                              </>
                            )}
                          </div>
                          {!isUser && (
                            <div className={styles["chat-model-name"]}>
                              阿布
                            </div>
                          )}
                          {/* 时间 */}
                          <div className={styles["chat-message-action-date"]}>
                            {isContext
                              ? Locale.Chat.IsContext
                              : message.date.toLocaleString()}
                          </div>
                        </div>
                        {/* 加载中 */}
                        {message?.tools?.length == 0 && showTyping && (
                          <div className={styles["chat-message-status"]}>
                            {Locale.Chat.Typing}
                          </div>
                        )}
                        {/* 工具 */}
                        {message?.tools && message?.tools.length > 0 && (
                          <div className={styles["chat-message-tools"]}>
                            {message?.tools?.map((tool) => (
                              <div
                                key={tool.id}
                                title={tool?.errorMsg}
                                className={styles["chat-message-tool"]}
                              >
                                {tool.isError === false ? (
                                  <ConfirmIcon />
                                ) : tool.isError === true ? (
                                  <CloseIcon />
                                ) : (
                                  <LoadingButtonIcon />
                                )}
                                <span>{tool?.function?.name}</span>
                              </div>
                            ))}
                          </div>
                        )}
                        {message?.thought && message.thought.length === 0 && isLast && (
                          <div className="my-2">
                            <ThoughtChain
                              size={'small'}
                              items={message.thought.map(item => {
                                return {
                                  status: 'success',
                                  title: item.name,
                                  icon: <CheckCircleOutlined />,
                                }
                              })}
                            />
                          </div>
                        )}
                        <div className={styles["chat-message-item"]}>
                          {/* 文本 */}
                          <Markdown
                            key={message.streaming ? "loading" : "done"}
                            content={getMessageTextContent(message)}
                            loading={
                              (message.preview || message.streaming) &&
                              message.content.length === 0 &&
                              !isUser
                            }
                            onContextMenu={(e) => onRightClick(e, message)} // hard to use
                            onDoubleClickCapture={() => {
                              if (!isMobileScreen) return;
                              setUserInput(getMessageTextContent(message));
                            }}
                            fontSize={DEFAULT_CONFIG.fontSize}
                            fontFamily={DEFAULT_CONFIG.fontFamily}
                            parentRef={scrollRef}
                            defaultShow={i >= messages.length - 6}
                          />
                          {
                            getMessageInteractives(message).length > 0 && (
                              getMessageInteractives(message).map((interactive, index) => {
                                if(!interactive) return null;
                                const { template, props, events } = interactive;
                                const InteractiveComponent = getInteractiveComponent(template);
                                return (
                                  <InteractiveComponent
                                    key={`${message.id}-${index}`}
                                    props={props || null}
                                    events={events || null}
                                  />
                                )
                              })
                            )
                          }
                          {/* File */}
                          {getMessageFiles(message).length ? (
                              getMessageFiles(message).map((file, index) => {
                                return (
                                  <div key={index}
                                    className={styles["chat-message-item-file"]}
                                    onClick={() => onFileClick(file)}
                                  >
                                    {
                                      file.type === "excel_url" ? <ExcelIcon className={styles["chat-message-item-file-icon"]}/> : file.type === "word_url" ? <WordIcon className={styles["chat-message-item-file-icon"]}/> : <PDFIcon className={styles["chat-message-item-file-icon"]} />
                                    }
                                    <div className={styles["chat-message-item-file-name"]}>
                                      {file.name}
                                    </div>
                                  </div>
                                )
                              })
                          ) : null}
                          {/* 单图 */}
                          {getMessageImages(message).length == 1 && (
                            <img
                              className={styles["chat-message-item-image"]}
                              src={getMessageImages(message)[0]}
                              alt=""
                            />
                          )}
                          {/* 多图 */}
                          {getMessageImages(message).length > 1 && (
                            <div
                              className={styles["chat-message-item-images"]}
                              style={
                                {
                                  "--image-count":
                                  getMessageImages(message).length,
                                } as React.CSSProperties
                              }
                            >
                              {getMessageImages(message).map((image, index) => {
                                return (
                                  <img
                                    className={
                                      styles["chat-message-item-image-multi"]
                                    }
                                    key={index}
                                    src={image}
                                    alt=""
                                  />
                                );
                              })}
                            </div>
                          )}
                        </div>
                        {/* 音频 */}
                        {message?.audio_url && (
                          <div className={styles["chat-message-audio"]}>
                            <audio src={message.audio_url} controls />
                          </div>
                        )}

                        {showActions && (
                          <div className={styles["chat-message-actions"]}>
                            <div className={styles["chat-input-actions"]}>
                              {message.streaming ? (
                                <ChatAction
                                  text={Locale.Chat.Actions.Stop}
                                  icon={<StopIcon />}
                                  onClick={() => onUserStop(message.id ?? i)}
                                />
                              ) : (
                                <>
                                  <ChatAction
                                    text={Locale.Chat.Actions.Retry}
                                    icon={<ResetIcon />}
                                    onClick={() => onResend(message)}
                                  />

                                  <ChatAction
                                    text={Locale.Chat.Actions.Delete}
                                    icon={<DeleteIcon />}
                                    onClick={() => onDelete(message.id ?? i.toString())}
                                  />

                                  <ChatAction
                                    text={Locale.Chat.Actions.Copy}
                                    icon={<CopyIcon />}
                                    onClick={() =>
                                      copyToClipboard(
                                        getMessageTextContent(message),
                                      )
                                    }
                                  />
                                </>
                              )}
                            </div>
                          </div>
                        )}
                        {showPrompts && (
                          <Prompts classNames={{item: 'py-[8px]'}} title="你可能还想问？" items={prompts} vertical onItemClick={(info) => {
                            doSubmit(info.data.description as string)
                          }}/>
                        )}
                      </div>
                    </div>
                    {shouldShowClearContextDivider && <ClearContextDivider />}
                  </Fragment>
                );
              })}
            </div>
            <div className={styles["chat-input-panel"]}>
              {/* <PromptHints
                prompts={promptHints}
                onPromptSelect={onPromptSelect}
              /> */}

              <ChatActions
                uploadImage={uploadImage}
                setAttachImages={setAttachImages}
                setUploading={setUploading}
                scrollToBottom={scrollToBottom}
                // showPromptHints={() => {
                //   // Click again to close
                //   if (promptHints.length > 0) {
                //     setPromptHints([]);
                //     return;
                //   }

                //   inputRef.current?.focus();
                //   setUserInput("/");
                //   onSearch("");
                // }}
                hitBottom={hitBottom}
                uploading={uploading}
                setUserInput={setUserInput}
              />
              <label
                className={clsx(styles["chat-input-panel-inner"], {
                  [styles["chat-input-panel-inner-attach"]]:
                  attachImages.length !== 0,
                })}
                htmlFor="chat-input"
              >
                <textarea
                  id="chat-input"
                  ref={inputRef}
                  className={styles["chat-input"]}
                  placeholder={Locale.Chat.Input('Enter', session.type)}
                  onInput={(e) => onInput(e.currentTarget.value)}
                  value={userInput}
                  onKeyDown={onInputKeyDown}
                  onFocus={scrollToBottom}
                  onClick={scrollToBottom}
                  onPaste={handlePaste}
                  rows={inputRows}
                  autoFocus={autoFocus}
                  style={{
                    fontSize: DEFAULT_CONFIG.fontSize,
                    fontFamily: DEFAULT_CONFIG.fontFamily,
                  }}
                />
                {attachImages.length != 0 && (
                  <div className={styles["attach-images"]}>
                    {attachImages.map((image, index) => {
                      return (
                        <div
                          key={index}
                          className={styles["attach-image"]}
                          style={{ backgroundImage: `url("${image}")` }}
                        >
                          <div className={styles["attach-image-mask"]}>
                            <DeleteImageButton
                              deleteImage={() => {
                                setAttachImages(
                                  attachImages.filter((_, i) => i !== index),
                                );
                              }}
                            />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
                <IconButton
                  icon={<SendWhiteIcon />}
                  // text={Locale.Chat.Send}
                  className={styles["chat-input-send"]}
                  type="primary"
                  onClick={() => doSubmit(userInput)}
                />
              </label>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export function Chat() {
  // const session = chatStore.currentSession();
  // const location = useLocation();
  // 处理自动发送
  // useEffect(() => {
  //   const state = location.state as { autoSend?: boolean; content?: string } | null;
  //   if (state?.autoSend && state.content) {
  //     // 清空当前会话的消息记录
  //     chatStore.updateTargetSession(session, (session) => {
  //       session.messages = [];
  //     });
  //     chatStore.onUserInput(state.content);
  //     // 清除状态，防止刷新页面重复发送
  //     window.history.replaceState({}, document.title);
  //   }
  // }, []);
  return <CChat></CChat>;
}
