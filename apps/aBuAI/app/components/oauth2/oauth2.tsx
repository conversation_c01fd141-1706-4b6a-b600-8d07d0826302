import { BASE_PREFIX, View } from '@/common/constant'
import { useViewStore } from '@/store'
import { useEffect } from 'react'

export function getOAuth2Url(corp_id: string, agent_id: string) {
  const redirectbase = `${window.location.origin}${BASE_PREFIX}?appid=${corp_id}&agentid=${agent_id}`
  return `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corp_id}&redirect_uri=${encodeURIComponent(redirectbase)}&agentid=${agent_id}&response_type=code&scope=snsapi_base&state=#wechat_redirect`
}

export function redirectToOAuth2(corp_id: string, agent_id: string) {
  const oauth2Url = getOAuth2Url(corp_id, agent_id)
  window.location.href = oauth2Url
}


export function OAuth() {
  const { setCurrentView } = useViewStore();

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const corp_id = searchParams.get('appid') || ''
    const agent_id = searchParams.get('agentid') || ''

    if (corp_id && agent_id) {
      // 如果是从企业微信跳转回来的（带有 code 参数）
      const code = searchParams.get('code')
      if (code) {
        // 设置为登录视图，并携带必要参数
        setCurrentView(View.Login, {
          code,
          corp_id,
          agent_id,
        })
      } else {
        // 否则跳转到企业微信授权
        redirectToOAuth2(corp_id, agent_id)
      }
    }
  }, [])

  return <div>授权中...</div>
}
