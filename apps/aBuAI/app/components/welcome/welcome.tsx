import { Attachments, AttachmentsProps, Sender, Welcome } from '@ant-design/x';
import React, { useEffect, useState } from 'react';
import ABUuPng from '@/asserts/icons/aBu.png'
import { Bar<PERSON><PERSON>Outlined, BookOutlined, CloudUploadOutlined, Line<PERSON><PERSON>Outlined, FileSearchOutlined, LinkOutlined, ShoppingCartOutlined } from '@ant-design/icons';
import { Button, Divider, Flex, GetProp, GetRef, message, Space } from 'antd';
import { checkKey, useMobileScreen } from '@/common/utils/utils';
import classNames from 'classnames';
import Image from 'next/image';
import SendButton from "@ant-design/x/es/sender/components/SendButton"
import LoadingButton from "@ant-design/x/es/sender/components/LoadingButton"
import styles from './welcome.module.scss'
import { BASE_PREFIX, IS_WIDGET, View } from '@/common/constant';
import { SessionType } from "@/types/session";
import {  useAccessStore, useChatStore, useViewStore } from '@/store';
import { AuthorityKey } from '@/common/authorityKey';
interface WelcomeProps {
  className?: string
}

export const WelcomeComp = (props: WelcomeProps) => {
  const {className} = props

  const accessStore = useAccessStore();
  const userInfo = accessStore.userInfo;
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 6) return '凌晨好';
    if (hour < 9) return '早上好';
    if (hour < 12) return '上午好';
    if (hour < 14) return '中午好';
    if (hour < 18) return '下午好';
    if (hour < 24) return '晚上好';
    return '您好';
  };
  return <Welcome
          className={classNames('w-[500px] !bg-[unset]', className)}
          icon={
            <div className="w-16 h-16">
              {IS_WIDGET ? <img
                src={ABUuPng}
                alt="阿布AI"
                className="w-full h-full object-cover"
              /> : <Image src={ABUuPng} alt='阿布AILogo' width={64} height={64} className="w-full h-full object-cover"></Image>}
            </div>
          }
          title={!accessStore.isLoggedIn ? `您好，陌生人` : `${getGreeting()}，${userInfo?.user_name || ''}，我是阿布AI`}
          description="想让我帮您做些什么~"
        />
}
interface SenderCompProps {
  isMobileScreen: boolean;
  placeholder: string
}

export const SenderComp = ({ placeholder, isMobileScreen }: SenderCompProps) => {
  const [open, setOpen] = React.useState(false);
  const [items, setItems] = React.useState<GetProp<AttachmentsProps, 'items'>>([]);
  const [text, setText] = React.useState('');
  const [loading, setLoading] = React.useState(false);
  const { setCurrentView } = useViewStore();
  // const navigate = useNavigate();
  const accessStore = useAccessStore();
  const attachmentsRef = React.useRef<GetRef<typeof Attachments>>(null);
  const senderRef = React.useRef<GetRef<typeof Sender>>(null);

  const senderHeader = (
    <Sender.Header
      title="Attachments"
      styles={{
        content: {
          padding: 0,
        },
      }}
      open={open}
      onOpenChange={setOpen}
      forceRender
    >
      <Attachments
        ref={attachmentsRef}
        beforeUpload={() => false}
        items={items}
        onChange={({ fileList }) => setItems(fileList)}
        placeholder={(type) =>
          type === 'drop'
            ? {
                title: 'Drop file here',
              }
            : {
                icon: <CloudUploadOutlined />,
                title: 'Upload files',
                description: 'Click or drag files to this area to upload',
              }
        }
        getDropContainer={() => senderRef.current?.nativeElement}
      />
    </Sender.Header>
  );
  const chatStore = useChatStore();

  const handleSubmit = () => {
    if (!accessStore.isLoggedIn) {
      setCurrentView(View.Login);
      return;
    }
    setItems([]);
    setText('');
    chatStore.newSession();

    setCurrentView(View.Chat, {
      autoSend: true,
      content: text,
      type: SessionType.Chat
    });
  };

  return (
    <Sender
      {...(!isMobileScreen && !IS_WIDGET ? {
        rootClassName: styles.sender,
        classNames:{
          input: '!h-[100px] !max-h-[100px]',
        }}
      : { rootClassName: classNames(styles.sender, '!w-[auto] m-2') })}
      className='mt-2'
      ref={senderRef}
      header={senderHeader}
      actions={
        <div>
          <Button
            type="text"
            icon={<LinkOutlined />}
            onClick={() => {
              setOpen(!open);
            }}
          />
          <Divider type='vertical'></Divider>
          {loading ? <LoadingButton></LoadingButton> : <SendButton></SendButton>}
        </div>
      }
      loading={loading}
      value={text}
      onChange={setText}
      onPasteFile={(file) => {
        attachmentsRef.current?.upload(file);
        setOpen(true);
      }}
      placeholder={placeholder}
      onSubmit={handleSubmit}
    />
  );
};
export const WelcomePage = () => {
  const isMobileScreen = useMobileScreen()
  const { setCurrentView } = useViewStore();
  // const navigate = useNavigate();
  const accessStore = useAccessStore();
  const MenuComp = () => {
    const handleClick = (view: View) => {
      if (!accessStore.isAuthorized()) {
        setCurrentView(view);
        return;
      }
      setCurrentView(view);
    };
    return <Space className='m-2' wrap>
          {checkKey(AuthorityKey.sale_order) ? <Button shape="round"
          onClick={() => handleClick(View.Order)} icon={
            <ShoppingCartOutlined className="text-blue-500 text-lg" />
          }>下单</Button> : null}
          {checkKey(AuthorityKey.encyclopaedia) ? <Button onClick={() => handleClick(View.Encyclopaedia)} shape="round" icon={
            <BookOutlined className="text-orange-500 text-lg" />
          }>查百科</Button> : null}
          {checkKey(AuthorityKey.data_analysis) ? <Button onClick={() => handleClick(View.DataAnalysis)} shape="round" icon={
            <BarChartOutlined className="text-purple-500 text-lg" />
          }>数据分析</Button> : null}
          {/* {checkKey(AuthorityKey.sales_forecast) ? <Button onClick={() => handleClick(View.Salesforecast)} shape="round" icon={
            <LineChartOutlined className="text-green-500 text-lg" />
          }>销售预测</Button> : null} */}
          {checkKey(AuthorityKey.fabric_quotation) ? <Button onClick={() => handleClick(View.FabricQuotation)} shape="round" icon={
            <LineChartOutlined className="text-green-500 text-lg" />
          }>面料价格</Button> : null}
          {checkKey(AuthorityKey.stock) ? <Button onClick={() => handleClick(View.Stock)} shape="round" icon={
            <FileSearchOutlined className="text-green-500 text-lg" />
          }>查库存</Button> : null}
        </Space>
  }
  return (
    <div className='flex justify-center items-center h-full'>
      {!isMobileScreen && !IS_WIDGET ? <Flex vertical>
        <WelcomeComp/>
        <SenderComp placeholder='查找关于纺织行业相关的知识，如市场、政策、工艺等信息' isMobileScreen={isMobileScreen} />
        <MenuComp/>
      </Flex>: <Flex vertical className='h-full w-full'>
        <Flex className='flex-1' vertical>
          <WelcomeComp className='w-full'/>
          <MenuComp/>
        </Flex>
        <SenderComp isMobileScreen={isMobileScreen} />
        </Flex>}
    </div>
  );
}
