import { LoadingOutlined } from '@ant-design/icons';
import React from 'react';

const Loading = () => {
  return (
    <div className="gpt-vis-loading flex items-center justify-center flex-col h-[300px] bg-gradient-to-br from-[#e3f3ff] to-[#f1eeff]">
      <div className="gpt-vis-loading-icon mb-2">
        <LoadingOutlined style={{ fontSize: '24px', color: 'rgb(56, 177, 246)' }} />
      </div>
      <p className='text-[#333]'>数据生成中</p>
    </div>
  );
};

export default Loading;
