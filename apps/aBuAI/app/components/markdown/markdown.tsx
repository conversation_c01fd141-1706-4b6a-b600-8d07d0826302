import ReactMarkdown from "react-markdown";
import "katex/dist/katex.min.css";
import RemarkMath from "remark-math";
import RemarkBreaks from "remark-breaks";
import RehypeKatex from "rehype-katex";
import RemarkGfm from "remark-gfm";
import Rehype<PERSON>ighlight from "rehype-highlight";
import { useRef, useState, RefObject, useEffect, useMemo, memo } from "react";
import { copyToClipboard, useWindowSize } from "@/common/utils/utils";
import mermaid from "mermaid";
import Locale from "@/locales";
import LoadingIcon from "@/asserts/icons/three-dots.svg";
import ReloadButtonIcon from "@/asserts/icons/reload.svg";
import React from "react";
import { useDebouncedCallback } from "use-debounce";
import { showImageModal, FullScreen } from "@/components/ui-lib/ui-lib";
import '../../styles/markdown.scss'
import styles from './markdown.module.scss'
import {
  HTMLPreview,
  HTMLPreviewHander,
} from "../artifacts/artifacts";
import { useChatStore } from "@/store";
import { IconButton } from "@/components/button/button";

import { useAppConfig } from "@/store/config";
import clsx from "clsx";
import { Chart } from "./chart";
import { Chart001 } from "./chart001";
import Loading from "./loading";
import { ChartComponents, ChartJson, CodeBlockComponent, WithChartCodeOptions } from "./chat";

export function Mermaid(props: { code: string }) {
  const ref = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    if (props.code && ref.current) {
      mermaid
        .run({
          nodes: [ref.current],
          suppressErrors: true,
        })
        .catch((e) => {
          setHasError(true);
          console.error("[Mermaid] ", e.message);
        });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [props.code]);

  function viewSvgInNewWindow() {
    const svg = ref.current?.querySelector("svg");
    if (!svg) return;
    const text = new XMLSerializer().serializeToString(svg);
    const blob = new Blob([text], { type: "image/svg+xml" });
    showImageModal(URL.createObjectURL(blob));
  }

  if (hasError) {
    return null;
  }

  return (
    <div
      className={clsx("no-dark", "mermaid")}
      style={{
        cursor: "pointer",
        overflow: "auto",
      }}
      ref={ref}
      onClick={() => viewSvgInNewWindow()}
    >
      {props.code}
    </div>
  );
}

export function PreCode(props: { children: any }) {
  const ref = useRef<HTMLPreElement>(null);
  const previewRef = useRef<HTMLPreviewHander>(null);
  const [mermaidCode, setMermaidCode] = useState("");
  const [htmlCode, setHtmlCode] = useState("");
  const { height } = useWindowSize();
  const chatStore = useChatStore();
  const session = chatStore.currentSession();
  // const [chartCode, setChartCode] = useState("");
  // const [chart001Code, setChart001Code] = useState("");
  const renderArtifacts = useDebouncedCallback(() => {
    if (!ref.current) return;
    const mermaidDom = ref.current.querySelector("code.language-mermaid");
    if (mermaidDom) {
      setMermaidCode((mermaidDom as HTMLElement).innerText);
    }
    const htmlDom = ref.current.querySelector("code.language-html");
    const refText = ref.current.querySelector("code")?.innerText;
    // const chartDom = ref.current.querySelector("code.language-chart");
    // const chart001Dom = ref.current.querySelector("code.language-chart001");
    // console.log('chartDom',chart001Dom)
    // if (chartDom) {
    //   setChartCode((chartDom as HTMLElement).innerText);
    // }
    // if(chart001Dom){
    //   setChart001Code((chart001Dom as HTMLElement).innerText);
    // }
    if (htmlDom) {
      setHtmlCode((htmlDom as HTMLElement).innerText);
    } else if (
      refText?.startsWith("<!DOCTYPE") ||
      refText?.startsWith("<svg") ||
      refText?.startsWith("<?xml")
    ) {
      setHtmlCode(refText);
    }
  }, 600);

  const config = useAppConfig();
  const enableArtifacts =
    session.mask?.enableArtifacts !== false && config.enableArtifacts;

  //Wrap the paragraph for plain-text
  useEffect(() => {
    if (ref.current) {
      const codeElements = ref.current.querySelectorAll(
        "code",
      ) as NodeListOf<HTMLElement>;
      const wrapLanguages = [
        "",
        "md",
        "markdown",
        "text",
        "txt",
        "plaintext",
        "tex",
        "latex",
      ];
      codeElements.forEach((codeElement) => {
        const languageClass = codeElement.className.match(/language-(\w+)/);
        const name = languageClass ? languageClass[1] : "";
        if (wrapLanguages.includes(name)) {
          codeElement.style.whiteSpace = "pre-wrap";
        }
      });
      setTimeout(renderArtifacts, 1);
    }
  }, []);
  
  const containerRef = useRef<HTMLDivElement>(null);
  // function handleChartLoadEnd() {
  //   // 移除所有 chart-loading 类的元素
  //   if (containerRef.current) {
  //     const loadingElements = containerRef.current.getElementsByClassName('chart-loading');
  //     while (loadingElements.length > 0) {
  //       loadingElements[0].remove();
  //     }
  //   }
  // }
  return (
    <div ref={containerRef}>
      <pre ref={ref}>
        <span
          className="copy-code-button" 
          onClick={() => {
            if (ref.current) {
              copyToClipboard(
                ref.current.querySelector("code")?.innerText ?? "",
              );
            }
          }}
        ></span>
        {props.children}
      </pre>
      {/* {chartCode.length > 0 && (
        <div className="my-4">
          <div className="flex justify-center items-center py-4 chart-loading">
            <LoadingIcon />
          </div>
          <Chart
            code={chartCode} 
            key={chartCode} 
            onLoad={handleChartLoadEnd}
          />
        </div>
      )}
      {chart001Code.length > 0 && (
        <div className="my-4">
          <div className="flex justify-center items-center py-4 chart-loading">
            <LoadingIcon />
          </div>
          <Chart001
            code={chart001Code} 
            key={chart001Code} 
            onLoad={handleChartLoadEnd}
          />
        </div>
      )} */}
      {mermaidCode.length > 0 && (
        <Mermaid code={mermaidCode} key={mermaidCode} />
      )}
      {htmlCode.length > 0 && enableArtifacts && (
        <FullScreen className="no-dark html" right={70}>
          {/* <ArtifactsShareButton
            style={{ position: "absolute", right: 20, top: 10 }}
            getCode={() => htmlCode}
          /> */}
          <IconButton
            style={{ position: "absolute", right: 120, top: 10 }}
            bordered
            icon={<ReloadButtonIcon />}
            shadow
            onClick={() => previewRef.current?.reload()}
          />
          <HTMLPreview
            ref={previewRef}
            code={htmlCode}
            autoHeight={!document.fullscreenElement}
            height={!document.fullscreenElement ? 600 : height}
          />
        </FullScreen>
      )}
    </div>
  );
}
interface RenderVisChartProps {
  className?: string;
  content: string;
  components: ChartComponents;
  loadingTimeout: number;
}
const RenderEchat: React.FC<RenderVisChartProps> = memo(({content, className='',components, loadingTimeout}) => {
  const [loading, setLoading] = useState(true)
  const timeoutRef = useRef<NodeJS.Timeout>();
  let chartJson: ChartJson;
  console.log('render RenderEchat', loading)
  try{
    chartJson = JSON.parse(content);

  }catch(e){
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = setTimeout(() => {
      setLoading(false);
    }, loadingTimeout);

    if (loading) {
      return (
        <Loading />
      );
    }

    return <p>Chart generation timeout.</p>;
  }
  
  const isChart001 = className.includes('language-chart001');
  if(isChart001){
    return <Chart001
      code={chartJson} 
    />
  }
  const { type, ...chartProps } = chartJson;
  const ChartComponent = components[type];
  // If the chart type is not supported, display an error message
  if (!ChartComponent) {
    return <p>{`Chart type "${type}" is not supported.`}</p>;
  }
        
  return <ChartComponent {...chartProps} />
})
const withCodeBlock = (options: WithChartCodeOptions): CodeBlockComponent => {
  // Render code block component
  return function CodeBlock(props) {
    const { children, className = '' } = props;
    const content = String(children).trim();
    const isChart = className.includes('language-chart');
    const {
      components,
      languageRenderers,
      defaultRenderer: DefaultRenderer,
      loadingTimeout = 5000,
    } = options;

    // If the code block is a VisChart, render the corresponding chart component
    if (isChart) {
      return (
        <div className='chart'>
          <RenderEchat
            className={className}
            content={content}
            components={components}
            loadingTimeout={loadingTimeout}
          />
        </div>
      );
    }

    // If the code block math extraRenderer  languageName, the corresponding extra languageRenderers component
    const languageName = className.match(/language-(.*)/)?.[1] || '';
    const extraLanguageRenderers = languageRenderers;
    const ExtraRendererComponent = extraLanguageRenderers && extraLanguageRenderers[languageName];
    if (ExtraRendererComponent) {
      console.log('is ExtraRendererComponent')
      return <ExtraRendererComponent {...props} />;
    }

    // If the code block is not a VisChart, render plain code
    return <div className="chart">
      {DefaultRenderer ? <DefaultRenderer {...props} /> : <CustomCode {...props} />}
    </div>;
  }
}
const CustomCode: CodeBlockComponent = (props) => {
  const { children, className = '', node, ...rest } = props;
  const chatStore = useChatStore();
  const session = chatStore.currentSession();
  const config = useAppConfig();
  const enableCodeFold =
    session.mask?.enableCodeFold !== false && config.enableCodeFold;

  const ref = useRef<HTMLPreElement>(null);
  const [collapsed, setCollapsed] = useState(true);
  const [showToggle, setShowToggle] = useState(false);

  useEffect(() => {
    if (ref.current) {
      const codeHeight = ref.current.scrollHeight;
      setShowToggle(codeHeight > 400);
      ref.current.scrollTop = ref.current.scrollHeight;
    }
  }, [children]);

  const toggleCollapsed = () => {
    setCollapsed((collapsed) => !collapsed);
  };
  const renderShowMoreButton = () => {
    if (showToggle && enableCodeFold && collapsed) {
      return (
        <div
          className={clsx("show-hide-button", {
            collapsed,
            expanded: !collapsed,
          })}
        >
          <button onClick={toggleCollapsed}>{Locale.NewChat.More}</button>
        </div>
      );
    }
    return null;
  };
  return (
    <>
      <code
        className={clsx(className)}
        ref={ref}
        style={{
          maxHeight: enableCodeFold && collapsed ? "400px" : "none",
          overflowY: "hidden",
        }}
      >
        {children}
      </code>

      {renderShowMoreButton()}
    </>
  );
}
// Create a higher-order component (HOC) with chart code
export const withChartCode = (options: WithChartCodeOptions): CodeBlockComponent => {
  return withCodeBlock(options);
};
function escapeBrackets(text: string) {
  const pattern =
    /(```[\s\S]*?```|`.*?`)|\\\[([\s\S]*?[^\\])\\\]|\\\((.*?)\\\)/g;
  return text.replace(
    pattern,
    (match, codeBlock, squareBracket, roundBracket) => {
      if (codeBlock) {
        return codeBlock;
      } else if (squareBracket) {
        return `$$${squareBracket}$$`;
      } else if (roundBracket) {
        return `$${roundBracket}$`;
      }
      return match;
    },
  );
}

function tryWrapHtmlCode(text: string) {
  // try add wrap html code (fixed: html codeblock include 2 newline)
  return text
    .replace(
      /([`]*?)(\w*?)([\n\r]*?)(<!DOCTYPE html>)/g,
      (match, quoteStart, lang, newLine, doctype) => {
        return !quoteStart ? "\n```html\n" + doctype : match;
      },
    )
    .replace(
      /(<\/body>)([\r\n\s]*?)(<\/html>)([\n\r]*)([`]*)([\n\r]*?)/g,
      (match, bodyEnd, space, htmlEnd, newLine, quoteEnd) => {
        return !quoteEnd ? bodyEnd + space + htmlEnd + "\n```\n" : match;
      },
    );
}
const CodeComponent = withChartCode({
  components: { 'chart': Chart },
  languageRenderers: {'chart': Chart},
  loadingTimeout: 3000,
});
function _MarkDownContent(props: { content: string }) {
  const escapedContent = useMemo(() => {
    return tryWrapHtmlCode(escapeBrackets(props.content));
  }, [props.content]);

  return (
    <ReactMarkdown
      remarkPlugins={[RemarkMath, RemarkGfm, RemarkBreaks]}
      rehypePlugins={[
        RehypeKatex,
        [
          RehypeHighlight,
          {
            detect: false,
            ignoreMissing: true,
          },
        ],
      ]}
      components={{
        pre: PreCode,
        code: CodeComponent,
        p: (pProps) => {
          return <p {...pProps} dir="auto" />
        },
        table: (props) => (
          <div className="table-container">
            <table {...props} className="markdown-table" />
          </div>
        ),
        thead: (props) => <thead {...props} />,
        tbody: (props) => <tbody {...props} />,
        tr: (props) => <tr {...props} />,
        th: (props) => <th {...props} />,
        td: (props) => <td {...props} />,
        a: (aProps) => {
          const href = aProps.href || "";
          if (/\.(aac|mp3|opus|wav)$/.test(href)) {
            return (
              <figure>
                <audio controls src={href}></audio>
              </figure>
            );
          }
          if (/\.(3gp|3g2|webm|ogv|mpeg|mp4|avi)$/.test(href)) {
            return (
              <video controls width="99.9%">
                <source src={href} />
              </video>
            );
          }
          const isInternal = /^\/#/i.test(href);
          const target = isInternal ? "_self" : aProps.target ?? "_blank";
          return <a {...aProps} target={target} />;
        },
      }}
    >
      {escapedContent}
    </ReactMarkdown>
  );
}

export const MarkdownContent = React.memo(_MarkDownContent);

export function Markdown(
  props: {
    content: string;
    loading?: boolean;
    fontSize?: number;
    fontFamily?: string;
    parentRef?: RefObject<HTMLDivElement>;
    defaultShow?: boolean;
  } & React.DOMAttributes<HTMLDivElement>,
) {
  const mdRef = useRef<HTMLDivElement>(null);

  return (
    <div
      className="markdown-body"
      style={{
        fontSize: `${props.fontSize ?? 14}px`,
        fontFamily: props.fontFamily || "inherit",
      }}
      ref={mdRef}
      onContextMenu={props.onContextMenu}
      onDoubleClickCapture={props.onDoubleClickCapture}
      dir="auto"
    >
      {props.loading ? (
        <LoadingIcon />
      ) : (
        <MarkdownContent content={props.content} />
      )}
    </div>
  );
}
