import { useEffect, useRef, useState } from "react";
import * as echarts from 'echarts';

export function Chart({ code, onLoad }: { code: string, onLoad?:() => void }) {
  const chartRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  console.log('chart code',code)
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    if (!chartRef.current) return;
    
    try {
      const chartData = JSON.parse(code);
      console.log('chartData',chartData)
      const chart = echarts.init(chartRef.current);
      // 通用配置
      const commonOptions = {
        title: {
          text: chartData.title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            color: '#000'
          }
        },
        legend: {
          show: true,
          right: '10%',
          top: '5%',
          textStyle: {
            fontSize: 12,
            color: '#000'
          }
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          backgroundColor: '#fff',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#666',
            fontSize: 12
          },
          formatter: function(params) {
            if (Array.isArray(params)) {
              return params.map(item => 
                `${item.seriesName}: ${item.value.toFixed(2)}`
              ).join('<br/>');
            }
            return `${params.name}: ${params.value.toFixed(2)}`;
          }
        }
      };
      // 不同图表类型的特定配置
      const typeSpecificOptions = {
        bar: {
          xAxis: {
            type: 'category',
            data: chartData?.xAxis?.data,
            axisLabel: {
              fontSize: 12,
              color: '#666'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#666',
              interval: 4
            },
            splitLine: { show: true }
          },
          series: chartData.series.map(item => ({
            name: item.name,
            type: 'bar',
            barWidth: 10,
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 12,
              color: '#000',
              formatter: '{c}'
            },
            data: item?.data
          }))
        },
        line: {
          xAxis: {
            type: 'category',
            data: chartData.data?.map(item => item.name),
            axisLabel: {
              fontSize: 12,
              color: '#666'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#666',
              interval: 4
            },
            splitLine: { show: true }
          },
          series: [{
            name: '', // 添加这行，使用图表标题或默认值
            type: 'line',
            lineStyle: { width: 2 },
            itemStyle: { color: '#409EFF' },
            symbol: 'circle',
            symbolSize: 6,
            label: {
              show: true,
              position: 'top',
              fontSize: 12,
              color: '#000',
              formatter: '{c}'
            },
            data: chartData.data?.map(item => item.value)
          }]
        },
        pie: {
          series: chartData.series.map(item => ({
            name: item.name, // 添加这行，使用图表标题或默认值
            type: 'pie',
            radius: '50%',
            center: ['50%', '50%'],
            data: item.data,
            label: {
              show: true,
              formatter: '{b}: {d}%',
              fontSize: 12,
              color: '#666'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
                fontWeight: 'bold'
              }
            }
          }))
        },
        // 散点图
        scatter: {
          xAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#666'
            },
            splitLine: { show: true }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#666'
            },
            splitLine: { show: true }
          },
          series: [{
            name: '',
            type: 'scatter',
            symbolSize: 12,
            itemStyle: {
              color: '#409EFF',
              opacity: 0.7
            },
            label: {
              show: true,
              position: 'top',
              fontSize: 12,
              color: '#000',
              formatter: '{c}'
            },
            data: chartData.data?.map(item => [item.x, item.y])
          }]
        },
        // 热力图
        heatmap: {
          grid: {
            top: '10%',
            right: '15%'
          },
          xAxis: {
            type: 'category',
            data: chartData.xAxis,
            axisLabel: {
              fontSize: 12,
              color: '#666'
            }
          },
          yAxis: {
            type: 'category',
            data: chartData.yAxis,
            axisLabel: {
              fontSize: 12,
              color: '#666'
            }
          },
          visualMap: {
            min: 0,
            max: chartData.maxValue || 100,
            calculable: true,
            orient: 'vertical',
            right: '0%',
            top: 'center',
            inRange: {
              color: ['#e9f7fe', '#409EFF']
            },
            textStyle: {
              color: '#666'
            }
          },
          series: [{
            name: '',
            type: 'heatmap',
            data: chartData.data,
            label: {
              show: true,
              fontSize: 12,
              color: '#000'
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }]
        }
        // 其他图表类型的配置可以继续添加...
      };
      // 合并通用配置和特定配置
      const finalOptions = {
        ...commonOptions,
        ...(typeSpecificOptions[chartData.type] || {}) // 这里通过chartData.type索引拿到特定配置
      };
      chart.setOption(finalOptions);
      setIsLoading(false);
      onLoad?.();

      const resizeHandler = () => {
        chart.resize();
      };
      window.addEventListener('resize', resizeHandler);

      return () => {
        chart.dispose();
        window.removeEventListener('resize', resizeHandler);
      };
    } catch (e) {
      console.error("[Chart] ", e);
      setHasError(true);
      setIsLoading(false);
      onLoad?.();
    }
  }, [code, onLoad]);

  if (hasError) return null;

  return (
    <>
      <div className="relative">
        <div
          ref={chartRef} 
          style={{ 
            width: '100%', 
            height: '400px',
            opacity: isLoading ? 0 : 1,
            transition: 'opacity 0.3s'
          }}
        />
      </div>
    </>
  );
}
