import { useEffect, useRef, useState } from "react";
import * as echarts from 'echarts';
import { useMobileScreen } from "@/common/utils/utils";
import { ChartJson } from "./chat";

export function Chart001({ code, onLoad }: { code: ChartJson, onLoad?:() => void }) {
  const chartRef = useRef<HTMLDivElement>(null);
  const [hasError, setHasError] = useState(false);
  console.log('chart code',code)
  const isMobileScreen = useMobileScreen();
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    if (!chartRef.current) return;
    
    try {
      console.log('code',code)
      const chart = echarts.init(chartRef.current);
      // 通用配置
      const commonOptions = {
        title: {
          text: code.title,
          left: 'center',
          textStyle: {
            fontSize: 18,
            color: '#000'
          }
        },
        legend: {
          show: true,
          right: '10%',
          top: '5%',
          textStyle: {
            fontSize: 12,
            color: '#000'
          }
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          backgroundColor: '#fff',
          borderColor: '#ccc',
          borderWidth: 1,
          textStyle: {
            color: '#666',
            fontSize: 12
          },
          formatter: function(params) {
            if (Array.isArray(params)) {
              const time = params[0]?.name || '';
              const content = params.map(item => 
                `${item.seriesName}: ${item.value.toFixed(2)}`
              ).join('<br/>');
              return `日期: ${time}<br/>${content}`;
            }
            return `日期: ${params.name}<br/>${params.seriesName}: ${params.value.toFixed(2)}`;
          }
        }
      };
      // 不同图表类型的特定配置
      const typeSpecificOptions = {
        bar: {
          color: ['#fac858', '#91cc75'], // 添加颜色配置
          legend: {
            data: ['销售金额', '销售匹数'],
            show: true,
            // right: '0%',
            top: '5%',
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          xAxis: [
            {
              type: 'category',
              data: code?.xAxis?.data,
              axisLabel: {
                fontSize: 12,
                color: '#666'
              }
            }
          ],
          grid: {
            bottom: '30'
          },
          yAxis: [
            {
              type: 'value',
              name: '销售金额',
              position: 'right',
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#fac858'
                }
              },
              axisLabel: {
                formatter: '{value} 元',
                fontSize: 12,
                color: '#666'
              },
              splitLine: { show: true }
            },
            {
              type: 'value',
              name: '销售匹数',
              position: 'left',
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#91cc75'
                }
              },
              axisLabel: {
                formatter: '{value} 匹',
                fontSize: 12,
                color: '#666'
              },
              splitLine: { show: false }
            }
          ],
          series: [
            {
              name: '销售金额',
              type: 'bar',
              barWidth: 10,
              itemStyle: {
                color: '#fac858'
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 12,
                color: '#000',
                formatter: '{c}'
              },
              data: code.series?.[0]?.data || []
            },
            {
              name: '销售匹数',
              type: 'bar',
              yAxisIndex: 1,
              barWidth: 10,
              itemStyle: {
                color: '#91cc75'
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 12,
                color: '#000',
                formatter: '{c}'
              },
              data: code.series?.[1]?.data || []
            }
          ]
        },
        line: {
          color: ['#fac858', '#91cc75'], // 添加颜色配置
          legend: {
            data: ['销售金额', '销售匹数'],
            show: true,
            // right: '0%',
            top: '5%',
            textStyle: {
              fontSize: 12,
              color: '#000'
            }
          },
          xAxis: [
            {
              type: 'category',
              data: code?.xAxis?.data,
              axisLabel: {
                fontSize: 12,
                color: '#666'
              }
            }
          ],
          grid: {
            bottom: '30'
          },
          yAxis: [
            {
              type: 'value',
              name: '销售金额',
              position: 'right',
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#fac858'
                }
              },
              axisLabel: {
                formatter: '{value} 元',
                fontSize: 12,
                color: '#666'
              },
              splitLine: { show: true }
            },
            {
              type: 'value',
              name: '销售匹数',
              position: 'left',
              alignTicks: true,
              axisLine: {
                show: true,
                lineStyle: {
                  color: '#91cc75'
                }
              },
              axisLabel: {
                formatter: '{value} 匹',
                fontSize: 12,
                color: '#666'
              },
              splitLine: { show: false }
            }
          ],
          series: [
            {
              name: '销售金额',
              type: 'line',
              barWidth: 10,
              itemStyle: {
                color: '#fac858'
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 12,
                color: '#000',
                formatter: '{c}'
              },
              data: code.series?.[0]?.data || []
            },
            {
              name: '销售匹数',
              type: 'line',
              yAxisIndex: 1,
              barWidth: 10,
              itemStyle: {
                color: '#91cc75'
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 12,
                color: '#000',
                formatter: '{c}'
              },
              data: code.series?.[1]?.data || []
            }
          ]
        },
      };
      // 合并通用配置和特定配置
      const finalOptions = {
        ...commonOptions,
        ...(typeSpecificOptions[code.type] || {}) // 这里通过code.type索引拿到特定配置
      };
      chart.setOption(finalOptions);
      setIsLoading(false);
      onLoad?.();

      const resizeHandler = () => {
        chart.resize();
      };
      window.addEventListener('resize', resizeHandler);

      return () => {
        chart.dispose();
        window.removeEventListener('resize', resizeHandler);
      };
    } catch (e) {
      console.error("[Chart] ", e);
      setHasError(true);
      setIsLoading(false);
      onLoad?.();
    }
  }, [code, onLoad]);

  if (hasError) return null;

  return (
    <div
      ref={chartRef} 
      style={{ 
        width: '100%',
        minWidth: isMobileScreen ? '330px':'500px',
        height: '400px',
        opacity: isLoading ? 0 : 1,
        transition: 'opacity 0.3s'
      }}
    />
  );
}
