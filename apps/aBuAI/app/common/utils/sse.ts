interface SSEMessage {
  type: 'delta' | 'message_end' | 'workflow' | 'ping' | 'unknown';
  content?: string;
  message_id?: string;
  conversation_id?: string;
  task_id?: string;
  metadata?: any;
  created_at?: number;
}

interface SSEOptions {
  onMessage?: (message: SSEMessage) => void;
  onComplete?: (result: any) => void;
  onError?: (error: Error) => void;
  onOpen?: () => void;
  onClose?: () => void;
}

export class SSEClient {
  private eventSource: EventSource | null = null;
  private options: SSEOptions;
  private completeAnswer = '';

  constructor(options: SSEOptions) {
    this.options = options;
  }

  connect(url: string): void {
    if (this.eventSource) {
      this.eventSource.close();
    }

    this.completeAnswer = '';
    this.eventSource = new EventSource(url);

    this.eventSource.onopen = () => {
      this.options.onOpen?.();
    };

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.warn('Failed to parse SSE message:', error);
      }
    };

    this.eventSource.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data.type === 'delta') {
          this.completeAnswer += data.content || '';
          this.options.onMessage?.({
            type: 'delta',
            content: data.content,
            message_id: data.message_id,
            conversation_id: data.conversation_id,
            task_id: data.task_id
          });
        }
      } catch (error) {
        console.warn('Failed to parse delta message:', error);
      }
    });

    this.eventSource.addEventListener('event', (event) => {
      try {
        const data = JSON.parse(event.data);
        this.handleMessage(data);
      } catch (error) {
        console.warn('Failed to parse event message:', error);
      }
    });

    this.eventSource.addEventListener('complete', (event) => {
      try {
        const data = JSON.parse(event.data);
        this.options.onComplete?.(data);
      } catch (error) {
        console.warn('Failed to parse complete message:', error);
      }
    });

    this.eventSource.onerror = () => {
      this.options.onError?.(new Error('SSE connection error'));
    };
  }

  private handleMessage(data: any): void {
    const message: SSEMessage = {
      type: data.type || 'unknown',
      content: data.content,
      message_id: data.message_id,
      conversation_id: data.conversation_id,
      task_id: data.task_id,
      metadata: data.metadata,
      created_at: data.created_at
    };

    this.options.onMessage?.(message);
  }

  disconnect(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
      this.options.onClose?.();
    }
  }

  getCompleteAnswer(): string {
    return this.completeAnswer;
  }

  isConnected(): boolean {
    return this.eventSource !== null && this.eventSource.readyState === EventSource.OPEN;
  }
}

export function createSSEUrl(baseUrl: string, params: {
  query: string;
  user: string;
  conversation_id?: string;
}): string {
  const url = new URL(`${baseUrl}/api/dify/chat/sse`);
  url.searchParams.append('query', params.query);
  url.searchParams.append('user', params.user);
  
  if (params.conversation_id) {
    url.searchParams.append('conversation_id', params.conversation_id);
  }

  return url.toString();
}