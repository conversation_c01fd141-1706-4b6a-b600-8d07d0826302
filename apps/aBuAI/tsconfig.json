{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": false, "noEmit": false, "incremental": true, "module": "esnext", "esModuleInterop": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}], "types": ["node", "vite/client"], "typeRoots": ["./node_modules/@types"], "paths": {"abu-ai": ["./.widget/abu-ai.es.js"], "@/*": ["./app/*"]}, "strictNullChecks": true}, "include": ["**/*.svg", "**/*.ts", "**/*.tsx", "app/types/**/*.d.ts", "./dist/types/**/*.ts", "next-env.d.ts", "vite-env.d.ts", "types/**/*", "vite.widget.config.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}